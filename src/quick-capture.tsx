import { useState, useEffect } from "react";
import {
  Form,
  ActionPanel,
  Action,
  showToast,
  Toast,
  List,
  getPreferenceValues,
  LocalStorage,
  Clipboard,
  showHUD,
} from "@raycast/api";
import { Client } from "@notionhq/client";

interface Preferences {
  notionToken: string;
  databaseId: string;
  imgurClientId: string;
}

interface CaptureRecord {
  id: string;
  title: string;
  url: string;
  createdAt: string;
}

export default function QuickCapture() {
  const [text, setText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [recentRecords, setRecentRecords] = useState<CaptureRecord[]>([]);
  const [hasImage, setHasImage] = useState(false);
  const preferences = getPreferenceValues<Preferences>();

  useEffect(() => {
    loadRecentRecords();
    checkClipboardForImage();
  }, []);

  const loadRecentRecords = async () => {
    try {
      const stored = await LocalStorage.getItem<string>("recentRecords");
      if (stored) {
        setRecentRecords(JSON.parse(stored));
      }
    } catch (error) {
      console.error("Failed to load recent records:", error);
    }
  };

  const checkClipboardForImage = async () => {
    try {
      const clipboardContent = await Clipboard.read();
      setHasImage(!!clipboardContent.file && clipboardContent.file.startsWith("data:image"));
    } catch (error) {
      console.error("Failed to check clipboard:", error);
    }
  };

  const saveRecord = async (record: CaptureRecord) => {
    const updatedRecords = [record, ...recentRecords.slice(0, 9)];
    setRecentRecords(updatedRecords);
    await LocalStorage.setItem("recentRecords", JSON.stringify(updatedRecords));
  };

  const handleSubmit = async () => {
    if (!text.trim()) {
      showToast(Toast.Style.Failure, "請輸入內容", "至少需要輸入一些文字");
      return;
    }

    if (!preferences.notionToken || !preferences.databaseId) {
      showToast(Toast.Style.Failure, "配置不完整", "請先在設置中配置 Notion API Token 和數據庫 ID");
      return;
    }

    setIsLoading(true);

    // Show loading toast
    const loadingToast = await showToast(Toast.Style.Animated, "正在儲存到 Notion...");

    try {
      const notion = new Client({ auth: preferences.notionToken });

      // Check if there's an image in clipboard
      const clipboardContent = await Clipboard.read();
      let imageUrl = null;

      if (clipboardContent.file && clipboardContent.file.startsWith("data:image")) {
        // Upload image to Imgur if available
        if (preferences.imgurClientId) {
          loadingToast.message = "正在上傳圖片...";
          imageUrl = await uploadToImgur(clipboardContent.file, preferences.imgurClientId);
        } else {
          showToast(Toast.Style.Failure, "無法上傳圖片", "請在設置中配置 Imgur Client ID");
        }
      }

      loadingToast.message = "正在創建 Notion 頁面...";

      // Prepare title and content
      const lines = text.split('\n').filter(line => line.trim());
      const title = lines[0] || "Quick Capture";
      const content = lines.slice(1).join('\n');

      // Create page in Notion
      const response = await notion.pages.create({
        parent: { database_id: preferences.databaseId },
        properties: {
          Name: {
            title: [
              {
                text: {
                  content: title,
                },
              },
            ],
          },
        },
        children: [
          ...(content ? [{
            object: "block" as const,
            type: "paragraph" as const,
            paragraph: {
              rich_text: [
                {
                  type: "text" as const,
                  text: {
                    content: content,
                  },
                },
              ],
            },
          }] : []),
          ...(imageUrl ? [{
            object: "block" as const,
            type: "image" as const,
            image: {
              type: "external" as const,
              external: {
                url: imageUrl,
              },
            },
          }] : []),
        ],
      });

      // Save to recent records
      const record: CaptureRecord = {
        id: response.id,
        title: title,
        url: `https://notion.so/${response.id.replace(/-/g, '')}`,
        createdAt: new Date().toISOString(),
      };

      await saveRecord(record);

      // Success feedback
      loadingToast.style = Toast.Style.Success;
      loadingToast.title = "✅ 已儲存至 Notion";
      loadingToast.message = `「${title}」已成功儲存`;

      // Clear form
      setText("");
      setHasImage(false);

    } catch (error) {
      console.error("Failed to save to Notion:", error);
      loadingToast.style = Toast.Style.Failure;
      loadingToast.title = "儲存失敗";
      loadingToast.message = String(error);
    } finally {
      setIsLoading(false);
    }
  };

  const uploadToImgur = async (imageData: string, clientId: string): Promise<string> => {
    const base64Data = imageData.split(',')[1];
    
    const response = await fetch('https://api.imgur.com/3/image', {
      method: 'POST',
      headers: {
        'Authorization': `Client-ID ${clientId}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image: base64Data,
        type: 'base64',
      }),
    });

    const result = await response.json() as any;
    if (result.success) {
      return result.data.link;
    } else {
      throw new Error('Failed to upload image to Imgur');
    }
  };

  return (
    <Form
      isLoading={isLoading}
      actions={
        <ActionPanel>
          <Action
            title="儲存到 Notion"
            onAction={handleSubmit}
            icon="💾"
          />
          <Action
            title="貼上文字"
            onAction={async () => {
              const clipboardText = await Clipboard.readText();
              if (clipboardText) {
                setText(clipboardText);
              }
            }}
            icon="📋"
          />
          <Action
            title="重新檢查剪貼簿"
            onAction={checkClipboardForImage}
            icon="🔄"
          />
        </ActionPanel>
      }
    >
      <Form.TextArea
        id="text"
        title="💭 快速捕捉"
        placeholder="輸入你的想法...&#10;&#10;• 第一行將作為 Notion 頁面標題&#10;• 支援多行文字內容&#10;• 可同時貼上圖片"
        value={text}
        onChange={setText}
        enableMarkdown={false}
      />

      {hasImage && (
        <Form.Description
          title="📷 圖片狀態"
          text="檢測到剪貼簿中有圖片，將一併儲存到 Notion"
        />
      )}

      <Form.Separator />

      <Form.Description
        title="📚 最近記錄"
        text={recentRecords.length > 0 ?
          `最近 ${recentRecords.length} 筆記錄 (按 ⌘+K 查看更多操作)` :
          "尚無記錄，開始你的第一次快速捕捉吧！"
        }
      />

      {recentRecords.slice(0, 5).map((record, index) => (
        <Form.Description
          key={record.id}
          title={`${index + 1}. ${record.title}`}
          text={`${new Date(record.createdAt).toLocaleString()}`}
        />
      ))}

      {recentRecords.length === 0 && (
        <Form.Description
          title="💡 使用提示"
          text="• 按 Enter 快速儲存&#10;• 支援 ⌘+V 貼上文字或圖片&#10;• 需要先在設置中配置 Notion API"
        />
      )}
    </Form>
  );
}
