Raycast 套件點子！「快速捕捉念頭」正是 Raycast 這類啟動器工具最擅長解決的問題。

---

### 專案名稱與核心理念

首先，給它一個好記的名字，這有助於我們聚焦。

- **名稱建議:** `Notion Quick Capture`,
- **核心理念:** **「零摩擦記錄」**。目標是讓使用者從「想到」到「記錄完成」的路徑最短、最快，盡可能不打斷當前的工作心流。

---

### 使用者體驗流程 (UX Flow)

我們來想像一下使用者是怎麼使用這個套件的。

1. **觸發:** 使用者按下 Raycast 快速鍵 (e.g., `⌥ + Space`)，輸入關鍵字，例如 `inbox`, `note`等，啟動我們的套件。
2. **主介面:** 啟動後，使用者會看到一個簡潔的介面：
    - **最上方:** 一個清晰的文字輸入框，佔據視覺焦點，提示著「輸入你的想法...」。
    - **輸入框下方:** 一個副標題或提示，說明可以「貼上圖片」或「拖曳檔案進來」。
    - **列表區域:** 顯示「最近 10 筆記錄」，讓使用者知道最近記了些什麼，也提供一種「完成」的回饋感。
3. **輸入與執行:**
    - **純文字:** 使用者直接輸入文字，按下 `Enter`。
    - **圖片:** 使用者可以直接 `⌘ + V` 貼上剪貼簿中的圖片。Raycast 視窗會顯示圖片的縮圖。這時，文字輸入框可以變成這張圖片的「標題」或「說明」。
    - **文字 + 圖片:** 使用者可以先輸入文字，然後再貼上圖片。我們可以定義：文字成為 Notion Page 的標題，圖片成為 Page 裡的內容。
    - **按下 `Enter` 後:**
        - 出現短暫的 "Saving to Notion..." 載入指示。
        - 成功後，跳出一個 Raycast 的 Toast 通知：「✅ 已儲存至 Notion」。
        - 主介面上的「最近 10 筆記錄」列表 **即時更新**，剛剛新增的項目出現在最頂端，預設 focus 停在這個項目上。
        - Raycast 視窗自動關閉，使用者回到原本的工作畫面。
4. **與歷史紀錄互動:**
    - 在主介面的「最近 10 筆記錄」列表中，使用者可以用方向鍵選擇。
    - 對著某個項目按下 `Enter`：直接在瀏覽器或 Notion App 中打開該筆 Notion Page。
    - 按下 `⌘ + K` (Raycast 的 Action Panel)：提供更多操作，例如「複製 Notion 連結」、「刪除此記錄」（這會同時從 Notion 和本地歷史紀錄中刪除）。

---

### 功能細節拆解

現在我們把你的需求逐一細化。

### 1. 執行後，讓用戶輸入 text/image

- **文字處理:**
    - 單行文字：預設成為 Notion Page 的標題 (Name 屬性)。
    - 多行文字（例如貼上一段文章）：我們可以設計成第一行當標題，其餘文字成為 Page 裡的段落內容。這可以在設定中讓使用者選擇。
- **圖片處理:**
    - 這是技術上的一個小挑戰。Notion API 需要圖片有一個公開的 URL 才能存入。所以我們的套件需要一個「圖片上傳」的中介步驟。
    - **流程會是:** 使用者貼上圖片 -> Raycast 套件將圖片上傳到一個圖床服務 (例如 Imgur 或我們自己架設的簡易服務) -> 取得圖片 URL -> 將 URL 寫入 Notion Page 的 Image Block。
    - 如果使用者沒有輸入文字，只上傳了圖片，Page 標題可以自動生成，例如「Image Capture - 2023-10-27 10:30」。

### 2. capture mind - 以 notion page 形式存在 Notion database

- 這就是核心的 API 串接。
- 每次執行，套件會呼叫 Notion API 的 `pages.create` 端點。
- **Page 結構:**
    - `parent`: `{ "database_id": "使用者設定的DB_ID" }`
    - `properties`:
        - `Name` (或你的標題屬性名稱): `{ "title": [{ "text": { "content": "使用者輸入的文字" } }] }`
    - `children` (頁面內容):
        - 如果有多行文字，這裡會是 `paragraph` block。
        - 如果有圖片，這裡會是 `image` block，其 `external.url` 指向我們上傳的圖片 URL。

### 3. 顯示最近10筆新增歷史紀錄

- **資料來源:**
    - **方案 A (API 優先):** 每次打開套件，都去 call Notion API `databases.query`，撈取該 database 中最新的 10 筆資料。
        - 優點：永遠與 Notion 同步，就算你在別處新增也看得到。
        - 缺點：速度較慢，依賴網路。
    - **方案 B (快取優先):** 每次成功新增一筆紀錄到 Notion 後，將該筆紀錄的 `Page ID` 和 `標題` 存放在 Raycast 的本地儲存 (`LocalStorage`) 中。只顯示這個本地列表。
        - 優點：極速載入，不依賴網路。
        - 缺點：無法同步在其他地方新增的紀錄。
- **建議方案 (混合式):**
    - 優先從本地快取載入，實現秒開。
    - 同時在背景非同步地去 call Notion API，如果發現不一致再更新列表。或者提供一個手動「重新整理」的按鈕。
    - 對於「快速捕捉」這個情境，**方案 B (快取優先)** 的體驗是最好的。

### 4. 可以設定要存在哪個 Notion database

- 這需要一個獨立的設定頁面。可以新增一個指令，例如 `Set Notion Inbox`。
- **設定流程:**
    1. **第一步：取得 API Token。**
        - 引導使用者去 Notion 建立一個內部整合 (Internal Integration)，取得 API Token。
        - 在設定頁提供一個輸入框，讓使用者貼上 Token。Raycast 會安全地儲存它。
    2. **第二步：選擇 Database。**
        - 一旦 Token 有效，套件會 call Notion API 的 `search` 端點，找出此 Token 有權限存取的所有 Database。
        - 將這些 Database 以「下拉選單」的形式呈現給使用者。
        - 使用者選擇 `00.DB inbox` (或其他他想用的 database)。
    3. **第三步：儲存設定。**
        - 套件將使用者選擇的 `Database ID` 儲存在本地。
        - 主功能 `inbox` 指令就會讀取這個 ID 來新增頁面。

---

### 技術與架構考量

- **開發語言:** TypeScript (Raycast 官方標配)。
- **核心依賴:**
    - `@raycast/api`: Raycast 核心功能庫。
    - `@notionhq/client`: Notion 官方的 JavaScript SDK，處理 API 互動。
    - `node-fetch` 或 `axios`: 用於上傳圖片到圖床。
- **驗證 (Authentication):** 使用者需自行提供 Notion Integration Token。這是標準做法。
- **圖片託管 (Image Hosting):** 這是最大的技術決策點。需要一個穩定、快速、免費或低成本的圖床方案。Imgur API 是一個常見的選擇，但有流量限制。
