{"$schema": "https://json.schemastore.org/tsconfig", "display": "Raycast", "compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}