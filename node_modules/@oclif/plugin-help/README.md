# @oclif/plugin-help

A CLI command to invoke the standard help functionality from [oclif/core](https://github.com/oclif/core).

[![Version](https://img.shields.io/npm/v/@oclif/plugin-help.svg)](https://npmjs.org/package/@oclif/plugin-help)
[![Downloads/week](https://img.shields.io/npm/dw/@oclif/plugin-help.svg)](https://npmjs.org/package/@oclif/plugin-help)
[![License](https://img.shields.io/npm/l/@oclif/plugin-help.svg)](https://github.com/oclif/plugin-help/blob/main/package.json)

<!-- commands -->
* [`oclif help [COMMAND]`](#oclif-help-command)

## `oclif help [COMMAND]`

Display help for oclif.

```
USAGE
  $ oclif help [COMMAND...] [-n]

ARGUMENTS
  COMMAND...  Command to show help for.

FLAGS
  -n, --nested-commands  Include all nested commands in the output.

DESCRIPTION
  Display help for oclif.
```

_See code: [src/commands/help.ts](https://github.com/oclif/plugin-help/blob/v6.2.30/src/commands/help.ts)_
<!-- commandsstop -->

# Contributing

See [contributing guide](./CONRTIBUTING.md)
