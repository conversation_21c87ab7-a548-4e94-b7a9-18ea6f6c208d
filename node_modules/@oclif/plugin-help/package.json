{"name": "@oclif/plugin-help", "description": "Standard help for oclif.", "version": "6.2.30", "author": "Salesforce", "bugs": "https://github.com/oclif/plugin-help/issues", "dependencies": {"@oclif/core": "^4"}, "devDependencies": {"@commitlint/config-conventional": "^19", "@eslint/compat": "^1.3.1", "@oclif/prettier-config": "^0.2.1", "@oclif/test": "^4", "@types/chai": "^4.3.16", "@types/mocha": "^10.0.10", "@types/node": "^18", "chai": "^4.5.0", "commitlint": "^19", "eslint": "^9.30.0", "eslint-config-oclif": "^6.0.73", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^15", "mocha": "^10.8.2", "oclif": "^4.20.1", "prettier": "^3.6.2", "shx": "^0.4.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "engines": {"node": ">=18.0.0"}, "exports": "./lib/index.js", "files": ["oclif.manifest.json", "/lib"], "homepage": "https://github.com/oclif/plugin-help", "keywords": ["oclif-plugin"], "license": "MIT", "oclif": {"commands": "./lib/commands", "bin": "oclif", "flexibleTaxonomy": true, "topicSeparator": " "}, "repository": "oclif/plugin-help", "scripts": {"build": "shx rm -rf lib && tsc", "clean": "shx rm -f oclif.manifest.json", "compile": "tsc", "lint": "eslint", "postpack": "yarn run clean", "posttest": "yarn lint", "prepack": "yarn build && oclif manifest && oclif readme", "prepare": "husky && yarn build", "pretest": "yarn build --noEmit && tsc -p test --noEmit", "test": "mocha --forbid-only \"test/**/*.test.ts\"", "version": "oclif readme && git add README.md"}, "type": "module", "types": "./lib/index.d.ts"}