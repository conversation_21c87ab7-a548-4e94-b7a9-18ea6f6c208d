{"commands": {"help": {"aliases": [], "args": {"command": {"description": "Command to show help for.", "name": "command", "required": false}}, "description": "Display help for <%= config.bin %>.", "flags": {"nested-commands": {"char": "n", "description": "Include all nested commands in the output.", "name": "nested-commands", "allowNo": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "help", "pluginAlias": "@oclif/plugin-help", "pluginName": "@oclif/plugin-help", "pluginType": "core", "strict": false, "enableJsonFlag": false, "isESM": true, "relativePath": ["lib", "commands", "help.js"], "aliasPermutations": [], "permutations": ["help"]}}, "version": "6.2.30"}