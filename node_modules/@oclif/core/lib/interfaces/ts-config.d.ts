export interface TSConfig {
    compilerOptions: {
        baseUrl?: string;
        emitDecoratorMetadata?: boolean;
        esModuleInterop?: boolean;
        experimentalDecorators?: boolean;
        jsx?: boolean;
        module?: string;
        moduleResolution?: string;
        outDir?: string;
        rootDir?: string;
        rootDirs?: string[];
        sourceMap?: boolean;
        target?: string;
    };
    extends?: string;
    'ts-node'?: {
        esm?: boolean;
        experimentalSpecifierResolution?: 'explicit' | 'node';
        scope?: boolean;
        swc?: boolean;
    };
}
