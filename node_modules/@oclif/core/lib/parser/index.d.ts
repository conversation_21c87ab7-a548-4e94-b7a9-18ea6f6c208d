import type { Input, OutputArgs, OutputFlags, ParserOutput } from '../interfaces/parser';
export type { ArgInput, FlagInput, Input, OutputArgs, OutputFlags, ParserOutput } from '../interfaces/parser';
export { flagUsages } from './help';
export { validate } from './validate';
export declare function parse<TFlags extends OutputFlags<any>, BFlags extends OutputFlags<any>, TArgs extends OutputArgs<any>>(argv: string[], options: Input<TFlags, BFlags, TArgs>): Promise<ParserOutput<TFlags, BFlags, TArgs>>;
