import { Deprecation, Config as IConfig } from '../interfaces';
export declare function template(context: any): (t: string) => string;
export declare function standardizeIDFromArgv(argv: string[], config: IConfig): string[];
export declare function getHelpFlagAdditions(config: IConfig): string[];
export declare function formatFlagDeprecationWarning(flag: string, opts: Deprecation | true): string;
export declare function formatCommandDeprecationWarning(command: string, opts?: Deprecation): string;
export declare function normalizeArgv(config: IConfig, argv?: string[]): string[];
