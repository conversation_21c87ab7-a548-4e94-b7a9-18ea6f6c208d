import { StandardAnsi, Theme } from '../interfaces/theme';
/**
 * Add color to text.
 * @param color color to use. Can be hex code (e.g. `#ff0000`), rgb (e.g. `rgb(255, 255, 255)`) or a standard ansi color (e.g. `red`)
 * @param text string to colorize
 * @returns colorized string
 */
export declare function colorize(color: string | StandardAnsi | undefined, text: string): string;
export declare function parseTheme(theme: Record<string, string | Record<string, string>>): Theme;
