import { ActionBase, ActionType } from './base';
import { Options } from './types';
export default class SpinnerAction extends ActionBase {
    type: ActionType;
    private color;
    private frameIndex;
    private frames;
    private spinner?;
    constructor();
    protected _frame(): string;
    protected _pause(icon?: string): void;
    protected _start(opts: Options): void;
    protected _stop(status: string): void;
    protected colorize(s: string): string;
    private _lines;
    private _render;
    private _reset;
    private getFrames;
}
