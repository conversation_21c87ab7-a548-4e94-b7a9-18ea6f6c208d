import { ActionBase, ActionType } from './base';
export default class SimpleAction extends ActionBase {
    type: ActionType;
    protected _pause(icon?: string): void;
    protected _resume(): void;
    protected _start(): void;
    protected _stop(status: string): void;
    protected _updateStatus(status: string, prevStatus?: string, newline?: boolean): void;
    private _flush;
    private _render;
}
