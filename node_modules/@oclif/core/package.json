{"name": "@oclif/core", "description": "base library for oclif CLIs", "version": "4.4.1", "author": "Salesforce", "bugs": "https://github.com/oclif/core/issues", "dependencies": {"ansi-escapes": "^4.3.2", "ansis": "^3.17.0", "clean-stack": "^3.0.1", "cli-spinners": "^2.9.2", "debug": "^4.4.0", "ejs": "^3.1.10", "get-package-type": "^0.1.0", "indent-string": "^4.0.0", "is-wsl": "^2.2.0", "lilconfig": "^3.1.3", "minimatch": "^9.0.5", "semver": "^7.6.3", "string-width": "^4.2.3", "supports-color": "^8", "tinyglobby": "^0.2.14", "widest-line": "^3.1.0", "wordwrap": "^1.0.0", "wrap-ansi": "^7.0.0"}, "devDependencies": {"@commitlint/config-conventional": "^19", "@eslint/compat": "^1.3.1", "@oclif/plugin-help": "^6", "@oclif/plugin-plugins": "^5", "@oclif/prettier-config": "^0.2.1", "@oclif/test": "^4", "@types/benchmark": "^2.1.5", "@types/chai": "^4.3.16", "@types/chai-as-promised": "^7.1.8", "@types/clean-stack": "^2.1.1", "@types/debug": "^4.1.10", "@types/ejs": "^3.1.5", "@types/indent-string": "^4.0.1", "@types/mocha": "^10.0.10", "@types/node": "^18", "@types/pnpapi": "^0.0.5", "@types/sinon": "^17.0.3", "@types/supports-color": "^8.1.3", "@types/wordwrap": "^1.0.3", "@types/wrap-ansi": "^3.0.0", "benchmark": "^2.1.4", "chai": "^4.5.0", "chai-as-promised": "^7.1.2", "commitlint": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-oclif": "^6", "eslint-config-prettier": "^10", "husky": "^9.1.7", "lint-staged": "^15", "madge": "^6.1.0", "mocha": "^10.8.2", "nyc": "^15.1.0", "prettier": "^3.6.2", "shx": "^0.4.0", "sinon": "^18", "ts-node": "^10.9.2", "tsd": "^0.32.0", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}, "files": ["/lib"], "homepage": "https://github.com/oclif/core", "keywords": ["oclif", "cli", "command", "command line", "parser", "args", "argv"], "license": "MIT", "exports": {".": "./lib/index.js", "./args": "./lib/args.js", "./command": "./lib/command.js", "./config": "./lib/config/index.js", "./errors": "./lib/errors/index.js", "./execute": "./lib/execute.js", "./flags": "./lib/flags.js", "./flush": "./lib/flush.js", "./handle": "./lib/errors/handle.js", "./help": "./lib/help/index.js", "./hooks": "./lib/interfaces/hooks.js", "./interfaces": "./lib/interfaces/index.js", "./logger": "./lib/logger.js", "./package.json": "./package.json", "./parser": "./lib/parser/index.js", "./performance": "./lib/performance.js", "./run": "./lib/main.js", "./settings": "./lib/settings.js", "./util/ids": "./lib/util/ids.js", "./ux": "./lib/ux/index.js"}, "repository": "oclif/core", "oclif": {"bin": "oclif", "devPlugins": ["@oclif/plugin-help", "@oclif/plugin-plugins"]}, "publishConfig": {"access": "public"}, "scripts": {"build": "shx rm -rf lib && tsc", "compile": "tsc", "format": "prettier --write \"+(src|test)/**/*.+(ts|js|json)\"", "lint": "eslint", "posttest": "yarn lint && yarn test:circular-deps", "prepack": "yarn run build", "prepare": "husky", "test:circular-deps": "yarn build && madge lib/ -c", "test:debug": "nyc mocha --debug-brk --inspect \"test/**/*.test.ts\"", "test:integration": "mocha --forbid-only \"test/**/*.integration.ts\" --parallel --timeout 1200000", "test:interoperability": "cross-env DEBUG=integration:* ts-node test/integration/interop.ts", "test:perf": "ts-node test/perf/parser.perf.ts", "test": "nyc mocha --forbid-only \"test/**/*.test.ts\" --parallel"}, "types": "lib/index.d.ts"}