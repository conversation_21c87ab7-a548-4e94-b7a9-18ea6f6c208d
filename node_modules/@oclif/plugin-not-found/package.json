{"name": "@oclif/plugin-not-found", "description": "\"did you mean\" for oclif", "version": "3.2.59", "author": "Salesforce", "bugs": "https://github.com/oclif/plugin-not-found/issues", "dependencies": {"@inquirer/prompts": "^7.6.0", "@oclif/core": "^4", "ansis": "^3.17.0", "fast-levenshtein": "^3.0.0"}, "devDependencies": {"@commitlint/config-conventional": "^19", "@eslint/compat": "^1.3.1", "@oclif/plugin-commands": "^3.3.3", "@oclif/plugin-help": "^6", "@oclif/prettier-config": "^0.2.1", "@oclif/test": "^4", "@types/chai": "^4.3.16", "@types/fast-levenshtein": "^0.0.4", "@types/mocha": "^10.0.9", "@types/node": "^18", "@types/shelljs": "^0.8.17", "@types/sinon": "^17.0.3", "chai": "^4.5.0", "commitlint": "^19", "eslint": "^9.30.1", "eslint-config-oclif": "^6.0.79", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^15", "mocha": "^10.8.2", "oclif": "^4.20.1", "prettier": "^3.6.2", "shelljs": "0.10.0", "shx": "^0.4.0", "sinon": "^18.0.1", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "engines": {"node": ">=18.0.0"}, "exports": "./lib/index.js", "files": ["oclif.manifest.json", "/lib"], "homepage": "https://github.com/oclif/plugin-not-found", "keywords": ["oclif-plugin"], "license": "MIT", "oclif": {"hooks": {"command_not_found": "./lib"}, "devPlugins": ["@oclif/plugin-help", "@oclif/plugin-commands"]}, "repository": "oclif/plugin-not-found", "scripts": {"build": "shx rm -rf lib && tsc", "clean": "shx rm -f oclif.manifest.json", "compile": "tsc", "lint": "eslint", "postpack": "yarn run clean", "posttest": "yarn lint", "prepack": "yarn build && oclif manifest && oclif readme", "prepare": "husky && yarn build", "pretest": "yarn build --noEmit && tsc -p test --noEmit", "test": "mocha --forbid-only \"test/**/*.test.ts\"", "test:integration:sf": "mocha \"test/**/sf.integration.ts\"", "version": "oclif readme && git add README.md"}, "type": "module", "types": "lib/index.d.ts"}