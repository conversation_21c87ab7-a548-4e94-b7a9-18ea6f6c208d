declare const script = "#!/usr/bin/env bash\n\n# This function joins an array using a character passed in\n# e.g. ARRAY=(one two three) -> join_by \":\" ${ARRAY[@]} -> \"one:two:three\"\nfunction join_by { local IFS=\"$1\"; shift; echo \"$*\"; }\n\n_<CLI_BIN>_autocomplete()\n{\n\n  local cur=\"${COMP_WORDS[COMP_CWORD]}\" opts normalizedCommand colonPrefix IFS=$' \\t\\n'\n  COMPREPLY=()\n\n  local commands=\"\n<BASH_COMMANDS_WITH_FLAGS_LIST>\n\"\n\n  function __trim_colon_commands()\n  {\n    # Turn $commands into an array\n    commands=(\"${commands[@]}\")\n\n    if [[ -z \"$colonPrefix\" ]]; then\n      colonPrefix=\"$normalizedCommand:\"\n    fi\n\n    # Remove colon-word prefix from $commands\n    commands=( \"${commands[@]/$colonPrefix}\" )\n\n    for i in \"${!commands[@]}\"; do\n      if [[ \"${commands[$i]}\" == \"$normalizedCommand\" ]]; then\n        # If the currently typed in command is a topic command we need to remove it to avoid suggesting it again\n        unset \"${commands[$i]}\"\n      else\n        # Trim subcommands from each command\n        commands[$i]=\"${commands[$i]%%:*}\"\n      fi\n    done\n  }\n\n  if [[ \"$cur\" != \"-\"* ]]; then\n    # Command\n    __COMP_WORDS=( \"${COMP_WORDS[@]:1}\" )\n\n    # The command typed by the user but separated by colons (e.g. \"mycli command subcom\" -> \"command:subcom\")\n    normalizedCommand=\"$( printf \"%s\" \"$(join_by \":\" \"${__COMP_WORDS[@]}\")\" )\"\n\n    # The command hirarchy, with colons, leading up to the last subcommand entered (e.g. \"mycli com subcommand subsubcom\" -> \"com:subcommand:\")\n    colonPrefix=\"${normalizedCommand%\"${normalizedCommand##*:}\"}\"\n\n    if [[ -z \"$normalizedCommand\" ]]; then\n      # If there is no normalizedCommand yet the user hasn't typed in a full command\n      # So we should trim all subcommands & flags from $commands so we can suggest all top level commands\n      opts=$(printf \"%s \" \"${commands[@]}\" | grep -Eo '^[a-zA-Z0-9_-]+')\n    else\n      # Filter $commands to just the ones that match the $normalizedCommand and turn into an array\n      commands=( $(compgen -W \"$commands\" -- \"${normalizedCommand}\") )\n      # Trim higher level and subcommands from the subcommands to suggest\n      __trim_colon_commands \"$colonPrefix\"\n\n      opts=$(printf \"%s \" \"${commands[@]}\") # | grep -Eo '^[a-zA-Z0-9_-]+'\n    fi\n  else \n    # Flag\n\n    # The full CLI command separated by colons (e.g. \"mycli command subcommand --fl\" -> \"command:subcommand\")\n    # This needs to be defined with $COMP_CWORD-1 as opposed to above because the current \"word\" on the command line is a flag and the command is everything before the flag\n    normalizedCommand=\"$( printf \"%s\" \"$(join_by \":\" \"${COMP_WORDS[@]:1:($COMP_CWORD - 1)}\")\" )\"\n\n    # The line below finds the command in $commands using grep\n    # Then, using sed, it removes everything from the found command before the --flags (e.g. \"command:subcommand:subsubcom --flag1 --flag2\" -> \"--flag1 --flag2\")\n    opts=$(printf \"%s \" \"${commands[@]}\" | grep \"${normalizedCommand}\" | sed -n \"s/^${normalizedCommand} //p\")\n  fi\n\n  COMPREPLY=($(compgen -W \"$opts\" -- \"${cur}\"))\n}\n\ncomplete -F _<CLI_BIN>_autocomplete <CLI_BIN>\n";
export default script;
