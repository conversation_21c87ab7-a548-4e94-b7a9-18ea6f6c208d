declare const script = "#!/usr/bin/env bash\n\n_<CLI_BIN>_autocomplete()\n{\n\n  local cur=\"${COMP_WORDS[COMP_CWORD]}\" opts IFS=$' \\t\\n'\n  COMPREPLY=()\n\n  local commands=\"\n<BASH_COMMANDS_WITH_FLAGS_LIST>\n\"\n\n  if [[ \"$cur\" != \"-\"* ]]; then\n    opts=$(printf \"$commands\" | grep -Eo '^[a-zA-Z0-9:_-]+')\n  else\n    local __COMP_WORDS\n    if [[ ${COMP_WORDS[2]} == \":\" ]]; then\n      #subcommand\n      __COMP_WORDS=$(printf \"%s\" \"${COMP_WORDS[@]:1:3}\")\n    else\n      #simple command\n      __COMP_WORDS=\"${COMP_WORDS[@]:1:1}\"\n    fi\n    opts=$(printf \"$commands\" | grep \"${__COMP_WORDS}\" | sed -n \"s/^${__COMP_WORDS} //p\")\n  fi\n  _get_comp_words_by_ref -n : cur\n  COMPREPLY=( $(compgen -W \"${opts}\" -- ${cur}) )\n  __ltrim_colon_completions \"$cur\"\n  return 0\n\n}\n\ncomplete -o default -F _<CLI_BIN>_autocomplete <CLI_BIN>\n";
export default script;
