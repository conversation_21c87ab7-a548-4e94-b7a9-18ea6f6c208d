{"commands": {"autocomplete:create": {"aliases": [], "args": {}, "description": "create autocomplete setup scripts and completion functions", "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "autocomplete:create", "pluginAlias": "@oclif/plugin-autocomplete", "pluginName": "@oclif/plugin-autocomplete", "pluginType": "core", "strict": true, "isESM": true, "relativePath": ["lib", "commands", "autocomplete", "create.js"], "aliasPermutations": [], "permutations": ["autocomplete:create", "create:autocomplete"]}, "autocomplete": {"aliases": [], "args": {"shell": {"description": "Shell type", "name": "shell", "options": ["zsh", "bash", "powershell"], "required": false}}, "description": "Display autocomplete installation instructions.", "examples": ["$ <%= config.bin %> autocomplete", "$ <%= config.bin %> autocomplete bash", "$ <%= config.bin %> autocomplete zsh", "$ <%= config.bin %> autocomplete powershell", "$ <%= config.bin %> autocomplete --refresh-cache"], "flags": {"refresh-cache": {"char": "r", "description": "Refresh cache (ignores displaying instructions)", "name": "refresh-cache", "allowNo": false, "type": "boolean"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "autocomplete", "pluginAlias": "@oclif/plugin-autocomplete", "pluginName": "@oclif/plugin-autocomplete", "pluginType": "core", "strict": true, "isESM": true, "relativePath": ["lib", "commands", "autocomplete", "index.js"], "aliasPermutations": [], "permutations": ["autocomplete"]}, "autocomplete:script": {"aliases": [], "args": {"shell": {"description": "Shell type", "name": "shell", "options": ["zsh", "bash", "powershell"], "required": false}}, "description": "outputs autocomplete config script for shells", "flags": {}, "hasDynamicHelp": false, "hidden": true, "hiddenAliases": [], "id": "autocomplete:script", "pluginAlias": "@oclif/plugin-autocomplete", "pluginName": "@oclif/plugin-autocomplete", "pluginType": "core", "strict": true, "isESM": true, "relativePath": ["lib", "commands", "autocomplete", "script.js"], "aliasPermutations": [], "permutations": ["autocomplete:script", "script:autocomplete"]}}, "version": "3.2.32"}