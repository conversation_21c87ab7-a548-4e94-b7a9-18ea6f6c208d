{"name": "@oclif/plugin-autocomplete", "description": "autocomplete plugin for oclif", "version": "3.2.32", "author": "Salesforce", "bugs": "https://github.com/oclif/plugin-autocomplete/issues", "dependencies": {"@oclif/core": "^4", "ansis": "^3.16.0", "debug": "^4.4.1", "ejs": "^3.1.10"}, "devDependencies": {"@commitlint/config-conventional": "^19", "@eslint/compat": "^1.3.1", "@oclif/plugin-help": "^6", "@oclif/prettier-config": "^0.2.1", "@oclif/test": "^4", "@types/chai": "^4", "@types/debug": "^4.1.12", "@types/ejs": "^3.1.5", "@types/mocha": "^10.0.9", "@types/nock": "^11.1.0", "@types/node": "^18", "chai": "^4", "commitlint": "^19", "eslint": "^9.30.0", "eslint-config-oclif": "^6.0.76", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^15.5.2", "mocha": "^10.8.2", "nock": "^13.5.6", "nyc": "^15.1.0", "oclif": "^4.20.1", "prettier": "^3.6.2", "shx": "^0.4.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "engines": {"node": ">=18.0.0"}, "exports": "./lib/index.js", "files": ["oclif.manifest.json", "/lib"], "homepage": "https://github.com/oclif/plugin-autocomplete", "keywords": ["oclif-plugin"], "license": "MIT", "oclif": {"commands": "./lib/commands", "bin": "oclif-example", "devPlugins": ["@oclif/plugin-help"], "flexibleTaxonomy": true, "hooks": {"plugins:postinstall": "./lib/hooks/refresh-cache.js", "plugins:postuninstall": "./lib/hooks/refresh-cache.js"}}, "repository": "oclif/plugin-autocomplete", "scripts": {"build": "shx rm -rf lib && tsc", "clean": "shx rm -f oclif.manifest.json", "compile": "tsc", "lint": "eslint", "postpack": "yarn run clean", "posttest": "yarn lint", "prepack": "yarn build && oclif manifest && oclif readme", "prepare": "husky && yarn build", "pretest": "yarn build && tsc -p test --noEmit", "test": "mocha --forbid-only \"test/**/*.test.ts\"", "version": "oclif readme && git add README.md"}, "type": "module"}