{"version": 3, "file": "ClassVisitor.js", "sourceRoot": "", "sources": ["../../src/referencer/ClassVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAyE;AAEzE,+CAA4C;AAC5C,uCAAoC;AAEpC,MAAM,YAAa,SAAQ,iBAAO;IAKhC,YACE,UAAsB,EACtB,IAA0D,EAC1D,qBAA8B;QAE9B,KAAK,CAAC,UAAU,CAAC,CAAC;QATX,0CAAiE;QACjE,2CAAwB;QACxB,sDAAgC;QAQvC,uBAAA,IAAI,4BAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,2BAAc,IAAI,MAAA,CAAC;QACvB,uBAAA,IAAI,uCAA0B,qBAAqB,MAAA,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,KAAK,CACV,UAAsB,EACtB,IAA0D,EAC1D,qBAA8B;QAE9B,MAAM,YAAY,GAAG,IAAI,YAAY,CACnC,UAAU,EACV,IAAI,EACJ,qBAAqB,CACtB,CAAC;QACF,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,IAAsC;QAC1C,+DAA+D;QAC/D,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;YAC7B,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACnB;aAAM;YACL,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC;IAED,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IAET,UAAU,CAClB,IAA0D;;QAE1D,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,IAAI,IAAI,CAAC,EAAE,EAAE;YAC5D,uBAAA,IAAI,gCAAY;iBACb,YAAY,EAAE;iBACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,gCAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;SACtE;QAED,MAAA,IAAI,CAAC,UAAU,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzD,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAEnD,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,mDAAmD;YACnD,0EAA0E;YAC1E,uBAAA,IAAI,gCAAY;iBACb,YAAY,EAAE;iBACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,gCAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;SACtE;QAED,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAExC,oCAAoC;QACpC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpC,kBAAkB;QAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,MAAA,IAAI,CAAC,UAAU,0CAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtB,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,uBAAuB,CAC/B,IAIyC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7B;;;;;WAKG;QACH,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAES,oCAAoC,CAC5C,IAAwB,EACxB,cAAuB;QAEvB,IAAI,gBAAgB,IAAI,IAAI,EAAE;YAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;SAC7D;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;YACzD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;SAClE;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE;YAC3D,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;SAC3E;IACH,CAAC;IAES,mBAAmB,CAC3B,IAAiC,EACjC,UAAqC;;QAErC,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,0DAA0D;YAC1D,+BAA+B;YAC/B,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC;SACrE;QAED,qDAAqD;QACrD,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAE5D;;;;;;;;WAQG;QACH,IAAI,oBAAoB,GAAG,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC;QACnD;;;;;;;;;;;;WAYG;QACH,oBAAoB;YAClB,oBAAoB;gBACpB,CAAC,UAAU,CAAC,IAAI,KAAK,KAAK;oBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,oBAAoB,IAAI,UAAU,CAAC,IAAI,KAAK,KAAK,EAAE;YACtD,MAAM,OAAO,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAC;YAEpD;;;;;;eAMG;YACH,IACE,OAAO,IAAI,IAAI;iBACf,MAAA,uBAAA,IAAI,+BAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAC5B,CAAC,IAAI,EAAqC,EAAE,CAC1C,IAAI,KAAK,UAAU;oBACnB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBAC7C,kCAAkC;oBAClC,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;oBACjC,uBAAuB,CAAC,IAAI,CAAC,KAAK,OAAO,CAC5C,0CAAE,UAAU,CAAA,EACb;gBACA,oBAAoB,GAAG,IAAI,CAAC;aAC7B;SACF;QAED;;;;;WAKG;QACH,IACE,CAAC,oBAAoB;YACrB,UAAU,CAAC,IAAI,KAAK,aAAa;YACjC,uBAAA,IAAI,+BAAW,CAAC,UAAU,EAC1B;YACA,oBAAoB,GAAG,IAAI,CAAC;SAC7B;QAED,kCAAkC;QAClC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC/B,IAAI,CAAC,YAAY,CACf,KAAK,EACL,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;gBAChB,uBAAA,IAAI,gCAAY;qBACb,YAAY,EAAE;qBACd,gBAAgB,CACf,OAAO,EACP,IAAI,gCAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAClD,CAAC;gBAEJ,uBAAA,IAAI,gCAAY,CAAC,uBAAuB,CACtC,OAAO,EACP,IAAI,CAAC,WAAW,EAChB,IAAI,EACJ,IAAI,CACL,CAAC;YACJ,CAAC,EACD,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAChC,CAAC;YACF,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;YACvE,MAAA,KAAK,CAAC,UAAU,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEpC,mFAAmF;QACnF,uCAAuC;QACvC,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,gEAAgE;YAChE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBACpD,uBAAA,IAAI,gCAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3C;iBAAM;gBACL,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnC;SACF;QAED,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB,CACzB,IAKuC;;QAEvC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAC7C;gBACA,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,8BAA8B,CAC1D,IAAI,CAAC,KAAK,CACX,CAAC;aACH;YAED,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnC,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAC7C;gBACA,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpC;SACF;QAED,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,MAAA,IAAI,CAAC,UAAU,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1D;IACH,CAAC;IAES,WAAW,CAAC,IAA+B;;QACnD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE;YACzD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAC5C;aAAM;YACL,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpC;QAED,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,MAAA,IAAI,CAAC,UAAU,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1D;IACH,CAAC;IAES,SAAS,CAAC,IAAsC;QACxD,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QACD,yBAAW,CAAC,KAAK,CAAC,uBAAA,IAAI,gCAAY,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAES,iBAAiB,CACzB,IAAkD,EAClD,cAAuB;QAEvB,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QACD,6EAA6E;QAC7E,IACE,uBAAA,IAAI,+BAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;YACxD,CAAC,uBAAA,IAAI,+BAAW,CAAC,OAAO;YACxB,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;YAC3D,uBAAA,IAAI,2CAAuB,EAC3B;YACA,IAAI,UAAyD,CAAC;YAC9D,IACE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EACpE;gBACA,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACxC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;oBACxD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;iBAClB;gBACD,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;aACxB;iBAAM;gBACL,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;aAC3C;YAED,IAAI,cAAc,EAAE;gBAClB,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;oBACjD,uBAAA,IAAI,gCAAY,CAAC,YAAY,EAAE,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;iBACpE;gBAED,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;oBACtC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;iBACpD;gBAED,4BAA4B;gBAC5B,OAAO;aACR;SACF;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAED,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IAEX,gBAAgB,CAAC,IAA+B;QACxD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,SAAS,CAAC,IAAwB;QAC1C,4EAA4E;QAC5E,qEAAqE;QACrE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAES,kBAAkB,CAAC,IAAiC;QAC5D,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAES,0BAA0B,CAClC,IAAyC;QAEzC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,4BAA4B,CACpC,IAA2C;QAE3C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,0BAA0B,CAClC,IAAyC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,UAAU,CAAC,IAAyB;QAC5C,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB;QACzB,qBAAqB;IACvB,CAAC;IAES,WAAW,CAAC,IAA0B;QAC9C,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;CACF;AAsCQ,oCAAY;;AApCrB;;;;;;;;;;;;;;;;;;;GAmBG;AACH,SAAS,uBAAuB,CAC9B,IAA+B;IAE/B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;QAC7D,IACE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ;YAClC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ,EAClC;YACA,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;SACvB;KACF;SAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;QACxE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;KACtB;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}