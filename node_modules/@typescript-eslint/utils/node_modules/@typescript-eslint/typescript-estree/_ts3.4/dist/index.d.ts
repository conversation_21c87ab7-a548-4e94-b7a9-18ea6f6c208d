export { AST, parse, parseAndGenerateServices, parseWithNodeMaps, ParseAndGenerateServicesResult, ParseWithNodeMapsResult, } from './parser';
export { ParserServices, TSESTreeOptions } from './parser-options';
export { simpleTraverse } from './simple-traverse';
export * from './ts-estree';
export { createProgramFromConfigFile as createProgram } from './create-program/useProvidedPrograms';
export * from './create-program/getScriptKind';
export { typescriptVersionIsAtLeast } from './version-check';
export * from './getModifiers';
export * from './clear-caches';
export { visitorKeys } from '@typescript-eslint/visitor-keys';
export declare const version: string;
//# sourceMappingURL=index.d.ts.map
