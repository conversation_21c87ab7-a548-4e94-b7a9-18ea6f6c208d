{"name": "@raycast/api", "version": "1.100.3", "description": "Build extensions for Raycast with React and Node.js.", "author": "Raycast Technologies Ltd.", "homepage": "https://developers.raycast.com", "repository": {"url": "https://github.com/raycast/extensions"}, "license": "MIT", "types": "types/index.d.ts", "engines": {"node": ">=22.14.0"}, "dependencies": {"@oclif/core": "^4.0.33", "@oclif/plugin-autocomplete": "^3.2.10", "@oclif/plugin-help": "^6.2.18", "@oclif/plugin-not-found": "^3.2.28", "@types/node": "22.13.10", "@types/react": "19.0.10", "esbuild": "^0.25.1", "react": "19.0.0"}, "peerDependencies": {"@types/node": "22.13.10", "@types/react": "19.0.10", "react-devtools": "6.1.1"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "@types/react": {"optional": true}, "react-devtools": {"optional": true}}, "bin": {"ray": "./bin/run.js"}, "oclif": {"bin": "ray", "dirname": "ray", "commands": "./dist/commands", "plugins": ["@oclif/plugin-autocomplete", "@oclif/plugin-help", "@oclif/plugin-not-found"]}}