"use strict";var I=Object.create;var f=Object.defineProperty;var k=Object.getOwnPropertyDescriptor;var N=Object.getOwnPropertyNames;var T=Object.getPrototypeOf,b=Object.prototype.hasOwnProperty;var C=(e,t)=>{for(var n in t)f(e,n,{get:t[n],enumerable:!0})},m=(e,t,n,d)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of N(t))!b.call(e,a)&&a!==n&&f(e,a,{get:()=>t[a],enumerable:!(d=k(t,a))||d.enumerable});return e};var i=(e,t,n)=>(n=e!=null?I(T(e)):{},m(t||!e||!e.__esModule?f(n,"default",{value:e,enumerable:!0}):n,e)),D=e=>m(f({},"__esModule",{value:!0}),e);var _={};C(_,{API_ENV:()=>p,extensionBuildDirectory:()=>v,getConfig:()=>A,initConfig:()=>S,raycastAppScheme:()=>R,raycastBundleID:()=>E,raycastConfigDirectory:()=>l,setConfig:()=>U});module.exports=D(_);var s=i(require("node:fs")),o=i(require("node:path")),x=i(require("node:os"));var g=i(require("node:path")),h=i(require("node:fs"));function y(){let e;try{e=g.resolve("package.json")}catch(n){throw new Error(`cannot resolve package manifest path: ${n}`)}let t;try{t=JSON.parse(h.readFileSync(e,"utf8"))}catch(n){throw new Error(`cannot read package manifest: ${n}`)}return t.name=t.name.replace(/^@workaround/g,""),t}var p={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],w="e69bae0ec90f5e838555",r={},u;function v(){let e=y();if(!e.name)throw new Error("extension name in manifest cannot be empty");return o.join(l(),"extensions",e.name)}function E(){let e=c(u);return e?.startsWith("x")?e==="x"?"com.raycast-x.macos":`com.raycast-x.macos.${e.replace("x-","")}`:e?`com.raycast.macos.${e}`:"com.raycast.macos"}function R(){let e=c(u);return e==="x"?"raycast":`raycast${e}`}function S(e){u=e;try{r=JSON.parse(s.readFileSync(o.join(l(),"config.json"),"utf8"))}catch(t){if(t instanceof Error&&t.code==="ENOENT")return;throw new Error(`Failed to read config file: ${t}`)}}function A(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||r.APIURL||p.url;case"raycastAccessToken":return process.env.RAY_TOKEN||r.Token||r.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||r.ClientID||p.clientID;case"githubClientId":return process.env.RAY_GithubClientID||r.GithubClientID||w;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||r.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof r.Target<"u"?r.Target:c(process.platform==="win32"?"x":"release")}}function U(e,t){switch(e){case"raycastApiURL":t===void 0?delete r.APIURL:r.APIURL=t;break;case"raycastAccessToken":t===void 0?delete r.Token:r.Token=t,delete r.AccessToken;break;case"raycastClientId":t===void 0?delete r.ClientID:r.ClientID=t;break;case"githubAccessToken":t===void 0?delete r.GithubAccessToken:r.GithubAccessToken=t;break;case"flavorName":t===void 0?delete r.Target:r.Target=t;break}let n=l();s.writeFileSync(o.join(n,"config.json"),JSON.stringify(r,null,"  "),"utf8")}function c(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return A("flavorName")}}function G(){let e=c(u);return e==""?"raycast":`raycast-${e}`}function l(){let e=o.join(x.default.homedir(),".config",G());return s.mkdirSync(e,{recursive:!0}),e}0&&(module.exports={API_ENV,extensionBuildDirectory,getConfig,initConfig,raycastAppScheme,raycastBundleID,raycastConfigDirectory,setConfig});
