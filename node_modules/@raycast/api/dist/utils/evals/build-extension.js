"use strict";var vl=Object.create;var ze=Object.defineProperty;var _l=Object.getOwnPropertyDescriptor;var Nl=Object.getOwnPropertyNames;var jl=Object.getPrototypeOf,Gl=Object.prototype.hasOwnProperty;var O=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),kl=(e,t)=>{for(var r in t)ze(e,r,{get:t[r],enumerable:!0})},Yn=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Nl(t))!Gl.call(e,o)&&o!==r&&ze(e,o,{get:()=>t[o],enumerable:!(n=_l(t,o))||n.enumerable});return e};var E=(e,t,r)=>(r=e!=null?vl(jl(e)):{},Yn(t||!e||!e.__esModule?ze(r,"default",{value:e,enumerable:!0}):r,e)),zl=e=>Yn(ze({},"__esModule",{value:!0}),e);var Po=O((Ry,Mo)=>{Mo.exports=Co;Co.sync=vf;var Io=require("fs");function Uf(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function Ro(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:Uf(t,r)}function Co(e,t,r){Io.stat(e,function(n,o){r(n,n?!1:Ro(o,e,t))})}function vf(e,t){return Ro(Io.statSync(e),e,t)}});var Uo=O((Cy,$o)=>{$o.exports=Bo;Bo.sync=_f;var Lo=require("fs");function Bo(e,t,r){Lo.stat(e,function(n,o){r(n,n?!1:Fo(o,t))})}function _f(e,t){return Fo(Lo.statSync(e),t)}function Fo(e,t){return e.isFile()&&Nf(e,t)}function Nf(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),s=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),a=parseInt("100",8),c=parseInt("010",8),f=parseInt("001",8),l=a|c,d=r&f||r&c&&o===s||r&a&&n===i||r&l&&i===0;return d}});var _o=O((Py,vo)=>{var My=require("fs"),nt;process.platform==="win32"||global.TESTING_WINDOWS?nt=Po():nt=Uo();vo.exports=gr;gr.sync=jf;function gr(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){gr(e,t||{},function(i,s){i?o(i):n(s)})})}nt(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function jf(e,t){try{return nt.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var Vo=O((Ly,Wo)=>{var le=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",No=require("path"),Gf=le?";":":",jo=_o(),Go=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),ko=(e,t)=>{let r=t.colon||Gf,n=e.match(/\//)||le&&e.match(/\\/)?[""]:[...le?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=le?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=le?o.split(r):[""];return le&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},zo=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=ko(e,t),s=[],a=f=>new Promise((l,d)=>{if(f===n.length)return t.all&&s.length?l(s):d(Go(e));let p=n[f],u=/^".*"$/.test(p)?p.slice(1,-1):p,m=No.join(u,e),g=!u&&/^\.[\\\/]/.test(e)?e.slice(0,2)+m:m;l(c(g,f,0))}),c=(f,l,d)=>new Promise((p,u)=>{if(d===o.length)return p(a(l+1));let m=o[d];jo(f+m,{pathExt:i},(g,b)=>{if(!g&&b)if(t.all)s.push(f+m);else return p(f+m);return p(c(f,l,d+1))})});return r?a(0).then(f=>r(null,f),r):a(0)},kf=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=ko(e,t),i=[];for(let s=0;s<r.length;s++){let a=r[s],c=/^".*"$/.test(a)?a.slice(1,-1):a,f=No.join(c,e),l=!c&&/^\.[\\\/]/.test(e)?e.slice(0,2)+f:f;for(let d=0;d<n.length;d++){let p=l+n[d];try{if(jo.sync(p,{pathExt:o}))if(t.all)i.push(p);else return p}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw Go(e)};Wo.exports=zo;zo.sync=kf});var qo=O((By,yr)=>{"use strict";var Yo=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};yr.exports=Yo;yr.exports.default=Yo});var Zo=O((Fy,Xo)=>{"use strict";var Ho=require("path"),zf=Vo(),Wf=qo();function Ko(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let s;try{s=zf.sync(e.command,{path:r[Wf({env:r})],pathExt:t?Ho.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return s&&(s=Ho.resolve(o?e.options.cwd:"",s)),s}function Vf(e){return Ko(e)||Ko(e,!0)}Xo.exports=Vf});var Jo=O(($y,br)=>{"use strict";var Sr=/([()\][%!^"`<>&|;, *?])/g;function Yf(e){return e=e.replace(Sr,"^$1"),e}function qf(e,t){return e=`${e}`,e=e.replace(/(\\*)"/g,'$1$1\\"'),e=e.replace(/(\\*)$/,"$1$1"),e=`"${e}"`,e=e.replace(Sr,"^$1"),t&&(e=e.replace(Sr,"^$1")),e}br.exports.command=Yf;br.exports.argument=qf});var ei=O((Uy,Qo)=>{"use strict";Qo.exports=/^#!(.*)/});var ri=O((vy,ti)=>{"use strict";var Hf=ei();ti.exports=(e="")=>{let t=e.match(Hf);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var oi=O((_y,ni)=>{"use strict";var wr=require("fs"),Kf=ri();function Xf(e){let r=Buffer.alloc(150),n;try{n=wr.openSync(e,"r"),wr.readSync(n,r,0,150,0),wr.closeSync(n)}catch{}return Kf(r.toString())}ni.exports=Xf});var ci=O((Ny,ai)=>{"use strict";var Zf=require("path"),ii=Zo(),si=Jo(),Jf=oi(),Qf=process.platform==="win32",ed=/\.(?:com|exe)$/i,td=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function rd(e){e.file=ii(e);let t=e.file&&Jf(e.file);return t?(e.args.unshift(e.file),e.command=t,ii(e)):e.file}function nd(e){if(!Qf)return e;let t=rd(e),r=!ed.test(t);if(e.options.forceShell||r){let n=td.test(t);e.command=Zf.normalize(e.command),e.command=si.command(e.command),e.args=e.args.map(i=>si.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function od(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:nd(n)}ai.exports=od});var di=O((jy,fi)=>{"use strict";var xr=process.platform==="win32";function Er(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function id(e,t){if(!xr)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=li(o,t,"spawn");if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function li(e,t){return xr&&e===1&&!t.file?Er(t.original,"spawn"):null}function sd(e,t){return xr&&e===1&&!t.file?Er(t.original,"spawnSync"):null}fi.exports={hookChildProcess:id,verifyENOENT:li,verifyENOENTSync:sd,notFoundError:Er}});var mi=O((Gy,fe)=>{"use strict";var pi=require("child_process"),Tr=ci(),Or=di();function ui(e,t,r){let n=Tr(e,t,r),o=pi.spawn(n.command,n.args,n.options);return Or.hookChildProcess(o,n),o}function ad(e,t,r){let n=Tr(e,t,r),o=pi.spawnSync(n.command,n.args,n.options);return o.error=o.error||Or.verifyENOENTSync(o.status,n),o}fe.exports=ui;fe.exports.spawn=ui;fe.exports.sync=ad;fe.exports._parse=Tr;fe.exports._enoent=Or});var tg={};kl(tg,{buildExtension:()=>eg});module.exports=zl(tg);var nr=E(require("path")),$l=E(require("fs")),Ul=E(require("os"));function S(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var qn=require("node:url"),se=(e,t)=>{let r=sr(Wl(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},Wl=e=>ir(e)?e.toString():e,ir=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,sr=e=>e instanceof URL?(0,qn.fileURLToPath)(e):e;var We=(e,t=[],r={})=>{let n=se(e,"First argument"),[o,i]=S(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(c=>typeof c=="object"&&c!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let s=o.map(String),a=s.find(c=>c.includes("\0"));if(a!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${a}`);if(!S(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,s,i]};var ro=require("node:child_process");var Hn=require("node:string_decoder"),{toString:Kn}=Object.prototype,Xn=e=>Kn.call(e)==="[object ArrayBuffer]",w=e=>Kn.call(e)==="[object Uint8Array]",j=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),Vl=new TextEncoder,Zn=e=>Vl.encode(e),Yl=new TextDecoder,Ve=e=>Yl.decode(e),Jn=(e,t)=>ql(e,t).join(""),ql=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new Hn.StringDecoder(t),n=e.map(i=>typeof i=="string"?Zn(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},Ae=e=>e.length===1&&w(e[0])?e[0]:ar(Hl(e)),Hl=e=>e.map(t=>typeof t=="string"?Zn(t):t),ar=e=>{let t=new Uint8Array(Kl(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},Kl=e=>{let t=0;for(let r of e)t+=r.length;return t};var no=e=>Array.isArray(e)&&Array.isArray(e.raw),oo=(e,t)=>{let r=[];for(let[i,s]of e.entries())r=Xl({templates:e,expressions:t,tokens:r,index:i,template:s});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},Xl=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:s,trailingWhitespaces:a}=Zl(o,e.raw[n]),c=eo(r,i,s);if(n===t.length)return c;let f=t[n],l=Array.isArray(f)?f.map(d=>to(d)):[to(f)];return eo(c,l,a)},Zl=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=Qn.has(t[0]);for(let s=0,a=0;s<e.length;s+=1,a+=1){let c=t[a];if(Qn.has(c))n!==s&&r.push(e.slice(n,s)),n=s+1;else if(c==="\\"){let f=t[a+1];f==="u"&&t[a+2]==="{"?a=t.indexOf("}",a+3):a+=Jl[f]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},Qn=new Set([" ","	","\r",`
`]),Jl={x:3,u:5},eo=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],to=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(S(e)&&("stdout"in e||"isMaxBuffer"in e))return Ql(e);throw e instanceof ro.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},Ql=({stdout:e})=>{if(typeof e=="string")return e;if(w(e))return Ve(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var hc=require("node:child_process");var so=require("node:util");var Ye=E(require("node:process"),1),C=e=>qe.includes(e),qe=[Ye.default.stdin,Ye.default.stdout,Ye.default.stderr],I=["stdin","stdout","stderr"],He=e=>I[e]??`stdio[${e}]`;var ao=e=>{let t={...e};for(let r of fr)t[r]=cr(e,r);return t},cr=(e,t)=>{let r=Array.from({length:ef(e)+1}),n=tf(e[t],r,t);return af(n,t)},ef=({stdio:e})=>Array.isArray(e)?Math.max(e.length,I.length):I.length,tf=(e,t,r)=>S(e)?rf(e,t,r):t.fill(e),rf=(e,t,r)=>{for(let n of Object.keys(e).sort(nf))for(let o of of(n,r,t))t[o]=e[n];return t},nf=(e,t)=>io(e)<io(t)?1:-1,io=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,of=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=lr(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},lr=e=>{if(e==="all")return e;if(I.includes(e))return I.indexOf(e);let t=sf.exec(e);if(t!==null)return Number(t[1])},sf=/^fd(\d+)$/,af=(e,t)=>e.map(r=>r===void 0?lf[t]:r),cf=(0,so.debuglog)("execa").enabled?"full":"none",lf={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:cf,stripFinalNewline:!0},fr=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],G=(e,t)=>t==="ipc"?e.at(-1):e[t];var ae=({verbose:e},t)=>dr(e,t)!=="none",ce=({verbose:e},t)=>!["none","short"].includes(dr(e,t)),co=({verbose:e},t)=>{let r=dr(e,t);return Ke(r)?r:void 0},dr=(e,t)=>t===void 0?ff(e):G(e,t),ff=e=>e.find(t=>Ke(t))??Xe.findLast(t=>e.includes(t)),Ke=e=>typeof e=="function",Xe=["none","short","full"];var Oo=require("node:util");var lo=require("node:process"),fo=require("node:util"),po=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>gf(uo(i))).join(" ");return{command:n,escapedCommand:o}},De=e=>(0,fo.stripVTControlCharacters)(e).split(`
`).map(t=>uo(t)).join(`
`),uo=e=>e.replaceAll(uf,t=>df(t)),df=e=>{let t=mf[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=hf?`\\u${n.padStart(4,"0")}`:`\\U${n}`},pf=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},uf=pf(),mf={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},hf=65535,gf=e=>yf.test(e)?e:lo.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,yf=/^[\w./-]+$/;var pr=E(require("node:process"),1);function ur(){let{env:e}=pr.default,{TERM:t,TERM_PROGRAM:r}=e;return pr.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var mo={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},ho={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},Sf={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},bf={...mo,...ho},wf={...mo,...Sf},xf=ur(),Ef=xf?bf:wf,Ze=Ef,xg=Object.entries(ho);var go=E(require("node:tty"),1),Tf=go.default?.WriteStream?.prototype?.hasColors?.()??!1,h=(e,t)=>{if(!Tf)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,c=0;for(;s!==-1;)a+=i.slice(c,s)+r,c=s+n.length,s=i.indexOf(n,c);return a+=i.slice(c)+n,a}},Tg=h(0,0),yo=h(1,22),Og=h(2,22),Ag=h(3,23),Dg=h(4,24),Ig=h(53,55),Rg=h(7,27),Cg=h(8,28),Mg=h(9,29),Pg=h(30,39),Lg=h(31,39),Bg=h(32,39),Fg=h(33,39),$g=h(34,39),Ug=h(35,39),vg=h(36,39),_g=h(37,39),Je=h(90,39),Ng=h(40,49),jg=h(41,49),Gg=h(42,49),kg=h(43,49),zg=h(44,49),Wg=h(45,49),Vg=h(46,49),Yg=h(47,49),qg=h(100,49),So=h(91,39),Hg=h(92,39),bo=h(93,39),Kg=h(94,39),Xg=h(95,39),Zg=h(96,39),Jg=h(97,39),Qg=h(101,49),ey=h(102,49),ty=h(103,49),ry=h(104,49),ny=h(105,49),oy=h(106,49),iy=h(107,49);var Eo=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:s=!0}})=>{let a=Of(r),c=Af[e]({failed:i,reject:s,piped:n}),f=Df[e]({reject:s});return`${Je(`[${a}]`)} ${Je(`[${o}]`)} ${f(c)} ${f(t)}`},Of=e=>`${Qe(e.getHours(),2)}:${Qe(e.getMinutes(),2)}:${Qe(e.getSeconds(),2)}.${Qe(e.getMilliseconds(),3)}`,Qe=(e,t)=>String(e).padStart(t,"0"),wo=({failed:e,reject:t})=>e?t?Ze.cross:Ze.warning:Ze.tick,Af={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:wo,duration:wo},xo=e=>e,Df={command:()=>yo,output:()=>xo,ipc:()=>xo,error:({reject:e})=>e?So:bo,duration:()=>Je};var To=(e,t,r)=>{let n=co(t,r);return e.map(({verboseLine:o,verboseObject:i})=>If(o,i,n)).filter(o=>o!==void 0).map(o=>Rf(o)).join("")},If=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},Rf=e=>e.endsWith(`
`)?e:`${e}
`;var B=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=Cf({type:e,result:o,verboseInfo:n}),s=Mf(t,i),a=To(s,n,r);a!==""&&console.warn(a.slice(0,-1))},Cf=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),Mf=(e,t)=>e.split(`
`).map(r=>Pf({...t,message:r})),Pf=e=>({verboseLine:Eo(e),verboseObject:e}),et=e=>{let t=typeof e=="string"?e:(0,Oo.inspect)(e);return De(t).replaceAll("	"," ".repeat(Lf))},Lf=2;var Ao=(e,t)=>{ae(t)&&B({type:"command",verboseMessage:e,verboseInfo:t})};var Do=(e,t,r)=>{$f(e);let n=Bf(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},Bf=e=>ae({verbose:e})?Ff++:void 0,Ff=0n,$f=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!Xe.includes(t)&&!Ke(t)){let r=Xe.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var mr=require("node:process"),tt=()=>mr.hrtime.bigint(),hr=e=>Number(mr.hrtime.bigint()-e)/1e6;var rt=(e,t,r)=>{let n=tt(),{command:o,escapedCommand:i}=po(e,t),s=cr(r,"verbose"),a=Do(s,i,{...r});return Ao(i,a),{command:o,escapedCommand:i,startTime:n,verboseInfo:a}};var js=E(require("node:path"),1),Nr=E(require("node:process"),1),Gs=E(mi(),1);var Ie=E(require("node:process"),1),Y=E(require("node:path"),1);function ot(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var hi=require("node:util"),Dr=require("node:child_process"),Ar=E(require("node:path"),1),gi=require("node:url"),zy=(0,hi.promisify)(Dr.execFile);function it(e){return e instanceof URL?(0,gi.fileURLToPath)(e):e}function yi(e){return{*[Symbol.iterator](){let t=Ar.default.resolve(it(e)),r;for(;r!==t;)yield t,r=t,t=Ar.default.resolve(t,"..")}}}var Wy=10*1024*1024;var cd=({cwd:e=Ie.default.cwd(),path:t=Ie.default.env[ot()],preferLocal:r=!0,execPath:n=Ie.default.execPath,addExecPath:o=!0}={})=>{let i=Y.default.resolve(it(e)),s=[],a=t.split(Y.default.delimiter);return r&&ld(s,a,i),o&&fd(s,a,n,i),t===""||t===Y.default.delimiter?`${s.join(Y.default.delimiter)}${t}`:[...s,t].join(Y.default.delimiter)},ld=(e,t,r)=>{for(let n of yi(r)){let o=Y.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},fd=(e,t,r,n)=>{let o=Y.default.resolve(n,it(r),"..");t.includes(o)||e.push(o)},Si=({env:e=Ie.default.env,...t}={})=>{e={...e};let r=ot({env:e});return t.path=e[r],e[r]=cd(t),e};var Fi=require("node:timers/promises");var bi=(e,t,r)=>{let n=r?Ce:Re,o=e instanceof M?{}:{cause:e};return new n(t,o)},M=class extends Error{},wi=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Ei,{value:!0,writable:!1,enumerable:!1,configurable:!1})},xi=e=>st(e)&&Ei in e,Ei=Symbol("isExecaError"),st=e=>Object.prototype.toString.call(e)==="[object Error]",Re=class extends Error{};wi(Re,Re.name);var Ce=class extends Error{};wi(Ce,Ce.name);var de=require("node:os");var Ri=require("node:os");var Ti=()=>{let e=Ai-Oi+1;return Array.from({length:e},dd)},dd=(e,t)=>({name:`SIGRT${t+1}`,number:Oi+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),Oi=34,Ai=64;var Ii=require("node:os");var Di=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var Ir=()=>{let e=Ti();return[...Di,...e].map(pd)},pd=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:s}}=Ii.constants,a=s!==void 0;return{name:e,number:a?s:t,description:r,supported:a,action:n,forced:o,standard:i}};var ud=()=>{let e=Ir();return Object.fromEntries(e.map(md))},md=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s}],Ci=ud(),hd=()=>{let e=Ir(),t=65,r=Array.from({length:t},(n,o)=>gd(o,e));return Object.assign({},...r)},gd=(e,t)=>{let r=yd(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:s,forced:a,standard:c}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:s,forced:a,standard:c}}},yd=(e,t)=>{let r=t.find(({name:n})=>Ri.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},nS=hd();var Pi=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return Bi(e,t)},Li=e=>e===0?e:Bi(e,"`subprocess.kill()`'s argument"),Bi=(e,t)=>{if(Number.isInteger(e))return Sd(e,t);if(typeof e=="string")return wd(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${Rr()}`)},Sd=(e,t)=>{if(Mi.has(e))return Mi.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${Rr()}`)},bd=()=>new Map(Object.entries(de.constants.signals).reverse().map(([e,t])=>[t,e])),Mi=bd(),wd=(e,t)=>{if(e in de.constants.signals)return e;throw e.toUpperCase()in de.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${Rr()}`)},Rr=()=>`Available signal names: ${xd()}.
Available signal numbers: ${Ed()}.`,xd=()=>Object.keys(de.constants.signals).sort().map(e=>`'${e}'`).join(", "),Ed=()=>[...new Set(Object.values(de.constants.signals).sort((e,t)=>e-t))].join(", "),at=e=>Ci[e].description;var $i=e=>{if(e===!1)return e;if(e===!0)return Td;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},Td=1e3*5,Ui=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},s,a)=>{let{signal:c,error:f}=Od(s,a,r);Ad(f,n);let l=e(c);return Dd({kill:e,signal:c,forceKillAfterDelay:t,killSignal:r,killResult:l,context:o,controller:i}),l},Od=(e,t,r)=>{let[n=r,o]=st(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!st(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Li(n),error:o}},Ad=(e,t)=>{e!==void 0&&t.reject(e)},Dd=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:s})=>{t===n&&o&&Cr({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:s.signal})},Cr=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,Fi.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var vi=require("node:events"),ct=async(e,t)=>{e.aborted||await(0,vi.once)(e,"abort",{signal:t})};var _i=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},Ni=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[Id(e,t,n,o)],Id=async(e,t,r,{signal:n})=>{throw await ct(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var Ss=require("node:timers/promises");var gs=require("node:util");var pe=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{Rd(e,t,r),Mr(e,t,n)},Rd=(e,t,r)=>{if(!r)throw new Error(`${P(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},Mr=(e,t,r)=>{if(!r)throw new Error(`${P(e,t)} cannot be used: the ${q(t)} has already exited or disconnected.`)},ji=e=>{throw new Error(`${P("getOneMessage",e)} could not complete: the ${q(e)} exited or disconnected.`)},Gi=e=>{throw new Error(`${P("sendMessage",e)} failed: the ${q(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${P("getOneMessage",e)},
	${P("sendMessage",e,"message, {strict: true}")},
]);`)},lt=(e,t)=>new Error(`${P("sendMessage",t)} failed when sending an acknowledgment response to the ${q(t)}.`,{cause:e}),ki=e=>{throw new Error(`${P("sendMessage",e)} failed: the ${q(e)} is not listening to incoming messages.`)},zi=e=>{throw new Error(`${P("sendMessage",e)} failed: the ${q(e)} exited without listening to incoming messages.`)},Wi=()=>new Error(`\`cancelSignal\` aborted: the ${q(!0)} disconnected.`),Vi=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},Yi=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${P(t,r)} cannot be used: the ${q(r)} is disconnecting.`,{cause:e})},qi=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(Cd(e))throw new Error(`${P(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},Cd=({code:e,message:t})=>Md.has(e)||Pd.some(r=>t.includes(r)),Md=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),Pd=["could not be cloned","circular structure","call stack size exceeded"],P=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${Ld(t)}${e}(${r})`,Ld=e=>e?"":"subprocess.",q=e=>e?"parent process":"subprocess",ue=e=>{e.connected&&e.disconnect()};var F=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var dt=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=$.get(e),i=Hi(o,t,!0),s=e.stdio[i];if(s===null)throw new TypeError(Ki(i,t,n,!0));return s},me=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=$.get(e),i=Hi(o,t,!1),s=i==="all"?e.all:e.stdio[i];if(s==null)throw new TypeError(Ki(i,t,n,!1));return s},$=new WeakMap,Hi=(e,t,r)=>{let n=Bd(t,r);return Fd(n,t,r,e),n},Bd=(e,t)=>{let r=lr(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${Me(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},Fd=(e,t,r,n)=>{let o=n[Xi(e)];if(o===void 0)throw new TypeError(`"${Me(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${Me(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${Me(r)}" must not be ${t}. It must be a writable stream, not readable.`)},Ki=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=$d(e,r);return`The "${o}: ${ft(i)}" option is incompatible with using "${Me(n)}: ${ft(t)}".
Please set this option with "pipe" instead.`},$d=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=Xi(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},Xi=e=>e==="all"?1:e,Me=e=>e?"to":"from",ft=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var ls=require("node:events");var Zi=require("node:events"),Q=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,Zi.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var cs=require("node:events");var es=require("node:events"),ts=require("node:timers/promises");var pt=(e,t)=>{t&&Pr(e)},Pr=e=>{e.refCounted()},ut=(e,t)=>{t&&Lr(e)},Lr=e=>{e.unrefCounted()},Ji=(e,t)=>{t&&(Lr(e),Lr(e))},Qi=(e,t)=>{t&&(Pr(e),Pr(e))};var rs=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(is(o)||as(o))return;mt.has(e)||mt.set(e,[]);let i=mt.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await ss(e,n,o),await ts.scheduler.yield();let s=await os({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",s),n.emit("message:done")}},ns=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{Br();let i=mt.get(e);for(;i?.length>0;)await(0,es.once)(n,"message:done");e.removeListener("message",o),Qi(t,r),n.connected=!1,n.emit("disconnect")},mt=new WeakMap;var H=(e,t,r)=>{if(ht.has(e))return ht.get(e);let n=new cs.EventEmitter;return n.connected=!0,ht.set(e,n),Ud({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},ht=new WeakMap,Ud=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=rs.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",ns.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),Ji(r,n)},gt=e=>{let t=ht.get(e);return t===void 0?e.channel!==null:t.connected};var fs=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=H(e,t,r),s=bt(e,i);return{id:vd++,type:St,message:n,hasListeners:s}},vd=0n,ds=(e,t)=>{if(!(t?.type!==St||t.hasListeners))for(let{id:r}of e)r!==void 0&&yt[r].resolve({isDeadlock:!0,hasListeners:!1})},os=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==St||!t.connected)return e;let{id:i,message:s}=e,a={id:i,type:us,message:bt(t,o)};try{await wt({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},a)}catch(c){o.emit("strict:error",c)}return s},is=e=>{if(e?.type!==us)return!1;let{id:t,message:r}=e;return yt[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},ps=async(e,t,r)=>{if(e?.type!==St)return;let n=F();yt[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:s}=await Promise.race([n,_d(t,r,o)]);i&&Gi(r),s||ki(r)}finally{o.abort(),delete yt[e.id]}},yt={},_d=async(e,t,{signal:r})=>{Q(e,1,r),await(0,ls.once)(e,"disconnect",{signal:r}),zi(t)},St="execa:ipc:request",us="execa:ipc:response";var ms=(e,t,r)=>{Pe.has(e)||Pe.set(e,new Set);let n=Pe.get(e),o=F(),i=r?t.id:void 0,s={onMessageSent:o,id:i};return n.add(s),{outgoingMessages:n,outgoingMessage:s}},hs=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},ss=async(e,t,r)=>{for(;!bt(e,t)&&Pe.get(e)?.size>0;){let n=[...Pe.get(e)];ds(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},Pe=new WeakMap,bt=(e,t)=>t.listenerCount("message")>Nd(e),Nd=e=>$.has(e)&&!G($.get(e).options.buffer,"ipc")?1:0;var wt=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let s="sendMessage";return pe({methodName:s,isSubprocess:r,ipc:n,isConnected:e.connected}),jd({anyProcess:e,channel:t,methodName:s,isSubprocess:r,message:o,strict:i})},jd=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let s=fs({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),a=ms(e,s,i);try{await $r({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:s,message:o})}catch(c){throw ue(e),c}finally{hs(a)}},$r=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=Gd(e);try{await Promise.all([ps(n,e,r),i(n)])}catch(s){throw Yi({error:s,methodName:t,isSubprocess:r}),qi({error:s,methodName:t,isSubprocess:r,message:o}),s}},Gd=e=>{if(Fr.has(e))return Fr.get(e);let t=(0,gs.promisify)(e.send.bind(e));return Fr.set(e,t),t},Fr=new WeakMap;var bs=(e,t)=>{let r="cancelSignal";return Mr(r,!1,e.connected),$r({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:xs,message:t},message:t})},ws=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await kd({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),Ur.signal),kd=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!ys){if(ys=!0,!n){Vi();return}if(t===null){Br();return}H(e,t,r),await Ss.scheduler.yield()}},ys=!1,as=e=>e?.type!==xs?!1:(Ur.abort(e.message),!0),xs="execa:ipc:cancel",Br=()=>{Ur.abort(Wi())},Ur=new AbortController;var Es=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},Ts=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[zd({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],zd=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await ct(t,o);let i=Wd(t);throw await bs(e,i),Cr({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},Wd=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var Os=require("node:timers/promises");var As=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},Ds=(e,t,r,n)=>t===0||t===void 0?[]:[Vd(e,t,r,n)],Vd=async(e,t,r,{signal:n})=>{throw await(0,Os.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new M};var xt=require("node:process"),vr=E(require("node:path"),1);var Is=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},Rs=(e,t,{node:r=!1,nodePath:n=xt.execPath,nodeOptions:o=xt.execArgv.filter(c=>!c.startsWith("--inspect")),cwd:i,execPath:s,...a})=>{if(s!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let c=se(n,'The "nodePath" option'),f=vr.default.resolve(i,c),l={...a,nodePath:f,node:r,cwd:i};if(!r)return[e,t,l];if(vr.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[f,[...o,e,...t],{ipc:!0,...l,shell:!1}]};var Cs=require("node:v8"),Ms=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");Hd[r](e)}},Yd=e=>{try{(0,Cs.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},qd=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},Hd={advanced:Yd,json:qd},Ps=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var Bs=({encoding:e})=>{if(_r.has(e))return;let t=Xd(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${Et(e)}\`.
Please rename it to ${Et(t)}.`);let r=[..._r].map(n=>Et(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${Et(e)}\`.
Please rename it to one of: ${r}.`)},Kd=new Set(["utf8","utf16le"]),A=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),_r=new Set([...Kd,...A]),Xd=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Ls)return Ls[t];if(_r.has(t))return t},Ls={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},Et=e=>typeof e=="string"?`"${e}"`:String(e);var Fs=require("node:fs"),$s=E(require("node:path"),1),Us=E(require("node:process"),1);var vs=(e=_s())=>{let t=se(e,'The "cwd" option');return $s.default.resolve(t)},_s=()=>{try{return Us.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},Ns=(e,t)=>{if(t===_s())return e;let r;try{r=(0,Fs.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var Tt=(e,t,r)=>{r.cwd=vs(r.cwd);let[n,o,i]=Rs(e,t,r),{command:s,args:a,options:c}=Gs.default._parse(n,o,i),f=ao(c),l=Zd(f);return As(l),Bs(l),Ms(l),_i(l),Es(l),l.shell=sr(l.shell),l.env=Jd(l),l.killSignal=Pi(l.killSignal),l.forceKillAfterDelay=$i(l.forceKillAfterDelay),l.lines=l.lines.map((d,p)=>d&&!A.has(l.encoding)&&l.buffer[p]),Nr.default.platform==="win32"&&js.default.basename(s,".exe")==="cmd"&&a.unshift("/q"),{file:s,commandArguments:a,options:l}},Zd=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:s=!0,all:a=!1,windowsHide:c=!0,killSignal:f="SIGTERM",forceKillAfterDelay:l=!0,gracefulCancel:d=!1,ipcInput:p,ipc:u=p!==void 0||d,serialization:m="advanced",...g})=>({...g,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:s,all:a,windowsHide:c,killSignal:f,forceKillAfterDelay:l,gracefulCancel:d,ipcInput:p,ipc:u,serialization:m}),Jd=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let s=t?{...Nr.default.env,...e}:e;return r||n?Si({env:s,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):s};var la=require("node:util");function he(e){if(typeof e=="string")return Qd(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return ep(e)}var Qd=e=>e.at(-1)===ks?e.slice(0,e.at(-2)===zs?-2:-1):e,ep=e=>e.at(-1)===tp?e.subarray(0,e.at(-2)===rp?-2:-1):e,ks=`
`,tp=ks.codePointAt(0),zs="\r",rp=zs.codePointAt(0);var ta=require("node:events"),ra=require("node:stream/promises");function L(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function jr(e,{checkOpen:t=!0}={}){return L(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function ee(e,{checkOpen:t=!0}={}){return L(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Gr(e,t){return jr(e,t)&&ee(e,t)}var np=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),kr=class{#t;#r;#e=!1;#n=void 0;constructor(t,r){this.#t=t,this.#r=r}next(){let t=()=>this.#i();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#o(t);return this.#n?this.#n.then(r,r):r()}async#i(){if(this.#e)return{done:!0,value:void 0};let t;try{t=await this.#t.read()}catch(r){throw this.#n=void 0,this.#e=!0,this.#t.releaseLock(),r}return t.done&&(this.#n=void 0,this.#e=!0,this.#t.releaseLock()),t}async#o(t){if(this.#e)return{done:!0,value:t};if(this.#e=!0,!this.#r){let r=this.#t.cancel(t);return this.#t.releaseLock(),await r,{done:!0,value:t}}return this.#t.releaseLock(),{done:!0,value:t}}},zr=Symbol();function Ws(){return this[zr].next()}Object.defineProperty(Ws,"name",{value:"next"});function Vs(e){return this[zr].return(e)}Object.defineProperty(Vs,"name",{value:"return"});var op=Object.create(np,{next:{enumerable:!0,configurable:!0,writable:!0,value:Ws},return:{enumerable:!0,configurable:!0,writable:!0,value:Vs}});function Wr({preventCancel:e=!1}={}){let t=this.getReader(),r=new kr(t,e),n=Object.create(op);return n[zr]=r,n}var Ys=e=>{if(ee(e,{checkOpen:!1})&&Le.on!==void 0)return sp(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(ip.call(e)==="[object ReadableStream]")return Wr.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:ip}=Object.prototype,sp=async function*(e){let t=new AbortController,r={};ap(e,t,r);try{for await(let[n]of Le.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},ap=async(e,t,r)=>{try{await Le.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},Le={};var ge=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,finalize:a},{maxBuffer:c=Number.POSITIVE_INFINITY}={})=>{let f=Ys(e),l=t();l.length=0;try{for await(let d of f){let p=lp(d),u=r[p](d,l);Ks({convertedChunk:u,state:l,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:c})}return cp({state:l,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,maxBuffer:c}),a(l)}catch(d){let p=typeof d=="object"&&d!==null?d:new Error(d);throw p.bufferedData=a(l),p}},cp=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let s=o(e);s!==void 0&&Ks({convertedChunk:s,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},Ks=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let s=r(e),a=t.length+s;if(a<=i){qs(e,t,o,a);return}let c=n(e,i-t.length);throw c!==void 0&&qs(c,t,o,i),new U},qs=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},lp=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=Hs.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&Hs.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:Hs}=Object.prototype,U=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var k=e=>e,Be=()=>{},Ot=({contents:e})=>e,At=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},Dt=e=>e.length;async function It(e,t){return ge(e,up,t)}var fp=()=>({contents:[]}),dp=()=>1,pp=(e,{contents:t})=>(t.push(e),t),up={init:fp,convertChunk:{string:k,buffer:k,arrayBuffer:k,dataView:k,typedArray:k,others:k},getSize:dp,truncateChunk:Be,addChunk:pp,getFinalChunk:Be,finalize:Ot};async function Rt(e,t){return ge(e,Ep,t)}var mp=()=>({contents:new ArrayBuffer(0)}),hp=e=>gp.encode(e),gp=new TextEncoder,Xs=e=>new Uint8Array(e),Zs=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),yp=(e,t)=>e.slice(0,t),Sp=(e,{contents:t,length:r},n)=>{let o=ea()?wp(t,n):bp(t,n);return new Uint8Array(o).set(e,r),o},bp=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(Qs(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},wp=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:Qs(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},Qs=e=>Js**Math.ceil(Math.log(e)/Math.log(Js)),Js=2,xp=({contents:e,length:t})=>ea()?e:e.slice(0,t),ea=()=>"resize"in ArrayBuffer.prototype,Ep={init:mp,convertChunk:{string:hp,buffer:Xs,arrayBuffer:Xs,dataView:Zs,typedArray:Zs,others:At},getSize:Dt,truncateChunk:yp,addChunk:Sp,getFinalChunk:Be,finalize:xp};async function Mt(e,t){return ge(e,Ip,t)}var Tp=()=>({contents:"",textDecoder:new TextDecoder}),Ct=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),Op=(e,{contents:t})=>t+e,Ap=(e,t)=>e.slice(0,t),Dp=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},Ip={init:Tp,convertChunk:{string:k,buffer:Ct,arrayBuffer:Ct,dataView:Ct,typedArray:Ct,others:At},getSize:Dt,truncateChunk:Ap,addChunk:Op,getFinalChunk:Dp,finalize:Ot};Object.assign(Le,{on:ta.on,finished:ra.finished});var na=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof U))throw e;if(i==="all")return e;let s=Rp(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:s},t.destroy(),e},Rp=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",oa=(e,t,r)=>{if(t.length!==r)return;let n=new U;throw n.maxBufferInfo={fdNumber:"ipc"},n},ia=(e,t)=>{let{streamName:r,threshold:n,unit:o}=Cp(e,t);return`Command's ${r} was larger than ${n} ${o}`},Cp=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=G(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:He(r),threshold:o,unit:n}},sa=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>Pt(r)),aa=(e,t,r)=>{if(!t)return e;let n=Pt(r);return e.length>n?e.slice(0,n):e},Pt=([,e])=>e;var fa=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:s,escapedCommand:a,timedOut:c,isCanceled:f,isGracefullyCanceled:l,isMaxBuffer:d,isForcefullyTerminated:p,forceKillAfterDelay:u,killSignal:m,maxBuffer:g,timeout:b,cwd:y})=>{let x=n?.code,T=Mp({originalError:n,timedOut:c,timeout:b,isMaxBuffer:d,maxBuffer:g,errorCode:x,signal:o,signalDescription:i,exitCode:s,isCanceled:f,isGracefullyCanceled:l,isForcefullyTerminated:p,forceKillAfterDelay:u,killSignal:m}),D=Lp(n,y),N=D===void 0?"":`
${D}`,W=`${T}: ${a}${N}`,J=t===void 0?[e[2],e[1]]:[t],ie=[W,...J,...e.slice(3),r.map(V=>Bp(V)).join(`
`)].map(V=>De(he(Fp(V)))).filter(Boolean).join(`

`);return{originalMessage:D,shortMessage:W,message:ie}},Mp=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:s,signalDescription:a,exitCode:c,isCanceled:f,isGracefullyCanceled:l,isForcefullyTerminated:d,forceKillAfterDelay:p,killSignal:u})=>{let m=Pp(d,p);return t?`Command timed out after ${r} milliseconds${m}`:l?s===void 0?`Command was gracefully canceled with exit code ${c}`:d?`Command was gracefully canceled${m}`:`Command was gracefully canceled with ${s} (${a})`:f?`Command was canceled${m}`:n?`${ia(e,o)}${m}`:i!==void 0?`Command failed with ${i}${m}`:d?`Command was killed with ${u} (${at(u)})${m}`:s!==void 0?`Command was killed with ${s} (${a})`:c!==void 0?`Command failed with exit code ${c}`:"Command failed"},Pp=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",Lp=(e,t)=>{if(e instanceof M)return;let r=xi(e)?e.originalMessage:String(e?.message??e),n=De(Ns(r,t));return n===""?void 0:n},Bp=e=>typeof e=="string"?e:(0,la.inspect)(e),Fp=e=>Array.isArray(e)?e.map(t=>he(ca(t))).filter(Boolean).join(`
`):ca(e),ca=e=>typeof e=="string"?e:w(e)?Ve(e):"";var Lt=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:s})=>da({command:e,escapedCommand:t,cwd:i,durationMs:hr(s),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),ye=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:s})=>Fe({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:s}),Fe=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,stdio:d,all:p,ipcOutput:u,options:{timeoutDuration:m,timeout:g=m,forceKillAfterDelay:b,killSignal:y,cwd:x,maxBuffer:T},isSync:D})=>{let{exitCode:N,signal:W,signalDescription:J}=Up(f,l),{originalMessage:ie,shortMessage:V,message:or}=fa({stdio:d,all:p,ipcOutput:u,originalError:e,signal:W,signalDescription:J,exitCode:N,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,forceKillAfterDelay:b,killSignal:y,maxBuffer:T,timeout:g,cwd:x}),Oe=bi(e,or,D);return Object.assign(Oe,$p({error:Oe,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:N,signal:W,signalDescription:J,stdio:d,all:p,ipcOutput:u,cwd:x,originalMessage:ie,shortMessage:V})),Oe},$p=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,signalDescription:d,stdio:p,all:u,ipcOutput:m,cwd:g,originalMessage:b,shortMessage:y})=>da({shortMessage:y,originalMessage:b,command:t,escapedCommand:r,cwd:g,durationMs:hr(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isTerminated:l!==void 0,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,signalDescription:d,code:e.cause?.code,stdout:p[1],stderr:p[2],all:u,stdio:p,ipcOutput:m,pipedFrom:[]}),da=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),Up=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:at(t);return{exitCode:r,signal:n,signalDescription:o}};var pa=e=>Number.isFinite(e)?e:0;function vp(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(pa(e*1e3)%1e3),nanoseconds:Math.trunc(pa(e*1e6)%1e3)}}function _p(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function Vr(e){switch(typeof e){case"number":{if(Number.isFinite(e))return vp(e);break}case"bigint":return _p(e)}throw new TypeError("Expected a finite number or bigint")}var Np=e=>e===0||e===0n,jp=(e,t)=>t===1||t===1n?e:`${e}s`,Gp=1e-7,kp=24n*60n*60n*1000n;function Yr(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(l,d)=>{let p=Math.floor(l*10**d+Gp);return(Math.round(p)/10**d).toFixed(d)},s=(l,d,p,u)=>{if(!((o.length===0||!t.colonNotation)&&Np(l)&&!(t.colonNotation&&p==="m"))){if(u??=String(l),t.colonNotation){let m=u.includes(".")?u.split(".")[0].length:u.length,g=o.length>0?2:1;u="0".repeat(Math.max(0,g-m))+u}else u+=t.verbose?" "+jp(d,l):p;o.push(u)}},a=Vr(e),c=BigInt(a.days);if(s(c/365n,"year","y"),s(c%365n,"day","d"),s(Number(a.hours),"hour","h"),s(Number(a.minutes),"minute","m"),t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let l=Number(a.seconds),d=Number(a.milliseconds),p=Number(a.microseconds),u=Number(a.nanoseconds);if(s(l,"second","s"),t.formatSubMilliseconds)s(d,"millisecond","ms"),s(p,"microsecond","\xB5s"),s(u,"nanosecond","ns");else{let m=d+p/1e3+u/1e6,g=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,b=m>=1?Math.round(m):Math.ceil(m),y=g?m.toFixed(g):b;s(Number.parseFloat(y),"millisecond","ms",y)}}else{let l=(r?Number(e%kp):e)/1e3%60,d=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,p=i(l,d),u=t.keepDecimalsOnWholeSeconds?p:p.replace(/\.0+$/,"");s(Number.parseFloat(u),"second","s",u)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let f=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(f)}var ua=(e,t)=>{e.failed&&B({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var ma=(e,t)=>{ae(t)&&(ua(e,t),zp(e,t))},zp=(e,t)=>{let r=`(done in ${Yr(e.durationMs)})`;B({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var Se=(e,t,{reject:r})=>{if(ma(e,t),e.failed&&r)throw e;return e};var en=require("node:fs");var ya=(e,t)=>te(e)?"asyncGenerator":wa(e)?"generator":Bt(e)?"fileUrl":Hp(e)?"filePath":Zp(e)?"webStream":L(e,{checkOpen:!1})?"native":w(e)?"uint8Array":Jp(e)?"asyncIterable":Qp(e)?"iterable":Kr(e)?Sa({transform:e},t):qp(e)?Wp(e,t):"native",Wp=(e,t)=>Gr(e.transform,{checkOpen:!1})?Vp(e,t):Kr(e.transform)?Sa(e,t):Yp(e,t),Vp=(e,t)=>(ba(e,t,"Duplex stream"),"duplex"),Sa=(e,t)=>(ba(e,t,"web TransformStream"),"webTransform"),ba=({final:e,binary:t,objectMode:r},n,o)=>{ha(e,`${n}.final`,o),ha(t,`${n}.binary`,o),qr(r,`${n}.objectMode`)},ha=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},Yp=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!ga(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(Gr(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(Kr(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!ga(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return qr(r,`${o}.binary`),qr(n,`${o}.objectMode`),te(e)||te(t)?"asyncGenerator":"generator"},qr=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},ga=e=>te(e)||wa(e),te=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",wa=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",qp=e=>S(e)&&(e.transform!==void 0||e.final!==void 0),Bt=e=>Object.prototype.toString.call(e)==="[object URL]",xa=e=>Bt(e)&&e.protocol!=="file:",Hp=e=>S(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>Kp.has(t))&&Hr(e.file),Kp=new Set(["file","append"]),Hr=e=>typeof e=="string",Ea=(e,t)=>e==="native"&&typeof t=="string"&&!Xp.has(t),Xp=new Set(["ipc","ignore","inherit","overlapped","pipe"]),Ta=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",Ft=e=>Object.prototype.toString.call(e)==="[object WritableStream]",Zp=e=>Ta(e)||Ft(e),Kr=e=>Ta(e?.readable)&&Ft(e?.writable),Jp=e=>Oa(e)&&typeof e[Symbol.asyncIterator]=="function",Qp=e=>Oa(e)&&typeof e[Symbol.iterator]=="function",Oa=e=>typeof e=="object"&&e!==null,R=new Set(["generator","asyncGenerator","duplex","webTransform"]),$t=new Set(["fileUrl","filePath","fileNumber"]),Xr=new Set(["fileUrl","filePath"]),Aa=new Set([...Xr,"webStream","nodeStream"]),Da=new Set(["webTransform","duplex"]),K={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var Zr=(e,t,r,n)=>n==="output"?eu(e,t,r):tu(e,t,r),eu=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},tu=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},Ia=(e,t)=>{let r=e.findLast(({type:n})=>R.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var Ra=(e,t,r,n)=>[...e.filter(({type:o})=>!R.has(o)),...ru(e,t,r,n)],ru=(e,t,r,{encoding:n})=>{let o=e.filter(({type:s})=>R.has(s)),i=Array.from({length:o.length});for(let[s,a]of Object.entries(o))i[s]=nu({stdioItem:a,index:Number(s),newTransforms:i,optionName:t,direction:r,encoding:n});return au(i,r)},nu=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:s})=>t==="duplex"?ou({stdioItem:e,optionName:o}):t==="webTransform"?iu({stdioItem:e,index:r,newTransforms:n,direction:i}):su({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:s}),ou=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},iu=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:s}=S(t)?t:{transform:t},{writableObjectMode:a,readableObjectMode:c}=Zr(s,r,n,o);return{...e,value:{transform:i,writableObjectMode:a,readableObjectMode:c}}},su=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:s,final:a,binary:c=!1,preserveNewlines:f=!1,objectMode:l}=S(t)?t:{transform:t},d=c||A.has(i),{writableObjectMode:p,readableObjectMode:u}=Zr(l,r,n,o);return{...e,value:{transform:s,final:a,binary:d,preserveNewlines:f,writableObjectMode:p,readableObjectMode:u}}},au=(e,t)=>t==="input"?e.reverse():e;var Ut=E(require("node:process"),1);var Ca=(e,t,r)=>{let n=e.map(o=>cu(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??du},cu=({type:e,value:t},r)=>lu[r]??Ma[e](t),lu=["input","output","output"],be=()=>{},Jr=()=>"input",Ma={generator:be,asyncGenerator:be,fileUrl:be,filePath:be,iterable:Jr,asyncIterable:Jr,uint8Array:Jr,webStream:e=>Ft(e)?"output":"input",nodeStream(e){return ee(e,{checkOpen:!1})?jr(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:be,duplex:be,native(e){let t=fu(e);if(t!==void 0)return t;if(L(e,{checkOpen:!1}))return Ma.nodeStream(e)}},fu=e=>{if([0,Ut.default.stdin].includes(e))return"input";if([1,2,Ut.default.stdout,Ut.default.stderr].includes(e))return"output"},du="output";var Pa=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var La=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let s=pu(e,n).map((a,c)=>Ba(a,c));return i?mu(s,r,o):Pa(s,t)},pu=(e,t)=>{if(e===void 0)return I.map(n=>t[n]);if(uu(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${I.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,I.length);return Array.from({length:r},(n,o)=>e[o])},uu=e=>I.some(t=>e[t]!==void 0),Ba=(e,t)=>Array.isArray(e)?e.map(r=>Ba(r,t)):e??(t>=I.length?"ignore":"pipe"),mu=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!ce(r,o)&&hu(n)?"ignore":n),hu=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var $a=require("node:fs"),Ua=E(require("node:tty"),1);var va=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?gu({stdioItem:e,fdNumber:n,direction:o}):bu({stdioItem:e,fdNumber:n}),gu=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=yu({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if(L(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},yu=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=Su(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(Ua.default.isatty(o))throw new TypeError(`The \`${t}: ${ft(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:j((0,$a.readFileSync)(o)),optionName:t}}},Su=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=qe.indexOf(e);if(r!==-1)return r},bu=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:Fa(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:Fa(t,t,r),optionName:r}:L(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,Fa=(e,t,r)=>{let n=qe[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var _a=({input:e,inputFile:t},r)=>r===0?[...wu(e),...Eu(t)]:[],wu=e=>e===void 0?[]:[{type:xu(e),value:e,optionName:"input"}],xu=e=>{if(ee(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(w(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},Eu=e=>e===void 0?[]:[{...Tu(e),optionName:"inputFile"}],Tu=e=>{if(Bt(e))return{type:"fileUrl",value:e};if(Hr(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var Na=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),ja=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let s=Ou(o,e);if(s.length!==0){if(i){Au({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});return}if(Aa.has(e))return Ga({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});Da.has(e)&&Iu({otherStdioItems:s,type:e,value:t,optionName:r})}},Ou=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),Au=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{Xr.has(t)&&Ga({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},Ga=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(a=>Du(a,r));if(i.length===0)return;let s=i.find(a=>a.direction!==o);return ka(s,n,t),o==="output"?i[0].stream:void 0},Du=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,Iu=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);ka(o,n,t)},ka=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${K[r]} that is the same.`)};var vt=(e,t,r,n)=>{let i=La(t,r,n).map((a,c)=>Ru({stdioOption:a,fdNumber:c,options:t,isSync:n})),s=Uu({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=s.map(({stdioItems:a})=>Nu(a)),s},Ru=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=He(t),{stdioItems:i,isStdioArray:s}=Cu({stdioOption:e,fdNumber:t,options:r,optionName:o}),a=Ca(i,t,o),c=i.map(d=>va({stdioItem:d,isStdioArray:s,fdNumber:t,direction:a,isSync:n})),f=Ra(c,o,a,r),l=Ia(f,a);return $u(f,l),{direction:a,objectMode:l,stdioItems:f}},Cu=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(c=>Mu(c,n)),..._a(r,t)],s=Na(i),a=s.length>1;return Pu(s,a,n),Bu(s),{stdioItems:s,isStdioArray:a}},Mu=(e,t)=>({type:ya(e,t),value:e,optionName:t}),Pu=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(Lu.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},Lu=new Set(["ignore","ipc"]),Bu=e=>{for(let t of e)Fu(t)},Fu=({type:e,value:t,optionName:r})=>{if(xa(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(Ea(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},$u=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>$t.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},Uu=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(vu({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw Qr(o),i}},vu=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:s})=>{let a=r.map(c=>_u({stdioItem:c,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:s}));return{direction:e,objectMode:t,stdioItems:a}},_u=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let s=ja({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return s!==void 0?{...e,stream:s}:{...e,...t[r][e.type](e,n)}},Qr=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!C(r)&&r.destroy()},Nu=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var Wa=(e,t)=>vt(Gu,e,t,!0),v=({type:e,optionName:t})=>{Va(t,K[e])},ju=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&Va(e,`"${t}"`),{}),Va=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},za={generator(){},asyncGenerator:v,webStream:v,nodeStream:v,webTransform:v,duplex:v,asyncIterable:v,native:ju},Gu={input:{...za,fileUrl:({value:e})=>({contents:[j((0,en.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[j((0,en.readFileSync)(e))]}),fileNumber:v,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...za,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:v,string:v,uint8Array:v}};var z=(e,{stripFinalNewline:t},r)=>tn(t,r)&&e!==void 0&&!Array.isArray(e)?he(e):e,tn=(e,t)=>t==="all"?e[1]||e[2]:e[t];var Ue=require("node:stream");var _t=(e,t,r,n)=>e||r?void 0:qa(t,n),nn=(e,t,r)=>r?e.flatMap(n=>Ya(n,t)):Ya(e,t),Ya=(e,t)=>{let{transform:r,final:n}=qa(t,{});return[...r(e),...n()]},qa=(e,t)=>(t.previousChunks="",{transform:ku.bind(void 0,t,e),final:Wu.bind(void 0,t)}),ku=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let s=zu(r,i,t,e),a=r.slice(o+1,i+1-s);n.length>0&&(a=rn(n,a),n=""),yield a,o=i}o!==r.length-1&&(n=rn(n,r.slice(o+1))),e.previousChunks=n},zu=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),Wu=function*({previousChunks:e}){e.length>0&&(yield e)},Ha=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:Vu.bind(void 0,n)},Vu=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?Yu:Hu;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},rn=(e,t)=>`${e}${t}`,Yu={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:rn},qu=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},Hu={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:qu};var Ka=require("node:buffer");var Xa=(e,t)=>e?void 0:Ku.bind(void 0,t),Ku=function*(e,t){if(typeof t!="string"&&!w(t)&&!Ka.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},Za=(e,t)=>e?Xu.bind(void 0,t):Zu.bind(void 0,t),Xu=function*(e,t){Ja(e,t),yield t},Zu=function*(e,t){if(Ja(e,t),typeof t!="string"&&!w(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},Ja=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var Qa=require("node:buffer"),ec=require("node:string_decoder");var Nt=(e,t,r)=>{if(r)return;if(e)return{transform:Ju.bind(void 0,new TextEncoder)};let n=new ec.StringDecoder(t);return{transform:Qu.bind(void 0,n),final:em.bind(void 0,n)}},Ju=function*(e,t){Qa.Buffer.isBuffer(t)?yield j(t):typeof t=="string"?yield e.encode(t):yield t},Qu=function*(e,t){yield w(t)?e.write(t):t},em=function*(e){let t=e.end();t!==""&&(yield t)};var on=require("node:util"),sn=(0,on.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),jt=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=rm}=t[r];for await(let o of n(e))yield*jt(o,t,r+1)},tc=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*tm(r,Number(t),e)},tm=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*jt(n,r,t+1)},rc=(0,on.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),rm=function*(e){yield e};var an=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},nc=(e,t)=>[...t.flatMap(r=>[...re(r,e,0)]),...$e(e)],re=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=om}=t[r];for(let o of n(e))yield*re(o,t,r+1)},$e=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*nm(r,Number(t),e)},nm=function*(e,t,r){if(e!==void 0)for(let n of e())yield*re(n,r,t+1)},om=function*(e){yield e};var cn=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:s})=>{let a={},c=oc(e,s,i),f=te(t),l=te(r),d=f?sn.bind(void 0,jt,a):an.bind(void 0,re),p=f||l?sn.bind(void 0,tc,a):an.bind(void 0,$e),u=f||l?rc.bind(void 0,a):void 0;return{stream:new Ue.Transform({writableObjectMode:n,writableHighWaterMark:(0,Ue.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,Ue.getDefaultHighWaterMark)(o),transform(g,b,y){d([g,c,0],this,y)},flush(g){p([c],this,g)},destroy:u})}},Gt=(e,t,r,n)=>{let o=t.filter(({type:s})=>s==="generator"),i=n?o.reverse():o;for(let{value:s,optionName:a}of i){let c=oc(s,r,a);e=nc(c,e)}return e},oc=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},s,a)=>{let c={};return[{transform:Xa(n,a)},Nt(r,s,n),_t(r,i,n,c),{transform:e,final:t},{transform:Za(o,a)},Ha({binary:r,preserveNewlines:i,readableObjectMode:o,state:c})].filter(Boolean)};var ic=(e,t)=>{for(let r of im(e))sm(e,r,t)},im=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),sm=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:a})=>a!==void 0);if(o.length===0)return;if(t!==0){let[{type:a,optionName:c}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${c}\`, can be ${K[a]} with synchronous methods.`)}let s=o.map(({contents:a})=>a).map(a=>am(a,n));r.input=Ae(s)},am=(e,t)=>{let r=Gt(e,t,"utf8",!0);return cm(r),Ae(r)},cm=e=>{let t=e.find(r=>typeof r!="string"&&!w(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var zt=require("node:fs");var kt=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&ce(r,n)&&!A.has(t)&&lm(n)&&(e.some(({type:o,value:i})=>o==="native"&&fm.has(i))||e.every(({type:o})=>R.has(o))),lm=e=>e===1||e===2,fm=new Set(["pipe","overlapped"]),sc=async(e,t,r,n)=>{for await(let o of e)dm(t)||cc(o,r,n)},ac=(e,t,r)=>{for(let n of e)cc(n,t,r)},dm=e=>e._readableState.pipes.length>0,cc=(e,t,r)=>{let n=et(e);B({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var lc=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},s=new Set([]);return{output:t.map((c,f)=>pm({result:c,fileDescriptors:e,fdNumber:f,state:i,outputFiles:s,isMaxBuffer:n,verboseInfo:o},r)),...i}},pm=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:s},{buffer:a,encoding:c,lines:f,stripFinalNewline:l,maxBuffer:d})=>{if(e===null)return;let p=aa(e,i,d),u=j(p),{stdioItems:m,objectMode:g}=t[r],b=um([u],m,c,n),{serializedResult:y,finalResult:x=y}=mm({chunks:b,objectMode:g,encoding:c,lines:f,stripFinalNewline:l,fdNumber:r});hm({serializedResult:y,fdNumber:r,state:n,verboseInfo:s,encoding:c,stdioItems:m,objectMode:g});let T=a[r]?x:void 0;try{return n.error===void 0&&gm(y,m,o),T}catch(D){return n.error=D,T}},um=(e,t,r,n)=>{try{return Gt(e,t,r,!1)}catch(o){return n.error=o,e}},mm=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:Ae(e)};let s=Jn(e,r);return n[i]?{serializedResult:s,finalResult:nn(s,!o[i],t)}:{serializedResult:s}},hm=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:s})=>{if(!kt({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let a=nn(e,!1,s);try{ac(a,t,n)}catch(c){r.error??=c}},gm=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>$t.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,zt.appendFileSync)(n,e):(r.add(i),(0,zt.writeFileSync)(n,e))}};var fc=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,z(t,r,"all")]:Array.isArray(t)?[z(e,r,"all"),...t]:w(e)&&w(t)?ar([e,t]):`${e}${t}`};var Wt=require("node:events");var dc=async(e,t)=>{let[r,n]=await ym(e);return t.isForcefullyTerminated??=!1,[r,n]},ym=async e=>{let[t,r]=await Promise.allSettled([(0,Wt.once)(e,"spawn"),(0,Wt.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?pc(e):r.value},pc=async e=>{try{return await(0,Wt.once)(e,"exit")}catch{return pc(e)}},uc=async e=>{let[t,r]=await e;if(!Sm(t,r)&&ln(t,r))throw new M;return[t,r]},Sm=(e,t)=>e===void 0&&t===void 0,ln=(e,t)=>e!==0||t!==null;var mc=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=bm(e,t,r),s=i?.code==="ETIMEDOUT",a=sa(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:s,isMaxBuffer:a}},bm=(e,t,r)=>e!==void 0?e:ln(t,r)?new M:void 0;var gc=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:s,startTime:a,verboseInfo:c,options:f,fileDescriptors:l}=wm(e,t,r),d=Tm({file:n,commandArguments:o,options:f,command:i,escapedCommand:s,verboseInfo:c,fileDescriptors:l,startTime:a});return Se(d,c,f)},wm=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=rt(e,t,r),a=xm(r),{file:c,commandArguments:f,options:l}=Tt(e,t,a);Em(l);let d=Wa(l,s);return{file:c,commandArguments:f,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:d}},xm=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,Em=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&Vt("ipcInput"),e&&Vt("ipc: true"),r&&Vt("detached: true"),n&&Vt("cancelSignal")},Vt=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},Tm=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:s,startTime:a})=>{let c=Om({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:s,startTime:a});if(c.failed)return c;let{resultError:f,exitCode:l,signal:d,timedOut:p,isMaxBuffer:u}=mc(c,r),{output:m,error:g=f}=lc({fileDescriptors:s,syncResult:c,options:r,isMaxBuffer:u,verboseInfo:i}),b=m.map((x,T)=>z(x,r,T)),y=z(fc(m,r),r,"all");return Dm({error:g,exitCode:l,signal:d,timedOut:p,isMaxBuffer:u,stdio:b,all:y,options:r,command:n,escapedCommand:o,startTime:a})},Om=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:s})=>{try{ic(i,r);let a=Am(r);return(0,hc.spawnSync)(e,t,a)}catch(a){return ye({error:a,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:s,isSync:!0})}},Am=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:Pt(t)}),Dm=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:s,options:a,command:c,escapedCommand:f,startTime:l})=>e===void 0?Lt({command:c,escapedCommand:f,stdio:i,all:s,ipcOutput:[],options:a,startTime:l}):Fe({error:e,command:c,escapedCommand:f,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:s,ipcOutput:[],options:a,startTime:l,isSync:!0});var Ol=require("node:events"),Al=require("node:child_process");var dn=E(require("node:process"),1);var we=require("node:events");var yc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(pe({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:gt(e)}),Im({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),Im=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{pt(t,o);let i=H(e,t,r),s=new AbortController;try{return await Promise.race([Rm(i,n,s),Cm(i,r,s),Mm(i,r,s)])}catch(a){throw ue(e),a}finally{s.abort(),ut(t,o)}},Rm=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,we.once)(e,"message",{signal:r});return n}for await(let[n]of(0,we.on)(e,"message",{signal:r}))if(t(n))return n},Cm=async(e,t,{signal:r})=>{await(0,we.once)(e,"disconnect",{signal:r}),ji(t)},Mm=async(e,t,{signal:r})=>{let[n]=await(0,we.once)(e,"strict:error",{signal:r});throw lt(n,t)};var ve=require("node:events");var bc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>fn({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),fn=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{pe({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:gt(e)}),pt(t,i);let s=H(e,t,r),a=new AbortController,c={};return Pm(e,s,a),Lm({ipcEmitter:s,isSubprocess:r,controller:a,state:c}),Bm({anyProcess:e,channel:t,ipcEmitter:s,isSubprocess:r,shouldAwait:o,controller:a,state:c,reference:i})},Pm=async(e,t,r)=>{try{await(0,ve.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},Lm=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,ve.once)(e,"strict:error",{signal:r.signal});n.error=lt(o,t),r.abort()}catch{}},Bm=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:s,reference:a}){try{for await(let[c]of(0,ve.on)(r,"message",{signal:i.signal}))Sc(s),yield c}catch{Sc(s)}finally{i.abort(),ut(t,a),n||ue(e),o&&await e}},Sc=({error:e})=>{if(e)throw e};var wc=(e,{ipc:t})=>{Object.assign(e,Ec(e,!1,t))},xc=()=>{let e=dn.default,t=!0,r=dn.default.channel!==void 0;return{...Ec(e,t,r),getCancelSignal:ws.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},Ec=(e,t,r)=>({sendMessage:wt.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:yc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:bc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var Tc=require("node:child_process"),X=require("node:stream");var Oc=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:s})=>{Qr(n);let a=new Tc.ChildProcess;Fm(a,n),Object.assign(a,{readable:$m,writable:Um,duplex:vm});let c=ye({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),f=_m(c,s,o);return{subprocess:a,promise:f}},Fm=(e,t)=>{let r=_e(),n=_e(),o=_e(),i=Array.from({length:t.length-3},_e),s=_e(),a=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:s,stdio:a})},_e=()=>{let e=new X.PassThrough;return e.end(),e},$m=()=>new X.Readable({read(){}}),Um=()=>new X.Writable({write(){}}),vm=()=>new X.Duplex({read(){},write(){}}),_m=async(e,t,r)=>Se(e,t,r);var xe=require("node:fs"),Dc=require("node:buffer"),_=require("node:stream");var Ic=(e,t)=>vt(Nm,e,t,!1),Ne=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${K[e]}.`)},Ac={fileNumber:Ne,generator:cn,asyncGenerator:cn,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:_.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},Nm={input:{...Ac,fileUrl:({value:e})=>({stream:(0,xe.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,xe.createReadStream)(e)}),webStream:({value:e})=>({stream:_.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:_.Readable.from(e)}),asyncIterable:({value:e})=>({stream:_.Readable.from(e)}),string:({value:e})=>({stream:_.Readable.from(e)}),uint8Array:({value:e})=>({stream:_.Readable.from(Dc.Buffer.from(e))})},output:{...Ac,fileUrl:({value:e})=>({stream:(0,xe.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,xe.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:_.Writable.fromWeb(e)}),iterable:Ne,asyncIterable:Ne,string:Ne,uint8Array:Ne}};var je=require("node:events"),qt=require("node:stream"),mn=require("node:stream/promises");function ne(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)un(o);let t=e.some(({readableObjectMode:o})=>o),r=jm(e,t),n=new pn({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var jm=(e,t)=>{if(e.length===0)return(0,qt.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},pn=class extends qt.PassThrough{#t=new Set([]);#r=new Set([]);#e=new Set([]);#n;#i=Symbol("unpipe");#o=new WeakMap;add(t){if(un(t),this.#t.has(t))return;this.#t.add(t),this.#n??=Gm(this,this.#t,this.#i);let r=Wm({passThroughStream:this,stream:t,streams:this.#t,ended:this.#r,aborted:this.#e,onFinished:this.#n,unpipeEvent:this.#i});this.#o.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(un(t),!this.#t.has(t))return!1;let r=this.#o.get(t);return r===void 0?!1:(this.#o.delete(t),t.unpipe(this),await r,!0)}},Gm=async(e,t,r)=>{Yt(e,Rc);let n=new AbortController;try{await Promise.race([km(e,n),zm(e,t,r,n)])}finally{n.abort(),Yt(e,-Rc)}},km=async(e,{signal:t})=>{try{await(0,mn.finished)(e,{signal:t,cleanup:!0})}catch(r){throw Mc(e,r),r}},zm=async(e,t,r,{signal:n})=>{for await(let[o]of(0,je.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},un=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},Wm=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:s})=>{Yt(e,Cc);let a=new AbortController;try{await Promise.race([Vm(i,t,a),Ym({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:a}),qm({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:s,controller:a})])}finally{a.abort(),Yt(e,-Cc)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?hn(e):Hm(e))},Vm=async(e,t,{signal:r})=>{try{await e,r.aborted||hn(t)}catch(n){r.aborted||Mc(t,n)}},Ym=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,mn.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(s){if(i.aborted||!r.has(t))return;Pc(s)?o.add(t):Lc(e,s)}},qm=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,je.once)(e,o,{signal:i}),!e.readable)return(0,je.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},Hm=e=>{e.writable&&e.end()},Mc=(e,t)=>{Pc(t)?hn(e):Lc(e,t)},Pc=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",hn=e=>{(e.readable||e.writable)&&e.destroy()},Lc=(e,t)=>{e.destroyed||(e.once("error",Km),e.destroy(t))},Km=()=>{},Yt=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},Rc=2,Cc=1;var gn=require("node:stream/promises");var Ee=(e,t)=>{e.pipe(t),Xm(e,t),Zm(e,t)},Xm=async(e,t)=>{if(!(C(e)||C(t))){try{await(0,gn.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}yn(t)}},yn=e=>{e.writable&&e.end()},Zm=async(e,t)=>{if(!(C(e)||C(t))){try{await(0,gn.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}Sn(e)}},Sn=e=>{e.readable&&e.destroy()};var Bc=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:s}]of Object.entries(t)){for(let{stream:a}of i.filter(({type:c})=>R.has(c)))Jm(e,a,s,o);for(let{stream:a}of i.filter(({type:c})=>!R.has(c)))eh({subprocess:e,stream:a,direction:s,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let s=i.length===1?i[0]:ne(i);Ee(s,o)}},Jm=(e,t,r,n)=>{r==="output"?Ee(e.stdio[n],t):Ee(t,e.stdio[n]);let o=Qm[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},Qm=["stdin","stdout","stderr"],eh=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;th(t,i);let[s,a]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],c=o.get(s)??[];o.set(s,[...c,a])},th=(e,{signal:t})=>{C(e)&&Q(e,rh,t)},rh=2;var $c=require("node:events");var oe=[];oe.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&oe.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&oe.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var Ht=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",bn=Symbol.for("signal-exit emitter"),wn=globalThis,nh=Object.defineProperty.bind(Object),xn=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(wn[bn])return wn[bn];nh(wn,bn,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},Kt=class{},oh=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),En=class extends Kt{onExit(){return()=>{}}load(){}unload(){}},Tn=class extends Kt{#t=On.platform==="win32"?"SIGINT":"SIGHUP";#r=new xn;#e;#n;#i;#o={};#s=!1;constructor(t){super(),this.#e=t,this.#o={};for(let r of oe)this.#o[r]=()=>{let n=this.#e.listeners(r),{count:o}=this.#r,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let s=this.#r.emit("exit",null,r),a=r==="SIGHUP"?this.#t:r;s||t.kill(t.pid,a)}};this.#i=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!Ht(this.#e))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#r.on(n,t),()=>{this.#r.removeListener(n,t),this.#r.listeners.exit.length===0&&this.#r.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#r.count+=1;for(let t of oe)try{let r=this.#o[t];r&&this.#e.on(t,r)}catch{}this.#e.emit=(t,...r)=>this.#c(t,...r),this.#e.reallyExit=t=>this.#a(t)}}unload(){this.#s&&(this.#s=!1,oe.forEach(t=>{let r=this.#o[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,r)}catch{}}),this.#e.emit=this.#n,this.#e.reallyExit=this.#i,this.#r.count-=1)}#a(t){return Ht(this.#e)?(this.#e.exitCode=t||0,this.#r.emit("exit",this.#e.exitCode,null),this.#i.call(this.#e,this.#e.exitCode)):0}#c(t,...r){let n=this.#n;if(t==="exit"&&Ht(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let o=n.call(this.#e,t,...r);return this.#r.emit("exit",this.#e.exitCode,null),o}else return n.call(this.#e,t,...r)}},On=globalThis.process,{onExit:Fc,load:YE,unload:qE}=oh(Ht(On)?new Tn(On):new En);var Uc=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=Fc(()=>{e.kill()});(0,$c.addAbortListener)(n,()=>{o()})};var _c=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=tt(),{destination:s,destinationStream:a,destinationError:c,from:f,unpipeSignal:l}=ih(r,n,o),{sourceStream:d,sourceError:p}=ah(e,f),{options:u,fileDescriptors:m}=$.get(e);return{sourcePromise:t,sourceStream:d,sourceOptions:u,sourceError:p,destination:s,destinationStream:a,destinationError:c,unpipeSignal:l,fileDescriptors:m,startTime:i}},ih=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:s}={}}=sh(e,t,...r),a=dt(n,i);return{destination:n,destinationStream:a,from:o,unpipeSignal:s}}catch(n){return{destinationError:n}}},sh=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(vc,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||ir(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,s]=We(r,...n);return{destination:t(vc)(o,i,s),pipeOptions:s}}if($.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},vc=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),ah=(e,t)=>{try{return{sourceStream:me(e,t)}}catch(r){return{sourceError:r}}};var jc=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:s})=>{let a=ch({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(a!==void 0)throw An({error:a,fileDescriptors:o,sourceOptions:i,startTime:s})},ch=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return Sn(e),n;if(t!==void 0)return yn(r),t},An=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>ye({error:e,command:Nc,escapedCommand:Nc,fileDescriptors:t,options:r,startTime:n,isSync:!1}),Nc="source.pipe(destination)";var Gc=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:s=i}]=await e;if(s.pipedFrom.includes(n)||s.pipedFrom.push(n),o==="rejected")throw s;if(t==="rejected")throw n;return s};var kc=require("node:stream/promises");var zc=(e,t,r)=>{let n=Xt.has(t)?fh(e,t):lh(e,t);return Q(e,ph,r.signal),Q(t,uh,r.signal),dh(t),n},lh=(e,t)=>{let r=ne([e]);return Ee(r,t),Xt.set(t,r),r},fh=(e,t)=>{let r=Xt.get(t);return r.add(e),r},dh=async e=>{try{await(0,kc.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}Xt.delete(e)},Xt=new WeakMap,ph=2,uh=1;var Wc=require("node:util");var Vc=(e,t)=>e===void 0?[]:[mh(e,t)],mh=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,Wc.aborted)(e,t),await r.remove(t);let s=new Error("Pipe canceled by `unpipeSignal` option.");throw An({error:s,fileDescriptors:n,sourceOptions:o,startTime:i})};var Zt=(e,...t)=>{if(S(t[0]))return Zt.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=_c(e,...t),o=hh({...n,destination:r});return o.pipe=Zt.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},hh=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:s,unpipeSignal:a,fileDescriptors:c,startTime:f})=>{let l=gh(e,o);jc({sourceStream:t,sourceError:n,destinationStream:i,destinationError:s,fileDescriptors:c,sourceOptions:r,startTime:f});let d=new AbortController;try{let p=zc(t,i,d);return await Promise.race([Gc(l),...Vc(a,{sourceStream:t,mergedStream:p,sourceOptions:r,fileDescriptors:c,startTime:f})])}finally{d.abort()}},gh=(e,t)=>Promise.allSettled([e,t]);var Xc=require("node:timers/promises");var qc=require("node:events"),Hc=require("node:stream");var Jt=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let s=new AbortController;return yh(t,s),Kc({stream:e,controller:s,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},yh=async(e,t)=>{try{await e}catch{}finally{t.abort()}},Dn=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let s=new AbortController;Sh(t,s,e);let a=e.readableObjectMode&&!i;return Kc({stream:e,controller:s,binary:n==="buffer",shouldEncode:!a,encoding:n,shouldSplit:!a&&r,preserveNewlines:!o})},Sh=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},Kc=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})=>{let a=(0,qc.on)(e,"data",{signal:t.signal,highWaterMark:Yc,highWatermark:Yc});return bh({onStdoutChunk:a,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})},In=(0,Hc.getDefaultHighWaterMark)(!0),Yc=In,bh=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s}){let a=wh({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s});try{for await(let[c]of e)yield*re(c,a,0)}catch(c){if(!t.signal.aborted)throw c}finally{yield*$e(a)}},wh=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[Nt(e,r,!t),_t(e,o,!n,{})].filter(Boolean);var Zc=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:s,allMixed:a,stripFinalNewline:c,verboseInfo:f,streamInfo:l})=>{let d=xh({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:a,verboseInfo:f,streamInfo:l});if(!o){await Promise.all([Eh(e),d]);return}let p=tn(c,r),u=Dn({stream:e,onStreamEnd:t,lines:s,encoding:n,stripFinalNewline:p,allMixed:a}),[m]=await Promise.all([Th({stream:e,iterable:u,fdNumber:r,encoding:n,maxBuffer:i,lines:s}),d]);return m},xh=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:s}})=>{if(!kt({stdioItems:s[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let a=Dn({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await sc(a,e,r,i)},Eh=async e=>{await(0,Xc.setImmediate)(),e.readableFlowing===null&&e.resume()},Th=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:s})=>{try{return t||s?await It(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await Rt(r,{maxBuffer:i})):await Mt(r,{maxBuffer:i})}catch(a){return Jc(na({error:a,stream:e,readableObjectMode:t,lines:s,encoding:o,fdNumber:n}))}},Rn=async e=>{try{return await e}catch(t){return Jc(t)}},Jc=({bufferedData:e})=>Xn(e)?new Uint8Array(e):e;var el=require("node:stream/promises"),Ge=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=Oh(e,r),s=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,el.finished)(e,{cleanup:!0,signal:s.signal})])}catch(a){i.stdinCleanedUp||Ih(a,t,r,n)}finally{s.abort()}},Oh=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&Ah(e,r,n),n},Ah=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{Dh(t,r),n.call(e,...o)}},Dh=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},Ih=(e,t,r,n)=>{if(!Rh(e,t,r,n))throw e},Rh=(e,t,r,n=!0)=>r.propagating?Qc(e)||Qt(e):(r.propagating=!0,Cn(r,t)===n?Qc(e):Qt(e)),Cn=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",Qt=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",Qc=e=>e?.code==="EPIPE";var tl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>e.stdio.map((c,f)=>Mn({stream:c,fdNumber:f,encoding:t,buffer:r[f],maxBuffer:n[f],lines:o[f],allMixed:!1,stripFinalNewline:i,verboseInfo:s,streamInfo:a})),Mn=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:c,streamInfo:f})=>{if(!e)return;let l=Ge(e,t,f);if(Cn(f,t)){await l;return}let[d]=await Promise.all([Zc({stream:e,onStreamEnd:l,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:c,streamInfo:f}),l]);return d};var rl=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?ne([e,t].filter(Boolean)):void 0,nl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>Mn({...Ch(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:Mh(e),stripFinalNewline:i,verboseInfo:s,streamInfo:a}),Ch=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},Mh=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var cl=require("node:events");var ol=e=>ce(e,"ipc"),il=(e,t)=>{let r=et(e);B({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var sl=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let s=ol(i),a=G(t,"ipc"),c=G(r,"ipc");for await(let f of fn({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))a&&(oa(e,o,c),o.push(f)),s&&il(f,i);return o},al=async(e,t)=>(await Promise.allSettled([e]),t);var ll=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:c,stripFinalNewline:f,ipc:l,ipcInput:d},context:p,verboseInfo:u,fileDescriptors:m,originalStreams:g,onInternalError:b,controller:y})=>{let x=dc(e,p),T={originalStreams:g,fileDescriptors:m,subprocess:e,exitPromise:x,propagating:!1},D=tl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:f,verboseInfo:u,streamInfo:T}),N=nl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:f,verboseInfo:u,streamInfo:T}),W=[],J=sl({subprocess:e,buffer:r,maxBuffer:n,ipc:l,ipcOutput:W,verboseInfo:u}),ie=Ph(g,e,T),V=Lh(m,T);try{return await Promise.race([Promise.all([{},uc(x),Promise.all(D),N,J,Ps(e,d),...ie,...V]),b,Bh(e,y),...Ds(e,i,p,y),...Ni({subprocess:e,cancelSignal:s,gracefulCancel:a,context:p,controller:y}),...Ts({subprocess:e,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:c,context:p,controller:y})])}catch(or){return p.terminationReason??="other",Promise.all([{error:or},x,Promise.all(D.map(Oe=>Rn(Oe))),Rn(N),al(J,W),Promise.allSettled(ie),Promise.allSettled(V)])}},Ph=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:Ge(n,o,r)),Lh=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>L(i,{checkOpen:!1})&&!C(i)).map(({type:o,value:i,stream:s=i})=>Ge(s,n,t,{isSameDirection:R.has(o),stopOnExit:o==="native"}))),Bh=async(e,{signal:t})=>{let[r]=await(0,cl.once)(e,"error",{signal:t});throw r};var fl=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),ke=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=F();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},Te=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var pl=require("node:stream"),ul=require("node:util");var Pn=require("node:stream/promises");var Ln=async e=>{if(e!==void 0)try{await Bn(e)}catch{}},dl=async e=>{if(e!==void 0)try{await Fn(e)}catch{}},Bn=async e=>{await(0,Pn.finished)(e,{cleanup:!0,readable:!1,writable:!0})},Fn=async e=>{await(0,Pn.finished)(e,{cleanup:!0,readable:!0,writable:!1})},er=async(e,t)=>{if(await e,t)throw t},tr=(e,t,r)=>{r&&!Qt(r)?e.destroy(r):t&&e.destroy()};var ml=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let s=o||A.has(r),{subprocessStdout:a,waitReadableDestroy:c}=$n(e,n,t),{readableEncoding:f,readableObjectMode:l,readableHighWaterMark:d}=Un(a,s),{read:p,onStdoutDataDone:u}=vn({subprocessStdout:a,subprocess:e,binary:s,encoding:r,preserveNewlines:i}),m=new pl.Readable({read:p,destroy:(0,ul.callbackify)(Nn.bind(void 0,{subprocessStdout:a,subprocess:e,waitReadableDestroy:c})),highWaterMark:d,objectMode:l,encoding:f});return _n({subprocessStdout:a,onStdoutDataDone:u,readable:m,subprocess:e}),m},$n=(e,t,r)=>{let n=me(e,t),o=ke(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},Un=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:In},vn=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=F(),s=Jt({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){Fh(this,s,i)},onStdoutDataDone:i}},Fh=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},_n=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await Fn(e),await n,await Ln(o),await t,r.readable&&r.push(null)}catch(i){await Ln(o),hl(r,i)}},Nn=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await Te(r,t)&&(hl(e,n),await er(t,n))},hl=(e,t)=>{tr(e,e.readable,t)};var gl=require("node:stream"),jn=require("node:util");var yl=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Gn(e,r,t),s=new gl.Writable({...kn(n,e,o),destroy:(0,jn.callbackify)(Wn.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return zn(n,s),s},Gn=(e,t,r)=>{let n=dt(e,t),o=ke(r,n,"writableFinal"),i=ke(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},kn=(e,t,r)=>({write:$h.bind(void 0,e),final:(0,jn.callbackify)(Uh.bind(void 0,e,t,r))}),$h=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},Uh=async(e,t,r)=>{await Te(r,t)&&(e.writable&&e.end(),await t)},zn=async(e,t,r)=>{try{await Bn(e),t.writable&&t.end()}catch(n){await dl(r),Sl(t,n)}},Wn=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await Te(r,t),await Te(n,t)&&(Sl(e,o),await er(t,o))},Sl=(e,t)=>{tr(e,e.writable,t)};var bl=require("node:stream"),wl=require("node:util");var xl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:s=!0}={})=>{let a=i||A.has(r),{subprocessStdout:c,waitReadableDestroy:f}=$n(e,n,t),{subprocessStdin:l,waitWritableFinal:d,waitWritableDestroy:p}=Gn(e,o,t),{readableEncoding:u,readableObjectMode:m,readableHighWaterMark:g}=Un(c,a),{read:b,onStdoutDataDone:y}=vn({subprocessStdout:c,subprocess:e,binary:a,encoding:r,preserveNewlines:s}),x=new bl.Duplex({read:b,...kn(l,e,d),destroy:(0,wl.callbackify)(vh.bind(void 0,{subprocessStdout:c,subprocessStdin:l,subprocess:e,waitReadableDestroy:f,waitWritableFinal:d,waitWritableDestroy:p})),readableHighWaterMark:g,writableHighWaterMark:l.writableHighWaterMark,readableObjectMode:m,writableObjectMode:l.writableObjectMode,encoding:u});return _n({subprocessStdout:c,onStdoutDataDone:y,readable:x,subprocess:e,subprocessStdin:l}),zn(l,x,c),x},vh=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},s)=>{await Promise.all([Nn({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},s),Wn({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},s)])};var Vn=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||A.has(t),s=me(e,r),a=Jt({subprocessStdout:s,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return _h(a,s,e)},_h=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var El=(e,{encoding:t})=>{let r=fl();e.readable=ml.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=yl.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=xl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=Vn.bind(void 0,e,t),e[Symbol.asyncIterator]=Vn.bind(void 0,e,t,{})};var Tl=(e,t)=>{for(let[r,n]of jh){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},Nh=(async()=>{})().constructor.prototype,jh=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(Nh,e)]);var Dl=(e,t,r,n)=>{let{file:o,commandArguments:i,command:s,escapedCommand:a,startTime:c,verboseInfo:f,options:l,fileDescriptors:d}=Gh(e,t,r),{subprocess:p,promise:u}=zh({file:o,commandArguments:i,options:l,startTime:c,verboseInfo:f,command:s,escapedCommand:a,fileDescriptors:d});return p.pipe=Zt.bind(void 0,{source:p,sourcePromise:u,boundOptions:{},createNested:n}),Tl(p,u),$.set(p,{options:l,fileDescriptors:d}),p},Gh=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=rt(e,t,r),{file:a,commandArguments:c,options:f}=Tt(e,t,r),l=kh(f),d=Ic(l,s);return{file:a,commandArguments:c,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:d}},kh=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},zh=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:s,fileDescriptors:a})=>{let c;try{c=(0,Al.spawn)(e,t,r)}catch(m){return Oc({error:m,command:i,escapedCommand:s,fileDescriptors:a,options:r,startTime:n,verboseInfo:o})}let f=new AbortController;(0,Ol.setMaxListeners)(Number.POSITIVE_INFINITY,f.signal);let l=[...c.stdio];Bc(c,a,f),Uc(c,r,f);let d={},p=F();c.kill=Ui.bind(void 0,{kill:c.kill.bind(c),options:r,onInternalError:p,context:d,controller:f}),c.all=rl(c,r),El(c,r),wc(c,r);let u=Wh({subprocess:c,options:r,startTime:n,verboseInfo:o,fileDescriptors:a,originalStreams:l,command:i,escapedCommand:s,context:d,onInternalError:p,controller:f});return{subprocess:c,promise:u}},Wh=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:s,escapedCommand:a,context:c,onInternalError:f,controller:l})=>{let[d,[p,u],m,g,b]=await ll({subprocess:e,options:t,context:c,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:f,controller:l});l.abort(),f.resolve();let y=m.map((D,N)=>z(D,t,N)),x=z(g,t,"all"),T=Vh({errorInfo:d,exitCode:p,signal:u,stdio:y,all:x,ipcOutput:b,context:c,options:t,command:s,escapedCommand:a,startTime:r});return Se(T,n,t)},Vh=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:s,options:a,command:c,escapedCommand:f,startTime:l})=>"error"in e?Fe({error:e.error,command:c,escapedCommand:f,timedOut:s.terminationReason==="timeout",isCanceled:s.terminationReason==="cancel"||s.terminationReason==="gracefulCancel",isGracefullyCanceled:s.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof U,isForcefullyTerminated:s.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:a,startTime:l,isSync:!1}):Lt({command:c,escapedCommand:f,stdio:n,all:o,ipcOutput:i,options:a,startTime:l});var rr=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,Yh(n,e[n],o)]));return{...e,...r}},Yh=(e,t,r)=>qh.has(e)&&S(t)&&S(r)?{...t,...r}:r,qh=new Set(["env",...fr]);var Z=(e,t,r,n)=>{let o=(s,a,c)=>Z(s,a,r,c),i=(...s)=>Hh({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...s);return n!==void 0&&n(i,o,t),i},Hh=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...s)=>{if(S(i))return o(e,rr(r,i),n);let{file:a,commandArguments:c,options:f,isSync:l}=Kh({mapArguments:e,firstArgument:i,nextArguments:s,deepOptions:t,boundOptions:r});return l?gc(a,c,f):Dl(a,c,f,o)},Kh=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=no(t)?oo(t,r):[t,...r],[s,a,c]=We(...i),f=rr(rr(n,o),c),{file:l=s,commandArguments:d=a,options:p=f,isSync:u=!1}=e({file:s,commandArguments:a,options:f});return{file:l,commandArguments:d,options:p,isSync:u}};var Il=({file:e,commandArguments:t})=>Cl(e,t),Rl=({file:e,commandArguments:t})=>({...Cl(e,t),isSync:!0}),Cl=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=Xh(e);return{file:r,commandArguments:n}},Xh=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(Zh)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},Zh=/ +/g;var Ml=(e,t,r)=>{e.sync=t(Jh,r),e.s=e.sync},Pl=({options:e})=>Ll(e),Jh=({options:e})=>({...Ll(e),isSync:!0}),Ll=e=>({options:{...Qh(e),...e}}),Qh=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Bl={preferLocal:!0};var Fl=Z(()=>({})),SA=Z(()=>({isSync:!0})),bA=Z(Il),wA=Z(Rl),xA=Z(Is),EA=Z(Pl,{},Bl,Ml),{sendMessage:TA,getOneMessage:OA,getEachMessage:AA,getCancelSignal:DA}=xc();async function eg(e){if(e=nr.default.resolve(e),e.endsWith("package.json")&&(e=e.replace(/package\.json$/,"")),!$l.default.existsSync(e))throw`Directory ${e} does not exist`;let t=nr.default.join(Ul.tmpdir(),nr.default.basename(e));try{await Fl("ray",["build","--output",t],{cwd:e})}catch{throw`Error building extension ${e}`}return t}0&&(module.exports={buildExtension});
