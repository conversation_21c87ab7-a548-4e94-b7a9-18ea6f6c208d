"use strict";var xf=Object.create;var mt=Object.defineProperty;var Af=Object.getOwnPropertyDescriptor;var Bf=Object.getOwnPropertyNames;var Tf=Object.getPrototypeOf,_f=Object.prototype.hasOwnProperty;var B=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Of=(e,t)=>{for(var r in t)mt(e,r,{get:t[r],enumerable:!0})},Jo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Bf(t))!_f.call(e,o)&&o!==r&&mt(e,o,{get:()=>t[o],enumerable:!(n=Af(t,o))||n.enumerable});return e};var h=(e,t,r)=>(r=e!=null?xf(Tf(e)):{},Jo(t||!e||!e.__esModule?mt(r,"default",{value:e,enumerable:!0}):r,e)),Rf=e=>Jo(mt({},"__esModule",{value:!0}),e);var vi=B((uE,Mi)=>{Mi.exports=Ii;Ii.sync=wD;var Oi=require("fs");function CD(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function Ri(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:CD(t,r)}function Ii(e,t,r){Oi.stat(e,function(n,o){r(n,n?!1:Ri(o,e,t))})}function wD(e,t){return Ri(Oi.statSync(e),e,t)}});var ji=B((aE,$i)=>{$i.exports=Li;Li.sync=xD;var Pi=require("fs");function Li(e,t,r){Pi.stat(e,function(n,o){r(n,n?!1:Ni(o,t))})}function xD(e,t){return Ni(Pi.statSync(e),t)}function Ni(e,t){return e.isFile()&&AD(e,t)}function AD(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),s=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),u=parseInt("100",8),a=parseInt("010",8),l=parseInt("001",8),c=u|a,f=r&l||r&a&&o===s||r&u&&n===i||r&c&&i===0;return f}});var ki=B((lE,Ui)=>{var cE=require("fs"),_t;process.platform==="win32"||global.TESTING_WINDOWS?_t=vi():_t=ji();Ui.exports=Qr;Qr.sync=BD;function Qr(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){Qr(e,t||{},function(i,s){i?o(i):n(s)})})}_t(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function BD(e,t){try{return _t.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var Hi=B((fE,qi)=>{var ye=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Gi=require("path"),TD=ye?";":":",Wi=ki(),zi=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),Vi=(e,t)=>{let r=t.colon||TD,n=e.match(/\//)||ye&&e.match(/\\/)?[""]:[...ye?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=ye?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=ye?o.split(r):[""];return ye&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},Yi=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=Vi(e,t),s=[],u=l=>new Promise((c,f)=>{if(l===n.length)return t.all&&s.length?c(s):f(zi(e));let D=n[l],d=/^".*"$/.test(D)?D.slice(1,-1):D,p=Gi.join(d,e),F=!d&&/^\.[\\\/]/.test(e)?e.slice(0,2)+p:p;c(a(F,l,0))}),a=(l,c,f)=>new Promise((D,d)=>{if(f===o.length)return D(u(c+1));let p=o[f];Wi(l+p,{pathExt:i},(F,w)=>{if(!F&&w)if(t.all)s.push(l+p);else return D(l+p);return D(a(l,c,f+1))})});return r?u(0).then(l=>r(null,l),r):u(0)},_D=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=Vi(e,t),i=[];for(let s=0;s<r.length;s++){let u=r[s],a=/^".*"$/.test(u)?u.slice(1,-1):u,l=Gi.join(a,e),c=!a&&/^\.[\\\/]/.test(e)?e.slice(0,2)+l:l;for(let f=0;f<n.length;f++){let D=c+n[f];try{if(Wi.sync(D,{pathExt:o}))if(t.all)i.push(D);else return D}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw zi(e)};qi.exports=Yi;Yi.sync=_D});var Ji=B((DE,en)=>{"use strict";var Ki=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};en.exports=Ki;en.exports.default=Ki});var es=B((dE,Qi)=>{"use strict";var Xi=require("path"),OD=Hi(),RD=Ji();function Zi(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let s;try{s=OD.sync(e.command,{path:r[RD({env:r})],pathExt:t?Xi.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return s&&(s=Xi.resolve(o?e.options.cwd:"",s)),s}function ID(e){return Zi(e)||Zi(e,!0)}Qi.exports=ID});var ts=B((pE,rn)=>{"use strict";var tn=/([()\][%!^"`<>&|;, *?])/g;function MD(e){return e=e.replace(tn,"^$1"),e}function vD(e,t){return e=`${e}`,e=e.replace(/(\\*)"/g,'$1$1\\"'),e=e.replace(/(\\*)$/,"$1$1"),e=`"${e}"`,e=e.replace(tn,"^$1"),t&&(e=e.replace(tn,"^$1")),e}rn.exports.command=MD;rn.exports.argument=vD});var ns=B((mE,rs)=>{"use strict";rs.exports=/^#!(.*)/});var is=B((hE,os)=>{"use strict";var PD=ns();os.exports=(e="")=>{let t=e.match(PD);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var us=B((FE,ss)=>{"use strict";var nn=require("fs"),LD=is();function ND(e){let r=Buffer.alloc(150),n;try{n=nn.openSync(e,"r"),nn.readSync(n,r,0,150,0),nn.closeSync(n)}catch{}return LD(r.toString())}ss.exports=ND});var fs=B((gE,ls)=>{"use strict";var $D=require("path"),as=es(),cs=ts(),jD=us(),UD=process.platform==="win32",kD=/\.(?:com|exe)$/i,GD=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function WD(e){e.file=as(e);let t=e.file&&jD(e.file);return t?(e.args.unshift(e.file),e.command=t,as(e)):e.file}function zD(e){if(!UD)return e;let t=WD(e),r=!kD.test(t);if(e.options.forceShell||r){let n=GD.test(t);e.command=$D.normalize(e.command),e.command=cs.command(e.command),e.args=e.args.map(i=>cs.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function VD(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:zD(n)}ls.exports=VD});var ps=B((EE,ds)=>{"use strict";var on=process.platform==="win32";function sn(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function YD(e,t){if(!on)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=Ds(o,t,"spawn");if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function Ds(e,t){return on&&e===1&&!t.file?sn(t.original,"spawn"):null}function qD(e,t){return on&&e===1&&!t.file?sn(t.original,"spawnSync"):null}ds.exports={hookChildProcess:YD,verifyENOENT:Ds,verifyENOENTSync:qD,notFoundError:sn}});var Fs=B((yE,be)=>{"use strict";var ms=require("child_process"),un=fs(),an=ps();function hs(e,t,r){let n=un(e,t,r),o=ms.spawn(n.command,n.args,n.options);return an.hookChildProcess(o,n),o}function HD(e,t,r){let n=un(e,t,r),o=ms.spawnSync(n.command,n.args,n.options);return o.error=o.error||an.verifyENOENTSync(o.status,n),o}be.exports=hs;be.exports.spawn=hs;be.exports.sync=HD;be.exports._parse=un;be.exports._enoent=an});var rf=B((B1,cF)=>{cF.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var No=B((T1,of)=>{"use strict";var Lr=Object.assign({},rf()),nf=Object.keys(Lr);Object.defineProperty(Lr,"random",{get(){let e=Math.floor(Math.random()*nf.length),t=nf[e];return Lr[t]}});of.exports=Lr});var Sf=B((J1,bf)=>{bf.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var vF={};Of(vF,{getPackageJson:()=>wf});module.exports=Rf(vF);var Ur=h(require("path"));var Or=h(require("path")),Nl=h(require("fs")),$l=h(require("os"));function C(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var Xo=require("node:url"),Fe=(e,t)=>{let r=zr(If(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},If=e=>Wr(e)?e.toString():e,Wr=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,zr=e=>e instanceof URL?(0,Xo.fileURLToPath)(e):e;var ht=(e,t=[],r={})=>{let n=Fe(e,"First argument"),[o,i]=C(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(a=>typeof a=="object"&&a!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let s=o.map(String),u=s.find(a=>a.includes("\0"));if(u!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${u}`);if(!C(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,s,i]};var si=require("node:child_process");var Zo=require("node:string_decoder"),{toString:Qo}=Object.prototype,ei=e=>Qo.call(e)==="[object ArrayBuffer]",x=e=>Qo.call(e)==="[object Uint8Array]",J=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),Mf=new TextEncoder,ti=e=>Mf.encode(e),vf=new TextDecoder,Ft=e=>vf.decode(e),ri=(e,t)=>Pf(e,t).join(""),Pf=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new Zo.StringDecoder(t),n=e.map(i=>typeof i=="string"?ti(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},ke=e=>e.length===1&&x(e[0])?e[0]:Vr(Lf(e)),Lf=e=>e.map(t=>typeof t=="string"?ti(t):t),Vr=e=>{let t=new Uint8Array(Nf(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},Nf=e=>{let t=0;for(let r of e)t+=r.length;return t};var ui=e=>Array.isArray(e)&&Array.isArray(e.raw),ai=(e,t)=>{let r=[];for(let[i,s]of e.entries())r=$f({templates:e,expressions:t,tokens:r,index:i,template:s});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},$f=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:s,trailingWhitespaces:u}=jf(o,e.raw[n]),a=oi(r,i,s);if(n===t.length)return a;let l=t[n],c=Array.isArray(l)?l.map(f=>ii(f)):[ii(l)];return oi(a,c,u)},jf=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=ni.has(t[0]);for(let s=0,u=0;s<e.length;s+=1,u+=1){let a=t[u];if(ni.has(a))n!==s&&r.push(e.slice(n,s)),n=s+1;else if(a==="\\"){let l=t[u+1];l==="u"&&t[u+2]==="{"?u=t.indexOf("}",u+3):u+=Uf[l]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},ni=new Set([" ","	","\r",`
`]),Uf={x:3,u:5},oi=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],ii=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(C(e)&&("stdout"in e||"isMaxBuffer"in e))return kf(e);throw e instanceof si.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},kf=({stdout:e})=>{if(typeof e=="string")return e;if(x(e))return Ft(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var gc=require("node:child_process");var li=require("node:util");var gt=h(require("node:process"),1),L=e=>Et.includes(e),Et=[gt.default.stdin,gt.default.stdout,gt.default.stderr],R=["stdin","stdout","stderr"],yt=e=>R[e]??`stdio[${e}]`;var fi=e=>{let t={...e};for(let r of Hr)t[r]=Yr(e,r);return t},Yr=(e,t)=>{let r=Array.from({length:Gf(e)+1}),n=Wf(e[t],r,t);return Hf(n,t)},Gf=({stdio:e})=>Array.isArray(e)?Math.max(e.length,R.length):R.length,Wf=(e,t,r)=>C(e)?zf(e,t,r):t.fill(e),zf=(e,t,r)=>{for(let n of Object.keys(e).sort(Vf))for(let o of Yf(n,r,t))t[o]=e[n];return t},Vf=(e,t)=>ci(e)<ci(t)?1:-1,ci=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,Yf=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=qr(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},qr=e=>{if(e==="all")return e;if(R.includes(e))return R.indexOf(e);let t=qf.exec(e);if(t!==null)return Number(t[1])},qf=/^fd(\d+)$/,Hf=(e,t)=>e.map(r=>r===void 0?Jf[t]:r),Kf=(0,li.debuglog)("execa").enabled?"full":"none",Jf={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:Kf,stripFinalNewline:!0},Hr=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],X=(e,t)=>t==="ipc"?e.at(-1):e[t];var ge=({verbose:e},t)=>Kr(e,t)!=="none",Ee=({verbose:e},t)=>!["none","short"].includes(Kr(e,t)),Di=({verbose:e},t)=>{let r=Kr(e,t);return bt(r)?r:void 0},Kr=(e,t)=>t===void 0?Xf(e):X(e,t),Xf=e=>e.find(t=>bt(t))??St.findLast(t=>e.includes(t)),bt=e=>typeof e=="function",St=["none","short","full"];var Bi=require("node:util");var di=require("node:process"),pi=require("node:util"),mi=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>nD(hi(i))).join(" ");return{command:n,escapedCommand:o}},Ge=e=>(0,pi.stripVTControlCharacters)(e).split(`
`).map(t=>hi(t)).join(`
`),hi=e=>e.replaceAll(eD,t=>Zf(t)),Zf=e=>{let t=tD[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=rD?`\\u${n.padStart(4,"0")}`:`\\U${n}`},Qf=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},eD=Qf(),tD={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},rD=65535,nD=e=>oD.test(e)?e:di.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,oD=/^[\w./-]+$/;var Jr=h(require("node:process"),1);function We(){let{env:e}=Jr.default,{TERM:t,TERM_PROGRAM:r}=e;return Jr.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Fi={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},gi={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},iD={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},sD={...Fi,...gi},uD={...Fi,...iD},aD=We(),cD=aD?sD:uD,Ct=cD,eg=Object.entries(gi);var Ei=h(require("node:tty"),1),lD=Ei.default?.WriteStream?.prototype?.hasColors?.()??!1,m=(e,t)=>{if(!lD)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let u=r,a=0;for(;s!==-1;)u+=i.slice(a,s)+r,a=s+n.length,s=i.indexOf(n,a);return u+=i.slice(a)+n,u}},rg=m(0,0),yi=m(1,22),ng=m(2,22),og=m(3,23),ig=m(4,24),sg=m(53,55),ug=m(7,27),ag=m(8,28),cg=m(9,29),lg=m(30,39),fg=m(31,39),Dg=m(32,39),dg=m(33,39),pg=m(34,39),mg=m(35,39),hg=m(36,39),Fg=m(37,39),wt=m(90,39),gg=m(40,49),Eg=m(41,49),yg=m(42,49),bg=m(43,49),Sg=m(44,49),Cg=m(45,49),wg=m(46,49),xg=m(47,49),Ag=m(100,49),bi=m(91,39),Bg=m(92,39),Si=m(93,39),Tg=m(94,39),_g=m(95,39),Og=m(96,39),Rg=m(97,39),Ig=m(101,49),Mg=m(102,49),vg=m(103,49),Pg=m(104,49),Lg=m(105,49),Ng=m(106,49),$g=m(107,49);var xi=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:s=!0}})=>{let u=fD(r),a=DD[e]({failed:i,reject:s,piped:n}),l=dD[e]({reject:s});return`${wt(`[${u}]`)} ${wt(`[${o}]`)} ${l(a)} ${l(t)}`},fD=e=>`${xt(e.getHours(),2)}:${xt(e.getMinutes(),2)}:${xt(e.getSeconds(),2)}.${xt(e.getMilliseconds(),3)}`,xt=(e,t)=>String(e).padStart(t,"0"),Ci=({failed:e,reject:t})=>e?t?Ct.cross:Ct.warning:Ct.tick,DD={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:Ci,duration:Ci},wi=e=>e,dD={command:()=>yi,output:()=>wi,ipc:()=>wi,error:({reject:e})=>e?bi:Si,duration:()=>wt};var Ai=(e,t,r)=>{let n=Di(t,r);return e.map(({verboseLine:o,verboseObject:i})=>pD(o,i,n)).filter(o=>o!==void 0).map(o=>mD(o)).join("")},pD=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},mD=e=>e.endsWith(`
`)?e:`${e}
`;var W=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=hD({type:e,result:o,verboseInfo:n}),s=FD(t,i),u=Ai(s,n,r);u!==""&&console.warn(u.slice(0,-1))},hD=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),FD=(e,t)=>e.split(`
`).map(r=>gD({...t,message:r})),gD=e=>({verboseLine:xi(e),verboseObject:e}),At=e=>{let t=typeof e=="string"?e:(0,Bi.inspect)(e);return Ge(t).replaceAll("	"," ".repeat(ED))},ED=2;var Ti=(e,t)=>{ge(t)&&W({type:"command",verboseMessage:e,verboseInfo:t})};var _i=(e,t,r)=>{SD(e);let n=yD(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},yD=e=>ge({verbose:e})?bD++:void 0,bD=0n,SD=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!St.includes(t)&&!bt(t)){let r=St.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var Xr=require("node:process"),Bt=()=>Xr.hrtime.bigint(),Zr=e=>Number(Xr.hrtime.bigint()-e)/1e6;var Tt=(e,t,r)=>{let n=Bt(),{command:o,escapedCommand:i}=mi(e,t),s=Yr(r,"verbose"),u=_i(s,i,{...r});return Ti(i,u),{command:o,escapedCommand:i,startTime:n,verboseInfo:u}};var Wu=h(require("node:path"),1),Cn=h(require("node:process"),1),zu=h(Fs(),1);var ze=h(require("node:process"),1),ne=h(require("node:path"),1);function Ot(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var gs=require("node:util"),ln=require("node:child_process"),cn=h(require("node:path"),1),Es=require("node:url"),SE=(0,gs.promisify)(ln.execFile);function Rt(e){return e instanceof URL?(0,Es.fileURLToPath)(e):e}function ys(e){return{*[Symbol.iterator](){let t=cn.default.resolve(Rt(e)),r;for(;r!==t;)yield t,r=t,t=cn.default.resolve(t,"..")}}}var CE=10*1024*1024;var KD=({cwd:e=ze.default.cwd(),path:t=ze.default.env[Ot()],preferLocal:r=!0,execPath:n=ze.default.execPath,addExecPath:o=!0}={})=>{let i=ne.default.resolve(Rt(e)),s=[],u=t.split(ne.default.delimiter);return r&&JD(s,u,i),o&&XD(s,u,n,i),t===""||t===ne.default.delimiter?`${s.join(ne.default.delimiter)}${t}`:[...s,t].join(ne.default.delimiter)},JD=(e,t,r)=>{for(let n of ys(r)){let o=ne.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},XD=(e,t,r,n)=>{let o=ne.default.resolve(n,Rt(r),"..");t.includes(o)||e.push(o)},bs=({env:e=ze.default.env,...t}={})=>{e={...e};let r=Ot({env:e});return t.path=e[r],e[r]=KD(t),e};var Ns=require("node:timers/promises");var Ss=(e,t,r)=>{let n=r?Ye:Ve,o=e instanceof N?{}:{cause:e};return new n(t,o)},N=class extends Error{},Cs=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,xs,{value:!0,writable:!1,enumerable:!1,configurable:!1})},ws=e=>It(e)&&xs in e,xs=Symbol("isExecaError"),It=e=>Object.prototype.toString.call(e)==="[object Error]",Ve=class extends Error{};Cs(Ve,Ve.name);var Ye=class extends Error{};Cs(Ye,Ye.name);var Se=require("node:os");var Rs=require("node:os");var As=()=>{let e=Ts-Bs+1;return Array.from({length:e},ZD)},ZD=(e,t)=>({name:`SIGRT${t+1}`,number:Bs+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),Bs=34,Ts=64;var Os=require("node:os");var _s=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var fn=()=>{let e=As();return[..._s,...e].map(QD)},QD=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:s}}=Os.constants,u=s!==void 0;return{name:e,number:u?s:t,description:r,supported:u,action:n,forced:o,standard:i}};var ed=()=>{let e=fn();return Object.fromEntries(e.map(td))},td=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s}],Is=ed(),rd=()=>{let e=fn(),t=65,r=Array.from({length:t},(n,o)=>nd(o,e));return Object.assign({},...r)},nd=(e,t)=>{let r=od(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:s,forced:u,standard:a}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:s,forced:u,standard:a}}},od=(e,t)=>{let r=t.find(({name:n})=>Rs.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},LE=rd();var vs=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return Ls(e,t)},Ps=e=>e===0?e:Ls(e,"`subprocess.kill()`'s argument"),Ls=(e,t)=>{if(Number.isInteger(e))return id(e,t);if(typeof e=="string")return ud(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${Dn()}`)},id=(e,t)=>{if(Ms.has(e))return Ms.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${Dn()}`)},sd=()=>new Map(Object.entries(Se.constants.signals).reverse().map(([e,t])=>[t,e])),Ms=sd(),ud=(e,t)=>{if(e in Se.constants.signals)return e;throw e.toUpperCase()in Se.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${Dn()}`)},Dn=()=>`Available signal names: ${ad()}.
Available signal numbers: ${cd()}.`,ad=()=>Object.keys(Se.constants.signals).sort().map(e=>`'${e}'`).join(", "),cd=()=>[...new Set(Object.values(Se.constants.signals).sort((e,t)=>e-t))].join(", "),Mt=e=>Is[e].description;var $s=e=>{if(e===!1)return e;if(e===!0)return ld;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},ld=1e3*5,js=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},s,u)=>{let{signal:a,error:l}=fd(s,u,r);Dd(l,n);let c=e(a);return dd({kill:e,signal:a,forceKillAfterDelay:t,killSignal:r,killResult:c,context:o,controller:i}),c},fd=(e,t,r)=>{let[n=r,o]=It(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!It(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Ps(n),error:o}},Dd=(e,t)=>{e!==void 0&&t.reject(e)},dd=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:s})=>{t===n&&o&&dn({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:s.signal})},dn=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,Ns.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var Us=require("node:events"),vt=async(e,t)=>{e.aborted||await(0,Us.once)(e,"abort",{signal:t})};var ks=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},Gs=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[pd(e,t,n,o)],pd=async(e,t,r,{signal:n})=>{throw await vt(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var bu=require("node:timers/promises");var Eu=require("node:util");var Ce=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{md(e,t,r),pn(e,t,n)},md=(e,t,r)=>{if(!r)throw new Error(`${$(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},pn=(e,t,r)=>{if(!r)throw new Error(`${$(e,t)} cannot be used: the ${oe(t)} has already exited or disconnected.`)},Ws=e=>{throw new Error(`${$("getOneMessage",e)} could not complete: the ${oe(e)} exited or disconnected.`)},zs=e=>{throw new Error(`${$("sendMessage",e)} failed: the ${oe(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${$("getOneMessage",e)},
	${$("sendMessage",e,"message, {strict: true}")},
]);`)},Pt=(e,t)=>new Error(`${$("sendMessage",t)} failed when sending an acknowledgment response to the ${oe(t)}.`,{cause:e}),Vs=e=>{throw new Error(`${$("sendMessage",e)} failed: the ${oe(e)} is not listening to incoming messages.`)},Ys=e=>{throw new Error(`${$("sendMessage",e)} failed: the ${oe(e)} exited without listening to incoming messages.`)},qs=()=>new Error(`\`cancelSignal\` aborted: the ${oe(!0)} disconnected.`),Hs=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},Ks=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${$(t,r)} cannot be used: the ${oe(r)} is disconnecting.`,{cause:e})},Js=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(hd(e))throw new Error(`${$(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},hd=({code:e,message:t})=>Fd.has(e)||gd.some(r=>t.includes(r)),Fd=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),gd=["could not be cloned","circular structure","call stack size exceeded"],$=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${Ed(t)}${e}(${r})`,Ed=e=>e?"":"subprocess.",oe=e=>e?"parent process":"subprocess",we=e=>{e.connected&&e.disconnect()};var z=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var Nt=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=V.get(e),i=Xs(o,t,!0),s=e.stdio[i];if(s===null)throw new TypeError(Zs(i,t,n,!0));return s},xe=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=V.get(e),i=Xs(o,t,!1),s=i==="all"?e.all:e.stdio[i];if(s==null)throw new TypeError(Zs(i,t,n,!1));return s},V=new WeakMap,Xs=(e,t,r)=>{let n=yd(t,r);return bd(n,t,r,e),n},yd=(e,t)=>{let r=qr(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${qe(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},bd=(e,t,r,n)=>{let o=n[Qs(e)];if(o===void 0)throw new TypeError(`"${qe(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${qe(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${qe(r)}" must not be ${t}. It must be a writable stream, not readable.`)},Zs=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=Sd(e,r);return`The "${o}: ${Lt(i)}" option is incompatible with using "${qe(n)}: ${Lt(t)}".
Please set this option with "pipe" instead.`},Sd=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=Qs(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},Qs=e=>e==="all"?1:e,qe=e=>e?"to":"from",Lt=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var Du=require("node:events");var eu=require("node:events"),le=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,eu.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var fu=require("node:events");var nu=require("node:events"),ou=require("node:timers/promises");var $t=(e,t)=>{t&&mn(e)},mn=e=>{e.refCounted()},jt=(e,t)=>{t&&hn(e)},hn=e=>{e.unrefCounted()},tu=(e,t)=>{t&&(hn(e),hn(e))},ru=(e,t)=>{t&&(mn(e),mn(e))};var iu=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(au(o)||lu(o))return;Ut.has(e)||Ut.set(e,[]);let i=Ut.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await cu(e,n,o),await ou.scheduler.yield();let s=await uu({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",s),n.emit("message:done")}},su=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{Fn();let i=Ut.get(e);for(;i?.length>0;)await(0,nu.once)(n,"message:done");e.removeListener("message",o),ru(t,r),n.connected=!1,n.emit("disconnect")},Ut=new WeakMap;var ie=(e,t,r)=>{if(kt.has(e))return kt.get(e);let n=new fu.EventEmitter;return n.connected=!0,kt.set(e,n),Cd({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},kt=new WeakMap,Cd=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=iu.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",su.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),tu(r,n)},Gt=e=>{let t=kt.get(e);return t===void 0?e.channel!==null:t.connected};var du=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=ie(e,t,r),s=Vt(e,i);return{id:wd++,type:zt,message:n,hasListeners:s}},wd=0n,pu=(e,t)=>{if(!(t?.type!==zt||t.hasListeners))for(let{id:r}of e)r!==void 0&&Wt[r].resolve({isDeadlock:!0,hasListeners:!1})},uu=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==zt||!t.connected)return e;let{id:i,message:s}=e,u={id:i,type:hu,message:Vt(t,o)};try{await Yt({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},u)}catch(a){o.emit("strict:error",a)}return s},au=e=>{if(e?.type!==hu)return!1;let{id:t,message:r}=e;return Wt[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},mu=async(e,t,r)=>{if(e?.type!==zt)return;let n=z();Wt[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:s}=await Promise.race([n,xd(t,r,o)]);i&&zs(r),s||Vs(r)}finally{o.abort(),delete Wt[e.id]}},Wt={},xd=async(e,t,{signal:r})=>{le(e,1,r),await(0,Du.once)(e,"disconnect",{signal:r}),Ys(t)},zt="execa:ipc:request",hu="execa:ipc:response";var Fu=(e,t,r)=>{He.has(e)||He.set(e,new Set);let n=He.get(e),o=z(),i=r?t.id:void 0,s={onMessageSent:o,id:i};return n.add(s),{outgoingMessages:n,outgoingMessage:s}},gu=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},cu=async(e,t,r)=>{for(;!Vt(e,t)&&He.get(e)?.size>0;){let n=[...He.get(e)];pu(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},He=new WeakMap,Vt=(e,t)=>t.listenerCount("message")>Ad(e),Ad=e=>V.has(e)&&!X(V.get(e).options.buffer,"ipc")?1:0;var Yt=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let s="sendMessage";return Ce({methodName:s,isSubprocess:r,ipc:n,isConnected:e.connected}),Bd({anyProcess:e,channel:t,methodName:s,isSubprocess:r,message:o,strict:i})},Bd=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let s=du({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),u=Fu(e,s,i);try{await En({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:s,message:o})}catch(a){throw we(e),a}finally{gu(u)}},En=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=Td(e);try{await Promise.all([mu(n,e,r),i(n)])}catch(s){throw Ks({error:s,methodName:t,isSubprocess:r}),Js({error:s,methodName:t,isSubprocess:r,message:o}),s}},Td=e=>{if(gn.has(e))return gn.get(e);let t=(0,Eu.promisify)(e.send.bind(e));return gn.set(e,t),t},gn=new WeakMap;var Su=(e,t)=>{let r="cancelSignal";return pn(r,!1,e.connected),En({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:wu,message:t},message:t})},Cu=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await _d({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),yn.signal),_d=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!yu){if(yu=!0,!n){Hs();return}if(t===null){Fn();return}ie(e,t,r),await bu.scheduler.yield()}},yu=!1,lu=e=>e?.type!==wu?!1:(yn.abort(e.message),!0),wu="execa:ipc:cancel",Fn=()=>{yn.abort(qs())},yn=new AbortController;var xu=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},Au=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[Od({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],Od=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await vt(t,o);let i=Rd(t);throw await Su(e,i),dn({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},Rd=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var Bu=require("node:timers/promises");var Tu=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},_u=(e,t,r,n)=>t===0||t===void 0?[]:[Id(e,t,r,n)],Id=async(e,t,r,{signal:n})=>{throw await(0,Bu.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new N};var qt=require("node:process"),bn=h(require("node:path"),1);var Ou=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},Ru=(e,t,{node:r=!1,nodePath:n=qt.execPath,nodeOptions:o=qt.execArgv.filter(a=>!a.startsWith("--inspect")),cwd:i,execPath:s,...u})=>{if(s!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let a=Fe(n,'The "nodePath" option'),l=bn.default.resolve(i,a),c={...u,nodePath:l,node:r,cwd:i};if(!r)return[e,t,c];if(bn.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[l,[...o,e,...t],{ipc:!0,...c,shell:!1}]};var Iu=require("node:v8"),Mu=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");Pd[r](e)}},Md=e=>{try{(0,Iu.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},vd=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},Pd={advanced:Md,json:vd},vu=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var Lu=({encoding:e})=>{if(Sn.has(e))return;let t=Nd(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${Ht(e)}\`.
Please rename it to ${Ht(t)}.`);let r=[...Sn].map(n=>Ht(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${Ht(e)}\`.
Please rename it to one of: ${r}.`)},Ld=new Set(["utf8","utf16le"]),_=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),Sn=new Set([...Ld,..._]),Nd=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Pu)return Pu[t];if(Sn.has(t))return t},Pu={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},Ht=e=>typeof e=="string"?`"${e}"`:String(e);var Nu=require("node:fs"),$u=h(require("node:path"),1),ju=h(require("node:process"),1);var Uu=(e=ku())=>{let t=Fe(e,'The "cwd" option');return $u.default.resolve(t)},ku=()=>{try{return ju.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},Gu=(e,t)=>{if(t===ku())return e;let r;try{r=(0,Nu.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var Kt=(e,t,r)=>{r.cwd=Uu(r.cwd);let[n,o,i]=Ru(e,t,r),{command:s,args:u,options:a}=zu.default._parse(n,o,i),l=fi(a),c=$d(l);return Tu(c),Lu(c),Mu(c),ks(c),xu(c),c.shell=zr(c.shell),c.env=jd(c),c.killSignal=vs(c.killSignal),c.forceKillAfterDelay=$s(c.forceKillAfterDelay),c.lines=c.lines.map((f,D)=>f&&!_.has(c.encoding)&&c.buffer[D]),Cn.default.platform==="win32"&&Wu.default.basename(s,".exe")==="cmd"&&u.unshift("/q"),{file:s,commandArguments:u,options:c}},$d=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:s=!0,all:u=!1,windowsHide:a=!0,killSignal:l="SIGTERM",forceKillAfterDelay:c=!0,gracefulCancel:f=!1,ipcInput:D,ipc:d=D!==void 0||f,serialization:p="advanced",...F})=>({...F,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:s,all:u,windowsHide:a,killSignal:l,forceKillAfterDelay:c,gracefulCancel:f,ipcInput:D,ipc:d,serialization:p}),jd=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let s=t?{...Cn.default.env,...e}:e;return r||n?bs({env:s,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):s};var Da=require("node:util");function Ae(e){if(typeof e=="string")return Ud(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return kd(e)}var Ud=e=>e.at(-1)===Vu?e.slice(0,e.at(-2)===Yu?-2:-1):e,kd=e=>e.at(-1)===Gd?e.subarray(0,e.at(-2)===Wd?-2:-1):e,Vu=`
`,Gd=Vu.codePointAt(0),Yu="\r",Wd=Yu.codePointAt(0);var oa=require("node:events"),ia=require("node:stream/promises");function j(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function wn(e,{checkOpen:t=!0}={}){return j(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function fe(e,{checkOpen:t=!0}={}){return j(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function xn(e,t){return wn(e,t)&&fe(e,t)}var zd=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),An=class{#r;#o;#e=!1;#n=void 0;constructor(t,r){this.#r=t,this.#o=r}next(){let t=()=>this.#u();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#t(t);return this.#n?this.#n.then(r,r):r()}async#u(){if(this.#e)return{done:!0,value:void 0};let t;try{t=await this.#r.read()}catch(r){throw this.#n=void 0,this.#e=!0,this.#r.releaseLock(),r}return t.done&&(this.#n=void 0,this.#e=!0,this.#r.releaseLock()),t}async#t(t){if(this.#e)return{done:!0,value:t};if(this.#e=!0,!this.#o){let r=this.#r.cancel(t);return this.#r.releaseLock(),await r,{done:!0,value:t}}return this.#r.releaseLock(),{done:!0,value:t}}},Bn=Symbol();function qu(){return this[Bn].next()}Object.defineProperty(qu,"name",{value:"next"});function Hu(e){return this[Bn].return(e)}Object.defineProperty(Hu,"name",{value:"return"});var Vd=Object.create(zd,{next:{enumerable:!0,configurable:!0,writable:!0,value:qu},return:{enumerable:!0,configurable:!0,writable:!0,value:Hu}});function Tn({preventCancel:e=!1}={}){let t=this.getReader(),r=new An(t,e),n=Object.create(Vd);return n[Bn]=r,n}var Ku=e=>{if(fe(e,{checkOpen:!1})&&Ke.on!==void 0)return qd(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(Yd.call(e)==="[object ReadableStream]")return Tn.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:Yd}=Object.prototype,qd=async function*(e){let t=new AbortController,r={};Hd(e,t,r);try{for await(let[n]of Ke.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},Hd=async(e,t,r)=>{try{await Ke.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},Ke={};var Be=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,finalize:u},{maxBuffer:a=Number.POSITIVE_INFINITY}={})=>{let l=Ku(e),c=t();c.length=0;try{for await(let f of l){let D=Jd(f),d=r[D](f,c);Zu({convertedChunk:d,state:c,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:a})}return Kd({state:c,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,maxBuffer:a}),u(c)}catch(f){let D=typeof f=="object"&&f!==null?f:new Error(f);throw D.bufferedData=u(c),D}},Kd=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let s=o(e);s!==void 0&&Zu({convertedChunk:s,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},Zu=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let s=r(e),u=t.length+s;if(u<=i){Ju(e,t,o,u);return}let a=n(e,i-t.length);throw a!==void 0&&Ju(a,t,o,i),new Y},Ju=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},Jd=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=Xu.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&Xu.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:Xu}=Object.prototype,Y=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var Z=e=>e,Je=()=>{},Jt=({contents:e})=>e,Xt=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},Zt=e=>e.length;async function Qt(e,t){return Be(e,ep,t)}var Xd=()=>({contents:[]}),Zd=()=>1,Qd=(e,{contents:t})=>(t.push(e),t),ep={init:Xd,convertChunk:{string:Z,buffer:Z,arrayBuffer:Z,dataView:Z,typedArray:Z,others:Z},getSize:Zd,truncateChunk:Je,addChunk:Qd,getFinalChunk:Je,finalize:Jt};async function er(e,t){return Be(e,cp,t)}var tp=()=>({contents:new ArrayBuffer(0)}),rp=e=>np.encode(e),np=new TextEncoder,Qu=e=>new Uint8Array(e),ea=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),op=(e,t)=>e.slice(0,t),ip=(e,{contents:t,length:r},n)=>{let o=na()?up(t,n):sp(t,n);return new Uint8Array(o).set(e,r),o},sp=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(ra(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},up=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:ra(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},ra=e=>ta**Math.ceil(Math.log(e)/Math.log(ta)),ta=2,ap=({contents:e,length:t})=>na()?e:e.slice(0,t),na=()=>"resize"in ArrayBuffer.prototype,cp={init:tp,convertChunk:{string:rp,buffer:Qu,arrayBuffer:Qu,dataView:ea,typedArray:ea,others:Xt},getSize:Zt,truncateChunk:op,addChunk:ip,getFinalChunk:Je,finalize:ap};async function rr(e,t){return Be(e,pp,t)}var lp=()=>({contents:"",textDecoder:new TextDecoder}),tr=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),fp=(e,{contents:t})=>t+e,Dp=(e,t)=>e.slice(0,t),dp=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},pp={init:lp,convertChunk:{string:Z,buffer:tr,arrayBuffer:tr,dataView:tr,typedArray:tr,others:Xt},getSize:Zt,truncateChunk:Dp,addChunk:fp,getFinalChunk:dp,finalize:Jt};Object.assign(Ke,{on:oa.on,finished:ia.finished});var sa=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof Y))throw e;if(i==="all")return e;let s=mp(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:s},t.destroy(),e},mp=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",ua=(e,t,r)=>{if(t.length!==r)return;let n=new Y;throw n.maxBufferInfo={fdNumber:"ipc"},n},aa=(e,t)=>{let{streamName:r,threshold:n,unit:o}=hp(e,t);return`Command's ${r} was larger than ${n} ${o}`},hp=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=X(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:yt(r),threshold:o,unit:n}},ca=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>nr(r)),la=(e,t,r)=>{if(!t)return e;let n=nr(r);return e.length>n?e.slice(0,n):e},nr=([,e])=>e;var da=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:s,escapedCommand:u,timedOut:a,isCanceled:l,isGracefullyCanceled:c,isMaxBuffer:f,isForcefullyTerminated:D,forceKillAfterDelay:d,killSignal:p,maxBuffer:F,timeout:w,cwd:g})=>{let A=n?.code,T=Fp({originalError:n,timedOut:a,timeout:w,isMaxBuffer:f,maxBuffer:F,errorCode:A,signal:o,signalDescription:i,exitCode:s,isCanceled:l,isGracefullyCanceled:c,isForcefullyTerminated:D,forceKillAfterDelay:d,killSignal:p}),O=Ep(n,g),K=O===void 0?"":`
${O}`,te=`${T}: ${u}${K}`,ce=t===void 0?[e[2],e[1]]:[t],he=[te,...ce,...e.slice(3),r.map(re=>yp(re)).join(`
`)].map(re=>Ge(Ae(bp(re)))).filter(Boolean).join(`

`);return{originalMessage:O,shortMessage:te,message:he}},Fp=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:s,signalDescription:u,exitCode:a,isCanceled:l,isGracefullyCanceled:c,isForcefullyTerminated:f,forceKillAfterDelay:D,killSignal:d})=>{let p=gp(f,D);return t?`Command timed out after ${r} milliseconds${p}`:c?s===void 0?`Command was gracefully canceled with exit code ${a}`:f?`Command was gracefully canceled${p}`:`Command was gracefully canceled with ${s} (${u})`:l?`Command was canceled${p}`:n?`${aa(e,o)}${p}`:i!==void 0?`Command failed with ${i}${p}`:f?`Command was killed with ${d} (${Mt(d)})${p}`:s!==void 0?`Command was killed with ${s} (${u})`:a!==void 0?`Command failed with exit code ${a}`:"Command failed"},gp=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",Ep=(e,t)=>{if(e instanceof N)return;let r=ws(e)?e.originalMessage:String(e?.message??e),n=Ge(Gu(r,t));return n===""?void 0:n},yp=e=>typeof e=="string"?e:(0,Da.inspect)(e),bp=e=>Array.isArray(e)?e.map(t=>Ae(fa(t))).filter(Boolean).join(`
`):fa(e),fa=e=>typeof e=="string"?e:x(e)?Ft(e):"";var or=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:s})=>pa({command:e,escapedCommand:t,cwd:i,durationMs:Zr(s),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),Te=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:s})=>Xe({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:s}),Xe=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:l,signal:c,stdio:f,all:D,ipcOutput:d,options:{timeoutDuration:p,timeout:F=p,forceKillAfterDelay:w,killSignal:g,cwd:A,maxBuffer:T},isSync:O})=>{let{exitCode:K,signal:te,signalDescription:ce}=Cp(l,c),{originalMessage:he,shortMessage:re,message:Gr}=da({stdio:f,all:D,ipcOutput:d,originalError:e,signal:te,signalDescription:ce,exitCode:K,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,forceKillAfterDelay:w,killSignal:g,maxBuffer:T,timeout:F,cwd:A}),Ue=Ss(e,Gr,O);return Object.assign(Ue,Sp({error:Ue,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:K,signal:te,signalDescription:ce,stdio:f,all:D,ipcOutput:d,cwd:A,originalMessage:he,shortMessage:re})),Ue},Sp=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:l,signal:c,signalDescription:f,stdio:D,all:d,ipcOutput:p,cwd:F,originalMessage:w,shortMessage:g})=>pa({shortMessage:g,originalMessage:w,command:t,escapedCommand:r,cwd:F,durationMs:Zr(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isTerminated:c!==void 0,isMaxBuffer:u,isForcefullyTerminated:a,exitCode:l,signal:c,signalDescription:f,code:e.cause?.code,stdout:D[1],stderr:D[2],all:d,stdio:D,ipcOutput:p,pipedFrom:[]}),pa=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),Cp=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:Mt(t);return{exitCode:r,signal:n,signalDescription:o}};var ma=e=>Number.isFinite(e)?e:0;function wp(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(ma(e*1e3)%1e3),nanoseconds:Math.trunc(ma(e*1e6)%1e3)}}function xp(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function _n(e){switch(typeof e){case"number":{if(Number.isFinite(e))return wp(e);break}case"bigint":return xp(e)}throw new TypeError("Expected a finite number or bigint")}var Ap=e=>e===0||e===0n,Bp=(e,t)=>t===1||t===1n?e:`${e}s`,Tp=1e-7,_p=24n*60n*60n*1000n;function On(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(c,f)=>{let D=Math.floor(c*10**f+Tp);return(Math.round(D)/10**f).toFixed(f)},s=(c,f,D,d)=>{if(!((o.length===0||!t.colonNotation)&&Ap(c)&&!(t.colonNotation&&D==="m"))){if(d??=String(c),t.colonNotation){let p=d.includes(".")?d.split(".")[0].length:d.length,F=o.length>0?2:1;d="0".repeat(Math.max(0,F-p))+d}else d+=t.verbose?" "+Bp(f,c):D;o.push(d)}},u=_n(e),a=BigInt(u.days);if(s(a/365n,"year","y"),s(a%365n,"day","d"),s(Number(u.hours),"hour","h"),s(Number(u.minutes),"minute","m"),t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let c=Number(u.seconds),f=Number(u.milliseconds),D=Number(u.microseconds),d=Number(u.nanoseconds);if(s(c,"second","s"),t.formatSubMilliseconds)s(f,"millisecond","ms"),s(D,"microsecond","\xB5s"),s(d,"nanosecond","ns");else{let p=f+D/1e3+d/1e6,F=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,w=p>=1?Math.round(p):Math.ceil(p),g=F?p.toFixed(F):w;s(Number.parseFloat(g),"millisecond","ms",g)}}else{let c=(r?Number(e%_p):e)/1e3%60,f=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,D=i(c,f),d=t.keepDecimalsOnWholeSeconds?D:D.replace(/\.0+$/,"");s(Number.parseFloat(d),"second","s",d)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let l=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(l)}var ha=(e,t)=>{e.failed&&W({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var Fa=(e,t)=>{ge(t)&&(ha(e,t),Op(e,t))},Op=(e,t)=>{let r=`(done in ${On(e.durationMs)})`;W({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var _e=(e,t,{reject:r})=>{if(Fa(e,t),e.failed&&r)throw e;return e};var $n=require("node:fs");var ya=(e,t)=>De(e)?"asyncGenerator":Ca(e)?"generator":ir(e)?"fileUrl":Pp(e)?"filePath":$p(e)?"webStream":j(e,{checkOpen:!1})?"native":x(e)?"uint8Array":jp(e)?"asyncIterable":Up(e)?"iterable":Mn(e)?ba({transform:e},t):vp(e)?Rp(e,t):"native",Rp=(e,t)=>xn(e.transform,{checkOpen:!1})?Ip(e,t):Mn(e.transform)?ba(e,t):Mp(e,t),Ip=(e,t)=>(Sa(e,t,"Duplex stream"),"duplex"),ba=(e,t)=>(Sa(e,t,"web TransformStream"),"webTransform"),Sa=({final:e,binary:t,objectMode:r},n,o)=>{ga(e,`${n}.final`,o),ga(t,`${n}.binary`,o),Rn(r,`${n}.objectMode`)},ga=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},Mp=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!Ea(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(xn(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(Mn(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!Ea(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return Rn(r,`${o}.binary`),Rn(n,`${o}.objectMode`),De(e)||De(t)?"asyncGenerator":"generator"},Rn=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},Ea=e=>De(e)||Ca(e),De=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",Ca=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",vp=e=>C(e)&&(e.transform!==void 0||e.final!==void 0),ir=e=>Object.prototype.toString.call(e)==="[object URL]",wa=e=>ir(e)&&e.protocol!=="file:",Pp=e=>C(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>Lp.has(t))&&In(e.file),Lp=new Set(["file","append"]),In=e=>typeof e=="string",xa=(e,t)=>e==="native"&&typeof t=="string"&&!Np.has(t),Np=new Set(["ipc","ignore","inherit","overlapped","pipe"]),Aa=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",sr=e=>Object.prototype.toString.call(e)==="[object WritableStream]",$p=e=>Aa(e)||sr(e),Mn=e=>Aa(e?.readable)&&sr(e?.writable),jp=e=>Ba(e)&&typeof e[Symbol.asyncIterator]=="function",Up=e=>Ba(e)&&typeof e[Symbol.iterator]=="function",Ba=e=>typeof e=="object"&&e!==null,I=new Set(["generator","asyncGenerator","duplex","webTransform"]),ur=new Set(["fileUrl","filePath","fileNumber"]),vn=new Set(["fileUrl","filePath"]),Ta=new Set([...vn,"webStream","nodeStream"]),_a=new Set(["webTransform","duplex"]),se={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var Pn=(e,t,r,n)=>n==="output"?kp(e,t,r):Gp(e,t,r),kp=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},Gp=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},Oa=(e,t)=>{let r=e.findLast(({type:n})=>I.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var Ra=(e,t,r,n)=>[...e.filter(({type:o})=>!I.has(o)),...Wp(e,t,r,n)],Wp=(e,t,r,{encoding:n})=>{let o=e.filter(({type:s})=>I.has(s)),i=Array.from({length:o.length});for(let[s,u]of Object.entries(o))i[s]=zp({stdioItem:u,index:Number(s),newTransforms:i,optionName:t,direction:r,encoding:n});return Hp(i,r)},zp=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:s})=>t==="duplex"?Vp({stdioItem:e,optionName:o}):t==="webTransform"?Yp({stdioItem:e,index:r,newTransforms:n,direction:i}):qp({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:s}),Vp=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},Yp=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:s}=C(t)?t:{transform:t},{writableObjectMode:u,readableObjectMode:a}=Pn(s,r,n,o);return{...e,value:{transform:i,writableObjectMode:u,readableObjectMode:a}}},qp=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:s,final:u,binary:a=!1,preserveNewlines:l=!1,objectMode:c}=C(t)?t:{transform:t},f=a||_.has(i),{writableObjectMode:D,readableObjectMode:d}=Pn(c,r,n,o);return{...e,value:{transform:s,final:u,binary:f,preserveNewlines:l,writableObjectMode:D,readableObjectMode:d}}},Hp=(e,t)=>t==="input"?e.reverse():e;var ar=h(require("node:process"),1);var Ia=(e,t,r)=>{let n=e.map(o=>Kp(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??Zp},Kp=({type:e,value:t},r)=>Jp[r]??Ma[e](t),Jp=["input","output","output"],Oe=()=>{},Ln=()=>"input",Ma={generator:Oe,asyncGenerator:Oe,fileUrl:Oe,filePath:Oe,iterable:Ln,asyncIterable:Ln,uint8Array:Ln,webStream:e=>sr(e)?"output":"input",nodeStream(e){return fe(e,{checkOpen:!1})?wn(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:Oe,duplex:Oe,native(e){let t=Xp(e);if(t!==void 0)return t;if(j(e,{checkOpen:!1}))return Ma.nodeStream(e)}},Xp=e=>{if([0,ar.default.stdin].includes(e))return"input";if([1,2,ar.default.stdout,ar.default.stderr].includes(e))return"output"},Zp="output";var va=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var Pa=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let s=Qp(e,n).map((u,a)=>La(u,a));return i?tm(s,r,o):va(s,t)},Qp=(e,t)=>{if(e===void 0)return R.map(n=>t[n]);if(em(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${R.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,R.length);return Array.from({length:r},(n,o)=>e[o])},em=e=>R.some(t=>e[t]!==void 0),La=(e,t)=>Array.isArray(e)?e.map(r=>La(r,t)):e??(t>=R.length?"ignore":"pipe"),tm=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!Ee(r,o)&&rm(n)?"ignore":n),rm=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var $a=require("node:fs"),ja=h(require("node:tty"),1);var Ua=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?nm({stdioItem:e,fdNumber:n,direction:o}):sm({stdioItem:e,fdNumber:n}),nm=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=om({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if(j(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},om=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=im(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(ja.default.isatty(o))throw new TypeError(`The \`${t}: ${Lt(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:J((0,$a.readFileSync)(o)),optionName:t}}},im=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=Et.indexOf(e);if(r!==-1)return r},sm=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:Na(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:Na(t,t,r),optionName:r}:j(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,Na=(e,t,r)=>{let n=Et[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var ka=({input:e,inputFile:t},r)=>r===0?[...um(e),...cm(t)]:[],um=e=>e===void 0?[]:[{type:am(e),value:e,optionName:"input"}],am=e=>{if(fe(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(x(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},cm=e=>e===void 0?[]:[{...lm(e),optionName:"inputFile"}],lm=e=>{if(ir(e))return{type:"fileUrl",value:e};if(In(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var Ga=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),Wa=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let s=fm(o,e);if(s.length!==0){if(i){Dm({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});return}if(Ta.has(e))return za({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});_a.has(e)&&pm({otherStdioItems:s,type:e,value:t,optionName:r})}},fm=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),Dm=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{vn.has(t)&&za({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},za=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(u=>dm(u,r));if(i.length===0)return;let s=i.find(u=>u.direction!==o);return Va(s,n,t),o==="output"?i[0].stream:void 0},dm=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,pm=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);Va(o,n,t)},Va=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${se[r]} that is the same.`)};var cr=(e,t,r,n)=>{let i=Pa(t,r,n).map((u,a)=>mm({stdioOption:u,fdNumber:a,options:t,isSync:n})),s=Cm({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=s.map(({stdioItems:u})=>Am(u)),s},mm=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=yt(t),{stdioItems:i,isStdioArray:s}=hm({stdioOption:e,fdNumber:t,options:r,optionName:o}),u=Ia(i,t,o),a=i.map(f=>Ua({stdioItem:f,isStdioArray:s,fdNumber:t,direction:u,isSync:n})),l=Ra(a,o,u,r),c=Oa(l,u);return Sm(l,c),{direction:u,objectMode:c,stdioItems:l}},hm=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(a=>Fm(a,n)),...ka(r,t)],s=Ga(i),u=s.length>1;return gm(s,u,n),ym(s),{stdioItems:s,isStdioArray:u}},Fm=(e,t)=>({type:ya(e,t),value:e,optionName:t}),gm=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(Em.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},Em=new Set(["ignore","ipc"]),ym=e=>{for(let t of e)bm(t)},bm=({type:e,value:t,optionName:r})=>{if(wa(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(xa(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},Sm=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>ur.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},Cm=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(wm({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw Nn(o),i}},wm=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:s})=>{let u=r.map(a=>xm({stdioItem:a,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:s}));return{direction:e,objectMode:t,stdioItems:u}},xm=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let s=Wa({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return s!==void 0?{...e,stream:s}:{...e,...t[r][e.type](e,n)}},Nn=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!L(r)&&r.destroy()},Am=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var qa=(e,t)=>cr(Tm,e,t,!0),q=({type:e,optionName:t})=>{Ha(t,se[e])},Bm=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&Ha(e,`"${t}"`),{}),Ha=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},Ya={generator(){},asyncGenerator:q,webStream:q,nodeStream:q,webTransform:q,duplex:q,asyncIterable:q,native:Bm},Tm={input:{...Ya,fileUrl:({value:e})=>({contents:[J((0,$n.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[J((0,$n.readFileSync)(e))]}),fileNumber:q,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...Ya,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:q,string:q,uint8Array:q}};var Q=(e,{stripFinalNewline:t},r)=>jn(t,r)&&e!==void 0&&!Array.isArray(e)?Ae(e):e,jn=(e,t)=>t==="all"?e[1]||e[2]:e[t];var Qe=require("node:stream");var lr=(e,t,r,n)=>e||r?void 0:Ja(t,n),kn=(e,t,r)=>r?e.flatMap(n=>Ka(n,t)):Ka(e,t),Ka=(e,t)=>{let{transform:r,final:n}=Ja(t,{});return[...r(e),...n()]},Ja=(e,t)=>(t.previousChunks="",{transform:_m.bind(void 0,t,e),final:Rm.bind(void 0,t)}),_m=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let s=Om(r,i,t,e),u=r.slice(o+1,i+1-s);n.length>0&&(u=Un(n,u),n=""),yield u,o=i}o!==r.length-1&&(n=Un(n,r.slice(o+1))),e.previousChunks=n},Om=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),Rm=function*({previousChunks:e}){e.length>0&&(yield e)},Xa=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:Im.bind(void 0,n)},Im=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?Mm:Pm;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},Un=(e,t)=>`${e}${t}`,Mm={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:Un},vm=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},Pm={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:vm};var Za=require("node:buffer");var Qa=(e,t)=>e?void 0:Lm.bind(void 0,t),Lm=function*(e,t){if(typeof t!="string"&&!x(t)&&!Za.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},ec=(e,t)=>e?Nm.bind(void 0,t):$m.bind(void 0,t),Nm=function*(e,t){tc(e,t),yield t},$m=function*(e,t){if(tc(e,t),typeof t!="string"&&!x(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},tc=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var rc=require("node:buffer"),nc=require("node:string_decoder");var fr=(e,t,r)=>{if(r)return;if(e)return{transform:jm.bind(void 0,new TextEncoder)};let n=new nc.StringDecoder(t);return{transform:Um.bind(void 0,n),final:km.bind(void 0,n)}},jm=function*(e,t){rc.Buffer.isBuffer(t)?yield J(t):typeof t=="string"?yield e.encode(t):yield t},Um=function*(e,t){yield x(t)?e.write(t):t},km=function*(e){let t=e.end();t!==""&&(yield t)};var Gn=require("node:util"),Wn=(0,Gn.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),Dr=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Wm}=t[r];for await(let o of n(e))yield*Dr(o,t,r+1)},oc=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Gm(r,Number(t),e)},Gm=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*Dr(n,r,t+1)},ic=(0,Gn.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),Wm=function*(e){yield e};var zn=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},sc=(e,t)=>[...t.flatMap(r=>[...de(r,e,0)]),...Ze(e)],de=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Vm}=t[r];for(let o of n(e))yield*de(o,t,r+1)},Ze=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*zm(r,Number(t),e)},zm=function*(e,t,r){if(e!==void 0)for(let n of e())yield*de(n,r,t+1)},Vm=function*(e){yield e};var Vn=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:s})=>{let u={},a=uc(e,s,i),l=De(t),c=De(r),f=l?Wn.bind(void 0,Dr,u):zn.bind(void 0,de),D=l||c?Wn.bind(void 0,oc,u):zn.bind(void 0,Ze),d=l||c?ic.bind(void 0,u):void 0;return{stream:new Qe.Transform({writableObjectMode:n,writableHighWaterMark:(0,Qe.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,Qe.getDefaultHighWaterMark)(o),transform(F,w,g){f([F,a,0],this,g)},flush(F){D([a],this,F)},destroy:d})}},dr=(e,t,r,n)=>{let o=t.filter(({type:s})=>s==="generator"),i=n?o.reverse():o;for(let{value:s,optionName:u}of i){let a=uc(s,r,u);e=sc(a,e)}return e},uc=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},s,u)=>{let a={};return[{transform:Qa(n,u)},fr(r,s,n),lr(r,i,n,a),{transform:e,final:t},{transform:ec(o,u)},Xa({binary:r,preserveNewlines:i,readableObjectMode:o,state:a})].filter(Boolean)};var ac=(e,t)=>{for(let r of Ym(e))qm(e,r,t)},Ym=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),qm=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:u})=>u!==void 0);if(o.length===0)return;if(t!==0){let[{type:u,optionName:a}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${a}\`, can be ${se[u]} with synchronous methods.`)}let s=o.map(({contents:u})=>u).map(u=>Hm(u,n));r.input=ke(s)},Hm=(e,t)=>{let r=dr(e,t,"utf8",!0);return Km(r),ke(r)},Km=e=>{let t=e.find(r=>typeof r!="string"&&!x(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var mr=require("node:fs");var pr=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&Ee(r,n)&&!_.has(t)&&Jm(n)&&(e.some(({type:o,value:i})=>o==="native"&&Xm.has(i))||e.every(({type:o})=>I.has(o))),Jm=e=>e===1||e===2,Xm=new Set(["pipe","overlapped"]),cc=async(e,t,r,n)=>{for await(let o of e)Zm(t)||fc(o,r,n)},lc=(e,t,r)=>{for(let n of e)fc(n,t,r)},Zm=e=>e._readableState.pipes.length>0,fc=(e,t,r)=>{let n=At(e);W({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var Dc=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},s=new Set([]);return{output:t.map((a,l)=>Qm({result:a,fileDescriptors:e,fdNumber:l,state:i,outputFiles:s,isMaxBuffer:n,verboseInfo:o},r)),...i}},Qm=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:s},{buffer:u,encoding:a,lines:l,stripFinalNewline:c,maxBuffer:f})=>{if(e===null)return;let D=la(e,i,f),d=J(D),{stdioItems:p,objectMode:F}=t[r],w=e0([d],p,a,n),{serializedResult:g,finalResult:A=g}=t0({chunks:w,objectMode:F,encoding:a,lines:l,stripFinalNewline:c,fdNumber:r});r0({serializedResult:g,fdNumber:r,state:n,verboseInfo:s,encoding:a,stdioItems:p,objectMode:F});let T=u[r]?A:void 0;try{return n.error===void 0&&n0(g,p,o),T}catch(O){return n.error=O,T}},e0=(e,t,r,n)=>{try{return dr(e,t,r,!1)}catch(o){return n.error=o,e}},t0=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:ke(e)};let s=ri(e,r);return n[i]?{serializedResult:s,finalResult:kn(s,!o[i],t)}:{serializedResult:s}},r0=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:s})=>{if(!pr({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let u=kn(e,!1,s);try{lc(u,t,n)}catch(a){r.error??=a}},n0=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>ur.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,mr.appendFileSync)(n,e):(r.add(i),(0,mr.writeFileSync)(n,e))}};var dc=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,Q(t,r,"all")]:Array.isArray(t)?[Q(e,r,"all"),...t]:x(e)&&x(t)?Vr([e,t]):`${e}${t}`};var hr=require("node:events");var pc=async(e,t)=>{let[r,n]=await o0(e);return t.isForcefullyTerminated??=!1,[r,n]},o0=async e=>{let[t,r]=await Promise.allSettled([(0,hr.once)(e,"spawn"),(0,hr.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?mc(e):r.value},mc=async e=>{try{return await(0,hr.once)(e,"exit")}catch{return mc(e)}},hc=async e=>{let[t,r]=await e;if(!i0(t,r)&&Yn(t,r))throw new N;return[t,r]},i0=(e,t)=>e===void 0&&t===void 0,Yn=(e,t)=>e!==0||t!==null;var Fc=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=s0(e,t,r),s=i?.code==="ETIMEDOUT",u=ca(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:s,isMaxBuffer:u}},s0=(e,t,r)=>e!==void 0?e:Yn(t,r)?new N:void 0;var Ec=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:s,startTime:u,verboseInfo:a,options:l,fileDescriptors:c}=u0(e,t,r),f=l0({file:n,commandArguments:o,options:l,command:i,escapedCommand:s,verboseInfo:a,fileDescriptors:c,startTime:u});return _e(f,a,l)},u0=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=Tt(e,t,r),u=a0(r),{file:a,commandArguments:l,options:c}=Kt(e,t,u);c0(c);let f=qa(c,s);return{file:a,commandArguments:l,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:c,fileDescriptors:f}},a0=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,c0=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&Fr("ipcInput"),e&&Fr("ipc: true"),r&&Fr("detached: true"),n&&Fr("cancelSignal")},Fr=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},l0=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:s,startTime:u})=>{let a=f0({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:s,startTime:u});if(a.failed)return a;let{resultError:l,exitCode:c,signal:f,timedOut:D,isMaxBuffer:d}=Fc(a,r),{output:p,error:F=l}=Dc({fileDescriptors:s,syncResult:a,options:r,isMaxBuffer:d,verboseInfo:i}),w=p.map((A,T)=>Q(A,r,T)),g=Q(dc(p,r),r,"all");return d0({error:F,exitCode:c,signal:f,timedOut:D,isMaxBuffer:d,stdio:w,all:g,options:r,command:n,escapedCommand:o,startTime:u})},f0=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:s})=>{try{ac(i,r);let u=D0(r);return(0,gc.spawnSync)(e,t,u)}catch(u){return Te({error:u,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:s,isSync:!0})}},D0=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:nr(t)}),d0=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:s,options:u,command:a,escapedCommand:l,startTime:c})=>e===void 0?or({command:a,escapedCommand:l,stdio:i,all:s,ipcOutput:[],options:u,startTime:c}):Xe({error:e,command:a,escapedCommand:l,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:s,ipcOutput:[],options:u,startTime:c,isSync:!0});var Al=require("node:events"),Bl=require("node:child_process");var Hn=h(require("node:process"),1);var Re=require("node:events");var yc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(Ce({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:Gt(e)}),p0({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),p0=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{$t(t,o);let i=ie(e,t,r),s=new AbortController;try{return await Promise.race([m0(i,n,s),h0(i,r,s),F0(i,r,s)])}catch(u){throw we(e),u}finally{s.abort(),jt(t,o)}},m0=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,Re.once)(e,"message",{signal:r});return n}for await(let[n]of(0,Re.on)(e,"message",{signal:r}))if(t(n))return n},h0=async(e,t,{signal:r})=>{await(0,Re.once)(e,"disconnect",{signal:r}),Ws(t)},F0=async(e,t,{signal:r})=>{let[n]=await(0,Re.once)(e,"strict:error",{signal:r});throw Pt(n,t)};var et=require("node:events");var Sc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>qn({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),qn=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{Ce({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:Gt(e)}),$t(t,i);let s=ie(e,t,r),u=new AbortController,a={};return g0(e,s,u),E0({ipcEmitter:s,isSubprocess:r,controller:u,state:a}),y0({anyProcess:e,channel:t,ipcEmitter:s,isSubprocess:r,shouldAwait:o,controller:u,state:a,reference:i})},g0=async(e,t,r)=>{try{await(0,et.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},E0=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,et.once)(e,"strict:error",{signal:r.signal});n.error=Pt(o,t),r.abort()}catch{}},y0=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:s,reference:u}){try{for await(let[a]of(0,et.on)(r,"message",{signal:i.signal}))bc(s),yield a}catch{bc(s)}finally{i.abort(),jt(t,u),n||we(e),o&&await e}},bc=({error:e})=>{if(e)throw e};var Cc=(e,{ipc:t})=>{Object.assign(e,xc(e,!1,t))},wc=()=>{let e=Hn.default,t=!0,r=Hn.default.channel!==void 0;return{...xc(e,t,r),getCancelSignal:Cu.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},xc=(e,t,r)=>({sendMessage:Yt.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:yc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:Sc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var Ac=require("node:child_process"),ue=require("node:stream");var Bc=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:s})=>{Nn(n);let u=new Ac.ChildProcess;b0(u,n),Object.assign(u,{readable:S0,writable:C0,duplex:w0});let a=Te({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),l=x0(a,s,o);return{subprocess:u,promise:l}},b0=(e,t)=>{let r=tt(),n=tt(),o=tt(),i=Array.from({length:t.length-3},tt),s=tt(),u=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:s,stdio:u})},tt=()=>{let e=new ue.PassThrough;return e.end(),e},S0=()=>new ue.Readable({read(){}}),C0=()=>new ue.Writable({write(){}}),w0=()=>new ue.Duplex({read(){},write(){}}),x0=async(e,t,r)=>_e(e,t,r);var Ie=require("node:fs"),_c=require("node:buffer"),H=require("node:stream");var Oc=(e,t)=>cr(A0,e,t,!1),rt=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${se[e]}.`)},Tc={fileNumber:rt,generator:Vn,asyncGenerator:Vn,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:H.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},A0={input:{...Tc,fileUrl:({value:e})=>({stream:(0,Ie.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,Ie.createReadStream)(e)}),webStream:({value:e})=>({stream:H.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:H.Readable.from(e)}),asyncIterable:({value:e})=>({stream:H.Readable.from(e)}),string:({value:e})=>({stream:H.Readable.from(e)}),uint8Array:({value:e})=>({stream:H.Readable.from(_c.Buffer.from(e))})},output:{...Tc,fileUrl:({value:e})=>({stream:(0,Ie.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,Ie.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:H.Writable.fromWeb(e)}),iterable:rt,asyncIterable:rt,string:rt,uint8Array:rt}};var nt=require("node:events"),Er=require("node:stream"),Xn=require("node:stream/promises");function pe(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)Jn(o);let t=e.some(({readableObjectMode:o})=>o),r=B0(e,t),n=new Kn({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var B0=(e,t)=>{if(e.length===0)return(0,Er.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},Kn=class extends Er.PassThrough{#r=new Set([]);#o=new Set([]);#e=new Set([]);#n;#u=Symbol("unpipe");#t=new WeakMap;add(t){if(Jn(t),this.#r.has(t))return;this.#r.add(t),this.#n??=T0(this,this.#r,this.#u);let r=R0({passThroughStream:this,stream:t,streams:this.#r,ended:this.#o,aborted:this.#e,onFinished:this.#n,unpipeEvent:this.#u});this.#t.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(Jn(t),!this.#r.has(t))return!1;let r=this.#t.get(t);return r===void 0?!1:(this.#t.delete(t),t.unpipe(this),await r,!0)}},T0=async(e,t,r)=>{gr(e,Rc);let n=new AbortController;try{await Promise.race([_0(e,n),O0(e,t,r,n)])}finally{n.abort(),gr(e,-Rc)}},_0=async(e,{signal:t})=>{try{await(0,Xn.finished)(e,{signal:t,cleanup:!0})}catch(r){throw Mc(e,r),r}},O0=async(e,t,r,{signal:n})=>{for await(let[o]of(0,nt.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},Jn=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},R0=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:s})=>{gr(e,Ic);let u=new AbortController;try{await Promise.race([I0(i,t,u),M0({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:u}),v0({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:s,controller:u})])}finally{u.abort(),gr(e,-Ic)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?Zn(e):P0(e))},I0=async(e,t,{signal:r})=>{try{await e,r.aborted||Zn(t)}catch(n){r.aborted||Mc(t,n)}},M0=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,Xn.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(s){if(i.aborted||!r.has(t))return;vc(s)?o.add(t):Pc(e,s)}},v0=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,nt.once)(e,o,{signal:i}),!e.readable)return(0,nt.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},P0=e=>{e.writable&&e.end()},Mc=(e,t)=>{vc(t)?Zn(e):Pc(e,t)},vc=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",Zn=e=>{(e.readable||e.writable)&&e.destroy()},Pc=(e,t)=>{e.destroyed||(e.once("error",L0),e.destroy(t))},L0=()=>{},gr=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},Rc=2,Ic=1;var Qn=require("node:stream/promises");var Me=(e,t)=>{e.pipe(t),N0(e,t),$0(e,t)},N0=async(e,t)=>{if(!(L(e)||L(t))){try{await(0,Qn.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}eo(t)}},eo=e=>{e.writable&&e.end()},$0=async(e,t)=>{if(!(L(e)||L(t))){try{await(0,Qn.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}to(e)}},to=e=>{e.readable&&e.destroy()};var Lc=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:s}]of Object.entries(t)){for(let{stream:u}of i.filter(({type:a})=>I.has(a)))j0(e,u,s,o);for(let{stream:u}of i.filter(({type:a})=>!I.has(a)))k0({subprocess:e,stream:u,direction:s,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let s=i.length===1?i[0]:pe(i);Me(s,o)}},j0=(e,t,r,n)=>{r==="output"?Me(e.stdio[n],t):Me(t,e.stdio[n]);let o=U0[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},U0=["stdin","stdout","stderr"],k0=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;G0(t,i);let[s,u]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],a=o.get(s)??[];o.set(s,[...a,u])},G0=(e,{signal:t})=>{L(e)&&le(e,W0,t)},W0=2;var Nc=require("node:events");var me=[];me.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&me.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&me.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var yr=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",ro=Symbol.for("signal-exit emitter"),no=globalThis,z0=Object.defineProperty.bind(Object),oo=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(no[ro])return no[ro];z0(no,ro,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},br=class{},V0=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),io=class extends br{onExit(){return()=>{}}load(){}unload(){}},so=class extends br{#r=uo.platform==="win32"?"SIGINT":"SIGHUP";#o=new oo;#e;#n;#u;#t={};#s=!1;constructor(t){super(),this.#e=t,this.#t={};for(let r of me)this.#t[r]=()=>{let n=this.#e.listeners(r),{count:o}=this.#o,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let s=this.#o.emit("exit",null,r),u=r==="SIGHUP"?this.#r:r;s||t.kill(t.pid,u)}};this.#u=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!yr(this.#e))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#o.on(n,t),()=>{this.#o.removeListener(n,t),this.#o.listeners.exit.length===0&&this.#o.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#o.count+=1;for(let t of me)try{let r=this.#t[t];r&&this.#e.on(t,r)}catch{}this.#e.emit=(t,...r)=>this.#d(t,...r),this.#e.reallyExit=t=>this.#i(t)}}unload(){this.#s&&(this.#s=!1,me.forEach(t=>{let r=this.#t[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,r)}catch{}}),this.#e.emit=this.#n,this.#e.reallyExit=this.#u,this.#o.count-=1)}#i(t){return yr(this.#e)?(this.#e.exitCode=t||0,this.#o.emit("exit",this.#e.exitCode,null),this.#u.call(this.#e,this.#e.exitCode)):0}#d(t,...r){let n=this.#n;if(t==="exit"&&yr(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let o=n.call(this.#e,t,...r);return this.#o.emit("exit",this.#e.exitCode,null),o}else return n.call(this.#e,t,...r)}},uo=globalThis.process,{onExit:Sr,load:xC,unload:AC}=V0(yr(uo)?new so(uo):new io);var $c=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=Sr(()=>{e.kill()});(0,Nc.addAbortListener)(n,()=>{o()})};var Uc=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=Bt(),{destination:s,destinationStream:u,destinationError:a,from:l,unpipeSignal:c}=Y0(r,n,o),{sourceStream:f,sourceError:D}=H0(e,l),{options:d,fileDescriptors:p}=V.get(e);return{sourcePromise:t,sourceStream:f,sourceOptions:d,sourceError:D,destination:s,destinationStream:u,destinationError:a,unpipeSignal:c,fileDescriptors:p,startTime:i}},Y0=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:s}={}}=q0(e,t,...r),u=Nt(n,i);return{destination:n,destinationStream:u,from:o,unpipeSignal:s}}catch(n){return{destinationError:n}}},q0=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(jc,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||Wr(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,s]=ht(r,...n);return{destination:t(jc)(o,i,s),pipeOptions:s}}if(V.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},jc=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),H0=(e,t)=>{try{return{sourceStream:xe(e,t)}}catch(r){return{sourceError:r}}};var Gc=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:s})=>{let u=K0({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(u!==void 0)throw ao({error:u,fileDescriptors:o,sourceOptions:i,startTime:s})},K0=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return to(e),n;if(t!==void 0)return eo(r),t},ao=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>Te({error:e,command:kc,escapedCommand:kc,fileDescriptors:t,options:r,startTime:n,isSync:!1}),kc="source.pipe(destination)";var Wc=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:s=i}]=await e;if(s.pipedFrom.includes(n)||s.pipedFrom.push(n),o==="rejected")throw s;if(t==="rejected")throw n;return s};var zc=require("node:stream/promises");var Vc=(e,t,r)=>{let n=Cr.has(t)?X0(e,t):J0(e,t);return le(e,Q0,r.signal),le(t,eh,r.signal),Z0(t),n},J0=(e,t)=>{let r=pe([e]);return Me(r,t),Cr.set(t,r),r},X0=(e,t)=>{let r=Cr.get(t);return r.add(e),r},Z0=async e=>{try{await(0,zc.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}Cr.delete(e)},Cr=new WeakMap,Q0=2,eh=1;var Yc=require("node:util");var qc=(e,t)=>e===void 0?[]:[th(e,t)],th=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,Yc.aborted)(e,t),await r.remove(t);let s=new Error("Pipe canceled by `unpipeSignal` option.");throw ao({error:s,fileDescriptors:n,sourceOptions:o,startTime:i})};var wr=(e,...t)=>{if(C(t[0]))return wr.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=Uc(e,...t),o=rh({...n,destination:r});return o.pipe=wr.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},rh=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:s,unpipeSignal:u,fileDescriptors:a,startTime:l})=>{let c=nh(e,o);Gc({sourceStream:t,sourceError:n,destinationStream:i,destinationError:s,fileDescriptors:a,sourceOptions:r,startTime:l});let f=new AbortController;try{let D=Vc(t,i,f);return await Promise.race([Wc(c),...qc(u,{sourceStream:t,mergedStream:D,sourceOptions:r,fileDescriptors:a,startTime:l})])}finally{f.abort()}},nh=(e,t)=>Promise.allSettled([e,t]);var Zc=require("node:timers/promises");var Kc=require("node:events"),Jc=require("node:stream");var xr=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let s=new AbortController;return oh(t,s),Xc({stream:e,controller:s,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},oh=async(e,t)=>{try{await e}catch{}finally{t.abort()}},co=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let s=new AbortController;ih(t,s,e);let u=e.readableObjectMode&&!i;return Xc({stream:e,controller:s,binary:n==="buffer",shouldEncode:!u,encoding:n,shouldSplit:!u&&r,preserveNewlines:!o})},ih=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},Xc=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})=>{let u=(0,Kc.on)(e,"data",{signal:t.signal,highWaterMark:Hc,highWatermark:Hc});return sh({onStdoutChunk:u,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})},lo=(0,Jc.getDefaultHighWaterMark)(!0),Hc=lo,sh=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s}){let u=uh({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s});try{for await(let[a]of e)yield*de(a,u,0)}catch(a){if(!t.signal.aborted)throw a}finally{yield*Ze(u)}},uh=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[fr(e,r,!t),lr(e,o,!n,{})].filter(Boolean);var Qc=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:s,allMixed:u,stripFinalNewline:a,verboseInfo:l,streamInfo:c})=>{let f=ah({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:u,verboseInfo:l,streamInfo:c});if(!o){await Promise.all([ch(e),f]);return}let D=jn(a,r),d=co({stream:e,onStreamEnd:t,lines:s,encoding:n,stripFinalNewline:D,allMixed:u}),[p]=await Promise.all([lh({stream:e,iterable:d,fdNumber:r,encoding:n,maxBuffer:i,lines:s}),f]);return p},ah=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:s}})=>{if(!pr({stdioItems:s[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let u=co({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await cc(u,e,r,i)},ch=async e=>{await(0,Zc.setImmediate)(),e.readableFlowing===null&&e.resume()},lh=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:s})=>{try{return t||s?await Qt(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await er(r,{maxBuffer:i})):await rr(r,{maxBuffer:i})}catch(u){return el(sa({error:u,stream:e,readableObjectMode:t,lines:s,encoding:o,fdNumber:n}))}},fo=async e=>{try{return await e}catch(t){return el(t)}},el=({bufferedData:e})=>ei(e)?new Uint8Array(e):e;var rl=require("node:stream/promises"),ot=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=fh(e,r),s=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,rl.finished)(e,{cleanup:!0,signal:s.signal})])}catch(u){i.stdinCleanedUp||ph(u,t,r,n)}finally{s.abort()}},fh=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&Dh(e,r,n),n},Dh=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{dh(t,r),n.call(e,...o)}},dh=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},ph=(e,t,r,n)=>{if(!mh(e,t,r,n))throw e},mh=(e,t,r,n=!0)=>r.propagating?tl(e)||Ar(e):(r.propagating=!0,Do(r,t)===n?tl(e):Ar(e)),Do=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",Ar=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",tl=e=>e?.code==="EPIPE";var nl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:u})=>e.stdio.map((a,l)=>po({stream:a,fdNumber:l,encoding:t,buffer:r[l],maxBuffer:n[l],lines:o[l],allMixed:!1,stripFinalNewline:i,verboseInfo:s,streamInfo:u})),po=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:u,verboseInfo:a,streamInfo:l})=>{if(!e)return;let c=ot(e,t,l);if(Do(l,t)){await c;return}let[f]=await Promise.all([Qc({stream:e,onStreamEnd:c,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:u,verboseInfo:a,streamInfo:l}),c]);return f};var ol=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?pe([e,t].filter(Boolean)):void 0,il=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:u})=>po({...hh(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:Fh(e),stripFinalNewline:i,verboseInfo:s,streamInfo:u}),hh=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},Fh=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var ll=require("node:events");var sl=e=>Ee(e,"ipc"),ul=(e,t)=>{let r=At(e);W({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var al=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let s=sl(i),u=X(t,"ipc"),a=X(r,"ipc");for await(let l of qn({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))u&&(ua(e,o,a),o.push(l)),s&&ul(l,i);return o},cl=async(e,t)=>(await Promise.allSettled([e]),t);var fl=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:s,gracefulCancel:u,forceKillAfterDelay:a,stripFinalNewline:l,ipc:c,ipcInput:f},context:D,verboseInfo:d,fileDescriptors:p,originalStreams:F,onInternalError:w,controller:g})=>{let A=pc(e,D),T={originalStreams:F,fileDescriptors:p,subprocess:e,exitPromise:A,propagating:!1},O=nl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:l,verboseInfo:d,streamInfo:T}),K=il({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:l,verboseInfo:d,streamInfo:T}),te=[],ce=al({subprocess:e,buffer:r,maxBuffer:n,ipc:c,ipcOutput:te,verboseInfo:d}),he=gh(F,e,T),re=Eh(p,T);try{return await Promise.race([Promise.all([{},hc(A),Promise.all(O),K,ce,vu(e,f),...he,...re]),w,yh(e,g),..._u(e,i,D,g),...Gs({subprocess:e,cancelSignal:s,gracefulCancel:u,context:D,controller:g}),...Au({subprocess:e,cancelSignal:s,gracefulCancel:u,forceKillAfterDelay:a,context:D,controller:g})])}catch(Gr){return D.terminationReason??="other",Promise.all([{error:Gr},A,Promise.all(O.map(Ue=>fo(Ue))),fo(K),cl(ce,te),Promise.allSettled(he),Promise.allSettled(re)])}},gh=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:ot(n,o,r)),Eh=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>j(i,{checkOpen:!1})&&!L(i)).map(({type:o,value:i,stream:s=i})=>ot(s,n,t,{isSameDirection:I.has(o),stopOnExit:o==="native"}))),yh=async(e,{signal:t})=>{let[r]=await(0,ll.once)(e,"error",{signal:t});throw r};var Dl=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),it=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=z();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},ve=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var pl=require("node:stream"),ml=require("node:util");var mo=require("node:stream/promises");var ho=async e=>{if(e!==void 0)try{await Fo(e)}catch{}},dl=async e=>{if(e!==void 0)try{await go(e)}catch{}},Fo=async e=>{await(0,mo.finished)(e,{cleanup:!0,readable:!1,writable:!0})},go=async e=>{await(0,mo.finished)(e,{cleanup:!0,readable:!0,writable:!1})},Br=async(e,t)=>{if(await e,t)throw t},Tr=(e,t,r)=>{r&&!Ar(r)?e.destroy(r):t&&e.destroy()};var hl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let s=o||_.has(r),{subprocessStdout:u,waitReadableDestroy:a}=Eo(e,n,t),{readableEncoding:l,readableObjectMode:c,readableHighWaterMark:f}=yo(u,s),{read:D,onStdoutDataDone:d}=bo({subprocessStdout:u,subprocess:e,binary:s,encoding:r,preserveNewlines:i}),p=new pl.Readable({read:D,destroy:(0,ml.callbackify)(Co.bind(void 0,{subprocessStdout:u,subprocess:e,waitReadableDestroy:a})),highWaterMark:f,objectMode:c,encoding:l});return So({subprocessStdout:u,onStdoutDataDone:d,readable:p,subprocess:e}),p},Eo=(e,t,r)=>{let n=xe(e,t),o=it(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},yo=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:lo},bo=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=z(),s=xr({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){bh(this,s,i)},onStdoutDataDone:i}},bh=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},So=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await go(e),await n,await ho(o),await t,r.readable&&r.push(null)}catch(i){await ho(o),Fl(r,i)}},Co=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await ve(r,t)&&(Fl(e,n),await Br(t,n))},Fl=(e,t)=>{Tr(e,e.readable,t)};var gl=require("node:stream"),wo=require("node:util");var El=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=xo(e,r,t),s=new gl.Writable({...Ao(n,e,o),destroy:(0,wo.callbackify)(To.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return Bo(n,s),s},xo=(e,t,r)=>{let n=Nt(e,t),o=it(r,n,"writableFinal"),i=it(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Ao=(e,t,r)=>({write:Sh.bind(void 0,e),final:(0,wo.callbackify)(Ch.bind(void 0,e,t,r))}),Sh=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},Ch=async(e,t,r)=>{await ve(r,t)&&(e.writable&&e.end(),await t)},Bo=async(e,t,r)=>{try{await Fo(e),t.writable&&t.end()}catch(n){await dl(r),yl(t,n)}},To=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await ve(r,t),await ve(n,t)&&(yl(e,o),await Br(t,o))},yl=(e,t)=>{Tr(e,e.writable,t)};var bl=require("node:stream"),Sl=require("node:util");var Cl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:s=!0}={})=>{let u=i||_.has(r),{subprocessStdout:a,waitReadableDestroy:l}=Eo(e,n,t),{subprocessStdin:c,waitWritableFinal:f,waitWritableDestroy:D}=xo(e,o,t),{readableEncoding:d,readableObjectMode:p,readableHighWaterMark:F}=yo(a,u),{read:w,onStdoutDataDone:g}=bo({subprocessStdout:a,subprocess:e,binary:u,encoding:r,preserveNewlines:s}),A=new bl.Duplex({read:w,...Ao(c,e,f),destroy:(0,Sl.callbackify)(wh.bind(void 0,{subprocessStdout:a,subprocessStdin:c,subprocess:e,waitReadableDestroy:l,waitWritableFinal:f,waitWritableDestroy:D})),readableHighWaterMark:F,writableHighWaterMark:c.writableHighWaterMark,readableObjectMode:p,writableObjectMode:c.writableObjectMode,encoding:d});return So({subprocessStdout:a,onStdoutDataDone:g,readable:A,subprocess:e,subprocessStdin:c}),Bo(c,A,a),A},wh=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},s)=>{await Promise.all([Co({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},s),To({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},s)])};var _o=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||_.has(t),s=xe(e,r),u=xr({subprocessStdout:s,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return xh(u,s,e)},xh=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var wl=(e,{encoding:t})=>{let r=Dl();e.readable=hl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=El.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=Cl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=_o.bind(void 0,e,t),e[Symbol.asyncIterator]=_o.bind(void 0,e,t,{})};var xl=(e,t)=>{for(let[r,n]of Bh){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},Ah=(async()=>{})().constructor.prototype,Bh=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(Ah,e)]);var Tl=(e,t,r,n)=>{let{file:o,commandArguments:i,command:s,escapedCommand:u,startTime:a,verboseInfo:l,options:c,fileDescriptors:f}=Th(e,t,r),{subprocess:D,promise:d}=Oh({file:o,commandArguments:i,options:c,startTime:a,verboseInfo:l,command:s,escapedCommand:u,fileDescriptors:f});return D.pipe=wr.bind(void 0,{source:D,sourcePromise:d,boundOptions:{},createNested:n}),xl(D,d),V.set(D,{options:c,fileDescriptors:f}),D},Th=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=Tt(e,t,r),{file:u,commandArguments:a,options:l}=Kt(e,t,r),c=_h(l),f=Oc(c,s);return{file:u,commandArguments:a,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:c,fileDescriptors:f}},_h=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},Oh=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:s,fileDescriptors:u})=>{let a;try{a=(0,Bl.spawn)(e,t,r)}catch(p){return Bc({error:p,command:i,escapedCommand:s,fileDescriptors:u,options:r,startTime:n,verboseInfo:o})}let l=new AbortController;(0,Al.setMaxListeners)(Number.POSITIVE_INFINITY,l.signal);let c=[...a.stdio];Lc(a,u,l),$c(a,r,l);let f={},D=z();a.kill=js.bind(void 0,{kill:a.kill.bind(a),options:r,onInternalError:D,context:f,controller:l}),a.all=ol(a,r),wl(a,r),Cc(a,r);let d=Rh({subprocess:a,options:r,startTime:n,verboseInfo:o,fileDescriptors:u,originalStreams:c,command:i,escapedCommand:s,context:f,onInternalError:D,controller:l});return{subprocess:a,promise:d}},Rh=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:s,escapedCommand:u,context:a,onInternalError:l,controller:c})=>{let[f,[D,d],p,F,w]=await fl({subprocess:e,options:t,context:a,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:l,controller:c});c.abort(),l.resolve();let g=p.map((O,K)=>Q(O,t,K)),A=Q(F,t,"all"),T=Ih({errorInfo:f,exitCode:D,signal:d,stdio:g,all:A,ipcOutput:w,context:a,options:t,command:s,escapedCommand:u,startTime:r});return _e(T,n,t)},Ih=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:s,options:u,command:a,escapedCommand:l,startTime:c})=>"error"in e?Xe({error:e.error,command:a,escapedCommand:l,timedOut:s.terminationReason==="timeout",isCanceled:s.terminationReason==="cancel"||s.terminationReason==="gracefulCancel",isGracefullyCanceled:s.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof Y,isForcefullyTerminated:s.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:u,startTime:c,isSync:!1}):or({command:a,escapedCommand:l,stdio:n,all:o,ipcOutput:i,options:u,startTime:c});var _r=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,Mh(n,e[n],o)]));return{...e,...r}},Mh=(e,t,r)=>vh.has(e)&&C(t)&&C(r)?{...t,...r}:r,vh=new Set(["env",...Hr]);var ae=(e,t,r,n)=>{let o=(s,u,a)=>ae(s,u,r,a),i=(...s)=>Ph({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...s);return n!==void 0&&n(i,o,t),i},Ph=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...s)=>{if(C(i))return o(e,_r(r,i),n);let{file:u,commandArguments:a,options:l,isSync:c}=Lh({mapArguments:e,firstArgument:i,nextArguments:s,deepOptions:t,boundOptions:r});return c?Ec(u,a,l):Tl(u,a,l,o)},Lh=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=ui(t)?ai(t,r):[t,...r],[s,u,a]=ht(...i),l=_r(_r(n,o),a),{file:c=s,commandArguments:f=u,options:D=l,isSync:d=!1}=e({file:s,commandArguments:u,options:l});return{file:c,commandArguments:f,options:D,isSync:d}};var _l=({file:e,commandArguments:t})=>Rl(e,t),Ol=({file:e,commandArguments:t})=>({...Rl(e,t),isSync:!0}),Rl=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=Nh(e);return{file:r,commandArguments:n}},Nh=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split($h)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},$h=/ +/g;var Il=(e,t,r)=>{e.sync=t(jh,r),e.s=e.sync},Ml=({options:e})=>vl(e),jh=({options:e})=>({...vl(e),isSync:!0}),vl=e=>({options:{...Uh(e),...e}}),Uh=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Pl={preferLocal:!0};var Ll=ae(()=>({})),Xx=ae(()=>({isSync:!0})),Zx=ae(_l),Qx=ae(Ol),e1=ae(Ou),t1=ae(Ml,{},Pl,Il),{sendMessage:r1,getOneMessage:n1,getEachMessage:o1,getCancelSignal:i1}=wc();async function jl(e){if(e=Or.default.resolve(e),e.endsWith("package.json")&&(e=e.replace(/package\.json$/,"")),!Nl.default.existsSync(e))throw`Directory ${e} does not exist`;let t=Or.default.join($l.tmpdir(),Or.default.basename(e));try{await Ll("ray",["build","--output",t],{cwd:e})}catch{throw`Error building extension ${e}`}return t}var kr=h(require("fs"));var dt=h(require("node:process"),1);var Ul=(e=0)=>t=>`\x1B[${t+e}m`,kl=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,Gl=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,E={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},c1=Object.keys(E.modifier),kh=Object.keys(E.color),Gh=Object.keys(E.bgColor),l1=[...kh,...Gh];function Wh(){let e=new Map;for(let[t,r]of Object.entries(E)){for(let[n,o]of Object.entries(r))E[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=E[n],e.set(o[0],o[1]);Object.defineProperty(E,t,{value:r,enumerable:!1})}return Object.defineProperty(E,"codes",{value:e,enumerable:!1}),E.color.close="\x1B[39m",E.bgColor.close="\x1B[49m",E.color.ansi=Ul(),E.color.ansi256=kl(),E.color.ansi16m=Gl(),E.bgColor.ansi=Ul(10),E.bgColor.ansi256=kl(10),E.bgColor.ansi16m=Gl(10),Object.defineProperties(E,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>E.rgbToAnsi256(...E.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let u=t%36;r=Math.floor(t/36)/5,n=Math.floor(u/6)/5,o=u%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>E.ansi256ToAnsi(E.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>E.ansi256ToAnsi(E.hexToAnsi256(t)),enumerable:!1}}),E}var zh=Wh(),U=zh;var Ir=h(require("node:process"),1),zl=h(require("node:os"),1),Oo=h(require("node:tty"),1);function M(e,t=globalThis.Deno?globalThis.Deno.args:Ir.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:b}=Ir.default,Rr;M("no-color")||M("no-colors")||M("color=false")||M("color=never")?Rr=0:(M("color")||M("colors")||M("color=true")||M("color=always"))&&(Rr=1);function Vh(){if("FORCE_COLOR"in b)return b.FORCE_COLOR==="true"?1:b.FORCE_COLOR==="false"?0:b.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(b.FORCE_COLOR,10),3)}function Yh(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function qh(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=Vh();n!==void 0&&(Rr=n);let o=r?Rr:n;if(o===0)return 0;if(r){if(M("color=16m")||M("color=full")||M("color=truecolor"))return 3;if(M("color=256"))return 2}if("TF_BUILD"in b&&"AGENT_NAME"in b)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(b.TERM==="dumb")return i;if(Ir.default.platform==="win32"){let s=zl.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in b)return"GITHUB_ACTIONS"in b||"GITEA_ACTIONS"in b?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in b)||b.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in b)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(b.TEAMCITY_VERSION)?1:0;if(b.COLORTERM==="truecolor"||b.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in b){let s=Number.parseInt((b.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(b.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(b.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(b.TERM)||"COLORTERM"in b?1:i}function Wl(e,t={}){let r=qh(e,{streamIsTTY:e&&e.isTTY,...t});return Yh(r)}var Hh={stdout:Wl({isTTY:Oo.default.isatty(1)}),stderr:Wl({isTTY:Oo.default.isatty(2)})},Vl=Hh;function Yl(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function ql(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:Hl,stderr:Kl}=Vl,Ro=Symbol("GENERATOR"),Pe=Symbol("STYLER"),st=Symbol("IS_EMPTY"),Jl=["ansi","ansi","ansi256","ansi16m"],Le=Object.create(null),Kh=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=Hl?Hl.level:0;e.level=t.level===void 0?r:t.level};var Jh=e=>{let t=(...r)=>r.join(" ");return Kh(t,e),Object.setPrototypeOf(t,ut.prototype),t};function ut(e){return Jh(e)}Object.setPrototypeOf(ut.prototype,Function.prototype);for(let[e,t]of Object.entries(U))Le[e]={get(){let r=Mr(this,Mo(t.open,t.close,this[Pe]),this[st]);return Object.defineProperty(this,e,{value:r}),r}};Le.visible={get(){let e=Mr(this,this[Pe],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Io=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?U[r].ansi16m(...n):t==="ansi256"?U[r].ansi256(U.rgbToAnsi256(...n)):U[r].ansi(U.rgbToAnsi(...n)):e==="hex"?Io("rgb",t,r,...U.hexToRgb(...n)):U[r][e](...n),Xh=["rgb","hex","ansi256"];for(let e of Xh){Le[e]={get(){let{level:r}=this;return function(...n){let o=Mo(Io(e,Jl[r],"color",...n),U.color.close,this[Pe]);return Mr(this,o,this[st])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);Le[t]={get(){let{level:r}=this;return function(...n){let o=Mo(Io(e,Jl[r],"bgColor",...n),U.bgColor.close,this[Pe]);return Mr(this,o,this[st])}}}}var Zh=Object.defineProperties(()=>{},{...Le,level:{enumerable:!0,get(){return this[Ro].level},set(e){this[Ro].level=e}}}),Mo=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},Mr=(e,t,r)=>{let n=(...o)=>Qh(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,Zh),n[Ro]=e,n[Pe]=t,n[st]=r,n},Qh=(e,t)=>{if(e.level<=0||!t)return e[st]?"":t;let r=e[Pe];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=Yl(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=ql(t,o,n,i)),n+t+o};Object.defineProperties(ut.prototype,Le);var eF=ut(),F1=ut({level:Kl?Kl.level:0});var Xl=eF;var Po=h(require("node:process"),1);var at=h(require("node:process"),1);var tF=(e,t,r,n)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;let o=Object.getOwnPropertyDescriptor(e,r),i=Object.getOwnPropertyDescriptor(t,r);!rF(o,i)&&n||Object.defineProperty(e,r,i)},rF=function(e,t){return e===void 0||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},nF=(e,t)=>{let r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},oF=(e,t)=>`/* Wrapped ${e}*/
${t}`,iF=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),sF=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),uF=(e,t,r)=>{let n=r===""?"":`with ${r.trim()}() `,o=oF.bind(null,n,t.toString());Object.defineProperty(o,"name",sF);let{writable:i,enumerable:s,configurable:u}=iF;Object.defineProperty(e,"toString",{value:o,writable:i,enumerable:s,configurable:u})};function vo(e,t,{ignoreNonConfigurable:r=!1}={}){let{name:n}=e;for(let o of Reflect.ownKeys(t))tF(e,t,o,r);return nF(e,t),uF(e,t,n),e}var vr=new WeakMap,Zl=(e,t={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let r,n=0,o=e.displayName||e.name||"<anonymous>",i=function(...s){if(vr.set(i,++n),n===1)r=e.apply(this,s),e=void 0;else if(t.throw===!0)throw new Error(`Function \`${o}\` can only be called once`);return r};return vo(i,e),vr.set(i,n),i};Zl.callCount=e=>{if(!vr.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return vr.get(e)};var Ql=Zl;var ef=at.default.stderr.isTTY?at.default.stderr:at.default.stdout.isTTY?at.default.stdout:void 0,aF=ef?Ql(()=>{Sr(()=>{ef.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},tf=aF;var Pr=!1,Ne={};Ne.show=(e=Po.default.stderr)=>{e.isTTY&&(Pr=!1,e.write("\x1B[?25h"))};Ne.hide=(e=Po.default.stderr)=>{e.isTTY&&(tf(),Pr=!0,e.write("\x1B[?25l"))};Ne.toggle=(e,t)=>{e!==void 0&&(Pr=e),Pr?Ne.show(t):Ne.hide(t)};var Lo=Ne;var pt=h(No(),1);var sf=(e=0)=>t=>`\x1B[${t+e}m`,uf=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,af=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,y={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},_1=Object.keys(y.modifier),lF=Object.keys(y.color),fF=Object.keys(y.bgColor),O1=[...lF,...fF];function DF(){let e=new Map;for(let[t,r]of Object.entries(y)){for(let[n,o]of Object.entries(r))y[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=y[n],e.set(o[0],o[1]);Object.defineProperty(y,t,{value:r,enumerable:!1})}return Object.defineProperty(y,"codes",{value:e,enumerable:!1}),y.color.close="\x1B[39m",y.bgColor.close="\x1B[49m",y.color.ansi=sf(),y.color.ansi256=uf(),y.color.ansi16m=af(),y.bgColor.ansi=sf(10),y.bgColor.ansi256=uf(10),y.bgColor.ansi16m=af(10),Object.defineProperties(y,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>y.rgbToAnsi256(...y.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let u=t%36;r=Math.floor(t/36)/5,n=Math.floor(u/6)/5,o=u%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>y.ansi256ToAnsi(y.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>y.ansi256ToAnsi(y.hexToAnsi256(t)),enumerable:!1}}),y}var dF=DF(),k=dF;var $r=h(require("node:process"),1),lf=h(require("node:os"),1),$o=h(require("node:tty"),1);function v(e,t=globalThis.Deno?globalThis.Deno.args:$r.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:S}=$r.default,Nr;v("no-color")||v("no-colors")||v("color=false")||v("color=never")?Nr=0:(v("color")||v("colors")||v("color=true")||v("color=always"))&&(Nr=1);function pF(){if("FORCE_COLOR"in S)return S.FORCE_COLOR==="true"?1:S.FORCE_COLOR==="false"?0:S.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(S.FORCE_COLOR,10),3)}function mF(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function hF(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=pF();n!==void 0&&(Nr=n);let o=r?Nr:n;if(o===0)return 0;if(r){if(v("color=16m")||v("color=full")||v("color=truecolor"))return 3;if(v("color=256"))return 2}if("TF_BUILD"in S&&"AGENT_NAME"in S)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(S.TERM==="dumb")return i;if($r.default.platform==="win32"){let s=lf.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in S)return"GITHUB_ACTIONS"in S||"GITEA_ACTIONS"in S?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in S)||S.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in S)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(S.TEAMCITY_VERSION)?1:0;if(S.COLORTERM==="truecolor"||S.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in S){let s=Number.parseInt((S.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(S.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(S.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(S.TERM)||"COLORTERM"in S?1:i}function cf(e,t={}){let r=hF(e,{streamIsTTY:e&&e.isTTY,...t});return mF(r)}var FF={stdout:cf({isTTY:$o.default.isatty(1)}),stderr:cf({isTTY:$o.default.isatty(2)})},ff=FF;function Df(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function df(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:pf,stderr:mf}=ff,jo=Symbol("GENERATOR"),$e=Symbol("STYLER"),ct=Symbol("IS_EMPTY"),hf=["ansi","ansi","ansi256","ansi16m"],je=Object.create(null),gF=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=pf?pf.level:0;e.level=t.level===void 0?r:t.level};var EF=e=>{let t=(...r)=>r.join(" ");return gF(t,e),Object.setPrototypeOf(t,lt.prototype),t};function lt(e){return EF(e)}Object.setPrototypeOf(lt.prototype,Function.prototype);for(let[e,t]of Object.entries(k))je[e]={get(){let r=jr(this,ko(t.open,t.close,this[$e]),this[ct]);return Object.defineProperty(this,e,{value:r}),r}};je.visible={get(){let e=jr(this,this[$e],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Uo=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?k[r].ansi16m(...n):t==="ansi256"?k[r].ansi256(k.rgbToAnsi256(...n)):k[r].ansi(k.rgbToAnsi(...n)):e==="hex"?Uo("rgb",t,r,...k.hexToRgb(...n)):k[r][e](...n),yF=["rgb","hex","ansi256"];for(let e of yF){je[e]={get(){let{level:r}=this;return function(...n){let o=ko(Uo(e,hf[r],"color",...n),k.color.close,this[$e]);return jr(this,o,this[ct])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);je[t]={get(){let{level:r}=this;return function(...n){let o=ko(Uo(e,hf[r],"bgColor",...n),k.bgColor.close,this[$e]);return jr(this,o,this[ct])}}}}var bF=Object.defineProperties(()=>{},{...je,level:{enumerable:!0,get(){return this[jo].level},set(e){this[jo].level=e}}}),ko=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},jr=(e,t,r)=>{let n=(...o)=>SF(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,bF),n[jo]=e,n[$e]=t,n[ct]=r,n},SF=(e,t)=>{if(e.level<=0||!t)return e[ct]?"":t;let r=e[$e];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=Df(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=df(t,o,n,i)),n+t+o};Object.defineProperties(lt.prototype,je);var CF=lt(),N1=lt({level:mf?mf.level:0});var ee=CF;var P=h(require("node:process"),1);function Go(){return P.default.platform!=="win32"?P.default.env.TERM!=="linux":!!P.default.env.CI||!!P.default.env.WT_SESSION||!!P.default.env.TERMINUS_SUBLIME||P.default.env.ConEmuTask==="{cmd::Cmder}"||P.default.env.TERM_PROGRAM==="Terminus-Sublime"||P.default.env.TERM_PROGRAM==="vscode"||P.default.env.TERM==="xterm-256color"||P.default.env.TERM==="alacritty"||P.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var wF={info:ee.blue("\u2139"),success:ee.green("\u2714"),warning:ee.yellow("\u26A0"),error:ee.red("\u2716")},xF={info:ee.blue("i"),success:ee.green("\u221A"),warning:ee.yellow("\u203C"),error:ee.red("\xD7")},AF=Go()?wF:xF,ft=AF;function Wo({onlyFirst:e=!1}={}){let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var BF=Wo();function Dt(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(BF,"")}function Ff(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function gf(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function Ef(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function TF(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function yf(e,{ambiguousAsWide:t=!1}={}){return TF(e),gf(e)||Ef(e)||t&&Ff(e)?2:1}var Cf=h(Sf(),1),_F=new Intl.Segmenter,OF=/^\p{Default_Ignorable_Code_Point}$/u;function zo(e,t={}){if(typeof e!="string"||e.length===0)return 0;let{ambiguousIsNarrow:r=!0,countAnsiEscapeCodes:n=!1}=t;if(n||(e=Dt(e)),e.length===0)return 0;let o=0,i={ambiguousAsWide:!r};for(let{segment:s}of _F.segment(e)){let u=s.codePointAt(0);if(!(u<=31||u>=127&&u<=159)&&!(u>=8203&&u<=8207||u===65279)&&!(u>=768&&u<=879||u>=6832&&u<=6911||u>=7616&&u<=7679||u>=8400&&u<=8447||u>=65056&&u<=65071)&&!(u>=55296&&u<=57343)&&!(u>=65024&&u<=65039)&&!OF.test(s)){if((0,Cf.default)().test(s)){o+=2;continue}o+=yf(u,i)}}return o}function Vo({stream:e=process.stdout}={}){return!!(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var G=h(require("node:process"),1),RF=3,Yo=class{#r=0;start(){this.#r++,this.#r===1&&this.#o()}stop(){if(this.#r<=0)throw new Error("`stop` called more times than `start`");this.#r--,this.#r===0&&this.#e()}#o(){G.default.platform==="win32"||!G.default.stdin.isTTY||(G.default.stdin.setRawMode(!0),G.default.stdin.on("data",this.#n),G.default.stdin.resume())}#e(){G.default.stdin.isTTY&&(G.default.stdin.off("data",this.#n),G.default.stdin.pause(),G.default.stdin.setRawMode(!1))}#n(t){t[0]===RF&&G.default.emit("SIGINT")}},IF=new Yo,qo=IF;var MF=h(No(),1),Ho=class{#r=0;#o=!1;#e=0;#n=-1;#u=0;#t;#s;#i;#d;#m;#l;#f;#D;#h;#a;#c;color;constructor(t){typeof t=="string"&&(t={text:t}),this.#t={color:"cyan",stream:dt.default.stderr,discardStdin:!0,hideCursor:!0,...t},this.color=this.#t.color,this.spinner=this.#t.spinner,this.#m=this.#t.interval,this.#i=this.#t.stream,this.#l=typeof this.#t.isEnabled=="boolean"?this.#t.isEnabled:Vo({stream:this.#i}),this.#f=typeof this.#t.isSilent=="boolean"?this.#t.isSilent:!1,this.text=this.#t.text,this.prefixText=this.#t.prefixText,this.suffixText=this.#t.suffixText,this.indent=this.#t.indent,dt.default.env.NODE_ENV==="test"&&(this._stream=this.#i,this._isEnabled=this.#l,Object.defineProperty(this,"_linesToClear",{get(){return this.#r},set(r){this.#r=r}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#n}}),Object.defineProperty(this,"_lineCount",{get(){return this.#e}}))}get indent(){return this.#D}set indent(t=0){if(!(t>=0&&Number.isInteger(t)))throw new Error("The `indent` option must be an integer from 0 and up");this.#D=t,this.#p()}get interval(){return this.#m??this.#s.interval??100}get spinner(){return this.#s}set spinner(t){if(this.#n=-1,this.#m=void 0,typeof t=="object"){if(t.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#s=t}else if(!We())this.#s=pt.default.line;else if(t===void 0)this.#s=pt.default.dots;else if(t!=="default"&&pt.default[t])this.#s=pt.default[t];else throw new Error(`There is no built-in spinner named '${t}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#h}set text(t=""){this.#h=t,this.#p()}get prefixText(){return this.#a}set prefixText(t=""){this.#a=t,this.#p()}get suffixText(){return this.#c}set suffixText(t=""){this.#c=t,this.#p()}get isSpinning(){return this.#d!==void 0}#F(t=this.#a,r=" "){return typeof t=="string"&&t!==""?t+r:typeof t=="function"?t()+r:""}#g(t=this.#c,r=" "){return typeof t=="string"&&t!==""?r+t:typeof t=="function"?r+t():""}#p(){let t=this.#i.columns??80,r=this.#F(this.#a,"-"),n=this.#g(this.#c,"-"),o=" ".repeat(this.#D)+r+"--"+this.#h+"--"+n;this.#e=0;for(let i of Dt(o).split(`
`))this.#e+=Math.max(1,Math.ceil(zo(i,{countAnsiEscapeCodes:!0})/t))}get isEnabled(){return this.#l&&!this.#f}set isEnabled(t){if(typeof t!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#l=t}get isSilent(){return this.#f}set isSilent(t){if(typeof t!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#f=t}frame(){let t=Date.now();(this.#n===-1||t-this.#u>=this.interval)&&(this.#n=++this.#n%this.#s.frames.length,this.#u=t);let{frames:r}=this.#s,n=r[this.#n];this.color&&(n=Xl[this.color](n));let o=typeof this.#a=="string"&&this.#a!==""?this.#a+" ":"",i=typeof this.text=="string"?" "+this.text:"",s=typeof this.#c=="string"&&this.#c!==""?" "+this.#c:"";return o+n+i+s}clear(){if(!this.#l||!this.#i.isTTY)return this;this.#i.cursorTo(0);for(let t=0;t<this.#r;t++)t>0&&this.#i.moveCursor(0,-1),this.#i.clearLine(1);return(this.#D||this.lastIndent!==this.#D)&&this.#i.cursorTo(this.#D),this.lastIndent=this.#D,this.#r=0,this}render(){return this.#f?this:(this.clear(),this.#i.write(this.frame()),this.#r=this.#e,this)}start(t){return t&&(this.text=t),this.#f?this:this.#l?this.isSpinning?this:(this.#t.hideCursor&&Lo.hide(this.#i),this.#t.discardStdin&&dt.default.stdin.isTTY&&(this.#o=!0,qo.start()),this.render(),this.#d=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#i.write(`- ${this.text}
`),this)}stop(){return this.#l?(clearInterval(this.#d),this.#d=void 0,this.#n=0,this.clear(),this.#t.hideCursor&&Lo.show(this.#i),this.#t.discardStdin&&dt.default.stdin.isTTY&&this.#o&&(qo.stop(),this.#o=!1),this):this}succeed(t){return this.stopAndPersist({symbol:ft.success,text:t})}fail(t){return this.stopAndPersist({symbol:ft.error,text:t})}warn(t){return this.stopAndPersist({symbol:ft.warning,text:t})}info(t){return this.stopAndPersist({symbol:ft.info,text:t})}stopAndPersist(t={}){if(this.#f)return this;let r=t.prefixText??this.#a,n=this.#F(r," "),o=t.symbol??" ",i=t.text??this.text,u=typeof i=="string"?(o?" ":"")+i:"",a=t.suffixText??this.#c,l=this.#g(a," "),c=n+o+u+l+`
`;return this.stop(),this.#i.write(c),this}};function Ko(e){return new Ho(e)}async function wf(e,t=!1){e=Ur.default.resolve(e);let r=kr.default.statSync(e).isDirectory(),n=Ur.default.join(e,"package.json");if(e.endsWith("package.json")){if(!t){let o=Ko(`Building extension ${e}`).start();try{let i=await jl(e);e=Ur.default.join(i,"package.json")}finally{o.stop()}}return JSON.parse(kr.default.readFileSync(e).toString())}if(r&&kr.default.existsSync(n))return wf(n);throw`${e}: no package.json file found`}0&&(module.exports={getPackageJson});
