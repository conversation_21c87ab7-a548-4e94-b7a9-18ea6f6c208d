"use strict";var B=Object.create;var m=Object.defineProperty;var $=Object.getOwnPropertyDescriptor;var G=Object.getOwnPropertyNames;var P=Object.getPrototypeOf,D=Object.prototype.hasOwnProperty;var O=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),L=(n,t)=>{for(var o in t)m(n,o,{get:t[o],enumerable:!0})},A=(n,t,o,c)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of G(t))!D.call(n,a)&&a!==o&&m(n,a,{get:()=>t[a],enumerable:!(c=$(t,a))||c.enumerable});return n};var h=(n,t,o)=>(o=n!=null?B(P(n)):{},A(t||!n||!n.__esModule?m(o,"default",{value:n,enumerable:!0}):o,n)),R=n=>A(m({},"__esModule",{value:!0}),n);var N=O((X,C)=>{var j=require("node:tty"),Y=j?.WriteStream?.prototype?.hasColors?.()??!1,r=(n,t)=>{if(!Y)return a=>a;let o=`\x1B[${n}m`,c=`\x1B[${t}m`;return a=>{let g=a+"",u=g.indexOf(c);if(u===-1)return o+g+c;let y=o,d=0;for(;u!==-1;)y+=g.slice(d,u)+o,d=u+c.length,u=g.indexOf(c,d);return y+=g.slice(d)+c,y}},e={};e.reset=r(0,0);e.bold=r(1,22);e.dim=r(2,22);e.italic=r(3,23);e.underline=r(4,24);e.overline=r(53,55);e.inverse=r(7,27);e.hidden=r(8,28);e.strikethrough=r(9,29);e.black=r(30,39);e.red=r(31,39);e.green=r(32,39);e.yellow=r(33,39);e.blue=r(34,39);e.magenta=r(35,39);e.cyan=r(36,39);e.white=r(37,39);e.gray=r(90,39);e.bgBlack=r(40,49);e.bgRed=r(41,49);e.bgGreen=r(42,49);e.bgYellow=r(43,49);e.bgBlue=r(44,49);e.bgMagenta=r(45,49);e.bgCyan=r(46,49);e.bgWhite=r(47,49);e.bgGray=r(100,49);e.redBright=r(91,39);e.greenBright=r(92,39);e.yellowBright=r(93,39);e.blueBright=r(94,39);e.magentaBright=r(95,39);e.cyanBright=r(96,39);e.whiteBright=r(97,39);e.bgRedBright=r(101,49);e.bgGreenBright=r(102,49);e.bgYellowBright=r(103,49);e.bgBlueBright=r(104,49);e.bgMagentaBright=r(105,49);e.bgCyanBright=r(106,49);e.bgWhiteBright=r(107,49);C.exports=e});var J={};L(J,{BaseCommand:()=>w});module.exports=R(J);var l=require("@oclif/core");var p=h(require("node:fs")),x=h(require("node:path")),T=h(require("node:os"));var I={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],S="e69bae0ec90f5e838555",i={},E;function v(n){E=n;try{i=JSON.parse(p.readFileSync(x.join(_(),"config.json"),"utf8"))}catch(t){if(t instanceof Error&&t.code==="ENOENT")return;throw new Error(`Failed to read config file: ${t}`)}}function F(n){switch(n){case"raycastApiURL":return process.env.RAY_APIURL||i.APIURL||I.url;case"raycastAccessToken":return process.env.RAY_TOKEN||i.Token||i.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||i.ClientID||I.clientID;case"githubClientId":return process.env.RAY_GithubClientID||i.GithubClientID||S;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||i.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof i.Target<"u"?i.Target:b(process.platform==="win32"?"x":"release")}}function b(n){switch(n){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return F("flavorName")}}function U(){let n=b(E);return n==""?"raycast":`raycast-${n}`}function _(){let n=x.join(T.default.homedir(),".config",U());return p.mkdirSync(n,{recursive:!0}),n}var s=h(N());var z=(0,s.blue)((0,s.dim)("internal only"));var f={wait:`\u{1F550}${(0,s.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,s.cyan)("info")}  - `,success:`\u2705${(0,s.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,s.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,s.red)("error")}  - `,event:`\u26A1\uFE0F${(0,s.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,s.yellowBright)("plan")}  - `},M=!0;function k(n,t){n||(f.wait=`${(0,s.blue)("wait")}  - `,f.info=`${(0,s.cyan)("info")}  - `,f.success=`${(0,s.green)("ready")}  - `,f.warn=`${(0,s.yellow)("warn")}  - `,f.error=`${(0,s.red)("error")}  - `,f.event=`${(0,s.magenta)("event")}  - `,f.paymentPrompt=`${(0,s.yellowBright)("plan")}  - `),t&&(M=!1)}var w=class extends l.Command{static baseFlags={"exit-on-error":l.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:l.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:l.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":l.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:l.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:t,flags:o}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=o,this.args=t,v(this.flags.target),k(this.flags.emoji,this.flags["non-interactive"])}error(t,o){return o?.message&&t instanceof Error&&(t.message=`${o.message} (${t.message})`,delete o.message),super.error(t,o)}async catch(t){return super.catch(t)}async finally(t){return super.finally(t)}};0&&(module.exports={BaseCommand});
