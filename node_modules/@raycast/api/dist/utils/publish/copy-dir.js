"use strict";var l=Object.create;var s=Object.defineProperty;var m=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var h=Object.getPrototypeOf,S=Object.prototype.hasOwnProperty;var g=(t,i)=>{for(var r in i)s(t,r,{get:i[r],enumerable:!0})},a=(t,i,r,n)=>{if(i&&typeof i=="object"||typeof i=="function")for(let c of p(i))!S.call(t,c)&&c!==r&&s(t,c,{get:()=>i[c],enumerable:!(n=m(i,c))||n.enumerable});return t};var d=(t,i,r)=>(r=t!=null?l(h(t)):{},a(i||!t||!t.__esModule?s(r,"default",{value:t,enumerable:!0}):r,t)),v=t=>a(s({},"__esModule",{value:!0}),t);var w={};g(w,{copyDir:()=>u});module.exports=v(w);var y=d(require("path")),e=d(require("fs"));function u(t,i,r=[".git",".github","node_modules","raycast-env.d.ts",".raycast-swift-build",".swiftpm","compiled_raycast_swift"]){let n=e.default.readdirSync(t);try{e.default.mkdirSync(i,{recursive:!0})}catch{}for(let c of n){let o=y.default.join(t,c),f=y.default.join(i,c);if(e.default.lstatSync(o).isDirectory()){if(!r.includes(c)){try{e.default.mkdirSync(f,{recursive:!0})}catch{}u(o,f,r)}}else r.includes(c)||e.default.copyFileSync(o,f)}}0&&(module.exports={copyDir});
