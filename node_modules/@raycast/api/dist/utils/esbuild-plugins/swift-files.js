"use strict";var vf=Object.create;var St=Object.defineProperty;var Mf=Object.getOwnPropertyDescriptor;var Pf=Object.getOwnPropertyNames;var $f=Object.getPrototypeOf,Lf=Object.prototype.hasOwnProperty;var L=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Nf=(e,t)=>{for(var r in t)St(e,r,{get:t[r],enumerable:!0})},ti=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Pf(t))!Lf.call(e,o)&&o!==r&&St(e,o,{get:()=>t[o],enumerable:!(n=Mf(t,o))||n.enumerable});return e};var E=(e,t,r)=>(r=e!=null?vf($f(e)):{},ti(t||!e||!e.__esModule?St(r,"default",{value:e,enumerable:!0}):r,e)),jf=e=>ti(St({},"__esModule",{value:!0}),e);var ji=L((wE,Ni)=>{Ni.exports=Li;Li.sync=ID;var Pi=require("fs");function RD(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function $i(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:RD(t,r)}function Li(e,t,r){Pi.stat(e,function(n,o){r(n,n?!1:$i(o,e,t))})}function ID(e,t){return $i(Pi.statSync(e),e,t)}});var zi=L((CE,Wi)=>{Wi.exports=ki;ki.sync=vD;var Ui=require("fs");function ki(e,t,r){Ui.stat(e,function(n,o){r(n,n?!1:Gi(o,t))})}function vD(e,t){return Gi(Ui.statSync(e),t)}function Gi(e,t){return e.isFile()&&MD(e,t)}function MD(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),s=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),a=parseInt("100",8),u=parseInt("010",8),l=parseInt("001",8),c=a|u,f=r&l||r&u&&o===s||r&a&&n===i||r&c&&i===0;return f}});var Yi=L((AE,Vi)=>{var xE=require("fs"),$t;process.platform==="win32"||global.TESTING_WINDOWS?$t=ji():$t=zi();Vi.exports=on;on.sync=PD;function on(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){on(e,t||{},function(i,s){i?o(i):n(s)})})}$t(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function PD(e,t){try{return $t.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var Qi=L((BE,Zi)=>{var Be=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",qi=require("path"),$D=Be?";":":",Hi=Yi(),Ki=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),Xi=(e,t)=>{let r=t.colon||$D,n=e.match(/\//)||Be&&e.match(/\\/)?[""]:[...Be?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=Be?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=Be?o.split(r):[""];return Be&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},Ji=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=Xi(e,t),s=[],a=l=>new Promise((c,f)=>{if(l===n.length)return t.all&&s.length?c(s):f(Ki(e));let D=n[l],d=/^".*"$/.test(D)?D.slice(1,-1):D,p=qi.join(d,e),g=!d&&/^\.[\\\/]/.test(e)?e.slice(0,2)+p:p;c(u(g,l,0))}),u=(l,c,f)=>new Promise((D,d)=>{if(f===o.length)return D(a(c+1));let p=o[f];Hi(l+p,{pathExt:i},(g,B)=>{if(!g&&B)if(t.all)s.push(l+p);else return D(l+p);return D(u(l,c,f+1))})});return r?a(0).then(l=>r(null,l),r):a(0)},LD=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=Xi(e,t),i=[];for(let s=0;s<r.length;s++){let a=r[s],u=/^".*"$/.test(a)?a.slice(1,-1):a,l=qi.join(u,e),c=!u&&/^\.[\\\/]/.test(e)?e.slice(0,2)+l:l;for(let f=0;f<n.length;f++){let D=c+n[f];try{if(Hi.sync(D,{pathExt:o}))if(t.all)i.push(D);else return D}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw Ki(e)};Zi.exports=Ji;Ji.sync=LD});var ts=L((TE,sn)=>{"use strict";var es=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};sn.exports=es;sn.exports.default=es});var is=L((_E,os)=>{"use strict";var rs=require("path"),ND=Qi(),jD=ts();function ns(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let s;try{s=ND.sync(e.command,{path:r[jD({env:r})],pathExt:t?rs.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return s&&(s=rs.resolve(o?e.options.cwd:"",s)),s}function UD(e){return ns(e)||ns(e,!0)}os.exports=UD});var ss=L((OE,un)=>{"use strict";var an=/([()\][%!^"`<>&|;, *?])/g;function kD(e){return e=e.replace(an,"^$1"),e}function GD(e,t){return e=`${e}`,e=e.replace(/(\\*)"/g,'$1$1\\"'),e=e.replace(/(\\*)$/,"$1$1"),e=`"${e}"`,e=e.replace(an,"^$1"),t&&(e=e.replace(an,"^$1")),e}un.exports.command=kD;un.exports.argument=GD});var us=L((RE,as)=>{"use strict";as.exports=/^#!(.*)/});var ls=L((IE,cs)=>{"use strict";var WD=us();cs.exports=(e="")=>{let t=e.match(WD);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var Ds=L((vE,fs)=>{"use strict";var cn=require("fs"),zD=ls();function VD(e){let r=Buffer.alloc(150),n;try{n=cn.openSync(e,"r"),cn.readSync(n,r,0,150,0),cn.closeSync(n)}catch{}return zD(r.toString())}fs.exports=VD});var hs=L((ME,ms)=>{"use strict";var YD=require("path"),ds=is(),ps=ss(),qD=Ds(),HD=process.platform==="win32",KD=/\.(?:com|exe)$/i,XD=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function JD(e){e.file=ds(e);let t=e.file&&qD(e.file);return t?(e.args.unshift(e.file),e.command=t,ds(e)):e.file}function ZD(e){if(!HD)return e;let t=JD(e),r=!KD.test(t);if(e.options.forceShell||r){let n=XD.test(t);e.command=YD.normalize(e.command),e.command=ps.command(e.command),e.args=e.args.map(i=>ps.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function QD(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:ZD(n)}ms.exports=QD});var Es=L((PE,gs)=>{"use strict";var ln=process.platform==="win32";function fn(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function ed(e,t){if(!ln)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=Fs(o,t,"spawn");if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function Fs(e,t){return ln&&e===1&&!t.file?fn(t.original,"spawn"):null}function td(e,t){return ln&&e===1&&!t.file?fn(t.original,"spawnSync"):null}gs.exports={hookChildProcess:ed,verifyENOENT:Fs,verifyENOENTSync:td,notFoundError:fn}});var Ss=L(($E,Te)=>{"use strict";var ys=require("child_process"),Dn=hs(),dn=Es();function bs(e,t,r){let n=Dn(e,t,r),o=ys.spawn(n.command,n.args,n.options);return dn.hookChildProcess(o,n),o}function rd(e,t,r){let n=Dn(e,t,r),o=ys.spawnSync(n.command,n.args,n.options);return o.error=o.error||dn.verifyENOENTSync(o.status,n),o}Te.exports=bs;Te.exports.spawn=bs;Te.exports.sync=rd;Te.exports._parse=Dn;Te.exports._enoent=dn});var Wl=L((w1,Gl)=>{var Kh=require("node:tty"),Xh=Kh?.WriteStream?.prototype?.hasColors?.()??!1,F=(e,t)=>{if(!Xh)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,u=0;for(;s!==-1;)a+=i.slice(u,s)+r,u=s+n.length,s=i.indexOf(n,u);return a+=i.slice(u)+n,a}},m={};m.reset=F(0,0);m.bold=F(1,22);m.dim=F(2,22);m.italic=F(3,23);m.underline=F(4,24);m.overline=F(53,55);m.inverse=F(7,27);m.hidden=F(8,28);m.strikethrough=F(9,29);m.black=F(30,39);m.red=F(31,39);m.green=F(32,39);m.yellow=F(33,39);m.blue=F(34,39);m.magenta=F(35,39);m.cyan=F(36,39);m.white=F(37,39);m.gray=F(90,39);m.bgBlack=F(40,49);m.bgRed=F(41,49);m.bgGreen=F(42,49);m.bgYellow=F(43,49);m.bgBlue=F(44,49);m.bgMagenta=F(45,49);m.bgCyan=F(46,49);m.bgWhite=F(47,49);m.bgGray=F(100,49);m.redBright=F(91,39);m.greenBright=F(92,39);m.yellowBright=F(93,39);m.blueBright=F(94,39);m.magentaBright=F(95,39);m.cyanBright=F(96,39);m.whiteBright=F(97,39);m.bgRedBright=F(101,49);m.bgGreenBright=F(102,49);m.bgYellowBright=F(103,49);m.bgBlueBright=F(104,49);m.bgMagentaBright=F(105,49);m.bgCyanBright=F(106,49);m.bgWhiteBright=F(107,49);Gl.exports=m});var af=L((G1,EF)=>{EF.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var Go=L((W1,cf)=>{"use strict";var Gr=Object.assign({},af()),uf=Object.keys(Gr);Object.defineProperty(Gr,"random",{get(){let e=Math.floor(Math.random()*uf.length),t=uf[e];return Gr[t]}});cf.exports=Gr});var Af=L((f3,xf)=>{xf.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var XF={};Nf(XF,{swiftFiles:()=>VF});module.exports=jf(XF);var b=E(require("node:fs")),I=E(require("node:path"));function O(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var ri=require("node:url"),Ce=(e,t)=>{let r=Kr(Uf(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},Uf=e=>Hr(e)?e.toString():e,Hr=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,Kr=e=>e instanceof URL?(0,ri.fileURLToPath)(e):e;var wt=(e,t=[],r={})=>{let n=Ce(e,"First argument"),[o,i]=O(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(u=>typeof u=="object"&&u!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let s=o.map(String),a=s.find(u=>u.includes("\0"));if(a!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${a}`);if(!O(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,s,i]};var fi=require("node:child_process");var ni=require("node:string_decoder"),{toString:oi}=Object.prototype,ii=e=>oi.call(e)==="[object ArrayBuffer]",v=e=>oi.call(e)==="[object Uint8Array]",se=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),kf=new TextEncoder,si=e=>kf.encode(e),Gf=new TextDecoder,Ct=e=>Gf.decode(e),ai=(e,t)=>Wf(e,t).join(""),Wf=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new ni.StringDecoder(t),n=e.map(i=>typeof i=="string"?si(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},qe=e=>e.length===1&&v(e[0])?e[0]:Xr(zf(e)),zf=e=>e.map(t=>typeof t=="string"?si(t):t),Xr=e=>{let t=new Uint8Array(Vf(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},Vf=e=>{let t=0;for(let r of e)t+=r.length;return t};var Di=e=>Array.isArray(e)&&Array.isArray(e.raw),di=(e,t)=>{let r=[];for(let[i,s]of e.entries())r=Yf({templates:e,expressions:t,tokens:r,index:i,template:s});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},Yf=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:s,trailingWhitespaces:a}=qf(o,e.raw[n]),u=ci(r,i,s);if(n===t.length)return u;let l=t[n],c=Array.isArray(l)?l.map(f=>li(f)):[li(l)];return ci(u,c,a)},qf=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=ui.has(t[0]);for(let s=0,a=0;s<e.length;s+=1,a+=1){let u=t[a];if(ui.has(u))n!==s&&r.push(e.slice(n,s)),n=s+1;else if(u==="\\"){let l=t[a+1];l==="u"&&t[a+2]==="{"?a=t.indexOf("}",a+3):a+=Hf[l]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},ui=new Set([" ","	","\r",`
`]),Hf={x:3,u:5},ci=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],li=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(O(e)&&("stdout"in e||"isMaxBuffer"in e))return Kf(e);throw e instanceof fi.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},Kf=({stdout:e})=>{if(typeof e=="string")return e;if(v(e))return Ct(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var wc=require("node:child_process");var mi=require("node:util");var xt=E(require("node:process"),1),q=e=>At.includes(e),At=[xt.default.stdin,xt.default.stdout,xt.default.stderr],G=["stdin","stdout","stderr"],Bt=e=>G[e]??`stdio[${e}]`;var hi=e=>{let t={...e};for(let r of Qr)t[r]=Jr(e,r);return t},Jr=(e,t)=>{let r=Array.from({length:Xf(e)+1}),n=Jf(e[t],r,t);return rD(n,t)},Xf=({stdio:e})=>Array.isArray(e)?Math.max(e.length,G.length):G.length,Jf=(e,t,r)=>O(e)?Zf(e,t,r):t.fill(e),Zf=(e,t,r)=>{for(let n of Object.keys(e).sort(Qf))for(let o of eD(n,r,t))t[o]=e[n];return t},Qf=(e,t)=>pi(e)<pi(t)?1:-1,pi=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,eD=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=Zr(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},Zr=e=>{if(e==="all")return e;if(G.includes(e))return G.indexOf(e);let t=tD.exec(e);if(t!==null)return Number(t[1])},tD=/^fd(\d+)$/,rD=(e,t)=>e.map(r=>r===void 0?oD[t]:r),nD=(0,mi.debuglog)("execa").enabled?"full":"none",oD={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:nD,stripFinalNewline:!0},Qr=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],ae=(e,t)=>t==="ipc"?e.at(-1):e[t];var xe=({verbose:e},t)=>en(e,t)!=="none",Ae=({verbose:e},t)=>!["none","short"].includes(en(e,t)),Fi=({verbose:e},t)=>{let r=en(e,t);return Tt(r)?r:void 0},en=(e,t)=>t===void 0?iD(e):ae(e,t),iD=e=>e.find(t=>Tt(t))??_t.findLast(t=>e.includes(t)),Tt=e=>typeof e=="function",_t=["none","short","full"];var Ii=require("node:util");var gi=require("node:process"),Ei=require("node:util"),yi=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>fD(bi(i))).join(" ");return{command:n,escapedCommand:o}},He=e=>(0,Ei.stripVTControlCharacters)(e).split(`
`).map(t=>bi(t)).join(`
`),bi=e=>e.replaceAll(uD,t=>sD(t)),sD=e=>{let t=cD[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=lD?`\\u${n.padStart(4,"0")}`:`\\U${n}`},aD=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},uD=aD(),cD={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},lD=65535,fD=e=>DD.test(e)?e:gi.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,DD=/^[\w./-]+$/;var tn=E(require("node:process"),1);function Ke(){let{env:e}=tn.default,{TERM:t,TERM_PROGRAM:r}=e;return tn.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Si={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},wi={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},dD={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},pD={...Si,...wi},mD={...Si,...dD},hD=Ke(),FD=hD?pD:mD,Ot=FD,hg=Object.entries(wi);var Ci=E(require("node:tty"),1),gD=Ci.default?.WriteStream?.prototype?.hasColors?.()??!1,h=(e,t)=>{if(!gD)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,u=0;for(;s!==-1;)a+=i.slice(u,s)+r,u=s+n.length,s=i.indexOf(n,u);return a+=i.slice(u)+n,a}},gg=h(0,0),xi=h(1,22),Eg=h(2,22),yg=h(3,23),bg=h(4,24),Sg=h(53,55),wg=h(7,27),Cg=h(8,28),xg=h(9,29),Ag=h(30,39),Bg=h(31,39),Tg=h(32,39),_g=h(33,39),Og=h(34,39),Rg=h(35,39),Ig=h(36,39),vg=h(37,39),Rt=h(90,39),Mg=h(40,49),Pg=h(41,49),$g=h(42,49),Lg=h(43,49),Ng=h(44,49),jg=h(45,49),Ug=h(46,49),kg=h(47,49),Gg=h(100,49),Ai=h(91,39),Wg=h(92,39),Bi=h(93,39),zg=h(94,39),Vg=h(95,39),Yg=h(96,39),qg=h(97,39),Hg=h(101,49),Kg=h(102,49),Xg=h(103,49),Jg=h(104,49),Zg=h(105,49),Qg=h(106,49),eE=h(107,49);var Oi=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:s=!0}})=>{let a=ED(r),u=yD[e]({failed:i,reject:s,piped:n}),l=bD[e]({reject:s});return`${Rt(`[${a}]`)} ${Rt(`[${o}]`)} ${l(u)} ${l(t)}`},ED=e=>`${It(e.getHours(),2)}:${It(e.getMinutes(),2)}:${It(e.getSeconds(),2)}.${It(e.getMilliseconds(),3)}`,It=(e,t)=>String(e).padStart(t,"0"),Ti=({failed:e,reject:t})=>e?t?Ot.cross:Ot.warning:Ot.tick,yD={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:Ti,duration:Ti},_i=e=>e,bD={command:()=>xi,output:()=>_i,ipc:()=>_i,error:({reject:e})=>e?Ai:Bi,duration:()=>Rt};var Ri=(e,t,r)=>{let n=Fi(t,r);return e.map(({verboseLine:o,verboseObject:i})=>SD(o,i,n)).filter(o=>o!==void 0).map(o=>wD(o)).join("")},SD=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},wD=e=>e.endsWith(`
`)?e:`${e}
`;var ee=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=CD({type:e,result:o,verboseInfo:n}),s=xD(t,i),a=Ri(s,n,r);a!==""&&console.warn(a.slice(0,-1))},CD=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),xD=(e,t)=>e.split(`
`).map(r=>AD({...t,message:r})),AD=e=>({verboseLine:Oi(e),verboseObject:e}),vt=e=>{let t=typeof e=="string"?e:(0,Ii.inspect)(e);return He(t).replaceAll("	"," ".repeat(BD))},BD=2;var vi=(e,t)=>{xe(t)&&ee({type:"command",verboseMessage:e,verboseInfo:t})};var Mi=(e,t,r)=>{OD(e);let n=TD(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},TD=e=>xe({verbose:e})?_D++:void 0,_D=0n,OD=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!_t.includes(t)&&!Tt(t)){let r=_t.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var rn=require("node:process"),Mt=()=>rn.hrtime.bigint(),nn=e=>Number(rn.hrtime.bigint()-e)/1e6;var Pt=(e,t,r)=>{let n=Mt(),{command:o,escapedCommand:i}=yi(e,t),s=Jr(r,"verbose"),a=Mi(s,i,{...r});return vi(i,a),{command:o,escapedCommand:i,startTime:n,verboseInfo:a}};var Ha=E(require("node:path"),1),Tn=E(require("node:process"),1),Ka=E(Ss(),1);var Xe=E(require("node:process"),1),fe=E(require("node:path"),1);function Lt(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var ws=require("node:util"),mn=require("node:child_process"),pn=E(require("node:path"),1),Cs=require("node:url"),NE=(0,ws.promisify)(mn.execFile);function Nt(e){return e instanceof URL?(0,Cs.fileURLToPath)(e):e}function xs(e){return{*[Symbol.iterator](){let t=pn.default.resolve(Nt(e)),r;for(;r!==t;)yield t,r=t,t=pn.default.resolve(t,"..")}}}var jE=10*1024*1024;var nd=({cwd:e=Xe.default.cwd(),path:t=Xe.default.env[Lt()],preferLocal:r=!0,execPath:n=Xe.default.execPath,addExecPath:o=!0}={})=>{let i=fe.default.resolve(Nt(e)),s=[],a=t.split(fe.default.delimiter);return r&&od(s,a,i),o&&id(s,a,n,i),t===""||t===fe.default.delimiter?`${s.join(fe.default.delimiter)}${t}`:[...s,t].join(fe.default.delimiter)},od=(e,t,r)=>{for(let n of xs(r)){let o=fe.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},id=(e,t,r,n)=>{let o=fe.default.resolve(n,Nt(r),"..");t.includes(o)||e.push(o)},As=({env:e=Xe.default.env,...t}={})=>{e={...e};let r=Lt({env:e});return t.path=e[r],e[r]=nd(t),e};var Gs=require("node:timers/promises");var Bs=(e,t,r)=>{let n=r?Ze:Je,o=e instanceof H?{}:{cause:e};return new n(t,o)},H=class extends Error{},Ts=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Os,{value:!0,writable:!1,enumerable:!1,configurable:!1})},_s=e=>jt(e)&&Os in e,Os=Symbol("isExecaError"),jt=e=>Object.prototype.toString.call(e)==="[object Error]",Je=class extends Error{};Ts(Je,Je.name);var Ze=class extends Error{};Ts(Ze,Ze.name);var _e=require("node:os");var $s=require("node:os");var Rs=()=>{let e=vs-Is+1;return Array.from({length:e},sd)},sd=(e,t)=>({name:`SIGRT${t+1}`,number:Is+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),Is=34,vs=64;var Ps=require("node:os");var Ms=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var hn=()=>{let e=Rs();return[...Ms,...e].map(ad)},ad=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:s}}=Ps.constants,a=s!==void 0;return{name:e,number:a?s:t,description:r,supported:a,action:n,forced:o,standard:i}};var ud=()=>{let e=hn();return Object.fromEntries(e.map(cd))},cd=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s}],Ls=ud(),ld=()=>{let e=hn(),t=65,r=Array.from({length:t},(n,o)=>fd(o,e));return Object.assign({},...r)},fd=(e,t)=>{let r=Dd(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:s,forced:a,standard:u}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:s,forced:a,standard:u}}},Dd=(e,t)=>{let r=t.find(({name:n})=>$s.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},ZE=ld();var js=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return ks(e,t)},Us=e=>e===0?e:ks(e,"`subprocess.kill()`'s argument"),ks=(e,t)=>{if(Number.isInteger(e))return dd(e,t);if(typeof e=="string")return md(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${Fn()}`)},dd=(e,t)=>{if(Ns.has(e))return Ns.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${Fn()}`)},pd=()=>new Map(Object.entries(_e.constants.signals).reverse().map(([e,t])=>[t,e])),Ns=pd(),md=(e,t)=>{if(e in _e.constants.signals)return e;throw e.toUpperCase()in _e.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${Fn()}`)},Fn=()=>`Available signal names: ${hd()}.
Available signal numbers: ${Fd()}.`,hd=()=>Object.keys(_e.constants.signals).sort().map(e=>`'${e}'`).join(", "),Fd=()=>[...new Set(Object.values(_e.constants.signals).sort((e,t)=>e-t))].join(", "),Ut=e=>Ls[e].description;var Ws=e=>{if(e===!1)return e;if(e===!0)return gd;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},gd=1e3*5,zs=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},s,a)=>{let{signal:u,error:l}=Ed(s,a,r);yd(l,n);let c=e(u);return bd({kill:e,signal:u,forceKillAfterDelay:t,killSignal:r,killResult:c,context:o,controller:i}),c},Ed=(e,t,r)=>{let[n=r,o]=jt(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!jt(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Us(n),error:o}},yd=(e,t)=>{e!==void 0&&t.reject(e)},bd=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:s})=>{t===n&&o&&gn({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:s.signal})},gn=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,Gs.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var Vs=require("node:events"),kt=async(e,t)=>{e.aborted||await(0,Vs.once)(e,"abort",{signal:t})};var Ys=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},qs=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[Sd(e,t,n,o)],Sd=async(e,t,r,{signal:n})=>{throw await kt(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var Aa=require("node:timers/promises");var Ca=require("node:util");var Oe=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{wd(e,t,r),En(e,t,n)},wd=(e,t,r)=>{if(!r)throw new Error(`${K(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},En=(e,t,r)=>{if(!r)throw new Error(`${K(e,t)} cannot be used: the ${De(t)} has already exited or disconnected.`)},Hs=e=>{throw new Error(`${K("getOneMessage",e)} could not complete: the ${De(e)} exited or disconnected.`)},Ks=e=>{throw new Error(`${K("sendMessage",e)} failed: the ${De(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${K("getOneMessage",e)},
	${K("sendMessage",e,"message, {strict: true}")},
]);`)},Gt=(e,t)=>new Error(`${K("sendMessage",t)} failed when sending an acknowledgment response to the ${De(t)}.`,{cause:e}),Xs=e=>{throw new Error(`${K("sendMessage",e)} failed: the ${De(e)} is not listening to incoming messages.`)},Js=e=>{throw new Error(`${K("sendMessage",e)} failed: the ${De(e)} exited without listening to incoming messages.`)},Zs=()=>new Error(`\`cancelSignal\` aborted: the ${De(!0)} disconnected.`),Qs=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},ea=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${K(t,r)} cannot be used: the ${De(r)} is disconnecting.`,{cause:e})},ta=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(Cd(e))throw new Error(`${K(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},Cd=({code:e,message:t})=>xd.has(e)||Ad.some(r=>t.includes(r)),xd=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),Ad=["could not be cloned","circular structure","call stack size exceeded"],K=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${Bd(t)}${e}(${r})`,Bd=e=>e?"":"subprocess.",De=e=>e?"parent process":"subprocess",Re=e=>{e.connected&&e.disconnect()};var te=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var zt=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=re.get(e),i=ra(o,t,!0),s=e.stdio[i];if(s===null)throw new TypeError(na(i,t,n,!0));return s},Ie=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=re.get(e),i=ra(o,t,!1),s=i==="all"?e.all:e.stdio[i];if(s==null)throw new TypeError(na(i,t,n,!1));return s},re=new WeakMap,ra=(e,t,r)=>{let n=Td(t,r);return _d(n,t,r,e),n},Td=(e,t)=>{let r=Zr(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${Qe(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},_d=(e,t,r,n)=>{let o=n[oa(e)];if(o===void 0)throw new TypeError(`"${Qe(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${Qe(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${Qe(r)}" must not be ${t}. It must be a writable stream, not readable.`)},na=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=Od(e,r);return`The "${o}: ${Wt(i)}" option is incompatible with using "${Qe(n)}: ${Wt(t)}".
Please set this option with "pipe" instead.`},Od=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=oa(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},oa=e=>e==="all"?1:e,Qe=e=>e?"to":"from",Wt=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var Fa=require("node:events");var ia=require("node:events"),ge=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,ia.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var ha=require("node:events");var ua=require("node:events"),ca=require("node:timers/promises");var Vt=(e,t)=>{t&&yn(e)},yn=e=>{e.refCounted()},Yt=(e,t)=>{t&&bn(e)},bn=e=>{e.unrefCounted()},sa=(e,t)=>{t&&(bn(e),bn(e))},aa=(e,t)=>{t&&(yn(e),yn(e))};var la=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(da(o)||ma(o))return;qt.has(e)||qt.set(e,[]);let i=qt.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await pa(e,n,o),await ca.scheduler.yield();let s=await Da({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",s),n.emit("message:done")}},fa=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{Sn();let i=qt.get(e);for(;i?.length>0;)await(0,ua.once)(n,"message:done");e.removeListener("message",o),aa(t,r),n.connected=!1,n.emit("disconnect")},qt=new WeakMap;var de=(e,t,r)=>{if(Ht.has(e))return Ht.get(e);let n=new ha.EventEmitter;return n.connected=!0,Ht.set(e,n),Rd({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},Ht=new WeakMap,Rd=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=la.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",fa.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),sa(r,n)},Kt=e=>{let t=Ht.get(e);return t===void 0?e.channel!==null:t.connected};var ga=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=de(e,t,r),s=Zt(e,i);return{id:Id++,type:Jt,message:n,hasListeners:s}},Id=0n,Ea=(e,t)=>{if(!(t?.type!==Jt||t.hasListeners))for(let{id:r}of e)r!==void 0&&Xt[r].resolve({isDeadlock:!0,hasListeners:!1})},Da=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==Jt||!t.connected)return e;let{id:i,message:s}=e,a={id:i,type:ba,message:Zt(t,o)};try{await Qt({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},a)}catch(u){o.emit("strict:error",u)}return s},da=e=>{if(e?.type!==ba)return!1;let{id:t,message:r}=e;return Xt[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},ya=async(e,t,r)=>{if(e?.type!==Jt)return;let n=te();Xt[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:s}=await Promise.race([n,vd(t,r,o)]);i&&Ks(r),s||Xs(r)}finally{o.abort(),delete Xt[e.id]}},Xt={},vd=async(e,t,{signal:r})=>{ge(e,1,r),await(0,Fa.once)(e,"disconnect",{signal:r}),Js(t)},Jt="execa:ipc:request",ba="execa:ipc:response";var Sa=(e,t,r)=>{et.has(e)||et.set(e,new Set);let n=et.get(e),o=te(),i=r?t.id:void 0,s={onMessageSent:o,id:i};return n.add(s),{outgoingMessages:n,outgoingMessage:s}},wa=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},pa=async(e,t,r)=>{for(;!Zt(e,t)&&et.get(e)?.size>0;){let n=[...et.get(e)];Ea(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},et=new WeakMap,Zt=(e,t)=>t.listenerCount("message")>Md(e),Md=e=>re.has(e)&&!ae(re.get(e).options.buffer,"ipc")?1:0;var Qt=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let s="sendMessage";return Oe({methodName:s,isSubprocess:r,ipc:n,isConnected:e.connected}),Pd({anyProcess:e,channel:t,methodName:s,isSubprocess:r,message:o,strict:i})},Pd=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let s=ga({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),a=Sa(e,s,i);try{await Cn({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:s,message:o})}catch(u){throw Re(e),u}finally{wa(a)}},Cn=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=$d(e);try{await Promise.all([ya(n,e,r),i(n)])}catch(s){throw ea({error:s,methodName:t,isSubprocess:r}),ta({error:s,methodName:t,isSubprocess:r,message:o}),s}},$d=e=>{if(wn.has(e))return wn.get(e);let t=(0,Ca.promisify)(e.send.bind(e));return wn.set(e,t),t},wn=new WeakMap;var Ba=(e,t)=>{let r="cancelSignal";return En(r,!1,e.connected),Cn({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:_a,message:t},message:t})},Ta=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await Ld({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),xn.signal),Ld=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!xa){if(xa=!0,!n){Qs();return}if(t===null){Sn();return}de(e,t,r),await Aa.scheduler.yield()}},xa=!1,ma=e=>e?.type!==_a?!1:(xn.abort(e.message),!0),_a="execa:ipc:cancel",Sn=()=>{xn.abort(Zs())},xn=new AbortController;var Oa=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},Ra=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[Nd({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],Nd=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await kt(t,o);let i=jd(t);throw await Ba(e,i),gn({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},jd=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var Ia=require("node:timers/promises");var va=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},Ma=(e,t,r,n)=>t===0||t===void 0?[]:[Ud(e,t,r,n)],Ud=async(e,t,r,{signal:n})=>{throw await(0,Ia.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new H};var er=require("node:process"),An=E(require("node:path"),1);var Pa=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},$a=(e,t,{node:r=!1,nodePath:n=er.execPath,nodeOptions:o=er.execArgv.filter(u=>!u.startsWith("--inspect")),cwd:i,execPath:s,...a})=>{if(s!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let u=Ce(n,'The "nodePath" option'),l=An.default.resolve(i,u),c={...a,nodePath:l,node:r,cwd:i};if(!r)return[e,t,c];if(An.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[l,[...o,e,...t],{ipc:!0,...c,shell:!1}]};var La=require("node:v8"),Na=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");Wd[r](e)}},kd=e=>{try{(0,La.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},Gd=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},Wd={advanced:kd,json:Gd},ja=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var ka=({encoding:e})=>{if(Bn.has(e))return;let t=Vd(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${tr(e)}\`.
Please rename it to ${tr(t)}.`);let r=[...Bn].map(n=>tr(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${tr(e)}\`.
Please rename it to one of: ${r}.`)},zd=new Set(["utf8","utf16le"]),U=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),Bn=new Set([...zd,...U]),Vd=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Ua)return Ua[t];if(Bn.has(t))return t},Ua={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},tr=e=>typeof e=="string"?`"${e}"`:String(e);var Ga=require("node:fs"),Wa=E(require("node:path"),1),za=E(require("node:process"),1);var Va=(e=Ya())=>{let t=Ce(e,'The "cwd" option');return Wa.default.resolve(t)},Ya=()=>{try{return za.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},qa=(e,t)=>{if(t===Ya())return e;let r;try{r=(0,Ga.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var rr=(e,t,r)=>{r.cwd=Va(r.cwd);let[n,o,i]=$a(e,t,r),{command:s,args:a,options:u}=Ka.default._parse(n,o,i),l=hi(u),c=Yd(l);return va(c),ka(c),Na(c),Ys(c),Oa(c),c.shell=Kr(c.shell),c.env=qd(c),c.killSignal=js(c.killSignal),c.forceKillAfterDelay=Ws(c.forceKillAfterDelay),c.lines=c.lines.map((f,D)=>f&&!U.has(c.encoding)&&c.buffer[D]),Tn.default.platform==="win32"&&Ha.default.basename(s,".exe")==="cmd"&&a.unshift("/q"),{file:s,commandArguments:a,options:c}},Yd=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:s=!0,all:a=!1,windowsHide:u=!0,killSignal:l="SIGTERM",forceKillAfterDelay:c=!0,gracefulCancel:f=!1,ipcInput:D,ipc:d=D!==void 0||f,serialization:p="advanced",...g})=>({...g,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:s,all:a,windowsHide:u,killSignal:l,forceKillAfterDelay:c,gracefulCancel:f,ipcInput:D,ipc:d,serialization:p}),qd=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let s=t?{...Tn.default.env,...e}:e;return r||n?As({env:s,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):s};var Fu=require("node:util");function ve(e){if(typeof e=="string")return Hd(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return Kd(e)}var Hd=e=>e.at(-1)===Xa?e.slice(0,e.at(-2)===Ja?-2:-1):e,Kd=e=>e.at(-1)===Xd?e.subarray(0,e.at(-2)===Jd?-2:-1):e,Xa=`
`,Xd=Xa.codePointAt(0),Ja="\r",Jd=Ja.codePointAt(0);var cu=require("node:events"),lu=require("node:stream/promises");function X(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function _n(e,{checkOpen:t=!0}={}){return X(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Ee(e,{checkOpen:t=!0}={}){return X(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function On(e,t){return _n(e,t)&&Ee(e,t)}var Zd=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),Rn=class{#r;#o;#e=!1;#n=void 0;constructor(t,r){this.#r=t,this.#o=r}next(){let t=()=>this.#a();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#t(t);return this.#n?this.#n.then(r,r):r()}async#a(){if(this.#e)return{done:!0,value:void 0};let t;try{t=await this.#r.read()}catch(r){throw this.#n=void 0,this.#e=!0,this.#r.releaseLock(),r}return t.done&&(this.#n=void 0,this.#e=!0,this.#r.releaseLock()),t}async#t(t){if(this.#e)return{done:!0,value:t};if(this.#e=!0,!this.#o){let r=this.#r.cancel(t);return this.#r.releaseLock(),await r,{done:!0,value:t}}return this.#r.releaseLock(),{done:!0,value:t}}},In=Symbol();function Za(){return this[In].next()}Object.defineProperty(Za,"name",{value:"next"});function Qa(e){return this[In].return(e)}Object.defineProperty(Qa,"name",{value:"return"});var Qd=Object.create(Zd,{next:{enumerable:!0,configurable:!0,writable:!0,value:Za},return:{enumerable:!0,configurable:!0,writable:!0,value:Qa}});function vn({preventCancel:e=!1}={}){let t=this.getReader(),r=new Rn(t,e),n=Object.create(Qd);return n[In]=r,n}var eu=e=>{if(Ee(e,{checkOpen:!1})&&tt.on!==void 0)return tp(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(ep.call(e)==="[object ReadableStream]")return vn.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:ep}=Object.prototype,tp=async function*(e){let t=new AbortController,r={};rp(e,t,r);try{for await(let[n]of tt.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},rp=async(e,t,r)=>{try{await tt.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},tt={};var Me=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,finalize:a},{maxBuffer:u=Number.POSITIVE_INFINITY}={})=>{let l=eu(e),c=t();c.length=0;try{for await(let f of l){let D=op(f),d=r[D](f,c);nu({convertedChunk:d,state:c,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:u})}return np({state:c,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,maxBuffer:u}),a(c)}catch(f){let D=typeof f=="object"&&f!==null?f:new Error(f);throw D.bufferedData=a(c),D}},np=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let s=o(e);s!==void 0&&nu({convertedChunk:s,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},nu=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let s=r(e),a=t.length+s;if(a<=i){tu(e,t,o,a);return}let u=n(e,i-t.length);throw u!==void 0&&tu(u,t,o,i),new ne},tu=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},op=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=ru.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&ru.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:ru}=Object.prototype,ne=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var ue=e=>e,rt=()=>{},nr=({contents:e})=>e,or=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},ir=e=>e.length;async function sr(e,t){return Me(e,up,t)}var ip=()=>({contents:[]}),sp=()=>1,ap=(e,{contents:t})=>(t.push(e),t),up={init:ip,convertChunk:{string:ue,buffer:ue,arrayBuffer:ue,dataView:ue,typedArray:ue,others:ue},getSize:sp,truncateChunk:rt,addChunk:ap,getFinalChunk:rt,finalize:nr};async function ar(e,t){return Me(e,Fp,t)}var cp=()=>({contents:new ArrayBuffer(0)}),lp=e=>fp.encode(e),fp=new TextEncoder,ou=e=>new Uint8Array(e),iu=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),Dp=(e,t)=>e.slice(0,t),dp=(e,{contents:t,length:r},n)=>{let o=uu()?mp(t,n):pp(t,n);return new Uint8Array(o).set(e,r),o},pp=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(au(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},mp=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:au(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},au=e=>su**Math.ceil(Math.log(e)/Math.log(su)),su=2,hp=({contents:e,length:t})=>uu()?e:e.slice(0,t),uu=()=>"resize"in ArrayBuffer.prototype,Fp={init:cp,convertChunk:{string:lp,buffer:ou,arrayBuffer:ou,dataView:iu,typedArray:iu,others:or},getSize:ir,truncateChunk:Dp,addChunk:dp,getFinalChunk:rt,finalize:hp};async function cr(e,t){return Me(e,Sp,t)}var gp=()=>({contents:"",textDecoder:new TextDecoder}),ur=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),Ep=(e,{contents:t})=>t+e,yp=(e,t)=>e.slice(0,t),bp=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},Sp={init:gp,convertChunk:{string:ue,buffer:ur,arrayBuffer:ur,dataView:ur,typedArray:ur,others:or},getSize:ir,truncateChunk:yp,addChunk:Ep,getFinalChunk:bp,finalize:nr};Object.assign(tt,{on:cu.on,finished:lu.finished});var fu=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof ne))throw e;if(i==="all")return e;let s=wp(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:s},t.destroy(),e},wp=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",Du=(e,t,r)=>{if(t.length!==r)return;let n=new ne;throw n.maxBufferInfo={fdNumber:"ipc"},n},du=(e,t)=>{let{streamName:r,threshold:n,unit:o}=Cp(e,t);return`Command's ${r} was larger than ${n} ${o}`},Cp=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=ae(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:Bt(r),threshold:o,unit:n}},pu=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>lr(r)),mu=(e,t,r)=>{if(!t)return e;let n=lr(r);return e.length>n?e.slice(0,n):e},lr=([,e])=>e;var gu=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:s,escapedCommand:a,timedOut:u,isCanceled:l,isGracefullyCanceled:c,isMaxBuffer:f,isForcefullyTerminated:D,forceKillAfterDelay:d,killSignal:p,maxBuffer:g,timeout:B,cwd:y})=>{let T=n?.code,P=xp({originalError:n,timedOut:u,timeout:B,isMaxBuffer:f,maxBuffer:g,errorCode:T,signal:o,signalDescription:i,exitCode:s,isCanceled:l,isGracefullyCanceled:c,isForcefullyTerminated:D,forceKillAfterDelay:d,killSignal:p}),N=Bp(n,y),k=N===void 0?"":`
${N}`,R=`${P}: ${a}${k}`,$=t===void 0?[e[2],e[1]]:[t],j=[R,...$,...e.slice(3),r.map(S=>Tp(S)).join(`
`)].map(S=>He(ve(_p(S)))).filter(Boolean).join(`

`);return{originalMessage:N,shortMessage:R,message:j}},xp=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:s,signalDescription:a,exitCode:u,isCanceled:l,isGracefullyCanceled:c,isForcefullyTerminated:f,forceKillAfterDelay:D,killSignal:d})=>{let p=Ap(f,D);return t?`Command timed out after ${r} milliseconds${p}`:c?s===void 0?`Command was gracefully canceled with exit code ${u}`:f?`Command was gracefully canceled${p}`:`Command was gracefully canceled with ${s} (${a})`:l?`Command was canceled${p}`:n?`${du(e,o)}${p}`:i!==void 0?`Command failed with ${i}${p}`:f?`Command was killed with ${d} (${Ut(d)})${p}`:s!==void 0?`Command was killed with ${s} (${a})`:u!==void 0?`Command failed with exit code ${u}`:"Command failed"},Ap=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",Bp=(e,t)=>{if(e instanceof H)return;let r=_s(e)?e.originalMessage:String(e?.message??e),n=He(qa(r,t));return n===""?void 0:n},Tp=e=>typeof e=="string"?e:(0,Fu.inspect)(e),_p=e=>Array.isArray(e)?e.map(t=>ve(hu(t))).filter(Boolean).join(`
`):hu(e),hu=e=>typeof e=="string"?e:v(e)?Ct(e):"";var fr=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:s})=>Eu({command:e,escapedCommand:t,cwd:i,durationMs:nn(s),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),Pe=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:s})=>nt({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:s}),nt=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:l,signal:c,stdio:f,all:D,ipcOutput:d,options:{timeoutDuration:p,timeout:g=p,forceKillAfterDelay:B,killSignal:y,cwd:T,maxBuffer:P},isSync:N})=>{let{exitCode:k,signal:R,signalDescription:$}=Rp(l,c),{originalMessage:j,shortMessage:S,message:Fe}=gu({stdio:f,all:D,ipcOutput:d,originalError:e,signal:R,signalDescription:$,exitCode:k,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,forceKillAfterDelay:B,killSignal:y,maxBuffer:P,timeout:g,cwd:T}),_=Bs(e,Fe,N);return Object.assign(_,Op({error:_,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:k,signal:R,signalDescription:$,stdio:f,all:D,ipcOutput:d,cwd:T,originalMessage:j,shortMessage:S})),_},Op=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:l,signal:c,signalDescription:f,stdio:D,all:d,ipcOutput:p,cwd:g,originalMessage:B,shortMessage:y})=>Eu({shortMessage:y,originalMessage:B,command:t,escapedCommand:r,cwd:g,durationMs:nn(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isTerminated:c!==void 0,isMaxBuffer:a,isForcefullyTerminated:u,exitCode:l,signal:c,signalDescription:f,code:e.cause?.code,stdout:D[1],stderr:D[2],all:d,stdio:D,ipcOutput:p,pipedFrom:[]}),Eu=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),Rp=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:Ut(t);return{exitCode:r,signal:n,signalDescription:o}};var yu=e=>Number.isFinite(e)?e:0;function Ip(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(yu(e*1e3)%1e3),nanoseconds:Math.trunc(yu(e*1e6)%1e3)}}function vp(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function Mn(e){switch(typeof e){case"number":{if(Number.isFinite(e))return Ip(e);break}case"bigint":return vp(e)}throw new TypeError("Expected a finite number or bigint")}var Mp=e=>e===0||e===0n,Pp=(e,t)=>t===1||t===1n?e:`${e}s`,$p=1e-7,Lp=24n*60n*60n*1000n;function Pn(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(c,f)=>{let D=Math.floor(c*10**f+$p);return(Math.round(D)/10**f).toFixed(f)},s=(c,f,D,d)=>{if(!((o.length===0||!t.colonNotation)&&Mp(c)&&!(t.colonNotation&&D==="m"))){if(d??=String(c),t.colonNotation){let p=d.includes(".")?d.split(".")[0].length:d.length,g=o.length>0?2:1;d="0".repeat(Math.max(0,g-p))+d}else d+=t.verbose?" "+Pp(f,c):D;o.push(d)}},a=Mn(e),u=BigInt(a.days);if(s(u/365n,"year","y"),s(u%365n,"day","d"),s(Number(a.hours),"hour","h"),s(Number(a.minutes),"minute","m"),t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let c=Number(a.seconds),f=Number(a.milliseconds),D=Number(a.microseconds),d=Number(a.nanoseconds);if(s(c,"second","s"),t.formatSubMilliseconds)s(f,"millisecond","ms"),s(D,"microsecond","\xB5s"),s(d,"nanosecond","ns");else{let p=f+D/1e3+d/1e6,g=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,B=p>=1?Math.round(p):Math.ceil(p),y=g?p.toFixed(g):B;s(Number.parseFloat(y),"millisecond","ms",y)}}else{let c=(r?Number(e%Lp):e)/1e3%60,f=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,D=i(c,f),d=t.keepDecimalsOnWholeSeconds?D:D.replace(/\.0+$/,"");s(Number.parseFloat(d),"second","s",d)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let l=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(l)}var bu=(e,t)=>{e.failed&&ee({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var Su=(e,t)=>{xe(t)&&(bu(e,t),Np(e,t))},Np=(e,t)=>{let r=`(done in ${Pn(e.durationMs)})`;ee({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var $e=(e,t,{reject:r})=>{if(Su(e,t),e.failed&&r)throw e;return e};var Wn=require("node:fs");var xu=(e,t)=>ye(e)?"asyncGenerator":Tu(e)?"generator":Dr(e)?"fileUrl":Wp(e)?"filePath":Yp(e)?"webStream":X(e,{checkOpen:!1})?"native":v(e)?"uint8Array":qp(e)?"asyncIterable":Hp(e)?"iterable":Nn(e)?Au({transform:e},t):Gp(e)?jp(e,t):"native",jp=(e,t)=>On(e.transform,{checkOpen:!1})?Up(e,t):Nn(e.transform)?Au(e,t):kp(e,t),Up=(e,t)=>(Bu(e,t,"Duplex stream"),"duplex"),Au=(e,t)=>(Bu(e,t,"web TransformStream"),"webTransform"),Bu=({final:e,binary:t,objectMode:r},n,o)=>{wu(e,`${n}.final`,o),wu(t,`${n}.binary`,o),$n(r,`${n}.objectMode`)},wu=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},kp=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!Cu(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(On(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(Nn(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!Cu(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return $n(r,`${o}.binary`),$n(n,`${o}.objectMode`),ye(e)||ye(t)?"asyncGenerator":"generator"},$n=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},Cu=e=>ye(e)||Tu(e),ye=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",Tu=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",Gp=e=>O(e)&&(e.transform!==void 0||e.final!==void 0),Dr=e=>Object.prototype.toString.call(e)==="[object URL]",_u=e=>Dr(e)&&e.protocol!=="file:",Wp=e=>O(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>zp.has(t))&&Ln(e.file),zp=new Set(["file","append"]),Ln=e=>typeof e=="string",Ou=(e,t)=>e==="native"&&typeof t=="string"&&!Vp.has(t),Vp=new Set(["ipc","ignore","inherit","overlapped","pipe"]),Ru=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",dr=e=>Object.prototype.toString.call(e)==="[object WritableStream]",Yp=e=>Ru(e)||dr(e),Nn=e=>Ru(e?.readable)&&dr(e?.writable),qp=e=>Iu(e)&&typeof e[Symbol.asyncIterator]=="function",Hp=e=>Iu(e)&&typeof e[Symbol.iterator]=="function",Iu=e=>typeof e=="object"&&e!==null,W=new Set(["generator","asyncGenerator","duplex","webTransform"]),pr=new Set(["fileUrl","filePath","fileNumber"]),jn=new Set(["fileUrl","filePath"]),vu=new Set([...jn,"webStream","nodeStream"]),Mu=new Set(["webTransform","duplex"]),pe={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var Un=(e,t,r,n)=>n==="output"?Kp(e,t,r):Xp(e,t,r),Kp=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},Xp=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},Pu=(e,t)=>{let r=e.findLast(({type:n})=>W.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var $u=(e,t,r,n)=>[...e.filter(({type:o})=>!W.has(o)),...Jp(e,t,r,n)],Jp=(e,t,r,{encoding:n})=>{let o=e.filter(({type:s})=>W.has(s)),i=Array.from({length:o.length});for(let[s,a]of Object.entries(o))i[s]=Zp({stdioItem:a,index:Number(s),newTransforms:i,optionName:t,direction:r,encoding:n});return rm(i,r)},Zp=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:s})=>t==="duplex"?Qp({stdioItem:e,optionName:o}):t==="webTransform"?em({stdioItem:e,index:r,newTransforms:n,direction:i}):tm({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:s}),Qp=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},em=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:s}=O(t)?t:{transform:t},{writableObjectMode:a,readableObjectMode:u}=Un(s,r,n,o);return{...e,value:{transform:i,writableObjectMode:a,readableObjectMode:u}}},tm=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:s,final:a,binary:u=!1,preserveNewlines:l=!1,objectMode:c}=O(t)?t:{transform:t},f=u||U.has(i),{writableObjectMode:D,readableObjectMode:d}=Un(c,r,n,o);return{...e,value:{transform:s,final:a,binary:f,preserveNewlines:l,writableObjectMode:D,readableObjectMode:d}}},rm=(e,t)=>t==="input"?e.reverse():e;var mr=E(require("node:process"),1);var Lu=(e,t,r)=>{let n=e.map(o=>nm(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??sm},nm=({type:e,value:t},r)=>om[r]??Nu[e](t),om=["input","output","output"],Le=()=>{},kn=()=>"input",Nu={generator:Le,asyncGenerator:Le,fileUrl:Le,filePath:Le,iterable:kn,asyncIterable:kn,uint8Array:kn,webStream:e=>dr(e)?"output":"input",nodeStream(e){return Ee(e,{checkOpen:!1})?_n(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:Le,duplex:Le,native(e){let t=im(e);if(t!==void 0)return t;if(X(e,{checkOpen:!1}))return Nu.nodeStream(e)}},im=e=>{if([0,mr.default.stdin].includes(e))return"input";if([1,2,mr.default.stdout,mr.default.stderr].includes(e))return"output"},sm="output";var ju=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var Uu=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let s=am(e,n).map((a,u)=>ku(a,u));return i?cm(s,r,o):ju(s,t)},am=(e,t)=>{if(e===void 0)return G.map(n=>t[n]);if(um(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${G.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,G.length);return Array.from({length:r},(n,o)=>e[o])},um=e=>G.some(t=>e[t]!==void 0),ku=(e,t)=>Array.isArray(e)?e.map(r=>ku(r,t)):e??(t>=G.length?"ignore":"pipe"),cm=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!Ae(r,o)&&lm(n)?"ignore":n),lm=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var Wu=require("node:fs"),zu=E(require("node:tty"),1);var Vu=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?fm({stdioItem:e,fdNumber:n,direction:o}):pm({stdioItem:e,fdNumber:n}),fm=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=Dm({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if(X(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},Dm=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=dm(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(zu.default.isatty(o))throw new TypeError(`The \`${t}: ${Wt(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:se((0,Wu.readFileSync)(o)),optionName:t}}},dm=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=At.indexOf(e);if(r!==-1)return r},pm=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:Gu(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:Gu(t,t,r),optionName:r}:X(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,Gu=(e,t,r)=>{let n=At[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var Yu=({input:e,inputFile:t},r)=>r===0?[...mm(e),...Fm(t)]:[],mm=e=>e===void 0?[]:[{type:hm(e),value:e,optionName:"input"}],hm=e=>{if(Ee(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(v(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},Fm=e=>e===void 0?[]:[{...gm(e),optionName:"inputFile"}],gm=e=>{if(Dr(e))return{type:"fileUrl",value:e};if(Ln(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var qu=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),Hu=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let s=Em(o,e);if(s.length!==0){if(i){ym({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});return}if(vu.has(e))return Ku({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});Mu.has(e)&&Sm({otherStdioItems:s,type:e,value:t,optionName:r})}},Em=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),ym=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{jn.has(t)&&Ku({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},Ku=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(a=>bm(a,r));if(i.length===0)return;let s=i.find(a=>a.direction!==o);return Xu(s,n,t),o==="output"?i[0].stream:void 0},bm=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,Sm=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);Xu(o,n,t)},Xu=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${pe[r]} that is the same.`)};var hr=(e,t,r,n)=>{let i=Uu(t,r,n).map((a,u)=>wm({stdioOption:a,fdNumber:u,options:t,isSync:n})),s=Rm({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=s.map(({stdioItems:a})=>Mm(a)),s},wm=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=Bt(t),{stdioItems:i,isStdioArray:s}=Cm({stdioOption:e,fdNumber:t,options:r,optionName:o}),a=Lu(i,t,o),u=i.map(f=>Vu({stdioItem:f,isStdioArray:s,fdNumber:t,direction:a,isSync:n})),l=$u(u,o,a,r),c=Pu(l,a);return Om(l,c),{direction:a,objectMode:c,stdioItems:l}},Cm=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(u=>xm(u,n)),...Yu(r,t)],s=qu(i),a=s.length>1;return Am(s,a,n),Tm(s),{stdioItems:s,isStdioArray:a}},xm=(e,t)=>({type:xu(e,t),value:e,optionName:t}),Am=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(Bm.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},Bm=new Set(["ignore","ipc"]),Tm=e=>{for(let t of e)_m(t)},_m=({type:e,value:t,optionName:r})=>{if(_u(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(Ou(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},Om=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>pr.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},Rm=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(Im({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw Gn(o),i}},Im=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:s})=>{let a=r.map(u=>vm({stdioItem:u,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:s}));return{direction:e,objectMode:t,stdioItems:a}},vm=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let s=Hu({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return s!==void 0?{...e,stream:s}:{...e,...t[r][e.type](e,n)}},Gn=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!q(r)&&r.destroy()},Mm=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var Zu=(e,t)=>hr($m,e,t,!0),oe=({type:e,optionName:t})=>{Qu(t,pe[e])},Pm=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&Qu(e,`"${t}"`),{}),Qu=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},Ju={generator(){},asyncGenerator:oe,webStream:oe,nodeStream:oe,webTransform:oe,duplex:oe,asyncIterable:oe,native:Pm},$m={input:{...Ju,fileUrl:({value:e})=>({contents:[se((0,Wn.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[se((0,Wn.readFileSync)(e))]}),fileNumber:oe,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...Ju,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:oe,string:oe,uint8Array:oe}};var ce=(e,{stripFinalNewline:t},r)=>zn(t,r)&&e!==void 0&&!Array.isArray(e)?ve(e):e,zn=(e,t)=>t==="all"?e[1]||e[2]:e[t];var it=require("node:stream");var Fr=(e,t,r,n)=>e||r?void 0:tc(t,n),Yn=(e,t,r)=>r?e.flatMap(n=>ec(n,t)):ec(e,t),ec=(e,t)=>{let{transform:r,final:n}=tc(t,{});return[...r(e),...n()]},tc=(e,t)=>(t.previousChunks="",{transform:Lm.bind(void 0,t,e),final:jm.bind(void 0,t)}),Lm=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let s=Nm(r,i,t,e),a=r.slice(o+1,i+1-s);n.length>0&&(a=Vn(n,a),n=""),yield a,o=i}o!==r.length-1&&(n=Vn(n,r.slice(o+1))),e.previousChunks=n},Nm=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),jm=function*({previousChunks:e}){e.length>0&&(yield e)},rc=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:Um.bind(void 0,n)},Um=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?km:Wm;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},Vn=(e,t)=>`${e}${t}`,km={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:Vn},Gm=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},Wm={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:Gm};var nc=require("node:buffer");var oc=(e,t)=>e?void 0:zm.bind(void 0,t),zm=function*(e,t){if(typeof t!="string"&&!v(t)&&!nc.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},ic=(e,t)=>e?Vm.bind(void 0,t):Ym.bind(void 0,t),Vm=function*(e,t){sc(e,t),yield t},Ym=function*(e,t){if(sc(e,t),typeof t!="string"&&!v(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},sc=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var ac=require("node:buffer"),uc=require("node:string_decoder");var gr=(e,t,r)=>{if(r)return;if(e)return{transform:qm.bind(void 0,new TextEncoder)};let n=new uc.StringDecoder(t);return{transform:Hm.bind(void 0,n),final:Km.bind(void 0,n)}},qm=function*(e,t){ac.Buffer.isBuffer(t)?yield se(t):typeof t=="string"?yield e.encode(t):yield t},Hm=function*(e,t){yield v(t)?e.write(t):t},Km=function*(e){let t=e.end();t!==""&&(yield t)};var qn=require("node:util"),Hn=(0,qn.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),Er=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Jm}=t[r];for await(let o of n(e))yield*Er(o,t,r+1)},cc=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Xm(r,Number(t),e)},Xm=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*Er(n,r,t+1)},lc=(0,qn.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),Jm=function*(e){yield e};var Kn=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},fc=(e,t)=>[...t.flatMap(r=>[...be(r,e,0)]),...ot(e)],be=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Qm}=t[r];for(let o of n(e))yield*be(o,t,r+1)},ot=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Zm(r,Number(t),e)},Zm=function*(e,t,r){if(e!==void 0)for(let n of e())yield*be(n,r,t+1)},Qm=function*(e){yield e};var Xn=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:s})=>{let a={},u=Dc(e,s,i),l=ye(t),c=ye(r),f=l?Hn.bind(void 0,Er,a):Kn.bind(void 0,be),D=l||c?Hn.bind(void 0,cc,a):Kn.bind(void 0,ot),d=l||c?lc.bind(void 0,a):void 0;return{stream:new it.Transform({writableObjectMode:n,writableHighWaterMark:(0,it.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,it.getDefaultHighWaterMark)(o),transform(g,B,y){f([g,u,0],this,y)},flush(g){D([u],this,g)},destroy:d})}},yr=(e,t,r,n)=>{let o=t.filter(({type:s})=>s==="generator"),i=n?o.reverse():o;for(let{value:s,optionName:a}of i){let u=Dc(s,r,a);e=fc(u,e)}return e},Dc=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},s,a)=>{let u={};return[{transform:oc(n,a)},gr(r,s,n),Fr(r,i,n,u),{transform:e,final:t},{transform:ic(o,a)},rc({binary:r,preserveNewlines:i,readableObjectMode:o,state:u})].filter(Boolean)};var dc=(e,t)=>{for(let r of e0(e))t0(e,r,t)},e0=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),t0=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:a})=>a!==void 0);if(o.length===0)return;if(t!==0){let[{type:a,optionName:u}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${u}\`, can be ${pe[a]} with synchronous methods.`)}let s=o.map(({contents:a})=>a).map(a=>r0(a,n));r.input=qe(s)},r0=(e,t)=>{let r=yr(e,t,"utf8",!0);return n0(r),qe(r)},n0=e=>{let t=e.find(r=>typeof r!="string"&&!v(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var Sr=require("node:fs");var br=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&Ae(r,n)&&!U.has(t)&&o0(n)&&(e.some(({type:o,value:i})=>o==="native"&&i0.has(i))||e.every(({type:o})=>W.has(o))),o0=e=>e===1||e===2,i0=new Set(["pipe","overlapped"]),pc=async(e,t,r,n)=>{for await(let o of e)s0(t)||hc(o,r,n)},mc=(e,t,r)=>{for(let n of e)hc(n,t,r)},s0=e=>e._readableState.pipes.length>0,hc=(e,t,r)=>{let n=vt(e);ee({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var Fc=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},s=new Set([]);return{output:t.map((u,l)=>a0({result:u,fileDescriptors:e,fdNumber:l,state:i,outputFiles:s,isMaxBuffer:n,verboseInfo:o},r)),...i}},a0=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:s},{buffer:a,encoding:u,lines:l,stripFinalNewline:c,maxBuffer:f})=>{if(e===null)return;let D=mu(e,i,f),d=se(D),{stdioItems:p,objectMode:g}=t[r],B=u0([d],p,u,n),{serializedResult:y,finalResult:T=y}=c0({chunks:B,objectMode:g,encoding:u,lines:l,stripFinalNewline:c,fdNumber:r});l0({serializedResult:y,fdNumber:r,state:n,verboseInfo:s,encoding:u,stdioItems:p,objectMode:g});let P=a[r]?T:void 0;try{return n.error===void 0&&f0(y,p,o),P}catch(N){return n.error=N,P}},u0=(e,t,r,n)=>{try{return yr(e,t,r,!1)}catch(o){return n.error=o,e}},c0=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:qe(e)};let s=ai(e,r);return n[i]?{serializedResult:s,finalResult:Yn(s,!o[i],t)}:{serializedResult:s}},l0=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:s})=>{if(!br({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let a=Yn(e,!1,s);try{mc(a,t,n)}catch(u){r.error??=u}},f0=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>pr.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,Sr.appendFileSync)(n,e):(r.add(i),(0,Sr.writeFileSync)(n,e))}};var gc=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,ce(t,r,"all")]:Array.isArray(t)?[ce(e,r,"all"),...t]:v(e)&&v(t)?Xr([e,t]):`${e}${t}`};var wr=require("node:events");var Ec=async(e,t)=>{let[r,n]=await D0(e);return t.isForcefullyTerminated??=!1,[r,n]},D0=async e=>{let[t,r]=await Promise.allSettled([(0,wr.once)(e,"spawn"),(0,wr.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?yc(e):r.value},yc=async e=>{try{return await(0,wr.once)(e,"exit")}catch{return yc(e)}},bc=async e=>{let[t,r]=await e;if(!d0(t,r)&&Jn(t,r))throw new H;return[t,r]},d0=(e,t)=>e===void 0&&t===void 0,Jn=(e,t)=>e!==0||t!==null;var Sc=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=p0(e,t,r),s=i?.code==="ETIMEDOUT",a=pu(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:s,isMaxBuffer:a}},p0=(e,t,r)=>e!==void 0?e:Jn(t,r)?new H:void 0;var Cc=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:s,startTime:a,verboseInfo:u,options:l,fileDescriptors:c}=m0(e,t,r),f=g0({file:n,commandArguments:o,options:l,command:i,escapedCommand:s,verboseInfo:u,fileDescriptors:c,startTime:a});return $e(f,u,l)},m0=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=Pt(e,t,r),a=h0(r),{file:u,commandArguments:l,options:c}=rr(e,t,a);F0(c);let f=Zu(c,s);return{file:u,commandArguments:l,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:c,fileDescriptors:f}},h0=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,F0=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&Cr("ipcInput"),e&&Cr("ipc: true"),r&&Cr("detached: true"),n&&Cr("cancelSignal")},Cr=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},g0=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:s,startTime:a})=>{let u=E0({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:s,startTime:a});if(u.failed)return u;let{resultError:l,exitCode:c,signal:f,timedOut:D,isMaxBuffer:d}=Sc(u,r),{output:p,error:g=l}=Fc({fileDescriptors:s,syncResult:u,options:r,isMaxBuffer:d,verboseInfo:i}),B=p.map((T,P)=>ce(T,r,P)),y=ce(gc(p,r),r,"all");return b0({error:g,exitCode:c,signal:f,timedOut:D,isMaxBuffer:d,stdio:B,all:y,options:r,command:n,escapedCommand:o,startTime:a})},E0=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:s})=>{try{dc(i,r);let a=y0(r);return(0,wc.spawnSync)(e,t,a)}catch(a){return Pe({error:a,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:s,isSync:!0})}},y0=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:lr(t)}),b0=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:s,options:a,command:u,escapedCommand:l,startTime:c})=>e===void 0?fr({command:u,escapedCommand:l,stdio:i,all:s,ipcOutput:[],options:a,startTime:c}):nt({error:e,command:u,escapedCommand:l,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:s,ipcOutput:[],options:a,startTime:c,isSync:!0});var Rl=require("node:events"),Il=require("node:child_process");var Qn=E(require("node:process"),1);var Ne=require("node:events");var xc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(Oe({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:Kt(e)}),S0({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),S0=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{Vt(t,o);let i=de(e,t,r),s=new AbortController;try{return await Promise.race([w0(i,n,s),C0(i,r,s),x0(i,r,s)])}catch(a){throw Re(e),a}finally{s.abort(),Yt(t,o)}},w0=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,Ne.once)(e,"message",{signal:r});return n}for await(let[n]of(0,Ne.on)(e,"message",{signal:r}))if(t(n))return n},C0=async(e,t,{signal:r})=>{await(0,Ne.once)(e,"disconnect",{signal:r}),Hs(t)},x0=async(e,t,{signal:r})=>{let[n]=await(0,Ne.once)(e,"strict:error",{signal:r});throw Gt(n,t)};var st=require("node:events");var Bc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>Zn({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),Zn=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{Oe({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:Kt(e)}),Vt(t,i);let s=de(e,t,r),a=new AbortController,u={};return A0(e,s,a),B0({ipcEmitter:s,isSubprocess:r,controller:a,state:u}),T0({anyProcess:e,channel:t,ipcEmitter:s,isSubprocess:r,shouldAwait:o,controller:a,state:u,reference:i})},A0=async(e,t,r)=>{try{await(0,st.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},B0=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,st.once)(e,"strict:error",{signal:r.signal});n.error=Gt(o,t),r.abort()}catch{}},T0=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:s,reference:a}){try{for await(let[u]of(0,st.on)(r,"message",{signal:i.signal}))Ac(s),yield u}catch{Ac(s)}finally{i.abort(),Yt(t,a),n||Re(e),o&&await e}},Ac=({error:e})=>{if(e)throw e};var Tc=(e,{ipc:t})=>{Object.assign(e,Oc(e,!1,t))},_c=()=>{let e=Qn.default,t=!0,r=Qn.default.channel!==void 0;return{...Oc(e,t,r),getCancelSignal:Ta.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},Oc=(e,t,r)=>({sendMessage:Qt.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:xc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:Bc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var Rc=require("node:child_process"),me=require("node:stream");var Ic=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:s})=>{Gn(n);let a=new Rc.ChildProcess;_0(a,n),Object.assign(a,{readable:O0,writable:R0,duplex:I0});let u=Pe({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),l=v0(u,s,o);return{subprocess:a,promise:l}},_0=(e,t)=>{let r=at(),n=at(),o=at(),i=Array.from({length:t.length-3},at),s=at(),a=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:s,stdio:a})},at=()=>{let e=new me.PassThrough;return e.end(),e},O0=()=>new me.Readable({read(){}}),R0=()=>new me.Writable({write(){}}),I0=()=>new me.Duplex({read(){},write(){}}),v0=async(e,t,r)=>$e(e,t,r);var je=require("node:fs"),Mc=require("node:buffer"),ie=require("node:stream");var Pc=(e,t)=>hr(M0,e,t,!1),ut=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${pe[e]}.`)},vc={fileNumber:ut,generator:Xn,asyncGenerator:Xn,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:ie.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},M0={input:{...vc,fileUrl:({value:e})=>({stream:(0,je.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,je.createReadStream)(e)}),webStream:({value:e})=>({stream:ie.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:ie.Readable.from(e)}),asyncIterable:({value:e})=>({stream:ie.Readable.from(e)}),string:({value:e})=>({stream:ie.Readable.from(e)}),uint8Array:({value:e})=>({stream:ie.Readable.from(Mc.Buffer.from(e))})},output:{...vc,fileUrl:({value:e})=>({stream:(0,je.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,je.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:ie.Writable.fromWeb(e)}),iterable:ut,asyncIterable:ut,string:ut,uint8Array:ut}};var ct=require("node:events"),Ar=require("node:stream"),ro=require("node:stream/promises");function Se(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)to(o);let t=e.some(({readableObjectMode:o})=>o),r=P0(e,t),n=new eo({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var P0=(e,t)=>{if(e.length===0)return(0,Ar.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},eo=class extends Ar.PassThrough{#r=new Set([]);#o=new Set([]);#e=new Set([]);#n;#a=Symbol("unpipe");#t=new WeakMap;add(t){if(to(t),this.#r.has(t))return;this.#r.add(t),this.#n??=$0(this,this.#r,this.#a);let r=j0({passThroughStream:this,stream:t,streams:this.#r,ended:this.#o,aborted:this.#e,onFinished:this.#n,unpipeEvent:this.#a});this.#t.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(to(t),!this.#r.has(t))return!1;let r=this.#t.get(t);return r===void 0?!1:(this.#t.delete(t),t.unpipe(this),await r,!0)}},$0=async(e,t,r)=>{xr(e,$c);let n=new AbortController;try{await Promise.race([L0(e,n),N0(e,t,r,n)])}finally{n.abort(),xr(e,-$c)}},L0=async(e,{signal:t})=>{try{await(0,ro.finished)(e,{signal:t,cleanup:!0})}catch(r){throw Nc(e,r),r}},N0=async(e,t,r,{signal:n})=>{for await(let[o]of(0,ct.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},to=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},j0=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:s})=>{xr(e,Lc);let a=new AbortController;try{await Promise.race([U0(i,t,a),k0({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:a}),G0({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:s,controller:a})])}finally{a.abort(),xr(e,-Lc)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?no(e):W0(e))},U0=async(e,t,{signal:r})=>{try{await e,r.aborted||no(t)}catch(n){r.aborted||Nc(t,n)}},k0=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,ro.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(s){if(i.aborted||!r.has(t))return;jc(s)?o.add(t):Uc(e,s)}},G0=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,ct.once)(e,o,{signal:i}),!e.readable)return(0,ct.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},W0=e=>{e.writable&&e.end()},Nc=(e,t)=>{jc(t)?no(e):Uc(e,t)},jc=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",no=e=>{(e.readable||e.writable)&&e.destroy()},Uc=(e,t)=>{e.destroyed||(e.once("error",z0),e.destroy(t))},z0=()=>{},xr=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},$c=2,Lc=1;var oo=require("node:stream/promises");var Ue=(e,t)=>{e.pipe(t),V0(e,t),Y0(e,t)},V0=async(e,t)=>{if(!(q(e)||q(t))){try{await(0,oo.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}io(t)}},io=e=>{e.writable&&e.end()},Y0=async(e,t)=>{if(!(q(e)||q(t))){try{await(0,oo.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}so(e)}},so=e=>{e.readable&&e.destroy()};var kc=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:s}]of Object.entries(t)){for(let{stream:a}of i.filter(({type:u})=>W.has(u)))q0(e,a,s,o);for(let{stream:a}of i.filter(({type:u})=>!W.has(u)))K0({subprocess:e,stream:a,direction:s,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let s=i.length===1?i[0]:Se(i);Ue(s,o)}},q0=(e,t,r,n)=>{r==="output"?Ue(e.stdio[n],t):Ue(t,e.stdio[n]);let o=H0[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},H0=["stdin","stdout","stderr"],K0=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;X0(t,i);let[s,a]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],u=o.get(s)??[];o.set(s,[...u,a])},X0=(e,{signal:t})=>{q(e)&&ge(e,J0,t)},J0=2;var Gc=require("node:events");var we=[];we.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&we.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&we.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var Br=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",ao=Symbol.for("signal-exit emitter"),uo=globalThis,Z0=Object.defineProperty.bind(Object),co=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(uo[ao])return uo[ao];Z0(uo,ao,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},Tr=class{},Q0=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),lo=class extends Tr{onExit(){return()=>{}}load(){}unload(){}},fo=class extends Tr{#r=Do.platform==="win32"?"SIGINT":"SIGHUP";#o=new co;#e;#n;#a;#t={};#s=!1;constructor(t){super(),this.#e=t,this.#t={};for(let r of we)this.#t[r]=()=>{let n=this.#e.listeners(r),{count:o}=this.#o,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let s=this.#o.emit("exit",null,r),a=r==="SIGHUP"?this.#r:r;s||t.kill(t.pid,a)}};this.#a=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!Br(this.#e))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#o.on(n,t),()=>{this.#o.removeListener(n,t),this.#o.listeners.exit.length===0&&this.#o.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#o.count+=1;for(let t of we)try{let r=this.#t[t];r&&this.#e.on(t,r)}catch{}this.#e.emit=(t,...r)=>this.#d(t,...r),this.#e.reallyExit=t=>this.#i(t)}}unload(){this.#s&&(this.#s=!1,we.forEach(t=>{let r=this.#t[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,r)}catch{}}),this.#e.emit=this.#n,this.#e.reallyExit=this.#a,this.#o.count-=1)}#i(t){return Br(this.#e)?(this.#e.exitCode=t||0,this.#o.emit("exit",this.#e.exitCode,null),this.#a.call(this.#e,this.#e.exitCode)):0}#d(t,...r){let n=this.#n;if(t==="exit"&&Br(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let o=n.call(this.#e,t,...r);return this.#o.emit("exit",this.#e.exitCode,null),o}else return n.call(this.#e,t,...r)}},Do=globalThis.process,{onExit:_r,load:kw,unload:Gw}=Q0(Br(Do)?new fo(Do):new lo);var Wc=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=_r(()=>{e.kill()});(0,Gc.addAbortListener)(n,()=>{o()})};var Vc=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=Mt(),{destination:s,destinationStream:a,destinationError:u,from:l,unpipeSignal:c}=eh(r,n,o),{sourceStream:f,sourceError:D}=rh(e,l),{options:d,fileDescriptors:p}=re.get(e);return{sourcePromise:t,sourceStream:f,sourceOptions:d,sourceError:D,destination:s,destinationStream:a,destinationError:u,unpipeSignal:c,fileDescriptors:p,startTime:i}},eh=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:s}={}}=th(e,t,...r),a=zt(n,i);return{destination:n,destinationStream:a,from:o,unpipeSignal:s}}catch(n){return{destinationError:n}}},th=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(zc,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||Hr(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,s]=wt(r,...n);return{destination:t(zc)(o,i,s),pipeOptions:s}}if(re.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},zc=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),rh=(e,t)=>{try{return{sourceStream:Ie(e,t)}}catch(r){return{sourceError:r}}};var qc=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:s})=>{let a=nh({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(a!==void 0)throw po({error:a,fileDescriptors:o,sourceOptions:i,startTime:s})},nh=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return so(e),n;if(t!==void 0)return io(r),t},po=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>Pe({error:e,command:Yc,escapedCommand:Yc,fileDescriptors:t,options:r,startTime:n,isSync:!1}),Yc="source.pipe(destination)";var Hc=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:s=i}]=await e;if(s.pipedFrom.includes(n)||s.pipedFrom.push(n),o==="rejected")throw s;if(t==="rejected")throw n;return s};var Kc=require("node:stream/promises");var Xc=(e,t,r)=>{let n=Or.has(t)?ih(e,t):oh(e,t);return ge(e,ah,r.signal),ge(t,uh,r.signal),sh(t),n},oh=(e,t)=>{let r=Se([e]);return Ue(r,t),Or.set(t,r),r},ih=(e,t)=>{let r=Or.get(t);return r.add(e),r},sh=async e=>{try{await(0,Kc.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}Or.delete(e)},Or=new WeakMap,ah=2,uh=1;var Jc=require("node:util");var Zc=(e,t)=>e===void 0?[]:[ch(e,t)],ch=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,Jc.aborted)(e,t),await r.remove(t);let s=new Error("Pipe canceled by `unpipeSignal` option.");throw po({error:s,fileDescriptors:n,sourceOptions:o,startTime:i})};var Rr=(e,...t)=>{if(O(t[0]))return Rr.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=Vc(e,...t),o=lh({...n,destination:r});return o.pipe=Rr.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},lh=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:s,unpipeSignal:a,fileDescriptors:u,startTime:l})=>{let c=fh(e,o);qc({sourceStream:t,sourceError:n,destinationStream:i,destinationError:s,fileDescriptors:u,sourceOptions:r,startTime:l});let f=new AbortController;try{let D=Xc(t,i,f);return await Promise.race([Hc(c),...Zc(a,{sourceStream:t,mergedStream:D,sourceOptions:r,fileDescriptors:u,startTime:l})])}finally{f.abort()}},fh=(e,t)=>Promise.allSettled([e,t]);var nl=require("node:timers/promises");var el=require("node:events"),tl=require("node:stream");var Ir=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let s=new AbortController;return Dh(t,s),rl({stream:e,controller:s,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},Dh=async(e,t)=>{try{await e}catch{}finally{t.abort()}},mo=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let s=new AbortController;dh(t,s,e);let a=e.readableObjectMode&&!i;return rl({stream:e,controller:s,binary:n==="buffer",shouldEncode:!a,encoding:n,shouldSplit:!a&&r,preserveNewlines:!o})},dh=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},rl=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})=>{let a=(0,el.on)(e,"data",{signal:t.signal,highWaterMark:Qc,highWatermark:Qc});return ph({onStdoutChunk:a,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})},ho=(0,tl.getDefaultHighWaterMark)(!0),Qc=ho,ph=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s}){let a=mh({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s});try{for await(let[u]of e)yield*be(u,a,0)}catch(u){if(!t.signal.aborted)throw u}finally{yield*ot(a)}},mh=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[gr(e,r,!t),Fr(e,o,!n,{})].filter(Boolean);var ol=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:s,allMixed:a,stripFinalNewline:u,verboseInfo:l,streamInfo:c})=>{let f=hh({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:a,verboseInfo:l,streamInfo:c});if(!o){await Promise.all([Fh(e),f]);return}let D=zn(u,r),d=mo({stream:e,onStreamEnd:t,lines:s,encoding:n,stripFinalNewline:D,allMixed:a}),[p]=await Promise.all([gh({stream:e,iterable:d,fdNumber:r,encoding:n,maxBuffer:i,lines:s}),f]);return p},hh=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:s}})=>{if(!br({stdioItems:s[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let a=mo({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await pc(a,e,r,i)},Fh=async e=>{await(0,nl.setImmediate)(),e.readableFlowing===null&&e.resume()},gh=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:s})=>{try{return t||s?await sr(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await ar(r,{maxBuffer:i})):await cr(r,{maxBuffer:i})}catch(a){return il(fu({error:a,stream:e,readableObjectMode:t,lines:s,encoding:o,fdNumber:n}))}},Fo=async e=>{try{return await e}catch(t){return il(t)}},il=({bufferedData:e})=>ii(e)?new Uint8Array(e):e;var al=require("node:stream/promises"),lt=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=Eh(e,r),s=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,al.finished)(e,{cleanup:!0,signal:s.signal})])}catch(a){i.stdinCleanedUp||Sh(a,t,r,n)}finally{s.abort()}},Eh=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&yh(e,r,n),n},yh=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{bh(t,r),n.call(e,...o)}},bh=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},Sh=(e,t,r,n)=>{if(!wh(e,t,r,n))throw e},wh=(e,t,r,n=!0)=>r.propagating?sl(e)||vr(e):(r.propagating=!0,go(r,t)===n?sl(e):vr(e)),go=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",vr=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",sl=e=>e?.code==="EPIPE";var ul=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>e.stdio.map((u,l)=>Eo({stream:u,fdNumber:l,encoding:t,buffer:r[l],maxBuffer:n[l],lines:o[l],allMixed:!1,stripFinalNewline:i,verboseInfo:s,streamInfo:a})),Eo=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:u,streamInfo:l})=>{if(!e)return;let c=lt(e,t,l);if(go(l,t)){await c;return}let[f]=await Promise.all([ol({stream:e,onStreamEnd:c,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:u,streamInfo:l}),c]);return f};var cl=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?Se([e,t].filter(Boolean)):void 0,ll=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>Eo({...Ch(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:xh(e),stripFinalNewline:i,verboseInfo:s,streamInfo:a}),Ch=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},xh=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var ml=require("node:events");var fl=e=>Ae(e,"ipc"),Dl=(e,t)=>{let r=vt(e);ee({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var dl=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let s=fl(i),a=ae(t,"ipc"),u=ae(r,"ipc");for await(let l of Zn({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))a&&(Du(e,o,u),o.push(l)),s&&Dl(l,i);return o},pl=async(e,t)=>(await Promise.allSettled([e]),t);var hl=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:u,stripFinalNewline:l,ipc:c,ipcInput:f},context:D,verboseInfo:d,fileDescriptors:p,originalStreams:g,onInternalError:B,controller:y})=>{let T=Ec(e,D),P={originalStreams:g,fileDescriptors:p,subprocess:e,exitPromise:T,propagating:!1},N=ul({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:l,verboseInfo:d,streamInfo:P}),k=ll({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:l,verboseInfo:d,streamInfo:P}),R=[],$=dl({subprocess:e,buffer:r,maxBuffer:n,ipc:c,ipcOutput:R,verboseInfo:d}),j=Ah(g,e,P),S=Bh(p,P);try{return await Promise.race([Promise.all([{},bc(T),Promise.all(N),k,$,ja(e,f),...j,...S]),B,Th(e,y),...Ma(e,i,D,y),...qs({subprocess:e,cancelSignal:s,gracefulCancel:a,context:D,controller:y}),...Ra({subprocess:e,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:u,context:D,controller:y})])}catch(Fe){return D.terminationReason??="other",Promise.all([{error:Fe},T,Promise.all(N.map(_=>Fo(_))),Fo(k),pl($,R),Promise.allSettled(j),Promise.allSettled(S)])}},Ah=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:lt(n,o,r)),Bh=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>X(i,{checkOpen:!1})&&!q(i)).map(({type:o,value:i,stream:s=i})=>lt(s,n,t,{isSameDirection:W.has(o),stopOnExit:o==="native"}))),Th=async(e,{signal:t})=>{let[r]=await(0,ml.once)(e,"error",{signal:t});throw r};var Fl=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),ft=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=te();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},ke=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var El=require("node:stream"),yl=require("node:util");var yo=require("node:stream/promises");var bo=async e=>{if(e!==void 0)try{await So(e)}catch{}},gl=async e=>{if(e!==void 0)try{await wo(e)}catch{}},So=async e=>{await(0,yo.finished)(e,{cleanup:!0,readable:!1,writable:!0})},wo=async e=>{await(0,yo.finished)(e,{cleanup:!0,readable:!0,writable:!1})},Mr=async(e,t)=>{if(await e,t)throw t},Pr=(e,t,r)=>{r&&!vr(r)?e.destroy(r):t&&e.destroy()};var bl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let s=o||U.has(r),{subprocessStdout:a,waitReadableDestroy:u}=Co(e,n,t),{readableEncoding:l,readableObjectMode:c,readableHighWaterMark:f}=xo(a,s),{read:D,onStdoutDataDone:d}=Ao({subprocessStdout:a,subprocess:e,binary:s,encoding:r,preserveNewlines:i}),p=new El.Readable({read:D,destroy:(0,yl.callbackify)(To.bind(void 0,{subprocessStdout:a,subprocess:e,waitReadableDestroy:u})),highWaterMark:f,objectMode:c,encoding:l});return Bo({subprocessStdout:a,onStdoutDataDone:d,readable:p,subprocess:e}),p},Co=(e,t,r)=>{let n=Ie(e,t),o=ft(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},xo=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:ho},Ao=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=te(),s=Ir({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){_h(this,s,i)},onStdoutDataDone:i}},_h=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},Bo=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await wo(e),await n,await bo(o),await t,r.readable&&r.push(null)}catch(i){await bo(o),Sl(r,i)}},To=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await ke(r,t)&&(Sl(e,n),await Mr(t,n))},Sl=(e,t)=>{Pr(e,e.readable,t)};var wl=require("node:stream"),_o=require("node:util");var Cl=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Oo(e,r,t),s=new wl.Writable({...Ro(n,e,o),destroy:(0,_o.callbackify)(vo.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return Io(n,s),s},Oo=(e,t,r)=>{let n=zt(e,t),o=ft(r,n,"writableFinal"),i=ft(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Ro=(e,t,r)=>({write:Oh.bind(void 0,e),final:(0,_o.callbackify)(Rh.bind(void 0,e,t,r))}),Oh=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},Rh=async(e,t,r)=>{await ke(r,t)&&(e.writable&&e.end(),await t)},Io=async(e,t,r)=>{try{await So(e),t.writable&&t.end()}catch(n){await gl(r),xl(t,n)}},vo=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await ke(r,t),await ke(n,t)&&(xl(e,o),await Mr(t,o))},xl=(e,t)=>{Pr(e,e.writable,t)};var Al=require("node:stream"),Bl=require("node:util");var Tl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:s=!0}={})=>{let a=i||U.has(r),{subprocessStdout:u,waitReadableDestroy:l}=Co(e,n,t),{subprocessStdin:c,waitWritableFinal:f,waitWritableDestroy:D}=Oo(e,o,t),{readableEncoding:d,readableObjectMode:p,readableHighWaterMark:g}=xo(u,a),{read:B,onStdoutDataDone:y}=Ao({subprocessStdout:u,subprocess:e,binary:a,encoding:r,preserveNewlines:s}),T=new Al.Duplex({read:B,...Ro(c,e,f),destroy:(0,Bl.callbackify)(Ih.bind(void 0,{subprocessStdout:u,subprocessStdin:c,subprocess:e,waitReadableDestroy:l,waitWritableFinal:f,waitWritableDestroy:D})),readableHighWaterMark:g,writableHighWaterMark:c.writableHighWaterMark,readableObjectMode:p,writableObjectMode:c.writableObjectMode,encoding:d});return Bo({subprocessStdout:u,onStdoutDataDone:y,readable:T,subprocess:e,subprocessStdin:c}),Io(c,T,u),T},Ih=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},s)=>{await Promise.all([To({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},s),vo({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},s)])};var Mo=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||U.has(t),s=Ie(e,r),a=Ir({subprocessStdout:s,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return vh(a,s,e)},vh=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var _l=(e,{encoding:t})=>{let r=Fl();e.readable=bl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=Cl.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=Tl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=Mo.bind(void 0,e,t),e[Symbol.asyncIterator]=Mo.bind(void 0,e,t,{})};var Ol=(e,t)=>{for(let[r,n]of Ph){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},Mh=(async()=>{})().constructor.prototype,Ph=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(Mh,e)]);var vl=(e,t,r,n)=>{let{file:o,commandArguments:i,command:s,escapedCommand:a,startTime:u,verboseInfo:l,options:c,fileDescriptors:f}=$h(e,t,r),{subprocess:D,promise:d}=Nh({file:o,commandArguments:i,options:c,startTime:u,verboseInfo:l,command:s,escapedCommand:a,fileDescriptors:f});return D.pipe=Rr.bind(void 0,{source:D,sourcePromise:d,boundOptions:{},createNested:n}),Ol(D,d),re.set(D,{options:c,fileDescriptors:f}),D},$h=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=Pt(e,t,r),{file:a,commandArguments:u,options:l}=rr(e,t,r),c=Lh(l),f=Pc(c,s);return{file:a,commandArguments:u,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:c,fileDescriptors:f}},Lh=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},Nh=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:s,fileDescriptors:a})=>{let u;try{u=(0,Il.spawn)(e,t,r)}catch(p){return Ic({error:p,command:i,escapedCommand:s,fileDescriptors:a,options:r,startTime:n,verboseInfo:o})}let l=new AbortController;(0,Rl.setMaxListeners)(Number.POSITIVE_INFINITY,l.signal);let c=[...u.stdio];kc(u,a,l),Wc(u,r,l);let f={},D=te();u.kill=zs.bind(void 0,{kill:u.kill.bind(u),options:r,onInternalError:D,context:f,controller:l}),u.all=cl(u,r),_l(u,r),Tc(u,r);let d=jh({subprocess:u,options:r,startTime:n,verboseInfo:o,fileDescriptors:a,originalStreams:c,command:i,escapedCommand:s,context:f,onInternalError:D,controller:l});return{subprocess:u,promise:d}},jh=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:s,escapedCommand:a,context:u,onInternalError:l,controller:c})=>{let[f,[D,d],p,g,B]=await hl({subprocess:e,options:t,context:u,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:l,controller:c});c.abort(),l.resolve();let y=p.map((N,k)=>ce(N,t,k)),T=ce(g,t,"all"),P=Uh({errorInfo:f,exitCode:D,signal:d,stdio:y,all:T,ipcOutput:B,context:u,options:t,command:s,escapedCommand:a,startTime:r});return $e(P,n,t)},Uh=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:s,options:a,command:u,escapedCommand:l,startTime:c})=>"error"in e?nt({error:e.error,command:u,escapedCommand:l,timedOut:s.terminationReason==="timeout",isCanceled:s.terminationReason==="cancel"||s.terminationReason==="gracefulCancel",isGracefullyCanceled:s.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof ne,isForcefullyTerminated:s.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:a,startTime:c,isSync:!1}):fr({command:u,escapedCommand:l,stdio:n,all:o,ipcOutput:i,options:a,startTime:c});var $r=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,kh(n,e[n],o)]));return{...e,...r}},kh=(e,t,r)=>Gh.has(e)&&O(t)&&O(r)?{...t,...r}:r,Gh=new Set(["env",...Qr]);var he=(e,t,r,n)=>{let o=(s,a,u)=>he(s,a,r,u),i=(...s)=>Wh({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...s);return n!==void 0&&n(i,o,t),i},Wh=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...s)=>{if(O(i))return o(e,$r(r,i),n);let{file:a,commandArguments:u,options:l,isSync:c}=zh({mapArguments:e,firstArgument:i,nextArguments:s,deepOptions:t,boundOptions:r});return c?Cc(a,u,l):vl(a,u,l,o)},zh=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=Di(t)?di(t,r):[t,...r],[s,a,u]=wt(...i),l=$r($r(n,o),u),{file:c=s,commandArguments:f=a,options:D=l,isSync:d=!1}=e({file:s,commandArguments:a,options:l});return{file:c,commandArguments:f,options:D,isSync:d}};var Ml=({file:e,commandArguments:t})=>$l(e,t),Pl=({file:e,commandArguments:t})=>({...$l(e,t),isSync:!0}),$l=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=Vh(e);return{file:r,commandArguments:n}},Vh=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(Yh)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},Yh=/ +/g;var Ll=(e,t,r)=>{e.sync=t(qh,r),e.s=e.sync},Nl=({options:e})=>jl(e),qh=({options:e})=>({...jl(e),isSync:!0}),jl=e=>({options:{...Hh(e),...e}}),Hh=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Ul={preferLocal:!0};var kl=he(()=>({})),d1=he(()=>({isSync:!0})),p1=he(Ml),m1=he(Pl),h1=he(Pa),F1=he(Nl,{},Ul,Ll),{sendMessage:g1,getOneMessage:E1,getEachMessage:y1,getCancelSignal:b1}=_c();var M=E(Wl());var Et=E(require("node:process"),1);var zl=(e=0)=>t=>`\x1B[${t+e}m`,Vl=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,Yl=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,w={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},C1=Object.keys(w.modifier),Jh=Object.keys(w.color),Zh=Object.keys(w.bgColor),x1=[...Jh,...Zh];function Qh(){let e=new Map;for(let[t,r]of Object.entries(w)){for(let[n,o]of Object.entries(r))w[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=w[n],e.set(o[0],o[1]);Object.defineProperty(w,t,{value:r,enumerable:!1})}return Object.defineProperty(w,"codes",{value:e,enumerable:!1}),w.color.close="\x1B[39m",w.bgColor.close="\x1B[49m",w.color.ansi=zl(),w.color.ansi256=Vl(),w.color.ansi16m=Yl(),w.bgColor.ansi=zl(10),w.bgColor.ansi256=Vl(10),w.bgColor.ansi16m=Yl(10),Object.defineProperties(w,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>w.rgbToAnsi256(...w.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let a=t%36;r=Math.floor(t/36)/5,n=Math.floor(a/6)/5,o=a%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>w.ansi256ToAnsi(w.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>w.ansi256ToAnsi(w.hexToAnsi256(t)),enumerable:!1}}),w}var eF=Qh(),J=eF;var Nr=E(require("node:process"),1),Hl=E(require("node:os"),1),Po=E(require("node:tty"),1);function z(e,t=globalThis.Deno?globalThis.Deno.args:Nr.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:x}=Nr.default,Lr;z("no-color")||z("no-colors")||z("color=false")||z("color=never")?Lr=0:(z("color")||z("colors")||z("color=true")||z("color=always"))&&(Lr=1);function tF(){if("FORCE_COLOR"in x)return x.FORCE_COLOR==="true"?1:x.FORCE_COLOR==="false"?0:x.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(x.FORCE_COLOR,10),3)}function rF(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function nF(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=tF();n!==void 0&&(Lr=n);let o=r?Lr:n;if(o===0)return 0;if(r){if(z("color=16m")||z("color=full")||z("color=truecolor"))return 3;if(z("color=256"))return 2}if("TF_BUILD"in x&&"AGENT_NAME"in x)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(x.TERM==="dumb")return i;if(Nr.default.platform==="win32"){let s=Hl.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in x)return"GITHUB_ACTIONS"in x||"GITEA_ACTIONS"in x?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in x)||x.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in x)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(x.TEAMCITY_VERSION)?1:0;if(x.COLORTERM==="truecolor"||x.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in x){let s=Number.parseInt((x.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(x.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(x.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(x.TERM)||"COLORTERM"in x?1:i}function ql(e,t={}){let r=nF(e,{streamIsTTY:e&&e.isTTY,...t});return rF(r)}var oF={stdout:ql({isTTY:Po.default.isatty(1)}),stderr:ql({isTTY:Po.default.isatty(2)})},Kl=oF;function Xl(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function Jl(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:Zl,stderr:Ql}=Kl,$o=Symbol("GENERATOR"),Ge=Symbol("STYLER"),Dt=Symbol("IS_EMPTY"),ef=["ansi","ansi","ansi256","ansi16m"],We=Object.create(null),iF=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=Zl?Zl.level:0;e.level=t.level===void 0?r:t.level};var sF=e=>{let t=(...r)=>r.join(" ");return iF(t,e),Object.setPrototypeOf(t,dt.prototype),t};function dt(e){return sF(e)}Object.setPrototypeOf(dt.prototype,Function.prototype);for(let[e,t]of Object.entries(J))We[e]={get(){let r=jr(this,No(t.open,t.close,this[Ge]),this[Dt]);return Object.defineProperty(this,e,{value:r}),r}};We.visible={get(){let e=jr(this,this[Ge],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Lo=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?J[r].ansi16m(...n):t==="ansi256"?J[r].ansi256(J.rgbToAnsi256(...n)):J[r].ansi(J.rgbToAnsi(...n)):e==="hex"?Lo("rgb",t,r,...J.hexToRgb(...n)):J[r][e](...n),aF=["rgb","hex","ansi256"];for(let e of aF){We[e]={get(){let{level:r}=this;return function(...n){let o=No(Lo(e,ef[r],"color",...n),J.color.close,this[Ge]);return jr(this,o,this[Dt])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);We[t]={get(){let{level:r}=this;return function(...n){let o=No(Lo(e,ef[r],"bgColor",...n),J.bgColor.close,this[Ge]);return jr(this,o,this[Dt])}}}}var uF=Object.defineProperties(()=>{},{...We,level:{enumerable:!0,get(){return this[$o].level},set(e){this[$o].level=e}}}),No=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},jr=(e,t,r)=>{let n=(...o)=>cF(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,uF),n[$o]=e,n[Ge]=t,n[Dt]=r,n},cF=(e,t)=>{if(e.level<=0||!t)return e[Dt]?"":t;let r=e[Ge];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=Xl(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=Jl(t,o,n,i)),n+t+o};Object.defineProperties(dt.prototype,We);var lF=dt(),I1=dt({level:Ql?Ql.level:0});var tf=lF;var Uo=E(require("node:process"),1);var pt=E(require("node:process"),1);var fF=(e,t,r,n)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;let o=Object.getOwnPropertyDescriptor(e,r),i=Object.getOwnPropertyDescriptor(t,r);!DF(o,i)&&n||Object.defineProperty(e,r,i)},DF=function(e,t){return e===void 0||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},dF=(e,t)=>{let r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},pF=(e,t)=>`/* Wrapped ${e}*/
${t}`,mF=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),hF=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),FF=(e,t,r)=>{let n=r===""?"":`with ${r.trim()}() `,o=pF.bind(null,n,t.toString());Object.defineProperty(o,"name",hF);let{writable:i,enumerable:s,configurable:a}=mF;Object.defineProperty(e,"toString",{value:o,writable:i,enumerable:s,configurable:a})};function jo(e,t,{ignoreNonConfigurable:r=!1}={}){let{name:n}=e;for(let o of Reflect.ownKeys(t))fF(e,t,o,r);return dF(e,t),FF(e,t,n),e}var Ur=new WeakMap,rf=(e,t={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let r,n=0,o=e.displayName||e.name||"<anonymous>",i=function(...s){if(Ur.set(i,++n),n===1)r=e.apply(this,s),e=void 0;else if(t.throw===!0)throw new Error(`Function \`${o}\` can only be called once`);return r};return jo(i,e),Ur.set(i,n),i};rf.callCount=e=>{if(!Ur.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return Ur.get(e)};var nf=rf;var of=pt.default.stderr.isTTY?pt.default.stderr:pt.default.stdout.isTTY?pt.default.stdout:void 0,gF=of?nf(()=>{_r(()=>{of.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},sf=gF;var kr=!1,ze={};ze.show=(e=Uo.default.stderr)=>{e.isTTY&&(kr=!1,e.write("\x1B[?25h"))};ze.hide=(e=Uo.default.stderr)=>{e.isTTY&&(sf(),kr=!0,e.write("\x1B[?25l"))};ze.toggle=(e,t)=>{e!==void 0&&(kr=e),kr?ze.show(t):ze.hide(t)};var ko=ze;var yt=E(Go(),1);var lf=(e=0)=>t=>`\x1B[${t+e}m`,ff=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,Df=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,C={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},z1=Object.keys(C.modifier),yF=Object.keys(C.color),bF=Object.keys(C.bgColor),V1=[...yF,...bF];function SF(){let e=new Map;for(let[t,r]of Object.entries(C)){for(let[n,o]of Object.entries(r))C[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=C[n],e.set(o[0],o[1]);Object.defineProperty(C,t,{value:r,enumerable:!1})}return Object.defineProperty(C,"codes",{value:e,enumerable:!1}),C.color.close="\x1B[39m",C.bgColor.close="\x1B[49m",C.color.ansi=lf(),C.color.ansi256=ff(),C.color.ansi16m=Df(),C.bgColor.ansi=lf(10),C.bgColor.ansi256=ff(10),C.bgColor.ansi16m=Df(10),Object.defineProperties(C,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>C.rgbToAnsi256(...C.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let a=t%36;r=Math.floor(t/36)/5,n=Math.floor(a/6)/5,o=a%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let s=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>C.ansi256ToAnsi(C.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>C.ansi256ToAnsi(C.hexToAnsi256(t)),enumerable:!1}}),C}var wF=SF(),Z=wF;var zr=E(require("node:process"),1),pf=E(require("node:os"),1),Wo=E(require("node:tty"),1);function V(e,t=globalThis.Deno?globalThis.Deno.args:zr.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:A}=zr.default,Wr;V("no-color")||V("no-colors")||V("color=false")||V("color=never")?Wr=0:(V("color")||V("colors")||V("color=true")||V("color=always"))&&(Wr=1);function CF(){if("FORCE_COLOR"in A)return A.FORCE_COLOR==="true"?1:A.FORCE_COLOR==="false"?0:A.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(A.FORCE_COLOR,10),3)}function xF(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function AF(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=CF();n!==void 0&&(Wr=n);let o=r?Wr:n;if(o===0)return 0;if(r){if(V("color=16m")||V("color=full")||V("color=truecolor"))return 3;if(V("color=256"))return 2}if("TF_BUILD"in A&&"AGENT_NAME"in A)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(A.TERM==="dumb")return i;if(zr.default.platform==="win32"){let s=pf.default.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in A)return"GITHUB_ACTIONS"in A||"GITEA_ACTIONS"in A?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(s=>s in A)||A.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in A)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(A.TEAMCITY_VERSION)?1:0;if(A.COLORTERM==="truecolor"||A.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in A){let s=Number.parseInt((A.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(A.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(A.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(A.TERM)||"COLORTERM"in A?1:i}function df(e,t={}){let r=AF(e,{streamIsTTY:e&&e.isTTY,...t});return xF(r)}var BF={stdout:df({isTTY:Wo.default.isatty(1)}),stderr:df({isTTY:Wo.default.isatty(2)})},mf=BF;function hf(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return s+=e.slice(i),s}function Ff(e,t,r,n){let o=0,i="";do{let s=e[n-1]==="\r";i+=e.slice(o,s?n-1:n)+t+(s?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:gf,stderr:Ef}=mf,zo=Symbol("GENERATOR"),Ve=Symbol("STYLER"),mt=Symbol("IS_EMPTY"),yf=["ansi","ansi","ansi256","ansi16m"],Ye=Object.create(null),TF=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=gf?gf.level:0;e.level=t.level===void 0?r:t.level};var _F=e=>{let t=(...r)=>r.join(" ");return TF(t,e),Object.setPrototypeOf(t,ht.prototype),t};function ht(e){return _F(e)}Object.setPrototypeOf(ht.prototype,Function.prototype);for(let[e,t]of Object.entries(Z))Ye[e]={get(){let r=Vr(this,Yo(t.open,t.close,this[Ve]),this[mt]);return Object.defineProperty(this,e,{value:r}),r}};Ye.visible={get(){let e=Vr(this,this[Ve],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var Vo=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?Z[r].ansi16m(...n):t==="ansi256"?Z[r].ansi256(Z.rgbToAnsi256(...n)):Z[r].ansi(Z.rgbToAnsi(...n)):e==="hex"?Vo("rgb",t,r,...Z.hexToRgb(...n)):Z[r][e](...n),OF=["rgb","hex","ansi256"];for(let e of OF){Ye[e]={get(){let{level:r}=this;return function(...n){let o=Yo(Vo(e,yf[r],"color",...n),Z.color.close,this[Ve]);return Vr(this,o,this[mt])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);Ye[t]={get(){let{level:r}=this;return function(...n){let o=Yo(Vo(e,yf[r],"bgColor",...n),Z.bgColor.close,this[Ve]);return Vr(this,o,this[mt])}}}}var RF=Object.defineProperties(()=>{},{...Ye,level:{enumerable:!0,get(){return this[zo].level},set(e){this[zo].level=e}}}),Yo=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},Vr=(e,t,r)=>{let n=(...o)=>IF(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,RF),n[zo]=e,n[Ve]=t,n[mt]=r,n},IF=(e,t)=>{if(e.level<=0||!t)return e[mt]?"":t;let r=e[Ve];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=hf(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=Ff(t,o,n,i)),n+t+o};Object.defineProperties(ht.prototype,Ye);var vF=ht(),Z1=ht({level:Ef?Ef.level:0});var le=vF;var Y=E(require("node:process"),1);function qo(){return Y.default.platform!=="win32"?Y.default.env.TERM!=="linux":!!Y.default.env.CI||!!Y.default.env.WT_SESSION||!!Y.default.env.TERMINUS_SUBLIME||Y.default.env.ConEmuTask==="{cmd::Cmder}"||Y.default.env.TERM_PROGRAM==="Terminus-Sublime"||Y.default.env.TERM_PROGRAM==="vscode"||Y.default.env.TERM==="xterm-256color"||Y.default.env.TERM==="alacritty"||Y.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var MF={info:le.blue("\u2139"),success:le.green("\u2714"),warning:le.yellow("\u26A0"),error:le.red("\u2716")},PF={info:le.blue("i"),success:le.green("\u221A"),warning:le.yellow("\u203C"),error:le.red("\xD7")},$F=qo()?MF:PF,Ft=$F;function Ho({onlyFirst:e=!1}={}){let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var LF=Ho();function gt(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(LF,"")}function bf(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function Sf(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function wf(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function NF(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function Cf(e,{ambiguousAsWide:t=!1}={}){return NF(e),Sf(e)||wf(e)||t&&bf(e)?2:1}var Bf=E(Af(),1),jF=new Intl.Segmenter,UF=/^\p{Default_Ignorable_Code_Point}$/u;function Ko(e,t={}){if(typeof e!="string"||e.length===0)return 0;let{ambiguousIsNarrow:r=!0,countAnsiEscapeCodes:n=!1}=t;if(n||(e=gt(e)),e.length===0)return 0;let o=0,i={ambiguousAsWide:!r};for(let{segment:s}of jF.segment(e)){let a=s.codePointAt(0);if(!(a<=31||a>=127&&a<=159)&&!(a>=8203&&a<=8207||a===65279)&&!(a>=768&&a<=879||a>=6832&&a<=6911||a>=7616&&a<=7679||a>=8400&&a<=8447||a>=65056&&a<=65071)&&!(a>=55296&&a<=57343)&&!(a>=65024&&a<=65039)&&!UF.test(s)){if((0,Bf.default)().test(s)){o+=2;continue}o+=Cf(a,i)}}return o}function Xo({stream:e=process.stdout}={}){return!!(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var Q=E(require("node:process"),1),kF=3,Jo=class{#r=0;start(){this.#r++,this.#r===1&&this.#o()}stop(){if(this.#r<=0)throw new Error("`stop` called more times than `start`");this.#r--,this.#r===0&&this.#e()}#o(){Q.default.platform==="win32"||!Q.default.stdin.isTTY||(Q.default.stdin.setRawMode(!0),Q.default.stdin.on("data",this.#n),Q.default.stdin.resume())}#e(){Q.default.stdin.isTTY&&(Q.default.stdin.off("data",this.#n),Q.default.stdin.pause(),Q.default.stdin.setRawMode(!1))}#n(t){t[0]===kF&&Q.default.emit("SIGINT")}},GF=new Jo,Zo=GF;var WF=E(Go(),1),Qo=class{#r=0;#o=!1;#e=0;#n=-1;#a=0;#t;#s;#i;#d;#m;#l;#f;#D;#h;#u;#c;color;constructor(t){typeof t=="string"&&(t={text:t}),this.#t={color:"cyan",stream:Et.default.stderr,discardStdin:!0,hideCursor:!0,...t},this.color=this.#t.color,this.spinner=this.#t.spinner,this.#m=this.#t.interval,this.#i=this.#t.stream,this.#l=typeof this.#t.isEnabled=="boolean"?this.#t.isEnabled:Xo({stream:this.#i}),this.#f=typeof this.#t.isSilent=="boolean"?this.#t.isSilent:!1,this.text=this.#t.text,this.prefixText=this.#t.prefixText,this.suffixText=this.#t.suffixText,this.indent=this.#t.indent,Et.default.env.NODE_ENV==="test"&&(this._stream=this.#i,this._isEnabled=this.#l,Object.defineProperty(this,"_linesToClear",{get(){return this.#r},set(r){this.#r=r}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#n}}),Object.defineProperty(this,"_lineCount",{get(){return this.#e}}))}get indent(){return this.#D}set indent(t=0){if(!(t>=0&&Number.isInteger(t)))throw new Error("The `indent` option must be an integer from 0 and up");this.#D=t,this.#p()}get interval(){return this.#m??this.#s.interval??100}get spinner(){return this.#s}set spinner(t){if(this.#n=-1,this.#m=void 0,typeof t=="object"){if(t.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#s=t}else if(!Ke())this.#s=yt.default.line;else if(t===void 0)this.#s=yt.default.dots;else if(t!=="default"&&yt.default[t])this.#s=yt.default[t];else throw new Error(`There is no built-in spinner named '${t}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#h}set text(t=""){this.#h=t,this.#p()}get prefixText(){return this.#u}set prefixText(t=""){this.#u=t,this.#p()}get suffixText(){return this.#c}set suffixText(t=""){this.#c=t,this.#p()}get isSpinning(){return this.#d!==void 0}#F(t=this.#u,r=" "){return typeof t=="string"&&t!==""?t+r:typeof t=="function"?t()+r:""}#g(t=this.#c,r=" "){return typeof t=="string"&&t!==""?r+t:typeof t=="function"?r+t():""}#p(){let t=this.#i.columns??80,r=this.#F(this.#u,"-"),n=this.#g(this.#c,"-"),o=" ".repeat(this.#D)+r+"--"+this.#h+"--"+n;this.#e=0;for(let i of gt(o).split(`
`))this.#e+=Math.max(1,Math.ceil(Ko(i,{countAnsiEscapeCodes:!0})/t))}get isEnabled(){return this.#l&&!this.#f}set isEnabled(t){if(typeof t!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#l=t}get isSilent(){return this.#f}set isSilent(t){if(typeof t!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#f=t}frame(){let t=Date.now();(this.#n===-1||t-this.#a>=this.interval)&&(this.#n=++this.#n%this.#s.frames.length,this.#a=t);let{frames:r}=this.#s,n=r[this.#n];this.color&&(n=tf[this.color](n));let o=typeof this.#u=="string"&&this.#u!==""?this.#u+" ":"",i=typeof this.text=="string"?" "+this.text:"",s=typeof this.#c=="string"&&this.#c!==""?" "+this.#c:"";return o+n+i+s}clear(){if(!this.#l||!this.#i.isTTY)return this;this.#i.cursorTo(0);for(let t=0;t<this.#r;t++)t>0&&this.#i.moveCursor(0,-1),this.#i.clearLine(1);return(this.#D||this.lastIndent!==this.#D)&&this.#i.cursorTo(this.#D),this.lastIndent=this.#D,this.#r=0,this}render(){return this.#f?this:(this.clear(),this.#i.write(this.frame()),this.#r=this.#e,this)}start(t){return t&&(this.text=t),this.#f?this:this.#l?this.isSpinning?this:(this.#t.hideCursor&&ko.hide(this.#i),this.#t.discardStdin&&Et.default.stdin.isTTY&&(this.#o=!0,Zo.start()),this.render(),this.#d=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#i.write(`- ${this.text}
`),this)}stop(){return this.#l?(clearInterval(this.#d),this.#d=void 0,this.#n=0,this.clear(),this.#t.hideCursor&&ko.show(this.#i),this.#t.discardStdin&&Et.default.stdin.isTTY&&this.#o&&(Zo.stop(),this.#o=!1),this):this}succeed(t){return this.stopAndPersist({symbol:Ft.success,text:t})}fail(t){return this.stopAndPersist({symbol:Ft.error,text:t})}warn(t){return this.stopAndPersist({symbol:Ft.warning,text:t})}info(t){return this.stopAndPersist({symbol:Ft.info,text:t})}stopAndPersist(t={}){if(this.#f)return this;let r=t.prefixText??this.#u,n=this.#F(r," "),o=t.symbol??" ",i=t.text??this.text,a=typeof i=="string"?(o?" ":"")+i:"",u=t.suffixText??this.#c,l=this.#g(u," "),c=n+o+a+l+`
`;return this.stop(),this.#i.write(c),this}};function ei(e){return new Qo(e)}var B3=(0,M.blue)((0,M.dim)("internal only"));function bt(e,t,r){console.log(_f[e]+t),typeof r?.exit<"u"&&process.exit(r.exit)}async function Tf(e,t,r){if(!zF){bt("wait",e);try{let o=await t();o&&console.log(o),bt("success",e);return}catch(o){return bt("error",e),r?.printError!==!1&&console.log((0,M.red)(o.message)),o}}let n=ei({spinner:"simpleDots",prefixText:_f.wait+e}).start();try{let o=await t();n.stop(),bt("success",e),o&&console.log(o)}catch(o){return n.stop(),bt("error",e),r?.printError!==!1&&console.error(o.message),o}}var _f={wait:`\u{1F550}${(0,M.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,M.cyan)("info")}  - `,success:`\u2705${(0,M.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,M.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,M.red)("error")}  - `,event:`\u26A1\uFE0F${(0,M.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,M.yellowBright)("plan")}  - `},zF=!0;function VF(e,t,r){let n=e?"release":"debug",o=/#exportFunction\((\w+)\)/g;return{name:"swift-files",setup(i){i.onResolve({filter:/^swift:/},s=>({path:I.default.join(s.resolveDir,s.path.replace(/^swift:/,"")),namespace:"swift",sideEffects:!1})),i.onLoad({filter:/.*/,namespace:"swift"},async s=>{let a=s.path,u=I.default.basename(a),l=I.default.join("compiled_raycast_swift",u),c=[],f="",D="",d="Sources",p=".raycast-swift-build",g=I.default.join(a,p);if(!b.existsSync(I.default.join(a,"Package.swift")))return{errors:[{text:`failed to import Swift package from "${a}". Make sure you reference the folder that includes the 'Package.swift' file`}]};if(await Tf("compiling swift package",async()=>{let R=await Rf("/bin/sh",["-c","swift package dump-package"],a,g),$;try{$=JSON.parse(R)}catch(_){throw new Error(`failed to parse the swift package: ${_}`)}if(f=$.name,!f)throw new Error("failed to read the schema name from swift package");for(let _ of $.targets||[])if(_.type==="executable"){if(D)throw new Error("failed to parse the swift package: expected one executable target but found more than one");D=_.name,_.path&&(d=_.path)}if(!D)throw new Error("failed to parse the swift package: expected one executable target but found none");let j=`xcodebuild build -skipMacroValidation -skipPackagePluginValidation -configuration ${n} -scheme ${f} -destination "generic/platform=macOS,name=Any Mac" -derivedDataPath ${p}`;await Rf("/bin/sh",["-c",j],a,g);let S=I.default.join(g,"Build/Products",n,D),Fe;try{if(Fe=b.statSync(S),(Fe.mode&73)===0)throw new Error("not executable")}catch{throw new Error(`failed to compile swift: Could not find the compiled asset.

Try to delete the ${I.default.join(g,"apple/Products",n)} folder and try again`)}if((!b.existsSync(I.default.join("assets",l))||!KF(S,I.default.join("assets",l)))&&b.cpSync(S,I.default.join("assets",l),{recursive:!0,force:!0}),t){let _=I.default.join(a,d),qr=process.cwd();try{_=I.default.relative(qr,_)}catch{}if(!b.existsSync(_))throw new Error(`failed to compile swift: Could not find Swift sources directory ${_}`);t(_)}},{printError:!0}))return{errors:[{text:`failed to import Swift package from "${a}". Make sure you reference the folder that includes the 'Package.swift' file`}]};let y="",T="RaycastTypeScriptPlugin",P=[I.default.join(g,"SourcePackages","plugins",`${u.toLowerCase()}.output`,D,T),I.default.join(g,"Build","Intermediates.noindex","BuildToolPluginIntermediates",`${u.toLowerCase()}.output`,D,T)];for(let R of P)if(b.existsSync(R)){let $=I.default.join(R,"raycast.d.ts");try{let S=b.readFileSync($,"utf-8");r(qF(u,S))}catch(S){throw new Error(`failed to compile swift: Could not load the content of ${$}
 ${S}`)}let j=I.default.join(R,"raycast.js");try{let S=b.readFileSync(j,"utf-8");return{contents:Of(l,S),loader:"js"}}catch(S){return{errors:[{text:`failed to compile swift: Could not load the content of ${$}
 ${S}`}]}}}function N(R){let $=b.readdirSync(R);for(let j of $){let S=I.default.join(R,j);if(b.statSync(S).isDirectory())N(S);else if(j.endsWith(".swift")){let _=b.readFileSync(S,"utf-8").matchAll(o);for(let qr of _)c.push(qr[1])}}}N(I.default.join(a,"Sources"));let k="";for(let R of c)k+=`export const ${R} = proxy.${R};
`;return y=Of(l,k),r(YF(I.default.basename(a),c)),{contents:y,loader:"js"}})}}}function YF(e,t){let r="";for(let n of t)r+=`    export const ${n}: <T = unknown, U = any>(input?: U) => Promise<T>;
`;return`declare module "swift:*/${e}" {
  const proxy: {[command: string]: <T = unknown, U = any>(input?: U) => Promise<T>};
  export default proxy;
  ${r}
  export class SwiftError extends Error {
    stderr: string;
    stdout: string;
  }
}
`}function qF(e,t){return`declare module "swift:*/${e}" {
${HF(t)}

  export class SwiftError extends Error {
    stderr: string;
    stdout: string;
  }
}`}function HF(e){let t=e.split(`
`);for(let r=0;r<t.length;r++)t[r]="  "+t[r];return t.join(`
`)}function Of(e,t){return`
import { environment } from "@raycast/api";
import { join } from "path";
import { chmod } from "fs/promises";
import { spawn } from "child_process";

async function runSwiftFunction(command, ...args) {
  const swiftPath = join(environment.assetsPath, "${e}");
  await chmod(swiftPath, "755");

  return new Promise((resolve, reject) => {
    const commandArgs = [command];
    for (const arg of args) {
      try {
        commandArgs.push(JSON.stringify(arg, (k, v) => v === undefined ? null : v));
      } catch (err) {
        reject(new SwiftError("Failed to serialize input to JSON: " + err.message));
        return;
      }
    }
    const child = spawn(swiftPath, commandArgs);
    const stdout = [];
    const stderr = [];

    child.stdout?.on("data", (data) => {
      stdout.push(data.toString());
    });
    child.stderr?.on("data", (data) => {
      stderr.push(data.toString());
    });

    child.on("exit", (code) => {
      if (code === 0) {
        try {
          const result = stdout.join("").trim();
          if (result.length != 0) {
            resolve(JSON.parse(result));
          } else {
            resolve(null);
          }
        } catch (err) {
          const error = new SwiftError("Failed to deserialize result from JSON: " + err.message);
          error.stdout = stdout.join("").trim();
          error.stderr = stderr.join("").trim();
          reject(error);
        }
      } else {
        const error = new SwiftError(stderr.join("").trim() || stdout.join("").trim() || "Could not get any data");
        error.stdout = stdout.join("").trim();
        error.stderr = stderr.join("").trim();
        reject(error);
      }
    });

    child.on("error", (error) => {
      reject(error);
    });
  });
}

${t}

export class SwiftError extends Error {
  constructor(message) {
    super(message);
    this.name = "SwiftError";
  }
}
`}var Yr=64e3;function KF(e,t){let r=b.openSync(e,"r"),n=b.openSync(t,"r");try{let o=Buffer.alloc(Yr),i=Buffer.alloc(Yr);for(;;){let s=b.readSync(r,o,0,Yr,null),a=b.readSync(n,i,0,Yr,null);if(s!==a)return!1;if(s===0&&a===0)return!0;if(!o.equals(i))return!1}}finally{b.closeSync(r),b.closeSync(n)}}async function Rf(e,t,r,n){let o=await kl(e,t,{cwd:r,preferLocal:!0,reject:!1});if(o.failed)throw o.stderr.includes("require Xcode")||o.stderr.includes("invalid active developer path")?new Error(`failed to compile swift: ${o.stderr}

Xcode setup error detected. Please ensure Xcode is correctly installed. Use 'sudo xcode-select -s /Applications/Xcode.app/Contents/Developer' to set the active developer directory`):new Error(`failed to compile swift: ${o.stderr}

You can try to delete the ${n} folder and run this command again, sometimes it helps`);return o.stdout}0&&(module.exports={swiftFiles});
