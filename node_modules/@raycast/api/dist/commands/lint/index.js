"use strict";var vF=Object.create;var us=Object.defineProperty;var FF=Object.getOwnPropertyDescriptor;var CF=Object.getOwnPropertyNames;var TF=Object.getPrototypeOf,RF=Object.prototype.hasOwnProperty;var C=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),AF=(e,t)=>{for(var r in t)us(e,r,{get:t[r],enumerable:!0})},zm=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of CF(t))!RF.call(e,o)&&o!==r&&us(e,o,{get:()=>t[o],enumerable:!(n=FF(t,o))||n.enumerable});return e};var N=(e,t,r)=>(r=e!=null?vF(TF(e)):{},zm(t||!e||!e.__esModule?us(r,"default",{value:e,enumerable:!0}):r,e)),$F=e=>zm(us({},"__esModule",{value:!0}),e);var Th=C((XM,Ch)=>{Ch.exports=Fh;Fh.sync=EC;var Eh=require("fs");function SC(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function vh(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:SC(t,r)}function Fh(e,t,r){Eh.stat(e,function(n,o){r(n,n?!1:vh(o,e,t))})}function EC(e,t){return vh(Eh.statSync(e),e,t)}});var Ph=C((ZM,xh)=>{xh.exports=Ah;Ah.sync=vC;var Rh=require("fs");function Ah(e,t,r){Rh.stat(e,function(n,o){r(n,n?!1:$h(o,t))})}function vC(e,t){return $h(Rh.statSync(e),t)}function $h(e,t){return e.isFile()&&FC(e,t)}function FC(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),a=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),l=parseInt("100",8),c=parseInt("010",8),d=parseInt("001",8),p=l|c,m=r&d||r&c&&o===a||r&l&&n===i||r&p&&i===0;return m}});var Bh=C((tN,Oh)=>{var eN=require("fs"),Ss;process.platform==="win32"||global.TESTING_WINDOWS?Ss=Th():Ss=Ph();Oh.exports=wl;wl.sync=CC;function wl(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){wl(e,t||{},function(i,a){i?o(i):n(a)})})}Ss(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function CC(e,t){try{return Ss.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var qh=C((rN,Lh)=>{var ln=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Ih=require("path"),TC=ln?";":":",kh=Bh(),Mh=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),Nh=(e,t)=>{let r=t.colon||TC,n=e.match(/\//)||ln&&e.match(/\\/)?[""]:[...ln?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=ln?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=ln?o.split(r):[""];return ln&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},jh=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=Nh(e,t),a=[],l=d=>new Promise((p,m)=>{if(d===n.length)return t.all&&a.length?p(a):m(Mh(e));let b=n[d],D=/^".*"$/.test(b)?b.slice(1,-1):b,g=Ih.join(D,e),y=!D&&/^\.[\\\/]/.test(e)?e.slice(0,2)+g:g;p(c(y,d,0))}),c=(d,p,m)=>new Promise((b,D)=>{if(m===o.length)return b(l(p+1));let g=o[m];kh(d+g,{pathExt:i},(y,S)=>{if(!y&&S)if(t.all)a.push(d+g);else return b(d+g);return b(c(d,p,m+1))})});return r?l(0).then(d=>r(null,d),r):l(0)},RC=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=Nh(e,t),i=[];for(let a=0;a<r.length;a++){let l=r[a],c=/^".*"$/.test(l)?l.slice(1,-1):l,d=Ih.join(c,e),p=!c&&/^\.[\\\/]/.test(e)?e.slice(0,2)+d:d;for(let m=0;m<n.length;m++){let b=p+n[m];try{if(kh.sync(b,{pathExt:o}))if(t.all)i.push(b);else return b}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw Mh(e)};Lh.exports=jh;jh.sync=RC});var zh=C((nN,Sl)=>{"use strict";var Uh=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};Sl.exports=Uh;Sl.exports.default=Uh});var Hh=C((oN,Gh)=>{"use strict";var Wh=require("path"),AC=qh(),$C=zh();function Vh(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let a;try{a=AC.sync(e.command,{path:r[$C({env:r})],pathExt:t?Wh.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return a&&(a=Wh.resolve(o?e.options.cwd:"",a)),a}function xC(e){return Vh(e)||Vh(e,!0)}Gh.exports=xC});var Yh=C((iN,vl)=>{"use strict";var El=/([()\][%!^"`<>&|;, *?])/g;function PC(e){return e=e.replace(El,"^$1"),e}function OC(e,t){return e=`${e}`,e=e.replace(/(\\*)"/g,'$1$1\\"'),e=e.replace(/(\\*)$/,"$1$1"),e=`"${e}"`,e=e.replace(El,"^$1"),t&&(e=e.replace(El,"^$1")),e}vl.exports.command=PC;vl.exports.argument=OC});var Jh=C((sN,Kh)=>{"use strict";Kh.exports=/^#!(.*)/});var Xh=C((aN,Qh)=>{"use strict";var BC=Jh();Qh.exports=(e="")=>{let t=e.match(BC);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var eg=C((uN,Zh)=>{"use strict";var Fl=require("fs"),IC=Xh();function kC(e){let r=Buffer.alloc(150),n;try{n=Fl.openSync(e,"r"),Fl.readSync(n,r,0,150,0),Fl.closeSync(n)}catch{}return IC(r.toString())}Zh.exports=kC});var og=C((lN,ng)=>{"use strict";var MC=require("path"),tg=Hh(),rg=Yh(),NC=eg(),jC=process.platform==="win32",LC=/\.(?:com|exe)$/i,qC=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function UC(e){e.file=tg(e);let t=e.file&&NC(e.file);return t?(e.args.unshift(e.file),e.command=t,tg(e)):e.file}function zC(e){if(!jC)return e;let t=UC(e),r=!LC.test(t);if(e.options.forceShell||r){let n=qC.test(t);e.command=MC.normalize(e.command),e.command=rg.command(e.command),e.args=e.args.map(i=>rg.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function WC(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:zC(n)}ng.exports=WC});var ag=C((cN,sg)=>{"use strict";var Cl=process.platform==="win32";function Tl(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function VC(e,t){if(!Cl)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=ig(o,t,"spawn");if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function ig(e,t){return Cl&&e===1&&!t.file?Tl(t.original,"spawn"):null}function GC(e,t){return Cl&&e===1&&!t.file?Tl(t.original,"spawnSync"):null}sg.exports={hookChildProcess:VC,verifyENOENT:ig,verifyENOENTSync:GC,notFoundError:Tl}});var cg=C((fN,cn)=>{"use strict";var ug=require("child_process"),Rl=og(),Al=ag();function lg(e,t,r){let n=Rl(e,t,r),o=ug.spawn(n.command,n.args,n.options);return Al.hookChildProcess(o,n),o}function HC(e,t,r){let n=Rl(e,t,r),o=ug.spawnSync(n.command,n.args,n.options);return o.error=o.error||Al.verifyENOENTSync(o.status,n),o}cn.exports=lg;cn.exports.spawn=lg;cn.exports.sync=HC;cn.exports._parse=Rl;cn.exports._enoent=Al});var $b=C(Qc=>{"use strict";var Ab={b:"\b",f:"\f",n:`
`,r:"\r",t:"	",'"':'"',"/":"/","\\":"\\"},L1=97;Qc.parse=function(e,t,r){var n={},o=0,i=0,a=0,l=r&&r.bigint&&typeof BigInt<"u";return{data:c("",!0),pointers:n};function c(P,x){d();var k;R(P,"value");var oe=y();switch(oe){case"t":g("rue"),k=!0;break;case"f":g("alse"),k=!1;break;case"n":g("ull"),k=null;break;case'"':k=p();break;case"[":k=b(P);break;case"{":k=D(P);break;default:S(),"-0123456789".indexOf(oe)>=0?k=m():B()}return R(P,"valueEnd"),d(),x&&a<e.length&&B(),k}function d(){e:for(;a<e.length;){switch(e[a]){case" ":i++;break;case"	":i+=4;break;case"\r":i=0;break;case`
`:i=0,o++;break;default:break e}a++}}function p(){for(var P="",x;x=y(),x!='"';)x=="\\"?(x=y(),x in Ab?P+=Ab[x]:x=="u"?P+=w():W()):P+=x;return P}function m(){var P="",x=!0;e[a]=="-"&&(P+=y()),P+=e[a]=="0"?y():T(),e[a]=="."&&(P+=y()+T(),x=!1),(e[a]=="e"||e[a]=="E")&&(P+=y(),(e[a]=="+"||e[a]=="-")&&(P+=y()),P+=T(),x=!1);var k=+P;return l&&x&&(k>Number.MAX_SAFE_INTEGER||k<Number.MIN_SAFE_INTEGER)?BigInt(P):k}function b(P){d();var x=[],k=0;if(y()=="]")return x;for(S();;){var oe=P+"/"+k;x.push(c(oe)),d();var re=y();if(re=="]")break;re!=","&&W(),d(),k++}return x}function D(P){d();var x={};if(y()=="}")return x;for(S();;){var k=v();y()!='"'&&W();var oe=p(),re=P+"/"+Jc(oe);A(re,"key",k),R(re,"keyEnd"),d(),y()!=":"&&W(),d(),x[oe]=c(re),d();var De=y();if(De=="}")break;De!=","&&W(),d()}return x}function g(P){for(var x=0;x<P.length;x++)y()!==P[x]&&W()}function y(){ae();var P=e[a];return a++,i++,P}function S(){a--,i--}function w(){for(var P=4,x=0;P--;){x<<=4;var k=y().toLowerCase();k>="a"&&k<="f"?x+=k.charCodeAt()-L1+10:k>="0"&&k<="9"?x+=+k:W()}return String.fromCharCode(x)}function T(){for(var P="";e[a]>="0"&&e[a]<="9";)P+=y();if(P.length)return P;ae(),B()}function R(P,x){A(P,x,v())}function A(P,x,k){n[P]=n[P]||{},n[P][x]=k}function v(){return{line:o,column:i,pos:a}}function B(){throw new SyntaxError("Unexpected token "+e[a]+" in JSON at position "+a)}function W(){S(),B()}function ae(){if(a>=e.length)throw new SyntaxError("Unexpected end of JSON input")}};Qc.stringify=function(e,t,r){if(!va(e))return;var n=0,o,i,a=typeof r=="object"?r.space:r;switch(typeof a){case"number":var l=a>10?10:a<0?0:Math.floor(a);a=l&&A(l," "),o=l,i=l;break;case"string":a=a.slice(0,10),o=0,i=0;for(var c=0;c<a.length;c++){var d=a[c];switch(d){case" ":i++;break;case"	":i+=4;break;case"\r":i=0;break;case`
`:i=0,n++;break;default:throw new Error("whitespace characters not allowed in JSON")}o++}break;default:a=void 0}var p="",m={},b=0,D=0,g=0,y=r&&r.es6&&typeof Map=="function";return S(e,0,""),{json:p,pointers:m};function S(v,B,W){switch(R(W,"value"),typeof v){case"number":case"bigint":case"boolean":w(""+v);break;case"string":w(Fa(v));break;case"object":v===null?w("null"):typeof v.toJSON=="function"?w(Fa(v.toJSON())):Array.isArray(v)?ae():y?v.constructor.BYTES_PER_ELEMENT?ae():v instanceof Map?x():v instanceof Set?x(!0):P():P()}R(W,"valueEnd");function ae(){if(v.length){w("[");for(var k=B+1,oe=0;oe<v.length;oe++){oe&&w(","),T(k);var re=va(v[oe])?v[oe]:null,De=W+"/"+oe;S(re,k,De)}T(B),w("]")}else w("[]")}function P(){var k=Object.keys(v);if(k.length){w("{");for(var oe=B+1,re=0;re<k.length;re++){var De=k[re],yt=v[De];if(va(yt)){re&&w(",");var xt=W+"/"+Jc(De);T(oe),R(xt,"key"),w(Fa(De)),R(xt,"keyEnd"),w(":"),a&&w(" "),S(yt,oe,xt)}}T(B),w("}")}else w("{}")}function x(k){if(v.size){w("{");for(var oe=B+1,re=!0,De=v.entries(),yt=De.next();!yt.done;){var xt=yt.value,Ai=xt[0],Hr=k?!0:xt[1];if(va(Hr)){re||w(","),re=!1;var eo=W+"/"+Jc(Ai);T(oe),R(eo,"key"),w(Fa(Ai)),R(eo,"keyEnd"),w(":"),a&&w(" "),S(Hr,oe,eo)}yt=De.next()}T(B),w("}")}else w("{}")}}function w(v){D+=v.length,g+=v.length,p+=v}function T(v){if(a){for(p+=`
`+A(v,a),b++,D=0;v--;)n?(b+=n,D=i):D+=i,g+=o;g+=1}}function R(v,B){m[v]=m[v]||{},m[v][B]={line:b,column:D,pos:g}}function A(v,B){return Array(v+1).join(B)}};var q1=["number","bigint","boolean","string","object"];function va(e){return q1.indexOf(typeof e)>=0}var U1=/"|\\/g,z1=/[\b]/g,W1=/\f/g,V1=/\n/g,G1=/\r/g,H1=/\t/g;function Fa(e){return e=e.replace(U1,"\\$&").replace(W1,"\\f").replace(z1,"\\b").replace(V1,"\\n").replace(G1,"\\r").replace(H1,"\\t"),'"'+e+'"'}var Y1=/~/g,K1=/\//g;function Jc(e){return e.replace(Y1,"~0").replace(K1,"~1")}});var Xc=C((Z9,xb)=>{var J1=require("node:tty"),Q1=J1?.WriteStream?.prototype?.hasColors?.()??!1,q=(e,t)=>{if(!Q1)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",a=i.indexOf(n);if(a===-1)return r+i+n;let l=r,c=0;for(;a!==-1;)l+=i.slice(c,a)+r,c=a+n.length,a=i.indexOf(n,c);return l+=i.slice(c)+n,l}},j={};j.reset=q(0,0);j.bold=q(1,22);j.dim=q(2,22);j.italic=q(3,23);j.underline=q(4,24);j.overline=q(53,55);j.inverse=q(7,27);j.hidden=q(8,28);j.strikethrough=q(9,29);j.black=q(30,39);j.red=q(31,39);j.green=q(32,39);j.yellow=q(33,39);j.blue=q(34,39);j.magenta=q(35,39);j.cyan=q(36,39);j.white=q(37,39);j.gray=q(90,39);j.bgBlack=q(40,49);j.bgRed=q(41,49);j.bgGreen=q(42,49);j.bgYellow=q(43,49);j.bgBlue=q(44,49);j.bgMagenta=q(45,49);j.bgCyan=q(46,49);j.bgWhite=q(47,49);j.bgGray=q(100,49);j.redBright=q(91,39);j.greenBright=q(92,39);j.yellowBright=q(93,39);j.blueBright=q(94,39);j.magentaBright=q(95,39);j.cyanBright=q(96,39);j.whiteBright=q(97,39);j.bgRedBright=q(101,49);j.bgGreenBright=q(102,49);j.bgYellowBright=q(103,49);j.bgBlueBright=q(104,49);j.bgMagentaBright=q(105,49);j.bgCyanBright=q(106,49);j.bgWhiteBright=q(107,49);xb.exports=j});var hf=C(a_=>{var X1=Object.create,Ba=Object.defineProperty,Z1=Object.getOwnPropertyDescriptor,ex=Object.getOwnPropertyNames,tx=Object.getPrototypeOf,rx=Object.prototype.hasOwnProperty,Gb=e=>Ba(e,"__esModule",{value:!0}),Uo=(e,t)=>function(){return e&&(t=(0,e[Object.keys(e)[0]])(e=0)),t},df=(e,t)=>function(){return t||(0,e[Object.keys(e)[0]])((t={exports:{}}).exports,t),t.exports},Hb=(e,t)=>{Gb(e);for(var r in t)Ba(e,r,{get:t[r],enumerable:!0})},nx=(e,t,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of ex(t))!rx.call(e,n)&&n!=="default"&&Ba(e,n,{get:()=>t[n],enumerable:!(r=Z1(t,n))||r.enumerable});return e},Ee=e=>nx(Gb(Ba(e!=null?X1(tx(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e),ox=df({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(e,t){(function(r,n){typeof e=="object"&&typeof t<"u"?n(e):typeof define=="function"&&define.amd?define(["exports"],n):(r=typeof globalThis<"u"?globalThis:r||self,n(r.WebStreamsPolyfill={}))})(e,function(r){"use strict";let n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:s=>`Symbol(${s})`;function o(){}function i(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let a=i();function l(s){return typeof s=="object"&&s!==null||typeof s=="function"}let c=o,d=Promise,p=Promise.prototype.then,m=Promise.resolve.bind(d),b=Promise.reject.bind(d);function D(s){return new d(s)}function g(s){return m(s)}function y(s){return b(s)}function S(s,u,f){return p.call(s,u,f)}function w(s,u,f){S(S(s,u,f),void 0,c)}function T(s,u){w(s,u)}function R(s,u){w(s,void 0,u)}function A(s,u,f){return S(s,u,f)}function v(s){S(s,void 0,c)}let B=(()=>{let s=a&&a.queueMicrotask;if(typeof s=="function")return s;let u=g(void 0);return f=>S(u,f)})();function W(s,u,f){if(typeof s!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(s,u,f)}function ae(s,u,f){try{return g(W(s,u,f))}catch(h){return y(h)}}let P=16384;class x{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(u){let f=this._back,h=f;f._elements.length===P-1&&(h={_elements:[],_next:void 0}),f._elements.push(u),h!==f&&(this._back=h,f._next=h),++this._size}shift(){let u=this._front,f=u,h=this._cursor,_=h+1,E=u._elements,F=E[h];return _===P&&(f=u._next,_=0),--this._size,this._cursor=_,u!==f&&(this._front=f),E[h]=void 0,F}forEach(u){let f=this._cursor,h=this._front,_=h._elements;for(;(f!==_.length||h._next!==void 0)&&!(f===_.length&&(h=h._next,_=h._elements,f=0,_.length===0));)u(_[f]),++f}peek(){let u=this._front,f=this._cursor;return u._elements[f]}}function k(s,u){s._ownerReadableStream=u,u._reader=s,u._state==="readable"?yt(s):u._state==="closed"?Ai(s):xt(s,u._storedError)}function oe(s,u){let f=s._ownerReadableStream;return tt(f,u)}function re(s){s._ownerReadableStream._state==="readable"?Hr(s,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):eo(s,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),s._ownerReadableStream._reader=void 0,s._ownerReadableStream=void 0}function De(s){return new TypeError("Cannot "+s+" a stream using a released reader")}function yt(s){s._closedPromise=D((u,f)=>{s._closedPromise_resolve=u,s._closedPromise_reject=f})}function xt(s,u){yt(s),Hr(s,u)}function Ai(s){yt(s),$p(s)}function Hr(s,u){s._closedPromise_reject!==void 0&&(v(s._closedPromise),s._closedPromise_reject(u),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0)}function eo(s,u){xt(s,u)}function $p(s){s._closedPromise_resolve!==void 0&&(s._closedPromise_resolve(void 0),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0)}let xp=n("[[AbortSteps]]"),Pp=n("[[ErrorSteps]]"),ku=n("[[CancelSteps]]"),Mu=n("[[PullSteps]]"),Op=Number.isFinite||function(s){return typeof s=="number"&&isFinite(s)},tv=Math.trunc||function(s){return s<0?Math.ceil(s):Math.floor(s)};function rv(s){return typeof s=="object"||typeof s=="function"}function Pt(s,u){if(s!==void 0&&!rv(s))throw new TypeError(`${u} is not an object.`)}function Ze(s,u){if(typeof s!="function")throw new TypeError(`${u} is not a function.`)}function nv(s){return typeof s=="object"&&s!==null||typeof s=="function"}function Bp(s,u){if(!nv(s))throw new TypeError(`${u} is not an object.`)}function Ot(s,u,f){if(s===void 0)throw new TypeError(`Parameter ${u} is required in '${f}'.`)}function Nu(s,u,f){if(s===void 0)throw new TypeError(`${u} is required in '${f}'.`)}function ju(s){return Number(s)}function Ip(s){return s===0?0:s}function ov(s){return Ip(tv(s))}function kp(s,u){let h=Number.MAX_SAFE_INTEGER,_=Number(s);if(_=Ip(_),!Op(_))throw new TypeError(`${u} is not a finite number`);if(_=ov(_),_<0||_>h)throw new TypeError(`${u} is outside the accepted range of 0 to ${h}, inclusive`);return!Op(_)||_===0?0:_}function Lu(s,u){if(!rr(s))throw new TypeError(`${u} is not a ReadableStream.`)}function Yr(s){return new to(s)}function Mp(s,u){s._reader._readRequests.push(u)}function qu(s,u,f){let _=s._reader._readRequests.shift();f?_._closeSteps():_._chunkSteps(u)}function $i(s){return s._reader._readRequests.length}function Np(s){let u=s._reader;return!(u===void 0||!Xt(u))}class to{constructor(u){if(Ot(u,1,"ReadableStreamDefaultReader"),Lu(u,"First parameter"),nr(u))throw new TypeError("This stream has already been locked for exclusive reading by another reader");k(this,u),this._readRequests=new x}get closed(){return Xt(this)?this._closedPromise:y(xi("closed"))}cancel(u=void 0){return Xt(this)?this._ownerReadableStream===void 0?y(De("cancel")):oe(this,u):y(xi("cancel"))}read(){if(!Xt(this))return y(xi("read"));if(this._ownerReadableStream===void 0)return y(De("read from"));let u,f,h=D((E,F)=>{u=E,f=F});return ro(this,{_chunkSteps:E=>u({value:E,done:!1}),_closeSteps:()=>u({value:void 0,done:!0}),_errorSteps:E=>f(E)}),h}releaseLock(){if(!Xt(this))throw xi("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");re(this)}}}Object.defineProperties(to.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(to.prototype,n.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function Xt(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_readRequests")?!1:s instanceof to}function ro(s,u){let f=s._ownerReadableStream;f._disturbed=!0,f._state==="closed"?u._closeSteps():f._state==="errored"?u._errorSteps(f._storedError):f._readableStreamController[Mu](u)}function xi(s){return new TypeError(`ReadableStreamDefaultReader.prototype.${s} can only be used on a ReadableStreamDefaultReader`)}let jp=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class Lp{constructor(u,f){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=u,this._preventCancel=f}next(){let u=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?A(this._ongoingPromise,u,u):u(),this._ongoingPromise}return(u){let f=()=>this._returnSteps(u);return this._ongoingPromise?A(this._ongoingPromise,f,f):f()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let u=this._reader;if(u._ownerReadableStream===void 0)return y(De("iterate"));let f,h,_=D((F,$)=>{f=F,h=$});return ro(u,{_chunkSteps:F=>{this._ongoingPromise=void 0,B(()=>f({value:F,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,re(u),f({value:void 0,done:!0})},_errorSteps:F=>{this._ongoingPromise=void 0,this._isFinished=!0,re(u),h(F)}}),_}_returnSteps(u){if(this._isFinished)return Promise.resolve({value:u,done:!0});this._isFinished=!0;let f=this._reader;if(f._ownerReadableStream===void 0)return y(De("finish iterating"));if(!this._preventCancel){let h=oe(f,u);return re(f),A(h,()=>({value:u,done:!0}))}return re(f),g({value:u,done:!0})}}let qp={next(){return Up(this)?this._asyncIteratorImpl.next():y(zp("next"))},return(s){return Up(this)?this._asyncIteratorImpl.return(s):y(zp("return"))}};jp!==void 0&&Object.setPrototypeOf(qp,jp);function iv(s,u){let f=Yr(s),h=new Lp(f,u),_=Object.create(qp);return _._asyncIteratorImpl=h,_}function Up(s){if(!l(s)||!Object.prototype.hasOwnProperty.call(s,"_asyncIteratorImpl"))return!1;try{return s._asyncIteratorImpl instanceof Lp}catch{return!1}}function zp(s){return new TypeError(`ReadableStreamAsyncIterator.${s} can only be used on a ReadableSteamAsyncIterator`)}let Wp=Number.isNaN||function(s){return s!==s};function no(s){return s.slice()}function Vp(s,u,f,h,_){new Uint8Array(s).set(new Uint8Array(f,h,_),u)}function F3(s){return s}function Pi(s){return!1}function Gp(s,u,f){if(s.slice)return s.slice(u,f);let h=f-u,_=new ArrayBuffer(h);return Vp(_,0,s,u,h),_}function sv(s){return!(typeof s!="number"||Wp(s)||s<0)}function Hp(s){let u=Gp(s.buffer,s.byteOffset,s.byteOffset+s.byteLength);return new Uint8Array(u)}function Uu(s){let u=s._queue.shift();return s._queueTotalSize-=u.size,s._queueTotalSize<0&&(s._queueTotalSize=0),u.value}function zu(s,u,f){if(!sv(f)||f===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");s._queue.push({value:u,size:f}),s._queueTotalSize+=f}function av(s){return s._queue.peek().value}function Zt(s){s._queue=new x,s._queueTotalSize=0}class oo{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Wu(this))throw Yu("view");return this._view}respond(u){if(!Wu(this))throw Yu("respond");if(Ot(u,1,"respond"),u=kp(u,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Pi(this._view.buffer),Mi(this._associatedReadableByteStreamController,u)}respondWithNewView(u){if(!Wu(this))throw Yu("respondWithNewView");if(Ot(u,1,"respondWithNewView"),!ArrayBuffer.isView(u))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Pi(u.buffer),Ni(this._associatedReadableByteStreamController,u)}}Object.defineProperties(oo.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(oo.prototype,n.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class Kr{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Sr(this))throw so("byobRequest");return Hu(this)}get desiredSize(){if(!Sr(this))throw so("desiredSize");return tm(this)}close(){if(!Sr(this))throw so("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let u=this._controlledReadableByteStream._state;if(u!=="readable")throw new TypeError(`The stream (in ${u} state) is not in the readable state and cannot be closed`);io(this)}enqueue(u){if(!Sr(this))throw so("enqueue");if(Ot(u,1,"enqueue"),!ArrayBuffer.isView(u))throw new TypeError("chunk must be an array buffer view");if(u.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(u.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let f=this._controlledReadableByteStream._state;if(f!=="readable")throw new TypeError(`The stream (in ${f} state) is not in the readable state and cannot be enqueued to`);ki(this,u)}error(u=void 0){if(!Sr(this))throw so("error");et(this,u)}[ku](u){Yp(this),Zt(this);let f=this._cancelAlgorithm(u);return Ii(this),f}[Mu](u){let f=this._controlledReadableByteStream;if(this._queueTotalSize>0){let _=this._queue.shift();this._queueTotalSize-=_.byteLength,Xp(this);let E=new Uint8Array(_.buffer,_.byteOffset,_.byteLength);u._chunkSteps(E);return}let h=this._autoAllocateChunkSize;if(h!==void 0){let _;try{_=new ArrayBuffer(h)}catch(F){u._errorSteps(F);return}let E={buffer:_,bufferByteLength:h,byteOffset:0,byteLength:h,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(E)}Mp(f,u),Er(this)}}Object.defineProperties(Kr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Kr.prototype,n.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function Sr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledReadableByteStream")?!1:s instanceof Kr}function Wu(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_associatedReadableByteStreamController")?!1:s instanceof oo}function Er(s){if(!fv(s))return;if(s._pulling){s._pullAgain=!0;return}s._pulling=!0;let f=s._pullAlgorithm();w(f,()=>{s._pulling=!1,s._pullAgain&&(s._pullAgain=!1,Er(s))},h=>{et(s,h)})}function Yp(s){Gu(s),s._pendingPullIntos=new x}function Vu(s,u){let f=!1;s._state==="closed"&&(f=!0);let h=Kp(u);u.readerType==="default"?qu(s,h,f):mv(s,h,f)}function Kp(s){let u=s.bytesFilled,f=s.elementSize;return new s.viewConstructor(s.buffer,s.byteOffset,u/f)}function Oi(s,u,f,h){s._queue.push({buffer:u,byteOffset:f,byteLength:h}),s._queueTotalSize+=h}function Jp(s,u){let f=u.elementSize,h=u.bytesFilled-u.bytesFilled%f,_=Math.min(s._queueTotalSize,u.byteLength-u.bytesFilled),E=u.bytesFilled+_,F=E-E%f,$=_,z=!1;F>h&&($=F-u.bytesFilled,z=!0);let K=s._queue;for(;$>0;){let ee=K.peek(),te=Math.min($,ee.byteLength),be=u.byteOffset+u.bytesFilled;Vp(u.buffer,be,ee.buffer,ee.byteOffset,te),ee.byteLength===te?K.shift():(ee.byteOffset+=te,ee.byteLength-=te),s._queueTotalSize-=te,Qp(s,te,u),$-=te}return z}function Qp(s,u,f){f.bytesFilled+=u}function Xp(s){s._queueTotalSize===0&&s._closeRequested?(Ii(s),ho(s._controlledReadableByteStream)):Er(s)}function Gu(s){s._byobRequest!==null&&(s._byobRequest._associatedReadableByteStreamController=void 0,s._byobRequest._view=null,s._byobRequest=null)}function Zp(s){for(;s._pendingPullIntos.length>0;){if(s._queueTotalSize===0)return;let u=s._pendingPullIntos.peek();Jp(s,u)&&(Bi(s),Vu(s._controlledReadableByteStream,u))}}function uv(s,u,f){let h=s._controlledReadableByteStream,_=1;u.constructor!==DataView&&(_=u.constructor.BYTES_PER_ELEMENT);let E=u.constructor,F=u.buffer,$={buffer:F,bufferByteLength:F.byteLength,byteOffset:u.byteOffset,byteLength:u.byteLength,bytesFilled:0,elementSize:_,viewConstructor:E,readerType:"byob"};if(s._pendingPullIntos.length>0){s._pendingPullIntos.push($),om(h,f);return}if(h._state==="closed"){let z=new E($.buffer,$.byteOffset,0);f._closeSteps(z);return}if(s._queueTotalSize>0){if(Jp(s,$)){let z=Kp($);Xp(s),f._chunkSteps(z);return}if(s._closeRequested){let z=new TypeError("Insufficient bytes to fill elements in the given buffer");et(s,z),f._errorSteps(z);return}}s._pendingPullIntos.push($),om(h,f),Er(s)}function lv(s,u){let f=s._controlledReadableByteStream;if(Ku(f))for(;im(f)>0;){let h=Bi(s);Vu(f,h)}}function cv(s,u,f){if(Qp(s,u,f),f.bytesFilled<f.elementSize)return;Bi(s);let h=f.bytesFilled%f.elementSize;if(h>0){let _=f.byteOffset+f.bytesFilled,E=Gp(f.buffer,_-h,_);Oi(s,E,0,E.byteLength)}f.bytesFilled-=h,Vu(s._controlledReadableByteStream,f),Zp(s)}function em(s,u){let f=s._pendingPullIntos.peek();Gu(s),s._controlledReadableByteStream._state==="closed"?lv(s):cv(s,u,f),Er(s)}function Bi(s){return s._pendingPullIntos.shift()}function fv(s){let u=s._controlledReadableByteStream;return u._state!=="readable"||s._closeRequested||!s._started?!1:!!(Np(u)&&$i(u)>0||Ku(u)&&im(u)>0||tm(s)>0)}function Ii(s){s._pullAlgorithm=void 0,s._cancelAlgorithm=void 0}function io(s){let u=s._controlledReadableByteStream;if(!(s._closeRequested||u._state!=="readable")){if(s._queueTotalSize>0){s._closeRequested=!0;return}if(s._pendingPullIntos.length>0&&s._pendingPullIntos.peek().bytesFilled>0){let h=new TypeError("Insufficient bytes to fill elements in the given buffer");throw et(s,h),h}Ii(s),ho(u)}}function ki(s,u){let f=s._controlledReadableByteStream;if(s._closeRequested||f._state!=="readable")return;let h=u.buffer,_=u.byteOffset,E=u.byteLength,F=h;if(s._pendingPullIntos.length>0){let $=s._pendingPullIntos.peek();Pi($.buffer),$.buffer=$.buffer}if(Gu(s),Np(f))if($i(f)===0)Oi(s,F,_,E);else{s._pendingPullIntos.length>0&&Bi(s);let $=new Uint8Array(F,_,E);qu(f,$,!1)}else Ku(f)?(Oi(s,F,_,E),Zp(s)):Oi(s,F,_,E);Er(s)}function et(s,u){let f=s._controlledReadableByteStream;f._state==="readable"&&(Yp(s),Zt(s),Ii(s),Am(f,u))}function Hu(s){if(s._byobRequest===null&&s._pendingPullIntos.length>0){let u=s._pendingPullIntos.peek(),f=new Uint8Array(u.buffer,u.byteOffset+u.bytesFilled,u.byteLength-u.bytesFilled),h=Object.create(oo.prototype);pv(h,s,f),s._byobRequest=h}return s._byobRequest}function tm(s){let u=s._controlledReadableByteStream._state;return u==="errored"?null:u==="closed"?0:s._strategyHWM-s._queueTotalSize}function Mi(s,u){let f=s._pendingPullIntos.peek();if(s._controlledReadableByteStream._state==="closed"){if(u!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(u===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(f.bytesFilled+u>f.byteLength)throw new RangeError("bytesWritten out of range")}f.buffer=f.buffer,em(s,u)}function Ni(s,u){let f=s._pendingPullIntos.peek();if(s._controlledReadableByteStream._state==="closed"){if(u.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(u.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(f.byteOffset+f.bytesFilled!==u.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(f.bufferByteLength!==u.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(f.bytesFilled+u.byteLength>f.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let _=u.byteLength;f.buffer=u.buffer,em(s,_)}function rm(s,u,f,h,_,E,F){u._controlledReadableByteStream=s,u._pullAgain=!1,u._pulling=!1,u._byobRequest=null,u._queue=u._queueTotalSize=void 0,Zt(u),u._closeRequested=!1,u._started=!1,u._strategyHWM=E,u._pullAlgorithm=h,u._cancelAlgorithm=_,u._autoAllocateChunkSize=F,u._pendingPullIntos=new x,s._readableStreamController=u;let $=f();w(g($),()=>{u._started=!0,Er(u)},z=>{et(u,z)})}function dv(s,u,f){let h=Object.create(Kr.prototype),_=()=>{},E=()=>g(void 0),F=()=>g(void 0);u.start!==void 0&&(_=()=>u.start(h)),u.pull!==void 0&&(E=()=>u.pull(h)),u.cancel!==void 0&&(F=z=>u.cancel(z));let $=u.autoAllocateChunkSize;if($===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");rm(s,h,_,E,F,f,$)}function pv(s,u,f){s._associatedReadableByteStreamController=u,s._view=f}function Yu(s){return new TypeError(`ReadableStreamBYOBRequest.prototype.${s} can only be used on a ReadableStreamBYOBRequest`)}function so(s){return new TypeError(`ReadableByteStreamController.prototype.${s} can only be used on a ReadableByteStreamController`)}function nm(s){return new ao(s)}function om(s,u){s._reader._readIntoRequests.push(u)}function mv(s,u,f){let _=s._reader._readIntoRequests.shift();f?_._closeSteps(u):_._chunkSteps(u)}function im(s){return s._reader._readIntoRequests.length}function Ku(s){let u=s._reader;return!(u===void 0||!vr(u))}class ao{constructor(u){if(Ot(u,1,"ReadableStreamBYOBReader"),Lu(u,"First parameter"),nr(u))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Sr(u._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");k(this,u),this._readIntoRequests=new x}get closed(){return vr(this)?this._closedPromise:y(ji("closed"))}cancel(u=void 0){return vr(this)?this._ownerReadableStream===void 0?y(De("cancel")):oe(this,u):y(ji("cancel"))}read(u){if(!vr(this))return y(ji("read"));if(!ArrayBuffer.isView(u))return y(new TypeError("view must be an array buffer view"));if(u.byteLength===0)return y(new TypeError("view must have non-zero byteLength"));if(u.buffer.byteLength===0)return y(new TypeError("view's buffer must have non-zero byteLength"));if(Pi(u.buffer),this._ownerReadableStream===void 0)return y(De("read from"));let f,h,_=D((F,$)=>{f=F,h=$});return sm(this,u,{_chunkSteps:F=>f({value:F,done:!1}),_closeSteps:F=>f({value:F,done:!0}),_errorSteps:F=>h(F)}),_}releaseLock(){if(!vr(this))throw ji("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");re(this)}}}Object.defineProperties(ao.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(ao.prototype,n.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function vr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_readIntoRequests")?!1:s instanceof ao}function sm(s,u,f){let h=s._ownerReadableStream;h._disturbed=!0,h._state==="errored"?f._errorSteps(h._storedError):uv(h._readableStreamController,u,f)}function ji(s){return new TypeError(`ReadableStreamBYOBReader.prototype.${s} can only be used on a ReadableStreamBYOBReader`)}function uo(s,u){let{highWaterMark:f}=s;if(f===void 0)return u;if(Wp(f)||f<0)throw new RangeError("Invalid highWaterMark");return f}function Li(s){let{size:u}=s;return u||(()=>1)}function qi(s,u){Pt(s,u);let f=s?.highWaterMark,h=s?.size;return{highWaterMark:f===void 0?void 0:ju(f),size:h===void 0?void 0:hv(h,`${u} has member 'size' that`)}}function hv(s,u){return Ze(s,u),f=>ju(s(f))}function gv(s,u){Pt(s,u);let f=s?.abort,h=s?.close,_=s?.start,E=s?.type,F=s?.write;return{abort:f===void 0?void 0:yv(f,s,`${u} has member 'abort' that`),close:h===void 0?void 0:Dv(h,s,`${u} has member 'close' that`),start:_===void 0?void 0:bv(_,s,`${u} has member 'start' that`),write:F===void 0?void 0:_v(F,s,`${u} has member 'write' that`),type:E}}function yv(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function Dv(s,u,f){return Ze(s,f),()=>ae(s,u,[])}function bv(s,u,f){return Ze(s,f),h=>W(s,u,[h])}function _v(s,u,f){return Ze(s,f),(h,_)=>ae(s,u,[h,_])}function am(s,u){if(!Jr(s))throw new TypeError(`${u} is not a WritableStream.`)}function wv(s){if(typeof s!="object"||s===null)return!1;try{return typeof s.aborted=="boolean"}catch{return!1}}let Sv=typeof AbortController=="function";function Ev(){if(Sv)return new AbortController}class lo{constructor(u={},f={}){u===void 0?u=null:Bp(u,"First parameter");let h=qi(f,"Second parameter"),_=gv(u,"First parameter");if(lm(this),_.type!==void 0)throw new RangeError("Invalid type is specified");let F=Li(h),$=uo(h,1);Mv(this,_,$,F)}get locked(){if(!Jr(this))throw Gi("locked");return Qr(this)}abort(u=void 0){return Jr(this)?Qr(this)?y(new TypeError("Cannot abort a stream that already has a writer")):Ui(this,u):y(Gi("abort"))}close(){return Jr(this)?Qr(this)?y(new TypeError("Cannot close a stream that already has a writer")):Dt(this)?y(new TypeError("Cannot close an already-closing stream")):cm(this):y(Gi("close"))}getWriter(){if(!Jr(this))throw Gi("getWriter");return um(this)}}Object.defineProperties(lo.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(lo.prototype,n.toStringTag,{value:"WritableStream",configurable:!0});function um(s){return new co(s)}function vv(s,u,f,h,_=1,E=()=>1){let F=Object.create(lo.prototype);lm(F);let $=Object.create(Xr.prototype);return gm(F,$,s,u,f,h,_,E),F}function lm(s){s._state="writable",s._storedError=void 0,s._writer=void 0,s._writableStreamController=void 0,s._writeRequests=new x,s._inFlightWriteRequest=void 0,s._closeRequest=void 0,s._inFlightCloseRequest=void 0,s._pendingAbortRequest=void 0,s._backpressure=!1}function Jr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_writableStreamController")?!1:s instanceof lo}function Qr(s){return s._writer!==void 0}function Ui(s,u){var f;if(s._state==="closed"||s._state==="errored")return g(void 0);s._writableStreamController._abortReason=u,(f=s._writableStreamController._abortController)===null||f===void 0||f.abort();let h=s._state;if(h==="closed"||h==="errored")return g(void 0);if(s._pendingAbortRequest!==void 0)return s._pendingAbortRequest._promise;let _=!1;h==="erroring"&&(_=!0,u=void 0);let E=D((F,$)=>{s._pendingAbortRequest={_promise:void 0,_resolve:F,_reject:$,_reason:u,_wasAlreadyErroring:_}});return s._pendingAbortRequest._promise=E,_||Qu(s,u),E}function cm(s){let u=s._state;if(u==="closed"||u==="errored")return y(new TypeError(`The stream (in ${u} state) is not in the writable state and cannot be closed`));let f=D((_,E)=>{let F={_resolve:_,_reject:E};s._closeRequest=F}),h=s._writer;return h!==void 0&&s._backpressure&&u==="writable"&&sl(h),Nv(s._writableStreamController),f}function Fv(s){return D((f,h)=>{let _={_resolve:f,_reject:h};s._writeRequests.push(_)})}function Ju(s,u){if(s._state==="writable"){Qu(s,u);return}Xu(s)}function Qu(s,u){let f=s._writableStreamController;s._state="erroring",s._storedError=u;let h=s._writer;h!==void 0&&dm(h,u),!$v(s)&&f._started&&Xu(s)}function Xu(s){s._state="errored",s._writableStreamController[Pp]();let u=s._storedError;if(s._writeRequests.forEach(_=>{_._reject(u)}),s._writeRequests=new x,s._pendingAbortRequest===void 0){zi(s);return}let f=s._pendingAbortRequest;if(s._pendingAbortRequest=void 0,f._wasAlreadyErroring){f._reject(u),zi(s);return}let h=s._writableStreamController[xp](f._reason);w(h,()=>{f._resolve(),zi(s)},_=>{f._reject(_),zi(s)})}function Cv(s){s._inFlightWriteRequest._resolve(void 0),s._inFlightWriteRequest=void 0}function Tv(s,u){s._inFlightWriteRequest._reject(u),s._inFlightWriteRequest=void 0,Ju(s,u)}function Rv(s){s._inFlightCloseRequest._resolve(void 0),s._inFlightCloseRequest=void 0,s._state==="erroring"&&(s._storedError=void 0,s._pendingAbortRequest!==void 0&&(s._pendingAbortRequest._resolve(),s._pendingAbortRequest=void 0)),s._state="closed";let f=s._writer;f!==void 0&&_m(f)}function Av(s,u){s._inFlightCloseRequest._reject(u),s._inFlightCloseRequest=void 0,s._pendingAbortRequest!==void 0&&(s._pendingAbortRequest._reject(u),s._pendingAbortRequest=void 0),Ju(s,u)}function Dt(s){return!(s._closeRequest===void 0&&s._inFlightCloseRequest===void 0)}function $v(s){return!(s._inFlightWriteRequest===void 0&&s._inFlightCloseRequest===void 0)}function xv(s){s._inFlightCloseRequest=s._closeRequest,s._closeRequest=void 0}function Pv(s){s._inFlightWriteRequest=s._writeRequests.shift()}function zi(s){s._closeRequest!==void 0&&(s._closeRequest._reject(s._storedError),s._closeRequest=void 0);let u=s._writer;u!==void 0&&ol(u,s._storedError)}function Zu(s,u){let f=s._writer;f!==void 0&&u!==s._backpressure&&(u?Vv(f):sl(f)),s._backpressure=u}class co{constructor(u){if(Ot(u,1,"WritableStreamDefaultWriter"),am(u,"First parameter"),Qr(u))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=u,u._writer=this;let f=u._state;if(f==="writable")!Dt(u)&&u._backpressure?Yi(this):wm(this),Hi(this);else if(f==="erroring")il(this,u._storedError),Hi(this);else if(f==="closed")wm(this),zv(this);else{let h=u._storedError;il(this,h),bm(this,h)}}get closed(){return Fr(this)?this._closedPromise:y(Cr("closed"))}get desiredSize(){if(!Fr(this))throw Cr("desiredSize");if(this._ownerWritableStream===void 0)throw fo("desiredSize");return kv(this)}get ready(){return Fr(this)?this._readyPromise:y(Cr("ready"))}abort(u=void 0){return Fr(this)?this._ownerWritableStream===void 0?y(fo("abort")):Ov(this,u):y(Cr("abort"))}close(){if(!Fr(this))return y(Cr("close"));let u=this._ownerWritableStream;return u===void 0?y(fo("close")):Dt(u)?y(new TypeError("Cannot close an already-closing stream")):fm(this)}releaseLock(){if(!Fr(this))throw Cr("releaseLock");this._ownerWritableStream!==void 0&&pm(this)}write(u=void 0){return Fr(this)?this._ownerWritableStream===void 0?y(fo("write to")):mm(this,u):y(Cr("write"))}}Object.defineProperties(co.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(co.prototype,n.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function Fr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_ownerWritableStream")?!1:s instanceof co}function Ov(s,u){let f=s._ownerWritableStream;return Ui(f,u)}function fm(s){let u=s._ownerWritableStream;return cm(u)}function Bv(s){let u=s._ownerWritableStream,f=u._state;return Dt(u)||f==="closed"?g(void 0):f==="errored"?y(u._storedError):fm(s)}function Iv(s,u){s._closedPromiseState==="pending"?ol(s,u):Wv(s,u)}function dm(s,u){s._readyPromiseState==="pending"?Sm(s,u):Gv(s,u)}function kv(s){let u=s._ownerWritableStream,f=u._state;return f==="errored"||f==="erroring"?null:f==="closed"?0:ym(u._writableStreamController)}function pm(s){let u=s._ownerWritableStream,f=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");dm(s,f),Iv(s,f),u._writer=void 0,s._ownerWritableStream=void 0}function mm(s,u){let f=s._ownerWritableStream,h=f._writableStreamController,_=jv(h,u);if(f!==s._ownerWritableStream)return y(fo("write to"));let E=f._state;if(E==="errored")return y(f._storedError);if(Dt(f)||E==="closed")return y(new TypeError("The stream is closing or closed and cannot be written to"));if(E==="erroring")return y(f._storedError);let F=Fv(f);return Lv(h,u,_),F}let hm={};class Xr{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!el(this))throw nl("abortReason");return this._abortReason}get signal(){if(!el(this))throw nl("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(u=void 0){if(!el(this))throw nl("error");this._controlledWritableStream._state==="writable"&&Dm(this,u)}[xp](u){let f=this._abortAlgorithm(u);return Wi(this),f}[Pp](){Zt(this)}}Object.defineProperties(Xr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Xr.prototype,n.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function el(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledWritableStream")?!1:s instanceof Xr}function gm(s,u,f,h,_,E,F,$){u._controlledWritableStream=s,s._writableStreamController=u,u._queue=void 0,u._queueTotalSize=void 0,Zt(u),u._abortReason=void 0,u._abortController=Ev(),u._started=!1,u._strategySizeAlgorithm=$,u._strategyHWM=F,u._writeAlgorithm=h,u._closeAlgorithm=_,u._abortAlgorithm=E;let z=rl(u);Zu(s,z);let K=f(),ee=g(K);w(ee,()=>{u._started=!0,Vi(u)},te=>{u._started=!0,Ju(s,te)})}function Mv(s,u,f,h){let _=Object.create(Xr.prototype),E=()=>{},F=()=>g(void 0),$=()=>g(void 0),z=()=>g(void 0);u.start!==void 0&&(E=()=>u.start(_)),u.write!==void 0&&(F=K=>u.write(K,_)),u.close!==void 0&&($=()=>u.close()),u.abort!==void 0&&(z=K=>u.abort(K)),gm(s,_,E,F,$,z,f,h)}function Wi(s){s._writeAlgorithm=void 0,s._closeAlgorithm=void 0,s._abortAlgorithm=void 0,s._strategySizeAlgorithm=void 0}function Nv(s){zu(s,hm,0),Vi(s)}function jv(s,u){try{return s._strategySizeAlgorithm(u)}catch(f){return tl(s,f),1}}function ym(s){return s._strategyHWM-s._queueTotalSize}function Lv(s,u,f){try{zu(s,u,f)}catch(_){tl(s,_);return}let h=s._controlledWritableStream;if(!Dt(h)&&h._state==="writable"){let _=rl(s);Zu(h,_)}Vi(s)}function Vi(s){let u=s._controlledWritableStream;if(!s._started||u._inFlightWriteRequest!==void 0)return;if(u._state==="erroring"){Xu(u);return}if(s._queue.length===0)return;let h=av(s);h===hm?qv(s):Uv(s,h)}function tl(s,u){s._controlledWritableStream._state==="writable"&&Dm(s,u)}function qv(s){let u=s._controlledWritableStream;xv(u),Uu(s);let f=s._closeAlgorithm();Wi(s),w(f,()=>{Rv(u)},h=>{Av(u,h)})}function Uv(s,u){let f=s._controlledWritableStream;Pv(f);let h=s._writeAlgorithm(u);w(h,()=>{Cv(f);let _=f._state;if(Uu(s),!Dt(f)&&_==="writable"){let E=rl(s);Zu(f,E)}Vi(s)},_=>{f._state==="writable"&&Wi(s),Tv(f,_)})}function rl(s){return ym(s)<=0}function Dm(s,u){let f=s._controlledWritableStream;Wi(s),Qu(f,u)}function Gi(s){return new TypeError(`WritableStream.prototype.${s} can only be used on a WritableStream`)}function nl(s){return new TypeError(`WritableStreamDefaultController.prototype.${s} can only be used on a WritableStreamDefaultController`)}function Cr(s){return new TypeError(`WritableStreamDefaultWriter.prototype.${s} can only be used on a WritableStreamDefaultWriter`)}function fo(s){return new TypeError("Cannot "+s+" a stream using a released writer")}function Hi(s){s._closedPromise=D((u,f)=>{s._closedPromise_resolve=u,s._closedPromise_reject=f,s._closedPromiseState="pending"})}function bm(s,u){Hi(s),ol(s,u)}function zv(s){Hi(s),_m(s)}function ol(s,u){s._closedPromise_reject!==void 0&&(v(s._closedPromise),s._closedPromise_reject(u),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0,s._closedPromiseState="rejected")}function Wv(s,u){bm(s,u)}function _m(s){s._closedPromise_resolve!==void 0&&(s._closedPromise_resolve(void 0),s._closedPromise_resolve=void 0,s._closedPromise_reject=void 0,s._closedPromiseState="resolved")}function Yi(s){s._readyPromise=D((u,f)=>{s._readyPromise_resolve=u,s._readyPromise_reject=f}),s._readyPromiseState="pending"}function il(s,u){Yi(s),Sm(s,u)}function wm(s){Yi(s),sl(s)}function Sm(s,u){s._readyPromise_reject!==void 0&&(v(s._readyPromise),s._readyPromise_reject(u),s._readyPromise_resolve=void 0,s._readyPromise_reject=void 0,s._readyPromiseState="rejected")}function Vv(s){Yi(s)}function Gv(s,u){il(s,u)}function sl(s){s._readyPromise_resolve!==void 0&&(s._readyPromise_resolve(void 0),s._readyPromise_resolve=void 0,s._readyPromise_reject=void 0,s._readyPromiseState="fulfilled")}let Em=typeof DOMException<"u"?DOMException:void 0;function Hv(s){if(!(typeof s=="function"||typeof s=="object"))return!1;try{return new s,!0}catch{return!1}}function Yv(){let s=function(f,h){this.message=f||"",this.name=h||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return s.prototype=Object.create(Error.prototype),Object.defineProperty(s.prototype,"constructor",{value:s,writable:!0,configurable:!0}),s}let Kv=Hv(Em)?Em:Yv();function vm(s,u,f,h,_,E){let F=Yr(s),$=um(u);s._disturbed=!0;let z=!1,K=g(void 0);return D((ee,te)=>{let be;if(E!==void 0){if(be=()=>{let I=new Kv("Aborted","AbortError"),Y=[];h||Y.push(()=>u._state==="writable"?Ui(u,I):g(void 0)),_||Y.push(()=>s._state==="readable"?tt(s,I):g(void 0)),Pe(()=>Promise.all(Y.map(ue=>ue())),!0,I)},E.aborted){be();return}E.addEventListener("abort",be)}function rt(){return D((I,Y)=>{function ue(Ne){Ne?I():S(tn(),ue,Y)}ue(!1)})}function tn(){return z?g(!0):S($._readyPromise,()=>D((I,Y)=>{ro(F,{_chunkSteps:ue=>{K=S(mm($,ue),void 0,o),I(!1)},_closeSteps:()=>I(!0),_errorSteps:Y})}))}if(Bt(s,F._closedPromise,I=>{h?qe(!0,I):Pe(()=>Ui(u,I),!0,I)}),Bt(u,$._closedPromise,I=>{_?qe(!0,I):Pe(()=>tt(s,I),!0,I)}),Ae(s,F._closedPromise,()=>{f?qe():Pe(()=>Bv($))}),Dt(u)||u._state==="closed"){let I=new TypeError("the destination writable stream closed before all data could be piped to it");_?qe(!0,I):Pe(()=>tt(s,I),!0,I)}v(rt());function or(){let I=K;return S(K,()=>I!==K?or():void 0)}function Bt(I,Y,ue){I._state==="errored"?ue(I._storedError):R(Y,ue)}function Ae(I,Y,ue){I._state==="closed"?ue():T(Y,ue)}function Pe(I,Y,ue){if(z)return;z=!0,u._state==="writable"&&!Dt(u)?T(or(),Ne):Ne();function Ne(){w(I(),()=>It(Y,ue),rn=>It(!0,rn))}}function qe(I,Y){z||(z=!0,u._state==="writable"&&!Dt(u)?T(or(),()=>It(I,Y)):It(I,Y))}function It(I,Y){pm($),re(F),E!==void 0&&E.removeEventListener("abort",be),I?te(Y):ee(void 0)}})}class Zr{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Ki(this))throw Xi("desiredSize");return al(this)}close(){if(!Ki(this))throw Xi("close");if(!en(this))throw new TypeError("The stream is not in a state that permits close");mo(this)}enqueue(u=void 0){if(!Ki(this))throw Xi("enqueue");if(!en(this))throw new TypeError("The stream is not in a state that permits enqueue");return Qi(this,u)}error(u=void 0){if(!Ki(this))throw Xi("error");er(this,u)}[ku](u){Zt(this);let f=this._cancelAlgorithm(u);return Ji(this),f}[Mu](u){let f=this._controlledReadableStream;if(this._queue.length>0){let h=Uu(this);this._closeRequested&&this._queue.length===0?(Ji(this),ho(f)):po(this),u._chunkSteps(h)}else Mp(f,u),po(this)}}Object.defineProperties(Zr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Zr.prototype,n.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Ki(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledReadableStream")?!1:s instanceof Zr}function po(s){if(!Fm(s))return;if(s._pulling){s._pullAgain=!0;return}s._pulling=!0;let f=s._pullAlgorithm();w(f,()=>{s._pulling=!1,s._pullAgain&&(s._pullAgain=!1,po(s))},h=>{er(s,h)})}function Fm(s){let u=s._controlledReadableStream;return!en(s)||!s._started?!1:!!(nr(u)&&$i(u)>0||al(s)>0)}function Ji(s){s._pullAlgorithm=void 0,s._cancelAlgorithm=void 0,s._strategySizeAlgorithm=void 0}function mo(s){if(!en(s))return;let u=s._controlledReadableStream;s._closeRequested=!0,s._queue.length===0&&(Ji(s),ho(u))}function Qi(s,u){if(!en(s))return;let f=s._controlledReadableStream;if(nr(f)&&$i(f)>0)qu(f,u,!1);else{let h;try{h=s._strategySizeAlgorithm(u)}catch(_){throw er(s,_),_}try{zu(s,u,h)}catch(_){throw er(s,_),_}}po(s)}function er(s,u){let f=s._controlledReadableStream;f._state==="readable"&&(Zt(s),Ji(s),Am(f,u))}function al(s){let u=s._controlledReadableStream._state;return u==="errored"?null:u==="closed"?0:s._strategyHWM-s._queueTotalSize}function Jv(s){return!Fm(s)}function en(s){let u=s._controlledReadableStream._state;return!s._closeRequested&&u==="readable"}function Cm(s,u,f,h,_,E,F){u._controlledReadableStream=s,u._queue=void 0,u._queueTotalSize=void 0,Zt(u),u._started=!1,u._closeRequested=!1,u._pullAgain=!1,u._pulling=!1,u._strategySizeAlgorithm=F,u._strategyHWM=E,u._pullAlgorithm=h,u._cancelAlgorithm=_,s._readableStreamController=u;let $=f();w(g($),()=>{u._started=!0,po(u)},z=>{er(u,z)})}function Qv(s,u,f,h){let _=Object.create(Zr.prototype),E=()=>{},F=()=>g(void 0),$=()=>g(void 0);u.start!==void 0&&(E=()=>u.start(_)),u.pull!==void 0&&(F=()=>u.pull(_)),u.cancel!==void 0&&($=z=>u.cancel(z)),Cm(s,_,E,F,$,f,h)}function Xi(s){return new TypeError(`ReadableStreamDefaultController.prototype.${s} can only be used on a ReadableStreamDefaultController`)}function Xv(s,u){return Sr(s._readableStreamController)?eF(s):Zv(s)}function Zv(s,u){let f=Yr(s),h=!1,_=!1,E=!1,F=!1,$,z,K,ee,te,be=D(Ae=>{te=Ae});function rt(){return h?(_=!0,g(void 0)):(h=!0,ro(f,{_chunkSteps:Pe=>{B(()=>{_=!1;let qe=Pe,It=Pe;E||Qi(K._readableStreamController,qe),F||Qi(ee._readableStreamController,It),h=!1,_&&rt()})},_closeSteps:()=>{h=!1,E||mo(K._readableStreamController),F||mo(ee._readableStreamController),(!E||!F)&&te(void 0)},_errorSteps:()=>{h=!1}}),g(void 0))}function tn(Ae){if(E=!0,$=Ae,F){let Pe=no([$,z]),qe=tt(s,Pe);te(qe)}return be}function or(Ae){if(F=!0,z=Ae,E){let Pe=no([$,z]),qe=tt(s,Pe);te(qe)}return be}function Bt(){}return K=ul(Bt,rt,tn),ee=ul(Bt,rt,or),R(f._closedPromise,Ae=>{er(K._readableStreamController,Ae),er(ee._readableStreamController,Ae),(!E||!F)&&te(void 0)}),[K,ee]}function eF(s){let u=Yr(s),f=!1,h=!1,_=!1,E=!1,F=!1,$,z,K,ee,te,be=D(I=>{te=I});function rt(I){R(I._closedPromise,Y=>{I===u&&(et(K._readableStreamController,Y),et(ee._readableStreamController,Y),(!E||!F)&&te(void 0))})}function tn(){vr(u)&&(re(u),u=Yr(s),rt(u)),ro(u,{_chunkSteps:Y=>{B(()=>{h=!1,_=!1;let ue=Y,Ne=Y;if(!E&&!F)try{Ne=Hp(Y)}catch(rn){et(K._readableStreamController,rn),et(ee._readableStreamController,rn),te(tt(s,rn));return}E||ki(K._readableStreamController,ue),F||ki(ee._readableStreamController,Ne),f=!1,h?Bt():_&&Ae()})},_closeSteps:()=>{f=!1,E||io(K._readableStreamController),F||io(ee._readableStreamController),K._readableStreamController._pendingPullIntos.length>0&&Mi(K._readableStreamController,0),ee._readableStreamController._pendingPullIntos.length>0&&Mi(ee._readableStreamController,0),(!E||!F)&&te(void 0)},_errorSteps:()=>{f=!1}})}function or(I,Y){Xt(u)&&(re(u),u=nm(s),rt(u));let ue=Y?ee:K,Ne=Y?K:ee;sm(u,I,{_chunkSteps:nn=>{B(()=>{h=!1,_=!1;let on=Y?F:E;if(Y?E:F)on||Ni(ue._readableStreamController,nn);else{let Um;try{Um=Hp(nn)}catch(cl){et(ue._readableStreamController,cl),et(Ne._readableStreamController,cl),te(tt(s,cl));return}on||Ni(ue._readableStreamController,nn),ki(Ne._readableStreamController,Um)}f=!1,h?Bt():_&&Ae()})},_closeSteps:nn=>{f=!1;let on=Y?F:E,as=Y?E:F;on||io(ue._readableStreamController),as||io(Ne._readableStreamController),nn!==void 0&&(on||Ni(ue._readableStreamController,nn),!as&&Ne._readableStreamController._pendingPullIntos.length>0&&Mi(Ne._readableStreamController,0)),(!on||!as)&&te(void 0)},_errorSteps:()=>{f=!1}})}function Bt(){if(f)return h=!0,g(void 0);f=!0;let I=Hu(K._readableStreamController);return I===null?tn():or(I._view,!1),g(void 0)}function Ae(){if(f)return _=!0,g(void 0);f=!0;let I=Hu(ee._readableStreamController);return I===null?tn():or(I._view,!0),g(void 0)}function Pe(I){if(E=!0,$=I,F){let Y=no([$,z]),ue=tt(s,Y);te(ue)}return be}function qe(I){if(F=!0,z=I,E){let Y=no([$,z]),ue=tt(s,Y);te(ue)}return be}function It(){}return K=Rm(It,Bt,Pe),ee=Rm(It,Ae,qe),rt(u),[K,ee]}function tF(s,u){Pt(s,u);let f=s,h=f?.autoAllocateChunkSize,_=f?.cancel,E=f?.pull,F=f?.start,$=f?.type;return{autoAllocateChunkSize:h===void 0?void 0:kp(h,`${u} has member 'autoAllocateChunkSize' that`),cancel:_===void 0?void 0:rF(_,f,`${u} has member 'cancel' that`),pull:E===void 0?void 0:nF(E,f,`${u} has member 'pull' that`),start:F===void 0?void 0:oF(F,f,`${u} has member 'start' that`),type:$===void 0?void 0:iF($,`${u} has member 'type' that`)}}function rF(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function nF(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function oF(s,u,f){return Ze(s,f),h=>W(s,u,[h])}function iF(s,u){if(s=`${s}`,s!=="bytes")throw new TypeError(`${u} '${s}' is not a valid enumeration value for ReadableStreamType`);return s}function sF(s,u){Pt(s,u);let f=s?.mode;return{mode:f===void 0?void 0:aF(f,`${u} has member 'mode' that`)}}function aF(s,u){if(s=`${s}`,s!=="byob")throw new TypeError(`${u} '${s}' is not a valid enumeration value for ReadableStreamReaderMode`);return s}function uF(s,u){return Pt(s,u),{preventCancel:!!s?.preventCancel}}function Tm(s,u){Pt(s,u);let f=s?.preventAbort,h=s?.preventCancel,_=s?.preventClose,E=s?.signal;return E!==void 0&&lF(E,`${u} has member 'signal' that`),{preventAbort:!!f,preventCancel:!!h,preventClose:!!_,signal:E}}function lF(s,u){if(!wv(s))throw new TypeError(`${u} is not an AbortSignal.`)}function cF(s,u){Pt(s,u);let f=s?.readable;Nu(f,"readable","ReadableWritablePair"),Lu(f,`${u} has member 'readable' that`);let h=s?.writable;return Nu(h,"writable","ReadableWritablePair"),am(h,`${u} has member 'writable' that`),{readable:f,writable:h}}class tr{constructor(u={},f={}){u===void 0?u=null:Bp(u,"First parameter");let h=qi(f,"Second parameter"),_=tF(u,"First parameter");if(ll(this),_.type==="bytes"){if(h.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let E=uo(h,0);dv(this,_,E)}else{let E=Li(h),F=uo(h,1);Qv(this,_,F,E)}}get locked(){if(!rr(this))throw Tr("locked");return nr(this)}cancel(u=void 0){return rr(this)?nr(this)?y(new TypeError("Cannot cancel a stream that already has a reader")):tt(this,u):y(Tr("cancel"))}getReader(u=void 0){if(!rr(this))throw Tr("getReader");return sF(u,"First parameter").mode===void 0?Yr(this):nm(this)}pipeThrough(u,f={}){if(!rr(this))throw Tr("pipeThrough");Ot(u,1,"pipeThrough");let h=cF(u,"First parameter"),_=Tm(f,"Second parameter");if(nr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Qr(h.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let E=vm(this,h.writable,_.preventClose,_.preventAbort,_.preventCancel,_.signal);return v(E),h.readable}pipeTo(u,f={}){if(!rr(this))return y(Tr("pipeTo"));if(u===void 0)return y("Parameter 1 is required in 'pipeTo'.");if(!Jr(u))return y(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let h;try{h=Tm(f,"Second parameter")}catch(_){return y(_)}return nr(this)?y(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Qr(u)?y(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):vm(this,u,h.preventClose,h.preventAbort,h.preventCancel,h.signal)}tee(){if(!rr(this))throw Tr("tee");let u=Xv(this);return no(u)}values(u=void 0){if(!rr(this))throw Tr("values");let f=uF(u,"First parameter");return iv(this,f.preventCancel)}}Object.defineProperties(tr.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(tr.prototype,n.toStringTag,{value:"ReadableStream",configurable:!0}),typeof n.asyncIterator=="symbol"&&Object.defineProperty(tr.prototype,n.asyncIterator,{value:tr.prototype.values,writable:!0,configurable:!0});function ul(s,u,f,h=1,_=()=>1){let E=Object.create(tr.prototype);ll(E);let F=Object.create(Zr.prototype);return Cm(E,F,s,u,f,h,_),E}function Rm(s,u,f){let h=Object.create(tr.prototype);ll(h);let _=Object.create(Kr.prototype);return rm(h,_,s,u,f,0,void 0),h}function ll(s){s._state="readable",s._reader=void 0,s._storedError=void 0,s._disturbed=!1}function rr(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_readableStreamController")?!1:s instanceof tr}function nr(s){return s._reader!==void 0}function tt(s,u){if(s._disturbed=!0,s._state==="closed")return g(void 0);if(s._state==="errored")return y(s._storedError);ho(s);let f=s._reader;f!==void 0&&vr(f)&&(f._readIntoRequests.forEach(_=>{_._closeSteps(void 0)}),f._readIntoRequests=new x);let h=s._readableStreamController[ku](u);return A(h,o)}function ho(s){s._state="closed";let u=s._reader;u!==void 0&&($p(u),Xt(u)&&(u._readRequests.forEach(f=>{f._closeSteps()}),u._readRequests=new x))}function Am(s,u){s._state="errored",s._storedError=u;let f=s._reader;f!==void 0&&(Hr(f,u),Xt(f)?(f._readRequests.forEach(h=>{h._errorSteps(u)}),f._readRequests=new x):(f._readIntoRequests.forEach(h=>{h._errorSteps(u)}),f._readIntoRequests=new x))}function Tr(s){return new TypeError(`ReadableStream.prototype.${s} can only be used on a ReadableStream`)}function $m(s,u){Pt(s,u);let f=s?.highWaterMark;return Nu(f,"highWaterMark","QueuingStrategyInit"),{highWaterMark:ju(f)}}let xm=s=>s.byteLength;try{Object.defineProperty(xm,"name",{value:"size",configurable:!0})}catch{}class Zi{constructor(u){Ot(u,1,"ByteLengthQueuingStrategy"),u=$m(u,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=u.highWaterMark}get highWaterMark(){if(!Om(this))throw Pm("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Om(this))throw Pm("size");return xm}}Object.defineProperties(Zi.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(Zi.prototype,n.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Pm(s){return new TypeError(`ByteLengthQueuingStrategy.prototype.${s} can only be used on a ByteLengthQueuingStrategy`)}function Om(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_byteLengthQueuingStrategyHighWaterMark")?!1:s instanceof Zi}let Bm=()=>1;try{Object.defineProperty(Bm,"name",{value:"size",configurable:!0})}catch{}class es{constructor(u){Ot(u,1,"CountQueuingStrategy"),u=$m(u,"First parameter"),this._countQueuingStrategyHighWaterMark=u.highWaterMark}get highWaterMark(){if(!km(this))throw Im("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!km(this))throw Im("size");return Bm}}Object.defineProperties(es.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(es.prototype,n.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function Im(s){return new TypeError(`CountQueuingStrategy.prototype.${s} can only be used on a CountQueuingStrategy`)}function km(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_countQueuingStrategyHighWaterMark")?!1:s instanceof es}function fF(s,u){Pt(s,u);let f=s?.flush,h=s?.readableType,_=s?.start,E=s?.transform,F=s?.writableType;return{flush:f===void 0?void 0:dF(f,s,`${u} has member 'flush' that`),readableType:h,start:_===void 0?void 0:pF(_,s,`${u} has member 'start' that`),transform:E===void 0?void 0:mF(E,s,`${u} has member 'transform' that`),writableType:F}}function dF(s,u,f){return Ze(s,f),h=>ae(s,u,[h])}function pF(s,u,f){return Ze(s,f),h=>W(s,u,[h])}function mF(s,u,f){return Ze(s,f),(h,_)=>ae(s,u,[h,_])}class ts{constructor(u={},f={},h={}){u===void 0&&(u=null);let _=qi(f,"Second parameter"),E=qi(h,"Third parameter"),F=fF(u,"First parameter");if(F.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(F.writableType!==void 0)throw new RangeError("Invalid writableType specified");let $=uo(E,0),z=Li(E),K=uo(_,1),ee=Li(_),te,be=D(rt=>{te=rt});hF(this,be,K,ee,$,z),yF(this,F),F.start!==void 0?te(F.start(this._transformStreamController)):te(void 0)}get readable(){if(!Mm(this))throw qm("readable");return this._readable}get writable(){if(!Mm(this))throw qm("writable");return this._writable}}Object.defineProperties(ts.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(ts.prototype,n.toStringTag,{value:"TransformStream",configurable:!0});function hF(s,u,f,h,_,E){function F(){return u}function $(be){return _F(s,be)}function z(be){return wF(s,be)}function K(){return SF(s)}s._writable=vv(F,$,K,z,f,h);function ee(){return EF(s)}function te(be){return ns(s,be),g(void 0)}s._readable=ul(F,ee,te,_,E),s._backpressure=void 0,s._backpressureChangePromise=void 0,s._backpressureChangePromise_resolve=void 0,os(s,!0),s._transformStreamController=void 0}function Mm(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_transformStreamController")?!1:s instanceof ts}function rs(s,u){er(s._readable._readableStreamController,u),ns(s,u)}function ns(s,u){Nm(s._transformStreamController),tl(s._writable._writableStreamController,u),s._backpressure&&os(s,!1)}function os(s,u){s._backpressureChangePromise!==void 0&&s._backpressureChangePromise_resolve(),s._backpressureChangePromise=D(f=>{s._backpressureChangePromise_resolve=f}),s._backpressure=u}class go{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!is(this))throw ss("desiredSize");let u=this._controlledTransformStream._readable._readableStreamController;return al(u)}enqueue(u=void 0){if(!is(this))throw ss("enqueue");jm(this,u)}error(u=void 0){if(!is(this))throw ss("error");DF(this,u)}terminate(){if(!is(this))throw ss("terminate");bF(this)}}Object.defineProperties(go.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof n.toStringTag=="symbol"&&Object.defineProperty(go.prototype,n.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function is(s){return!l(s)||!Object.prototype.hasOwnProperty.call(s,"_controlledTransformStream")?!1:s instanceof go}function gF(s,u,f,h){u._controlledTransformStream=s,s._transformStreamController=u,u._transformAlgorithm=f,u._flushAlgorithm=h}function yF(s,u){let f=Object.create(go.prototype),h=E=>{try{return jm(f,E),g(void 0)}catch(F){return y(F)}},_=()=>g(void 0);u.transform!==void 0&&(h=E=>u.transform(E,f)),u.flush!==void 0&&(_=()=>u.flush(f)),gF(s,f,h,_)}function Nm(s){s._transformAlgorithm=void 0,s._flushAlgorithm=void 0}function jm(s,u){let f=s._controlledTransformStream,h=f._readable._readableStreamController;if(!en(h))throw new TypeError("Readable side is not in a state that permits enqueue");try{Qi(h,u)}catch(E){throw ns(f,E),f._readable._storedError}Jv(h)!==f._backpressure&&os(f,!0)}function DF(s,u){rs(s._controlledTransformStream,u)}function Lm(s,u){let f=s._transformAlgorithm(u);return A(f,void 0,h=>{throw rs(s._controlledTransformStream,h),h})}function bF(s){let u=s._controlledTransformStream,f=u._readable._readableStreamController;mo(f);let h=new TypeError("TransformStream terminated");ns(u,h)}function _F(s,u){let f=s._transformStreamController;if(s._backpressure){let h=s._backpressureChangePromise;return A(h,()=>{let _=s._writable;if(_._state==="erroring")throw _._storedError;return Lm(f,u)})}return Lm(f,u)}function wF(s,u){return rs(s,u),g(void 0)}function SF(s){let u=s._readable,f=s._transformStreamController,h=f._flushAlgorithm();return Nm(f),A(h,()=>{if(u._state==="errored")throw u._storedError;mo(u._readableStreamController)},_=>{throw rs(s,_),u._storedError})}function EF(s){return os(s,!1),s._backpressureChangePromise}function ss(s){return new TypeError(`TransformStreamDefaultController.prototype.${s} can only be used on a TransformStreamDefaultController`)}function qm(s){return new TypeError(`TransformStream.prototype.${s} can only be used on a TransformStream`)}r.ByteLengthQueuingStrategy=Zi,r.CountQueuingStrategy=es,r.ReadableByteStreamController=Kr,r.ReadableStream=tr,r.ReadableStreamBYOBReader=ao,r.ReadableStreamBYOBRequest=oo,r.ReadableStreamDefaultController=Zr,r.ReadableStreamDefaultReader=to,r.TransformStream=ts,r.TransformStreamDefaultController=go,r.WritableStream=lo,r.WritableStreamDefaultController=Xr,r.WritableStreamDefaultWriter=co,Object.defineProperty(r,"__esModule",{value:!0})})}}),ix=df({"node_modules/fetch-blob/streams.cjs"(){var e=65536;if(!globalThis.ReadableStream)try{let t=require("process"),{emitWarning:r}=t;try{t.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),t.emitWarning=r}catch(n){throw t.emitWarning=r,n}}catch{Object.assign(globalThis,ox())}try{let{Blob:t}=require("buffer");t&&!t.prototype.stream&&(t.prototype.stream=function(n){let o=0,i=this;return new ReadableStream({type:"bytes",async pull(a){let c=await i.slice(o,Math.min(i.size,o+e)).arrayBuffer();o+=c.byteLength,a.enqueue(new Uint8Array(c)),o===i.size&&a.close()}})})}catch{}}});async function*Zc(e,t=!0){for(let r of e)if("stream"in r)yield*r.stream();else if(ArrayBuffer.isView(r))if(t){let n=r.byteOffset,o=r.byteOffset+r.byteLength;for(;n!==o;){let i=Math.min(o-n,af),a=r.buffer.slice(n,n+i);n+=a.byteLength,yield new Uint8Array(a)}}else yield r;else{let n=0,o=r;for(;n!==o.size;){let a=await o.slice(n,Math.min(o.size,n+af)).arrayBuffer();n+=a.byteLength,yield new Uint8Array(a)}}}var sx,af,ef,uf,Rn,zo=Uo({"node_modules/fetch-blob/index.js"(){sx=Ee(ix()),af=65536,ef=class lf{#e=[];#r="";#t=0;#n="transparent";constructor(t=[],r={}){if(typeof t!="object"||t===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof t[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof r!="object"&&typeof r!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");r===null&&(r={});let n=new TextEncoder;for(let i of t){let a;ArrayBuffer.isView(i)?a=new Uint8Array(i.buffer.slice(i.byteOffset,i.byteOffset+i.byteLength)):i instanceof ArrayBuffer?a=new Uint8Array(i.slice(0)):i instanceof lf?a=i:a=n.encode(`${i}`),this.#t+=ArrayBuffer.isView(a)?a.byteLength:a.size,this.#e.push(a)}this.#n=`${r.endings===void 0?"transparent":r.endings}`;let o=r.type===void 0?"":String(r.type);this.#r=/^[\x20-\x7E]*$/.test(o)?o:""}get size(){return this.#t}get type(){return this.#r}async text(){let t=new TextDecoder,r="";for await(let n of Zc(this.#e,!1))r+=t.decode(n,{stream:!0});return r+=t.decode(),r}async arrayBuffer(){let t=new Uint8Array(this.size),r=0;for await(let n of Zc(this.#e,!1))t.set(n,r),r+=n.length;return t.buffer}stream(){let t=Zc(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(r){let n=await t.next();n.done?r.close():r.enqueue(n.value)},async cancel(){await t.return()}})}slice(t=0,r=this.size,n=""){let{size:o}=this,i=t<0?Math.max(o+t,0):Math.min(t,o),a=r<0?Math.max(o+r,0):Math.min(r,o),l=Math.max(a-i,0),c=this.#e,d=[],p=0;for(let b of c){if(p>=l)break;let D=ArrayBuffer.isView(b)?b.byteLength:b.size;if(i&&D<=i)i-=D,a-=D;else{let g;ArrayBuffer.isView(b)?(g=b.subarray(i,Math.min(D,a)),p+=g.byteLength):(g=b.slice(i,Math.min(D,a)),p+=g.size),a-=D,d.push(g),i=0}}let m=new lf([],{type:String(n).toLowerCase()});return m.#t=l,m.#e=d,m}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](t){return t&&typeof t=="object"&&typeof t.constructor=="function"&&(typeof t.stream=="function"||typeof t.arrayBuffer=="function")&&/^(Blob|File)$/.test(t[Symbol.toStringTag])}},Object.defineProperties(ef.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),uf=ef,Rn=uf}}),Pb,Ob,Wo,Yb=Uo({"node_modules/fetch-blob/file.js"(){zo(),Pb=class extends Rn{#e=0;#r="";constructor(t,r,n={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(t,n),n===null&&(n={});let o=n.lastModified===void 0?Date.now():Number(n.lastModified);Number.isNaN(o)||(this.#e=o),this.#r=String(r)}get name(){return this.#r}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](t){return!!t&&t instanceof Rn&&/^(File)$/.test(t[Symbol.toStringTag])}},Ob=Pb,Wo=Ob}});function ax(e,t=Rn){var r=`${cf()}${cf()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),n=[],o=`--${r}\r
Content-Disposition: form-data; name="`;return e.forEach((i,a)=>typeof i=="string"?n.push(o+Aa(a)+`"\r
\r
${i.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):n.push(o+Aa(a)+`"; filename="${Aa(i.name,1)}"\r
Content-Type: ${i.type||"application/octet-stream"}\r
\r
`,i,`\r
`)),n.push(`--${r}--`),new t(n,{type:"multipart/form-data; boundary="+r})}var vn,Bb,Ib,cf,kb,tf,Aa,fr,An,Ia=Uo({"node_modules/formdata-polyfill/esm.min.js"(){zo(),Yb(),{toStringTag:vn,iterator:Bb,hasInstance:Ib}=Symbol,cf=Math.random,kb="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),tf=(e,t,r)=>(e+="",/^(Blob|File)$/.test(t&&t[vn])?[(r=r!==void 0?r+"":t[vn]=="File"?t.name:"blob",e),t.name!==r||t[vn]=="blob"?new Wo([t],r,t):t]:[e,t+""]),Aa=(e,t)=>(t?e:e.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),fr=(e,t,r)=>{if(t.length<r)throw new TypeError(`Failed to execute '${e}' on 'FormData': ${r} arguments required, but only ${t.length} present.`)},An=class{#e=[];constructor(...t){if(t.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[vn](){return"FormData"}[Bb](){return this.entries()}static[Ib](t){return t&&typeof t=="object"&&t[vn]==="FormData"&&!kb.some(r=>typeof t[r]!="function")}append(...t){fr("append",arguments,2),this.#e.push(tf(...t))}delete(t){fr("delete",arguments,1),t+="",this.#e=this.#e.filter(([r])=>r!==t)}get(t){fr("get",arguments,1),t+="";for(var r=this.#e,n=r.length,o=0;o<n;o++)if(r[o][0]===t)return r[o][1];return null}getAll(t,r){return fr("getAll",arguments,1),r=[],t+="",this.#e.forEach(n=>n[0]===t&&r.push(n[1])),r}has(t){return fr("has",arguments,1),t+="",this.#e.some(r=>r[0]===t)}forEach(t,r){fr("forEach",arguments,1);for(var[n,o]of this)t.call(r,o,n,this)}set(...t){fr("set",arguments,2);var r=[],n=!0;t=tf(...t),this.#e.forEach(o=>{o[0]===t[0]?n&&(n=!r.push(t)):r.push(o)}),n&&r.push(t),this.#e=r}*entries(){yield*this.#e}*keys(){for(var[t]of this)yield t}*values(){for(var[,t]of this)yield t}}}}),ux=df({"node_modules/node-domexception/index.js"(e,t){if(!globalThis.DOMException)try{let{MessageChannel:r}=require("worker_threads"),n=new r().port1,o=new ArrayBuffer;n.postMessage(o,[o,o])}catch(r){r.constructor.name==="DOMException"&&(globalThis.DOMException=r.constructor)}t.exports=globalThis.DOMException}}),ko,Mb,Nb,Ca,Kb,Jb,Qb,Xb,rf,nf,Ta,Zb=Uo({"node_modules/fetch-blob/from.js"(){ko=Ee(require("fs")),Mb=Ee(require("path")),Nb=Ee(ux()),Yb(),zo(),{stat:Ca}=ko.promises,Kb=(e,t)=>rf((0,ko.statSync)(e),e,t),Jb=(e,t)=>Ca(e).then(r=>rf(r,e,t)),Qb=(e,t)=>Ca(e).then(r=>nf(r,e,t)),Xb=(e,t)=>nf((0,ko.statSync)(e),e,t),rf=(e,t,r="")=>new Rn([new Ta({path:t,size:e.size,lastModified:e.mtimeMs,start:0})],{type:r}),nf=(e,t,r="")=>new Wo([new Ta({path:t,size:e.size,lastModified:e.mtimeMs,start:0})],(0,Mb.basename)(t),{type:r,lastModified:e.mtimeMs}),Ta=class{#e;#r;constructor(e){this.#e=e.path,this.#r=e.start,this.size=e.size,this.lastModified=e.lastModified}slice(e,t){return new Ta({path:this.#e,lastModified:this.lastModified,size:t-e,start:this.#r+e})}async*stream(){let{mtimeMs:e}=await Ca(this.#e);if(e>this.lastModified)throw new Nb.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,ko.createReadStream)(this.#e,{start:this.#r,end:this.#r+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),e_={};Hb(e_,{toFormData:()=>cx});function lx(e){let t=e.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!t)return;let r=t[2]||t[3]||"",n=r.slice(r.lastIndexOf("\\")+1);return n=n.replace(/%22/g,'"'),n=n.replace(/&#(\d{4});/g,(o,i)=>String.fromCharCode(i)),n}async function cx(e,t){if(!/multipart/i.test(t))throw new TypeError("Failed to fetch");let r=t.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!r)throw new TypeError("no or bad content-type header, no multipart boundary");let n=new t_(r[1]||r[2]),o,i,a,l,c,d,p=[],m=new An,b=w=>{a+=S.decode(w,{stream:!0})},D=w=>{p.push(w)},g=()=>{let w=new Wo(p,d,{type:c});m.append(l,w)},y=()=>{m.append(l,a)},S=new TextDecoder("utf-8");S.decode(),n.onPartBegin=function(){n.onPartData=b,n.onPartEnd=y,o="",i="",a="",l="",c="",d=null,p.length=0},n.onHeaderField=function(w){o+=S.decode(w,{stream:!0})},n.onHeaderValue=function(w){i+=S.decode(w,{stream:!0})},n.onHeaderEnd=function(){if(i+=S.decode(),o=o.toLowerCase(),o==="content-disposition"){let w=i.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);w&&(l=w[2]||w[3]||""),d=lx(i),d&&(n.onPartData=D,n.onPartEnd=g)}else o==="content-type"&&(c=i);i="",o=""};for await(let w of e)n.write(w);return n.end(),m}var at,ie,of,Lt,Mo,No,jb,Fn,Lb,qb,Ub,zb,dr,t_,fx=Uo({"node_modules/node-fetch/src/utils/multipart-parser.js"(){Zb(),Ia(),at=0,ie={START_BOUNDARY:at++,HEADER_FIELD_START:at++,HEADER_FIELD:at++,HEADER_VALUE_START:at++,HEADER_VALUE:at++,HEADER_VALUE_ALMOST_DONE:at++,HEADERS_ALMOST_DONE:at++,PART_DATA_START:at++,PART_DATA:at++,END:at++},of=1,Lt={PART_BOUNDARY:of,LAST_BOUNDARY:of*=2},Mo=10,No=13,jb=32,Fn=45,Lb=58,qb=97,Ub=122,zb=e=>e|32,dr=()=>{},t_=class{constructor(e){this.index=0,this.flags=0,this.onHeaderEnd=dr,this.onHeaderField=dr,this.onHeadersEnd=dr,this.onHeaderValue=dr,this.onPartBegin=dr,this.onPartData=dr,this.onPartEnd=dr,this.boundaryChars={},e=`\r
--`+e;let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r),this.boundaryChars[t[r]]=!0;this.boundary=t,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=ie.START_BOUNDARY}write(e){let t=0,r=e.length,n=this.index,{lookbehind:o,boundary:i,boundaryChars:a,index:l,state:c,flags:d}=this,p=this.boundary.length,m=p-1,b=e.length,D,g,y=R=>{this[R+"Mark"]=t},S=R=>{delete this[R+"Mark"]},w=(R,A,v,B)=>{(A===void 0||A!==v)&&this[R](B&&B.subarray(A,v))},T=(R,A)=>{let v=R+"Mark";v in this&&(A?(w(R,this[v],t,e),delete this[v]):(w(R,this[v],e.length,e),this[v]=0))};for(t=0;t<r;t++)switch(D=e[t],c){case ie.START_BOUNDARY:if(l===i.length-2){if(D===Fn)d|=Lt.LAST_BOUNDARY;else if(D!==No)return;l++;break}else if(l-1===i.length-2){if(d&Lt.LAST_BOUNDARY&&D===Fn)c=ie.END,d=0;else if(!(d&Lt.LAST_BOUNDARY)&&D===Mo)l=0,w("onPartBegin"),c=ie.HEADER_FIELD_START;else return;break}D!==i[l+2]&&(l=-2),D===i[l+2]&&l++;break;case ie.HEADER_FIELD_START:c=ie.HEADER_FIELD,y("onHeaderField"),l=0;case ie.HEADER_FIELD:if(D===No){S("onHeaderField"),c=ie.HEADERS_ALMOST_DONE;break}if(l++,D===Fn)break;if(D===Lb){if(l===1)return;T("onHeaderField",!0),c=ie.HEADER_VALUE_START;break}if(g=zb(D),g<qb||g>Ub)return;break;case ie.HEADER_VALUE_START:if(D===jb)break;y("onHeaderValue"),c=ie.HEADER_VALUE;case ie.HEADER_VALUE:D===No&&(T("onHeaderValue",!0),w("onHeaderEnd"),c=ie.HEADER_VALUE_ALMOST_DONE);break;case ie.HEADER_VALUE_ALMOST_DONE:if(D!==Mo)return;c=ie.HEADER_FIELD_START;break;case ie.HEADERS_ALMOST_DONE:if(D!==Mo)return;w("onHeadersEnd"),c=ie.PART_DATA_START;break;case ie.PART_DATA_START:c=ie.PART_DATA,y("onPartData");case ie.PART_DATA:if(n=l,l===0){for(t+=m;t<b&&!(e[t]in a);)t+=p;t-=m,D=e[t]}if(l<i.length)i[l]===D?(l===0&&T("onPartData",!0),l++):l=0;else if(l===i.length)l++,D===No?d|=Lt.PART_BOUNDARY:D===Fn?d|=Lt.LAST_BOUNDARY:l=0;else if(l-1===i.length)if(d&Lt.PART_BOUNDARY){if(l=0,D===Mo){d&=~Lt.PART_BOUNDARY,w("onPartEnd"),w("onPartBegin"),c=ie.HEADER_FIELD_START;break}}else d&Lt.LAST_BOUNDARY&&D===Fn?(w("onPartEnd"),c=ie.END,d=0):l=0;if(l>0)o[l-1]=D;else if(n>0){let R=new Uint8Array(o.buffer,o.byteOffset,o.byteLength);w("onPartData",0,n,R),n=0,y("onPartData"),t--}break;case ie.END:break;default:throw new Error(`Unexpected state entered: ${c}`)}T("onHeaderField"),T("onHeaderValue"),T("onPartData"),this.index=l,this.state=c,this.flags=d}end(){if(this.state===ie.HEADER_FIELD_START&&this.index===0||this.state===ie.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==ie.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});Hb(a_,{AbortError:()=>i_,Blob:()=>uf,FetchError:()=>lt,File:()=>Wo,FormData:()=>An,Headers:()=>qt,Request:()=>qo,Response:()=>Le,blobFrom:()=>Jb,blobFromSync:()=>Kb,default:()=>s_,fileFrom:()=>Qb,fileFromSync:()=>Xb,isRedirect:()=>mf});var dx=Ee(require("http")),px=Ee(require("https")),Cn=Ee(require("zlib")),Ft=Ee(require("stream")),Ra=Ee(require("buffer"));function mx(e){if(!/^data:/i.test(e))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');e=e.replace(/\r?\n/g,"");let t=e.indexOf(",");if(t===-1||t<=4)throw new TypeError("malformed data: URI");let r=e.substring(5,t).split(";"),n="",o=!1,i=r[0]||"text/plain",a=i;for(let p=1;p<r.length;p++)r[p]==="base64"?o=!0:r[p]&&(a+=`;${r[p]}`,r[p].indexOf("charset=")===0&&(n=r[p].substring(8)));!r[0]&&!n.length&&(a+=";charset=US-ASCII",n="US-ASCII");let l=o?"base64":"ascii",c=unescape(e.substring(t+1)),d=Buffer.from(c,l);return d.type=i,d.typeFull=a,d.charset=n,d}var hx=mx,Ct=Ee(require("stream")),$n=Ee(require("util")),We=Ee(require("buffer"));zo();Ia();var ka=class extends Error{constructor(e,t){super(e),Error.captureStackTrace(this,this.constructor),this.type=t}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},lt=class extends ka{constructor(e,t,r){super(e,t),r&&(this.code=this.errno=r.code,this.erroredSysCall=r.syscall)}},xa=Symbol.toStringTag,r_=e=>typeof e=="object"&&typeof e.append=="function"&&typeof e.delete=="function"&&typeof e.get=="function"&&typeof e.getAll=="function"&&typeof e.has=="function"&&typeof e.set=="function"&&typeof e.sort=="function"&&e[xa]==="URLSearchParams",Pa=e=>e&&typeof e=="object"&&typeof e.arrayBuffer=="function"&&typeof e.type=="string"&&typeof e.stream=="function"&&typeof e.constructor=="function"&&/^(Blob|File)$/.test(e[xa]),gx=e=>typeof e=="object"&&(e[xa]==="AbortSignal"||e[xa]==="EventTarget"),yx=(e,t)=>{let r=new URL(t).hostname,n=new URL(e).hostname;return r===n||r.endsWith(`.${n}`)},Dx=(e,t)=>{let r=new URL(t).protocol,n=new URL(e).protocol;return r===n},bx=(0,$n.promisify)(Ct.default.pipeline),Oe=Symbol("Body internals"),Lo=class{constructor(e,{size:t=0}={}){let r=null;e===null?e=null:r_(e)?e=We.Buffer.from(e.toString()):Pa(e)||We.Buffer.isBuffer(e)||($n.types.isAnyArrayBuffer(e)?e=We.Buffer.from(e):ArrayBuffer.isView(e)?e=We.Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof Ct.default||(e instanceof An?(e=ax(e),r=e.type.split("=")[1]):e=We.Buffer.from(String(e))));let n=e;We.Buffer.isBuffer(e)?n=Ct.default.Readable.from(e):Pa(e)&&(n=Ct.default.Readable.from(e.stream())),this[Oe]={body:e,stream:n,boundary:r,disturbed:!1,error:null},this.size=t,e instanceof Ct.default&&e.on("error",o=>{let i=o instanceof ka?o:new lt(`Invalid response body while trying to fetch ${this.url}: ${o.message}`,"system",o);this[Oe].error=i})}get body(){return this[Oe].stream}get bodyUsed(){return this[Oe].disturbed}async arrayBuffer(){let{buffer:e,byteOffset:t,byteLength:r}=await sf(this);return e.slice(t,t+r)}async formData(){let e=this.headers.get("content-type");if(e.startsWith("application/x-www-form-urlencoded")){let r=new An,n=new URLSearchParams(await this.text());for(let[o,i]of n)r.append(o,i);return r}let{toFormData:t}=await Promise.resolve().then(()=>(fx(),e_));return t(this.body,e)}async blob(){let e=this.headers&&this.headers.get("content-type")||this[Oe].body&&this[Oe].body.type||"",t=await this.arrayBuffer();return new Rn([t],{type:e})}async json(){let e=await this.text();return JSON.parse(e)}async text(){let e=await sf(this);return new TextDecoder().decode(e)}buffer(){return sf(this)}};Lo.prototype.buffer=(0,$n.deprecate)(Lo.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(Lo.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,$n.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function sf(e){if(e[Oe].disturbed)throw new TypeError(`body used already for: ${e.url}`);if(e[Oe].disturbed=!0,e[Oe].error)throw e[Oe].error;let{body:t}=e;if(t===null||!(t instanceof Ct.default))return We.Buffer.alloc(0);let r=[],n=0;try{for await(let o of t){if(e.size>0&&n+o.length>e.size){let i=new lt(`content size at ${e.url} over limit: ${e.size}`,"max-size");throw t.destroy(i),i}n+=o.length,r.push(o)}}catch(o){throw o instanceof ka?o:new lt(`Invalid response body while trying to fetch ${e.url}: ${o.message}`,"system",o)}if(t.readableEnded===!0||t._readableState.ended===!0)try{return r.every(o=>typeof o=="string")?We.Buffer.from(r.join("")):We.Buffer.concat(r,n)}catch(o){throw new lt(`Could not create Buffer from response body for ${e.url}: ${o.message}`,"system",o)}else throw new lt(`Premature close of server response while trying to fetch ${e.url}`)}var pf=(e,t)=>{let r,n,{body:o}=e[Oe];if(e.bodyUsed)throw new Error("cannot clone body after it is used");return o instanceof Ct.default&&typeof o.getBoundary!="function"&&(r=new Ct.PassThrough({highWaterMark:t}),n=new Ct.PassThrough({highWaterMark:t}),o.pipe(r),o.pipe(n),e[Oe].stream=r,o=n),o},_x=(0,$n.deprecate)(e=>e.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),n_=(e,t)=>e===null?null:typeof e=="string"?"text/plain;charset=UTF-8":r_(e)?"application/x-www-form-urlencoded;charset=UTF-8":Pa(e)?e.type||null:We.Buffer.isBuffer(e)||$n.types.isAnyArrayBuffer(e)||ArrayBuffer.isView(e)?null:e instanceof An?`multipart/form-data; boundary=${t[Oe].boundary}`:e&&typeof e.getBoundary=="function"?`multipart/form-data;boundary=${_x(e)}`:e instanceof Ct.default?null:"text/plain;charset=UTF-8",wx=e=>{let{body:t}=e[Oe];return t===null?0:Pa(t)?t.size:We.Buffer.isBuffer(t)?t.length:t&&typeof t.getLengthSync=="function"&&t.hasKnownLength&&t.hasKnownLength()?t.getLengthSync():null},Sx=async(e,{body:t})=>{t===null?e.end():await bx(t,e)},Wb=Ee(require("util")),Oa=Ee(require("http")),$a=typeof Oa.default.validateHeaderName=="function"?Oa.default.validateHeaderName:e=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(e)){let t=new TypeError(`Header name must be a valid HTTP token [${e}]`);throw Object.defineProperty(t,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),t}},ff=typeof Oa.default.validateHeaderValue=="function"?Oa.default.validateHeaderValue:(e,t)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(t)){let r=new TypeError(`Invalid character in header content ["${e}"]`);throw Object.defineProperty(r,"code",{value:"ERR_INVALID_CHAR"}),r}},qt=class extends URLSearchParams{constructor(e){let t=[];if(e instanceof qt){let r=e.raw();for(let[n,o]of Object.entries(r))t.push(...o.map(i=>[n,i]))}else if(e!=null)if(typeof e=="object"&&!Wb.types.isBoxedPrimitive(e)){let r=e[Symbol.iterator];if(r==null)t.push(...Object.entries(e));else{if(typeof r!="function")throw new TypeError("Header pairs must be iterable");t=[...e].map(n=>{if(typeof n!="object"||Wb.types.isBoxedPrimitive(n))throw new TypeError("Each header pair must be an iterable object");return[...n]}).map(n=>{if(n.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...n]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return t=t.length>0?t.map(([r,n])=>($a(r),ff(r,String(n)),[String(r).toLowerCase(),String(n)])):void 0,super(t),new Proxy(this,{get(r,n,o){switch(n){case"append":case"set":return(i,a)=>($a(i),ff(i,String(a)),URLSearchParams.prototype[n].call(r,String(i).toLowerCase(),String(a)));case"delete":case"has":case"getAll":return i=>($a(i),URLSearchParams.prototype[n].call(r,String(i).toLowerCase()));case"keys":return()=>(r.sort(),new Set(URLSearchParams.prototype.keys.call(r)).keys());default:return Reflect.get(r,n,o)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(e){let t=this.getAll(e);if(t.length===0)return null;let r=t.join(", ");return/^content-encoding$/i.test(e)&&(r=r.toLowerCase()),r}forEach(e,t=void 0){for(let r of this.keys())Reflect.apply(e,t,[this.get(r),r,this])}*values(){for(let e of this.keys())yield this.get(e)}*entries(){for(let e of this.keys())yield[e,this.get(e)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((e,t)=>(e[t]=this.getAll(t),e),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((e,t)=>{let r=this.getAll(t);return t==="host"?e[t]=r[0]:e[t]=r.length>1?r:r[0],e},{})}};Object.defineProperties(qt.prototype,["get","entries","forEach","values"].reduce((e,t)=>(e[t]={enumerable:!0},e),{}));function Ex(e=[]){return new qt(e.reduce((t,r,n,o)=>(n%2===0&&t.push(o.slice(n,n+2)),t),[]).filter(([t,r])=>{try{return $a(t),ff(t,String(r)),!0}catch{return!1}}))}var vx=new Set([301,302,303,307,308]),mf=e=>vx.has(e),ut=Symbol("Response internals"),Le=class extends Lo{constructor(e=null,t={}){super(e,t);let r=t.status!=null?t.status:200,n=new qt(t.headers);if(e!==null&&!n.has("Content-Type")){let o=n_(e,this);o&&n.append("Content-Type",o)}this[ut]={type:"default",url:t.url,status:r,statusText:t.statusText||"",headers:n,counter:t.counter,highWaterMark:t.highWaterMark}}get type(){return this[ut].type}get url(){return this[ut].url||""}get status(){return this[ut].status}get ok(){return this[ut].status>=200&&this[ut].status<300}get redirected(){return this[ut].counter>0}get statusText(){return this[ut].statusText}get headers(){return this[ut].headers}get highWaterMark(){return this[ut].highWaterMark}clone(){return new Le(pf(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(e,t=302){if(!mf(t))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new Le(null,{headers:{location:new URL(e).toString()},status:t})}static error(){let e=new Le(null,{status:0,statusText:""});return e[ut].type="error",e}static json(e=void 0,t={}){let r=JSON.stringify(e);if(r===void 0)throw new TypeError("data is not JSON serializable");let n=new qt(t&&t.headers);return n.has("content-type")||n.set("content-type","application/json"),new Le(r,{...t,headers:n})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(Le.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var Fx=Ee(require("url")),Cx=Ee(require("util")),Tx=e=>{if(e.search)return e.search;let t=e.href.length-1,r=e.hash||(e.href[t]==="#"?"#":"");return e.href[t-r.length]==="?"?"?":""},Rx=Ee(require("net"));function Vb(e,t=!1){return e==null||(e=new URL(e),/^(about|blob|data):$/.test(e.protocol))?"no-referrer":(e.username="",e.password="",e.hash="",t&&(e.pathname="",e.search=""),e)}var o_=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),Ax="strict-origin-when-cross-origin";function $x(e){if(!o_.has(e))throw new TypeError(`Invalid referrerPolicy: ${e}`);return e}function xx(e){if(/^(http|ws)s:$/.test(e.protocol))return!0;let t=e.host.replace(/(^\[)|(]$)/g,""),r=(0,Rx.isIP)(t);return r===4&&/^127\./.test(t)||r===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(t)?!0:e.host==="localhost"||e.host.endsWith(".localhost")?!1:e.protocol==="file:"}function Tn(e){return/^about:(blank|srcdoc)$/.test(e)||e.protocol==="data:"||/^(blob|filesystem):$/.test(e.protocol)?!0:xx(e)}function Px(e,{referrerURLCallback:t,referrerOriginCallback:r}={}){if(e.referrer==="no-referrer"||e.referrerPolicy==="")return null;let n=e.referrerPolicy;if(e.referrer==="about:client")return"no-referrer";let o=e.referrer,i=Vb(o),a=Vb(o,!0);i.toString().length>4096&&(i=a),t&&(i=t(i)),r&&(a=r(a));let l=new URL(e.url);switch(n){case"no-referrer":return"no-referrer";case"origin":return a;case"unsafe-url":return i;case"strict-origin":return Tn(i)&&!Tn(l)?"no-referrer":a.toString();case"strict-origin-when-cross-origin":return i.origin===l.origin?i:Tn(i)&&!Tn(l)?"no-referrer":a;case"same-origin":return i.origin===l.origin?i:"no-referrer";case"origin-when-cross-origin":return i.origin===l.origin?i:a;case"no-referrer-when-downgrade":return Tn(i)&&!Tn(l)?"no-referrer":i;default:throw new TypeError(`Invalid referrerPolicy: ${n}`)}}function Ox(e){let t=(e.get("referrer-policy")||"").split(/[,\s]+/),r="";for(let n of t)n&&o_.has(n)&&(r=n);return r}var Se=Symbol("Request internals"),jo=e=>typeof e=="object"&&typeof e[Se]=="object",Bx=(0,Cx.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),qo=class extends Lo{constructor(e,t={}){let r;if(jo(e)?r=new URL(e.url):(r=new URL(e),e={}),r.username!==""||r.password!=="")throw new TypeError(`${r} is an url with embedded credentials.`);let n=t.method||e.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(n)&&(n=n.toUpperCase()),!jo(t)&&"data"in t&&Bx(),(t.body!=null||jo(e)&&e.body!==null)&&(n==="GET"||n==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let o=t.body?t.body:jo(e)&&e.body!==null?pf(e):null;super(o,{size:t.size||e.size||0});let i=new qt(t.headers||e.headers||{});if(o!==null&&!i.has("Content-Type")){let c=n_(o,this);c&&i.set("Content-Type",c)}let a=jo(e)?e.signal:null;if("signal"in t&&(a=t.signal),a!=null&&!gx(a))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let l=t.referrer==null?e.referrer:t.referrer;if(l==="")l="no-referrer";else if(l){let c=new URL(l);l=/^about:(\/\/)?client$/.test(c)?"client":c}else l=void 0;this[Se]={method:n,redirect:t.redirect||e.redirect||"follow",headers:i,parsedURL:r,signal:a,referrer:l},this.follow=t.follow===void 0?e.follow===void 0?20:e.follow:t.follow,this.compress=t.compress===void 0?e.compress===void 0?!0:e.compress:t.compress,this.counter=t.counter||e.counter||0,this.agent=t.agent||e.agent,this.highWaterMark=t.highWaterMark||e.highWaterMark||16384,this.insecureHTTPParser=t.insecureHTTPParser||e.insecureHTTPParser||!1,this.referrerPolicy=t.referrerPolicy||e.referrerPolicy||""}get method(){return this[Se].method}get url(){return(0,Fx.format)(this[Se].parsedURL)}get headers(){return this[Se].headers}get redirect(){return this[Se].redirect}get signal(){return this[Se].signal}get referrer(){if(this[Se].referrer==="no-referrer")return"";if(this[Se].referrer==="client")return"about:client";if(this[Se].referrer)return this[Se].referrer.toString()}get referrerPolicy(){return this[Se].referrerPolicy}set referrerPolicy(e){this[Se].referrerPolicy=$x(e)}clone(){return new qo(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(qo.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var Ix=e=>{let{parsedURL:t}=e[Se],r=new qt(e[Se].headers);r.has("Accept")||r.set("Accept","*/*");let n=null;if(e.body===null&&/^(post|put)$/i.test(e.method)&&(n="0"),e.body!==null){let l=wx(e);typeof l=="number"&&!Number.isNaN(l)&&(n=String(l))}n&&r.set("Content-Length",n),e.referrerPolicy===""&&(e.referrerPolicy=Ax),e.referrer&&e.referrer!=="no-referrer"?e[Se].referrer=Px(e):e[Se].referrer="no-referrer",e[Se].referrer instanceof URL&&r.set("Referer",e.referrer),r.has("User-Agent")||r.set("User-Agent","node-fetch"),e.compress&&!r.has("Accept-Encoding")&&r.set("Accept-Encoding","gzip, deflate, br");let{agent:o}=e;typeof o=="function"&&(o=o(t));let i=Tx(t),a={path:t.pathname+i,method:e.method,headers:r[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:e.insecureHTTPParser,agent:o};return{parsedURL:t,options:a}},i_=class extends ka{constructor(e,t="aborted"){super(e,t)}};Ia();Zb();var kx=new Set(["data:","http:","https:"]);async function s_(e,t){return new Promise((r,n)=>{let o=new qo(e,t),{parsedURL:i,options:a}=Ix(o);if(!kx.has(i.protocol))throw new TypeError(`node-fetch cannot load ${e}. URL scheme "${i.protocol.replace(/:$/,"")}" is not supported.`);if(i.protocol==="data:"){let g=hx(o.url),y=new Le(g,{headers:{"Content-Type":g.typeFull}});r(y);return}let l=(i.protocol==="https:"?px.default:dx.default).request,{signal:c}=o,d=null,p=()=>{let g=new i_("The operation was aborted.");n(g),o.body&&o.body instanceof Ft.default.Readable&&o.body.destroy(g),!(!d||!d.body)&&d.body.emit("error",g)};if(c&&c.aborted){p();return}let m=()=>{p(),D()},b=l(i.toString(),a);c&&c.addEventListener("abort",m);let D=()=>{b.abort(),c&&c.removeEventListener("abort",m)};b.on("error",g=>{n(new lt(`request to ${o.url} failed, reason: ${g.message}`,"system",g)),D()}),Mx(b,g=>{d&&d.body&&d.body.destroy(g)}),process.version<"v14"&&b.on("socket",g=>{let y;g.prependListener("end",()=>{y=g._eventsCount}),g.prependListener("close",S=>{if(d&&y<g._eventsCount&&!S){let w=new Error("Premature close");w.code="ERR_STREAM_PREMATURE_CLOSE",d.body.emit("error",w)}})}),b.on("response",g=>{b.setTimeout(0);let y=Ex(g.rawHeaders);if(mf(g.statusCode)){let A=y.get("Location"),v=null;try{v=A===null?null:new URL(A,o.url)}catch{if(o.redirect!=="manual"){n(new lt(`uri requested responds with an invalid redirect URL: ${A}`,"invalid-redirect")),D();return}}switch(o.redirect){case"error":n(new lt(`uri requested responds with a redirect, redirect mode is set to error: ${o.url}`,"no-redirect")),D();return;case"manual":break;case"follow":{if(v===null)break;if(o.counter>=o.follow){n(new lt(`maximum redirect reached at: ${o.url}`,"max-redirect")),D();return}let B={headers:new qt(o.headers),follow:o.follow,counter:o.counter+1,agent:o.agent,compress:o.compress,method:o.method,body:pf(o),signal:o.signal,size:o.size,referrer:o.referrer,referrerPolicy:o.referrerPolicy};if(!yx(o.url,v)||!Dx(o.url,v))for(let ae of["authorization","www-authenticate","cookie","cookie2"])B.headers.delete(ae);if(g.statusCode!==303&&o.body&&t.body instanceof Ft.default.Readable){n(new lt("Cannot follow redirect with body being a readable stream","unsupported-redirect")),D();return}(g.statusCode===303||(g.statusCode===301||g.statusCode===302)&&o.method==="POST")&&(B.method="GET",B.body=void 0,B.headers.delete("content-length"));let W=Ox(y);W&&(B.referrerPolicy=W),r(s_(new qo(v,B))),D();return}default:return n(new TypeError(`Redirect option '${o.redirect}' is not a valid value of RequestRedirect`))}}c&&g.once("end",()=>{c.removeEventListener("abort",m)});let S=(0,Ft.pipeline)(g,new Ft.PassThrough,A=>{A&&n(A)});process.version<"v12.10"&&g.on("aborted",m);let w={url:o.url,status:g.statusCode,statusText:g.statusMessage,headers:y,size:o.size,counter:o.counter,highWaterMark:o.highWaterMark},T=y.get("Content-Encoding");if(!o.compress||o.method==="HEAD"||T===null||g.statusCode===204||g.statusCode===304){d=new Le(S,w),r(d);return}let R={flush:Cn.default.Z_SYNC_FLUSH,finishFlush:Cn.default.Z_SYNC_FLUSH};if(T==="gzip"||T==="x-gzip"){S=(0,Ft.pipeline)(S,Cn.default.createGunzip(R),A=>{A&&n(A)}),d=new Le(S,w),r(d);return}if(T==="deflate"||T==="x-deflate"){let A=(0,Ft.pipeline)(g,new Ft.PassThrough,v=>{v&&n(v)});A.once("data",v=>{(v[0]&15)===8?S=(0,Ft.pipeline)(S,Cn.default.createInflate(),B=>{B&&n(B)}):S=(0,Ft.pipeline)(S,Cn.default.createInflateRaw(),B=>{B&&n(B)}),d=new Le(S,w),r(d)}),A.once("end",()=>{d||(d=new Le(S,w),r(d))});return}if(T==="br"){S=(0,Ft.pipeline)(S,Cn.default.createBrotliDecompress(),A=>{A&&n(A)}),d=new Le(S,w),r(d);return}d=new Le(S,w),r(d)}),Sx(b,o).catch(n)})}function Mx(e,t){let r=Ra.Buffer.from(`0\r
\r
`),n=!1,o=!1,i;e.on("response",a=>{let{headers:l}=a;n=l["transfer-encoding"]==="chunked"&&!l["content-length"]}),e.on("socket",a=>{let l=()=>{if(n&&!o){let d=new Error("Premature close");d.code="ERR_STREAM_PREMATURE_CLOSE",t(d)}},c=d=>{o=Ra.Buffer.compare(d.slice(-5),r)===0,!o&&i&&(o=Ra.Buffer.compare(i.slice(-3),r.slice(0,3))===0&&Ra.Buffer.compare(d.slice(-2),r.slice(3))===0),i=d};a.prependListener("close",l),a.on("data",c),e.on("close",()=>{a.removeListener("close",l),a.removeListener("data",c)})})}zo();Ia();});var Ho=C(X=>{"use strict";Object.defineProperty(X,"__esModule",{value:!0});X.regexpCode=X.getEsmExportName=X.getProperty=X.safeStringify=X.stringify=X.strConcat=X.addCodeArg=X.str=X._=X.nil=X._Code=X.Name=X.IDENTIFIER=X._CodeOrName=void 0;var Vo=class{};X._CodeOrName=Vo;X.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;var Br=class extends Vo{constructor(t){if(super(),!X.IDENTIFIER.test(t))throw new Error("CodeGen: name must be a valid identifier");this.str=t}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}};X.Name=Br;var Ve=class extends Vo{constructor(t){super(),this._items=typeof t=="string"?[t]:t}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;let t=this._items[0];return t===""||t==='""'}get str(){var t;return(t=this._str)!==null&&t!==void 0?t:this._str=this._items.reduce((r,n)=>`${r}${n}`,"")}get names(){var t;return(t=this._names)!==null&&t!==void 0?t:this._names=this._items.reduce((r,n)=>(n instanceof Br&&(r[n.str]=(r[n.str]||0)+1),r),{})}};X._Code=Ve;X.nil=new Ve("");function u_(e,...t){let r=[e[0]],n=0;for(;n<t.length;)yf(r,t[n]),r.push(e[++n]);return new Ve(r)}X._=u_;var gf=new Ve("+");function l_(e,...t){let r=[Go(e[0])],n=0;for(;n<t.length;)r.push(gf),yf(r,t[n]),r.push(gf,Go(e[++n]));return Nx(r),new Ve(r)}X.str=l_;function yf(e,t){t instanceof Ve?e.push(...t._items):t instanceof Br?e.push(t):e.push(qx(t))}X.addCodeArg=yf;function Nx(e){let t=1;for(;t<e.length-1;){if(e[t]===gf){let r=jx(e[t-1],e[t+1]);if(r!==void 0){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}function jx(e,t){if(t==='""')return e;if(e==='""')return t;if(typeof e=="string")return t instanceof Br||e[e.length-1]!=='"'?void 0:typeof t!="string"?`${e.slice(0,-1)}${t}"`:t[0]==='"'?e.slice(0,-1)+t.slice(1):void 0;if(typeof t=="string"&&t[0]==='"'&&!(e instanceof Br))return`"${e}${t.slice(1)}`}function Lx(e,t){return t.emptyStr()?e:e.emptyStr()?t:l_`${e}${t}`}X.strConcat=Lx;function qx(e){return typeof e=="number"||typeof e=="boolean"||e===null?e:Go(Array.isArray(e)?e.join(","):e)}function Ux(e){return new Ve(Go(e))}X.stringify=Ux;function Go(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}X.safeStringify=Go;function zx(e){return typeof e=="string"&&X.IDENTIFIER.test(e)?new Ve(`.${e}`):u_`[${e}]`}X.getProperty=zx;function Wx(e){if(typeof e=="string"&&X.IDENTIFIER.test(e))return new Ve(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)}X.getEsmExportName=Wx;function Vx(e){return new Ve(e.toString())}X.regexpCode=Vx});var _f=C(Ie=>{"use strict";Object.defineProperty(Ie,"__esModule",{value:!0});Ie.ValueScope=Ie.ValueScopeName=Ie.Scope=Ie.varKinds=Ie.UsedValueState=void 0;var Be=Ho(),Df=class extends Error{constructor(t){super(`CodeGen: "code" for ${t} not defined`),this.value=t.value}},Ma;(function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"})(Ma||(Ie.UsedValueState=Ma={}));Ie.varKinds={const:new Be.Name("const"),let:new Be.Name("let"),var:new Be.Name("var")};var Na=class{constructor({prefixes:t,parent:r}={}){this._names={},this._prefixes=t,this._parent=r}toName(t){return t instanceof Be.Name?t:this.name(t)}name(t){return new Be.Name(this._newName(t))}_newName(t){let r=this._names[t]||this._nameGroup(t);return`${t}${r.index++}`}_nameGroup(t){var r,n;if(!((n=(r=this._parent)===null||r===void 0?void 0:r._prefixes)===null||n===void 0)&&n.has(t)||this._prefixes&&!this._prefixes.has(t))throw new Error(`CodeGen: prefix "${t}" is not allowed in this scope`);return this._names[t]={prefix:t,index:0}}};Ie.Scope=Na;var ja=class extends Be.Name{constructor(t,r){super(r),this.prefix=t}setValue(t,{property:r,itemIndex:n}){this.value=t,this.scopePath=(0,Be._)`.${new Be.Name(r)}[${n}]`}};Ie.ValueScopeName=ja;var Gx=(0,Be._)`\n`,bf=class extends Na{constructor(t){super(t),this._values={},this._scope=t.scope,this.opts={...t,_n:t.lines?Gx:Be.nil}}get(){return this._scope}name(t){return new ja(t,this._newName(t))}value(t,r){var n;if(r.ref===void 0)throw new Error("CodeGen: ref must be passed in value");let o=this.toName(t),{prefix:i}=o,a=(n=r.key)!==null&&n!==void 0?n:r.ref,l=this._values[i];if(l){let p=l.get(a);if(p)return p}else l=this._values[i]=new Map;l.set(a,o);let c=this._scope[i]||(this._scope[i]=[]),d=c.length;return c[d]=r.ref,o.setValue(r,{property:i,itemIndex:d}),o}getValue(t,r){let n=this._values[t];if(n)return n.get(r)}scopeRefs(t,r=this._values){return this._reduceValues(r,n=>{if(n.scopePath===void 0)throw new Error(`CodeGen: name "${n}" has no value`);return(0,Be._)`${t}${n.scopePath}`})}scopeCode(t=this._values,r,n){return this._reduceValues(t,o=>{if(o.value===void 0)throw new Error(`CodeGen: name "${o}" has no value`);return o.value.code},r,n)}_reduceValues(t,r,n={},o){let i=Be.nil;for(let a in t){let l=t[a];if(!l)continue;let c=n[a]=n[a]||new Map;l.forEach(d=>{if(c.has(d))return;c.set(d,Ma.Started);let p=r(d);if(p){let m=this.opts.es5?Ie.varKinds.var:Ie.varKinds.const;i=(0,Be._)`${i}${m} ${d} = ${p};${this.opts._n}`}else if(p=o?.(d))i=(0,Be._)`${i}${p}${this.opts._n}`;else throw new Df(d);c.set(d,Ma.Completed)})}return i}};Ie.ValueScope=bf});var G=C(V=>{"use strict";Object.defineProperty(V,"__esModule",{value:!0});V.or=V.and=V.not=V.CodeGen=V.operators=V.varKinds=V.ValueScopeName=V.ValueScope=V.Scope=V.Name=V.regexpCode=V.stringify=V.getProperty=V.nil=V.strConcat=V.str=V._=void 0;var J=Ho(),ct=_f(),pr=Ho();Object.defineProperty(V,"_",{enumerable:!0,get:function(){return pr._}});Object.defineProperty(V,"str",{enumerable:!0,get:function(){return pr.str}});Object.defineProperty(V,"strConcat",{enumerable:!0,get:function(){return pr.strConcat}});Object.defineProperty(V,"nil",{enumerable:!0,get:function(){return pr.nil}});Object.defineProperty(V,"getProperty",{enumerable:!0,get:function(){return pr.getProperty}});Object.defineProperty(V,"stringify",{enumerable:!0,get:function(){return pr.stringify}});Object.defineProperty(V,"regexpCode",{enumerable:!0,get:function(){return pr.regexpCode}});Object.defineProperty(V,"Name",{enumerable:!0,get:function(){return pr.Name}});var za=_f();Object.defineProperty(V,"Scope",{enumerable:!0,get:function(){return za.Scope}});Object.defineProperty(V,"ValueScope",{enumerable:!0,get:function(){return za.ValueScope}});Object.defineProperty(V,"ValueScopeName",{enumerable:!0,get:function(){return za.ValueScopeName}});Object.defineProperty(V,"varKinds",{enumerable:!0,get:function(){return za.varKinds}});V.operators={GT:new J._Code(">"),GTE:new J._Code(">="),LT:new J._Code("<"),LTE:new J._Code("<="),EQ:new J._Code("==="),NEQ:new J._Code("!=="),NOT:new J._Code("!"),OR:new J._Code("||"),AND:new J._Code("&&"),ADD:new J._Code("+")};var Ut=class{optimizeNodes(){return this}optimizeNames(t,r){return this}},wf=class extends Ut{constructor(t,r,n){super(),this.varKind=t,this.name=r,this.rhs=n}render({es5:t,_n:r}){let n=t?ct.varKinds.var:this.varKind,o=this.rhs===void 0?"":` = ${this.rhs}`;return`${n} ${this.name}${o};`+r}optimizeNames(t,r){if(t[this.name.str])return this.rhs&&(this.rhs=Pn(this.rhs,t,r)),this}get names(){return this.rhs instanceof J._CodeOrName?this.rhs.names:{}}},La=class extends Ut{constructor(t,r,n){super(),this.lhs=t,this.rhs=r,this.sideEffects=n}render({_n:t}){return`${this.lhs} = ${this.rhs};`+t}optimizeNames(t,r){if(!(this.lhs instanceof J.Name&&!t[this.lhs.str]&&!this.sideEffects))return this.rhs=Pn(this.rhs,t,r),this}get names(){let t=this.lhs instanceof J.Name?{}:{...this.lhs.names};return Ua(t,this.rhs)}},Sf=class extends La{constructor(t,r,n,o){super(t,n,o),this.op=r}render({_n:t}){return`${this.lhs} ${this.op}= ${this.rhs};`+t}},Ef=class extends Ut{constructor(t){super(),this.label=t,this.names={}}render({_n:t}){return`${this.label}:`+t}},vf=class extends Ut{constructor(t){super(),this.label=t,this.names={}}render({_n:t}){return`break${this.label?` ${this.label}`:""};`+t}},Ff=class extends Ut{constructor(t){super(),this.error=t}render({_n:t}){return`throw ${this.error};`+t}get names(){return this.error.names}},Cf=class extends Ut{constructor(t){super(),this.code=t}render({_n:t}){return`${this.code};`+t}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(t,r){return this.code=Pn(this.code,t,r),this}get names(){return this.code instanceof J._CodeOrName?this.code.names:{}}},Yo=class extends Ut{constructor(t=[]){super(),this.nodes=t}render(t){return this.nodes.reduce((r,n)=>r+n.render(t),"")}optimizeNodes(){let{nodes:t}=this,r=t.length;for(;r--;){let n=t[r].optimizeNodes();Array.isArray(n)?t.splice(r,1,...n):n?t[r]=n:t.splice(r,1)}return t.length>0?this:void 0}optimizeNames(t,r){let{nodes:n}=this,o=n.length;for(;o--;){let i=n[o];i.optimizeNames(t,r)||(Hx(t,i.names),n.splice(o,1))}return n.length>0?this:void 0}get names(){return this.nodes.reduce((t,r)=>Mr(t,r.names),{})}},zt=class extends Yo{render(t){return"{"+t._n+super.render(t)+"}"+t._n}},Tf=class extends Yo{},xn=class extends zt{};xn.kind="else";var Ir=class e extends zt{constructor(t,r){super(r),this.condition=t}render(t){let r=`if(${this.condition})`+super.render(t);return this.else&&(r+="else "+this.else.render(t)),r}optimizeNodes(){super.optimizeNodes();let t=this.condition;if(t===!0)return this.nodes;let r=this.else;if(r){let n=r.optimizeNodes();r=this.else=Array.isArray(n)?new xn(n):n}if(r)return t===!1?r instanceof e?r:r.nodes:this.nodes.length?this:new e(c_(t),r instanceof e?[r]:r.nodes);if(!(t===!1||!this.nodes.length))return this}optimizeNames(t,r){var n;if(this.else=(n=this.else)===null||n===void 0?void 0:n.optimizeNames(t,r),!!(super.optimizeNames(t,r)||this.else))return this.condition=Pn(this.condition,t,r),this}get names(){let t=super.names;return Ua(t,this.condition),this.else&&Mr(t,this.else.names),t}};Ir.kind="if";var kr=class extends zt{};kr.kind="for";var Rf=class extends kr{constructor(t){super(),this.iteration=t}render(t){return`for(${this.iteration})`+super.render(t)}optimizeNames(t,r){if(super.optimizeNames(t,r))return this.iteration=Pn(this.iteration,t,r),this}get names(){return Mr(super.names,this.iteration.names)}},Af=class extends kr{constructor(t,r,n,o){super(),this.varKind=t,this.name=r,this.from=n,this.to=o}render(t){let r=t.es5?ct.varKinds.var:this.varKind,{name:n,from:o,to:i}=this;return`for(${r} ${n}=${o}; ${n}<${i}; ${n}++)`+super.render(t)}get names(){let t=Ua(super.names,this.from);return Ua(t,this.to)}},qa=class extends kr{constructor(t,r,n,o){super(),this.loop=t,this.varKind=r,this.name=n,this.iterable=o}render(t){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(t)}optimizeNames(t,r){if(super.optimizeNames(t,r))return this.iterable=Pn(this.iterable,t,r),this}get names(){return Mr(super.names,this.iterable.names)}},Ko=class extends zt{constructor(t,r,n){super(),this.name=t,this.args=r,this.async=n}render(t){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(t)}};Ko.kind="func";var Jo=class extends Yo{render(t){return"return "+super.render(t)}};Jo.kind="return";var $f=class extends zt{render(t){let r="try"+super.render(t);return this.catch&&(r+=this.catch.render(t)),this.finally&&(r+=this.finally.render(t)),r}optimizeNodes(){var t,r;return super.optimizeNodes(),(t=this.catch)===null||t===void 0||t.optimizeNodes(),(r=this.finally)===null||r===void 0||r.optimizeNodes(),this}optimizeNames(t,r){var n,o;return super.optimizeNames(t,r),(n=this.catch)===null||n===void 0||n.optimizeNames(t,r),(o=this.finally)===null||o===void 0||o.optimizeNames(t,r),this}get names(){let t=super.names;return this.catch&&Mr(t,this.catch.names),this.finally&&Mr(t,this.finally.names),t}},Qo=class extends zt{constructor(t){super(),this.error=t}render(t){return`catch(${this.error})`+super.render(t)}};Qo.kind="catch";var Xo=class extends zt{render(t){return"finally"+super.render(t)}};Xo.kind="finally";var xf=class{constructor(t,r={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...r,_n:r.lines?`
`:""},this._extScope=t,this._scope=new ct.Scope({parent:t}),this._nodes=[new Tf]}toString(){return this._root.render(this.opts)}name(t){return this._scope.name(t)}scopeName(t){return this._extScope.name(t)}scopeValue(t,r){let n=this._extScope.value(t,r);return(this._values[n.prefix]||(this._values[n.prefix]=new Set)).add(n),n}getScopeValue(t,r){return this._extScope.getValue(t,r)}scopeRefs(t){return this._extScope.scopeRefs(t,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(t,r,n,o){let i=this._scope.toName(r);return n!==void 0&&o&&(this._constants[i.str]=n),this._leafNode(new wf(t,i,n)),i}const(t,r,n){return this._def(ct.varKinds.const,t,r,n)}let(t,r,n){return this._def(ct.varKinds.let,t,r,n)}var(t,r,n){return this._def(ct.varKinds.var,t,r,n)}assign(t,r,n){return this._leafNode(new La(t,r,n))}add(t,r){return this._leafNode(new Sf(t,V.operators.ADD,r))}code(t){return typeof t=="function"?t():t!==J.nil&&this._leafNode(new Cf(t)),this}object(...t){let r=["{"];for(let[n,o]of t)r.length>1&&r.push(","),r.push(n),(n!==o||this.opts.es5)&&(r.push(":"),(0,J.addCodeArg)(r,o));return r.push("}"),new J._Code(r)}if(t,r,n){if(this._blockNode(new Ir(t)),r&&n)this.code(r).else().code(n).endIf();else if(r)this.code(r).endIf();else if(n)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(t){return this._elseNode(new Ir(t))}else(){return this._elseNode(new xn)}endIf(){return this._endBlockNode(Ir,xn)}_for(t,r){return this._blockNode(t),r&&this.code(r).endFor(),this}for(t,r){return this._for(new Rf(t),r)}forRange(t,r,n,o,i=this.opts.es5?ct.varKinds.var:ct.varKinds.let){let a=this._scope.toName(t);return this._for(new Af(i,a,r,n),()=>o(a))}forOf(t,r,n,o=ct.varKinds.const){let i=this._scope.toName(t);if(this.opts.es5){let a=r instanceof J.Name?r:this.var("_arr",r);return this.forRange("_i",0,(0,J._)`${a}.length`,l=>{this.var(i,(0,J._)`${a}[${l}]`),n(i)})}return this._for(new qa("of",o,i,r),()=>n(i))}forIn(t,r,n,o=this.opts.es5?ct.varKinds.var:ct.varKinds.const){if(this.opts.ownProperties)return this.forOf(t,(0,J._)`Object.keys(${r})`,n);let i=this._scope.toName(t);return this._for(new qa("in",o,i,r),()=>n(i))}endFor(){return this._endBlockNode(kr)}label(t){return this._leafNode(new Ef(t))}break(t){return this._leafNode(new vf(t))}return(t){let r=new Jo;if(this._blockNode(r),this.code(t),r.nodes.length!==1)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(Jo)}try(t,r,n){if(!r&&!n)throw new Error('CodeGen: "try" without "catch" and "finally"');let o=new $f;if(this._blockNode(o),this.code(t),r){let i=this.name("e");this._currNode=o.catch=new Qo(i),r(i)}return n&&(this._currNode=o.finally=new Xo,this.code(n)),this._endBlockNode(Qo,Xo)}throw(t){return this._leafNode(new Ff(t))}block(t,r){return this._blockStarts.push(this._nodes.length),t&&this.code(t).endBlock(r),this}endBlock(t){let r=this._blockStarts.pop();if(r===void 0)throw new Error("CodeGen: not in self-balancing block");let n=this._nodes.length-r;if(n<0||t!==void 0&&n!==t)throw new Error(`CodeGen: wrong number of nodes: ${n} vs ${t} expected`);return this._nodes.length=r,this}func(t,r=J.nil,n,o){return this._blockNode(new Ko(t,r,n)),o&&this.code(o).endFunc(),this}endFunc(){return this._endBlockNode(Ko)}optimize(t=1){for(;t-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(t){return this._currNode.nodes.push(t),this}_blockNode(t){this._currNode.nodes.push(t),this._nodes.push(t)}_endBlockNode(t,r){let n=this._currNode;if(n instanceof t||r&&n instanceof r)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${r?`${t.kind}/${r.kind}`:t.kind}"`)}_elseNode(t){let r=this._currNode;if(!(r instanceof Ir))throw new Error('CodeGen: "else" without "if"');return this._currNode=r.else=t,this}get _root(){return this._nodes[0]}get _currNode(){let t=this._nodes;return t[t.length-1]}set _currNode(t){let r=this._nodes;r[r.length-1]=t}};V.CodeGen=xf;function Mr(e,t){for(let r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function Ua(e,t){return t instanceof J._CodeOrName?Mr(e,t.names):e}function Pn(e,t,r){if(e instanceof J.Name)return n(e);if(!o(e))return e;return new J._Code(e._items.reduce((i,a)=>(a instanceof J.Name&&(a=n(a)),a instanceof J._Code?i.push(...a._items):i.push(a),i),[]));function n(i){let a=r[i.str];return a===void 0||t[i.str]!==1?i:(delete t[i.str],a)}function o(i){return i instanceof J._Code&&i._items.some(a=>a instanceof J.Name&&t[a.str]===1&&r[a.str]!==void 0)}}function Hx(e,t){for(let r in t)e[r]=(e[r]||0)-(t[r]||0)}function c_(e){return typeof e=="boolean"||typeof e=="number"||e===null?!e:(0,J._)`!${Pf(e)}`}V.not=c_;var Yx=f_(V.operators.AND);function Kx(...e){return e.reduce(Yx)}V.and=Kx;var Jx=f_(V.operators.OR);function Qx(...e){return e.reduce(Jx)}V.or=Qx;function f_(e){return(t,r)=>t===J.nil?r:r===J.nil?t:(0,J._)`${Pf(t)} ${e} ${Pf(r)}`}function Pf(e){return e instanceof J.Name?e:(0,J._)`(${e})`}});var Z=C(H=>{"use strict";Object.defineProperty(H,"__esModule",{value:!0});H.checkStrictMode=H.getErrorPath=H.Type=H.useFunc=H.setEvaluated=H.evaluatedPropsToName=H.mergeEvaluated=H.eachItem=H.unescapeJsonPointer=H.escapeJsonPointer=H.escapeFragment=H.unescapeFragment=H.schemaRefOrVal=H.schemaHasRulesButRef=H.schemaHasRules=H.checkUnknownRules=H.alwaysValidSchema=H.toHash=void 0;var se=G(),Xx=Ho();function Zx(e){let t={};for(let r of e)t[r]=!0;return t}H.toHash=Zx;function e2(e,t){return typeof t=="boolean"?t:Object.keys(t).length===0?!0:(m_(e,t),!h_(t,e.self.RULES.all))}H.alwaysValidSchema=e2;function m_(e,t=e.schema){let{opts:r,self:n}=e;if(!r.strictSchema||typeof t=="boolean")return;let o=n.RULES.keywords;for(let i in t)o[i]||D_(e,`unknown keyword: "${i}"`)}H.checkUnknownRules=m_;function h_(e,t){if(typeof e=="boolean")return!e;for(let r in e)if(t[r])return!0;return!1}H.schemaHasRules=h_;function t2(e,t){if(typeof e=="boolean")return!e;for(let r in e)if(r!=="$ref"&&t.all[r])return!0;return!1}H.schemaHasRulesButRef=t2;function r2({topSchemaRef:e,schemaPath:t},r,n,o){if(!o){if(typeof r=="number"||typeof r=="boolean")return r;if(typeof r=="string")return(0,se._)`${r}`}return(0,se._)`${e}${t}${(0,se.getProperty)(n)}`}H.schemaRefOrVal=r2;function n2(e){return g_(decodeURIComponent(e))}H.unescapeFragment=n2;function o2(e){return encodeURIComponent(Bf(e))}H.escapeFragment=o2;function Bf(e){return typeof e=="number"?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}H.escapeJsonPointer=Bf;function g_(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}H.unescapeJsonPointer=g_;function i2(e,t){if(Array.isArray(e))for(let r of e)t(r);else t(e)}H.eachItem=i2;function d_({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:n}){return(o,i,a,l)=>{let c=a===void 0?i:a instanceof se.Name?(i instanceof se.Name?e(o,i,a):t(o,i,a),a):i instanceof se.Name?(t(o,a,i),i):r(i,a);return l===se.Name&&!(c instanceof se.Name)?n(o,c):c}}H.mergeEvaluated={props:d_({mergeNames:(e,t,r)=>e.if((0,se._)`${r} !== true && ${t} !== undefined`,()=>{e.if((0,se._)`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,(0,se._)`${r} || {}`).code((0,se._)`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if((0,se._)`${r} !== true`,()=>{t===!0?e.assign(r,!0):(e.assign(r,(0,se._)`${r} || {}`),If(e,r,t))}),mergeValues:(e,t)=>e===!0?!0:{...e,...t},resultToName:y_}),items:d_({mergeNames:(e,t,r)=>e.if((0,se._)`${r} !== true && ${t} !== undefined`,()=>e.assign(r,(0,se._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if((0,se._)`${r} !== true`,()=>e.assign(r,t===!0?!0:(0,se._)`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>e===!0?!0:Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})};function y_(e,t){if(t===!0)return e.var("props",!0);let r=e.var("props",(0,se._)`{}`);return t!==void 0&&If(e,r,t),r}H.evaluatedPropsToName=y_;function If(e,t,r){Object.keys(r).forEach(n=>e.assign((0,se._)`${t}${(0,se.getProperty)(n)}`,!0))}H.setEvaluated=If;var p_={};function s2(e,t){return e.scopeValue("func",{ref:t,code:p_[t.code]||(p_[t.code]=new Xx._Code(t.code))})}H.useFunc=s2;var Of;(function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"})(Of||(H.Type=Of={}));function a2(e,t,r){if(e instanceof se.Name){let n=t===Of.Num;return r?n?(0,se._)`"[" + ${e} + "]"`:(0,se._)`"['" + ${e} + "']"`:n?(0,se._)`"/" + ${e}`:(0,se._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,se.getProperty)(e).toString():"/"+Bf(e)}H.getErrorPath=a2;function D_(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,r===!0)throw new Error(t);e.self.logger.warn(t)}}H.checkStrictMode=D_});var Wt=C(kf=>{"use strict";Object.defineProperty(kf,"__esModule",{value:!0});var Ce=G(),u2={data:new Ce.Name("data"),valCxt:new Ce.Name("valCxt"),instancePath:new Ce.Name("instancePath"),parentData:new Ce.Name("parentData"),parentDataProperty:new Ce.Name("parentDataProperty"),rootData:new Ce.Name("rootData"),dynamicAnchors:new Ce.Name("dynamicAnchors"),vErrors:new Ce.Name("vErrors"),errors:new Ce.Name("errors"),this:new Ce.Name("this"),self:new Ce.Name("self"),scope:new Ce.Name("scope"),json:new Ce.Name("json"),jsonPos:new Ce.Name("jsonPos"),jsonLen:new Ce.Name("jsonLen"),jsonPart:new Ce.Name("jsonPart")};kf.default=u2});var Zo=C(Te=>{"use strict";Object.defineProperty(Te,"__esModule",{value:!0});Te.extendErrors=Te.resetErrorsCount=Te.reportExtraError=Te.reportError=Te.keyword$DataError=Te.keywordError=void 0;var Q=G(),Wa=Z(),$e=Wt();Te.keywordError={message:({keyword:e})=>(0,Q.str)`must pass "${e}" keyword validation`};Te.keyword$DataError={message:({keyword:e,schemaType:t})=>t?(0,Q.str)`"${e}" keyword must be ${t} ($data)`:(0,Q.str)`"${e}" keyword is invalid ($data)`};function l2(e,t=Te.keywordError,r,n){let{it:o}=e,{gen:i,compositeRule:a,allErrors:l}=o,c=w_(e,t,r);n??(a||l)?b_(i,c):__(o,(0,Q._)`[${c}]`)}Te.reportError=l2;function c2(e,t=Te.keywordError,r){let{it:n}=e,{gen:o,compositeRule:i,allErrors:a}=n,l=w_(e,t,r);b_(o,l),i||a||__(n,$e.default.vErrors)}Te.reportExtraError=c2;function f2(e,t){e.assign($e.default.errors,t),e.if((0,Q._)`${$e.default.vErrors} !== null`,()=>e.if(t,()=>e.assign((0,Q._)`${$e.default.vErrors}.length`,t),()=>e.assign($e.default.vErrors,null)))}Te.resetErrorsCount=f2;function d2({gen:e,keyword:t,schemaValue:r,data:n,errsCount:o,it:i}){if(o===void 0)throw new Error("ajv implementation error");let a=e.name("err");e.forRange("i",o,$e.default.errors,l=>{e.const(a,(0,Q._)`${$e.default.vErrors}[${l}]`),e.if((0,Q._)`${a}.instancePath === undefined`,()=>e.assign((0,Q._)`${a}.instancePath`,(0,Q.strConcat)($e.default.instancePath,i.errorPath))),e.assign((0,Q._)`${a}.schemaPath`,(0,Q.str)`${i.errSchemaPath}/${t}`),i.opts.verbose&&(e.assign((0,Q._)`${a}.schema`,r),e.assign((0,Q._)`${a}.data`,n))})}Te.extendErrors=d2;function b_(e,t){let r=e.const("err",t);e.if((0,Q._)`${$e.default.vErrors} === null`,()=>e.assign($e.default.vErrors,(0,Q._)`[${r}]`),(0,Q._)`${$e.default.vErrors}.push(${r})`),e.code((0,Q._)`${$e.default.errors}++`)}function __(e,t){let{gen:r,validateName:n,schemaEnv:o}=e;o.$async?r.throw((0,Q._)`new ${e.ValidationError}(${t})`):(r.assign((0,Q._)`${n}.errors`,t),r.return(!1))}var Nr={keyword:new Q.Name("keyword"),schemaPath:new Q.Name("schemaPath"),params:new Q.Name("params"),propertyName:new Q.Name("propertyName"),message:new Q.Name("message"),schema:new Q.Name("schema"),parentSchema:new Q.Name("parentSchema")};function w_(e,t,r){let{createErrors:n}=e.it;return n===!1?(0,Q._)`{}`:p2(e,t,r)}function p2(e,t,r={}){let{gen:n,it:o}=e,i=[m2(o,r),h2(e,r)];return g2(e,t,i),n.object(...i)}function m2({errorPath:e},{instancePath:t}){let r=t?(0,Q.str)`${e}${(0,Wa.getErrorPath)(t,Wa.Type.Str)}`:e;return[$e.default.instancePath,(0,Q.strConcat)($e.default.instancePath,r)]}function h2({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:n}){let o=n?t:(0,Q.str)`${t}/${e}`;return r&&(o=(0,Q.str)`${o}${(0,Wa.getErrorPath)(r,Wa.Type.Str)}`),[Nr.schemaPath,o]}function g2(e,{params:t,message:r},n){let{keyword:o,data:i,schemaValue:a,it:l}=e,{opts:c,propertyName:d,topSchemaRef:p,schemaPath:m}=l;n.push([Nr.keyword,o],[Nr.params,typeof t=="function"?t(e):t||(0,Q._)`{}`]),c.messages&&n.push([Nr.message,typeof r=="function"?r(e):r]),c.verbose&&n.push([Nr.schema,a],[Nr.parentSchema,(0,Q._)`${p}${m}`],[$e.default.data,i]),d&&n.push([Nr.propertyName,d])}});var E_=C(On=>{"use strict";Object.defineProperty(On,"__esModule",{value:!0});On.boolOrEmptySchema=On.topBoolOrEmptySchema=void 0;var y2=Zo(),D2=G(),b2=Wt(),_2={message:"boolean schema is false"};function w2(e){let{gen:t,schema:r,validateName:n}=e;r===!1?S_(e,!1):typeof r=="object"&&r.$async===!0?t.return(b2.default.data):(t.assign((0,D2._)`${n}.errors`,null),t.return(!0))}On.topBoolOrEmptySchema=w2;function S2(e,t){let{gen:r,schema:n}=e;n===!1?(r.var(t,!1),S_(e)):r.var(t,!0)}On.boolOrEmptySchema=S2;function S_(e,t){let{gen:r,data:n}=e,o={gen:r,keyword:"false schema",data:n,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,y2.reportError)(o,_2,void 0,t)}});var Mf=C(Bn=>{"use strict";Object.defineProperty(Bn,"__esModule",{value:!0});Bn.getRules=Bn.isJSONType=void 0;var E2=["string","number","integer","boolean","null","object","array"],v2=new Set(E2);function F2(e){return typeof e=="string"&&v2.has(e)}Bn.isJSONType=F2;function C2(){let e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}Bn.getRules=C2});var Nf=C(mr=>{"use strict";Object.defineProperty(mr,"__esModule",{value:!0});mr.shouldUseRule=mr.shouldUseGroup=mr.schemaHasRulesForType=void 0;function T2({schema:e,self:t},r){let n=t.RULES.types[r];return n&&n!==!0&&v_(e,n)}mr.schemaHasRulesForType=T2;function v_(e,t){return t.rules.some(r=>F_(e,r))}mr.shouldUseGroup=v_;function F_(e,t){var r;return e[t.keyword]!==void 0||((r=t.definition.implements)===null||r===void 0?void 0:r.some(n=>e[n]!==void 0))}mr.shouldUseRule=F_});var ei=C(Re=>{"use strict";Object.defineProperty(Re,"__esModule",{value:!0});Re.reportTypeError=Re.checkDataTypes=Re.checkDataType=Re.coerceAndCheckDataType=Re.getJSONTypes=Re.getSchemaTypes=Re.DataType=void 0;var R2=Mf(),A2=Nf(),$2=Zo(),U=G(),C_=Z(),In;(function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"})(In||(Re.DataType=In={}));function x2(e){let t=T_(e.type);if(t.includes("null")){if(e.nullable===!1)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&e.nullable!==void 0)throw new Error('"nullable" cannot be used without "type"');e.nullable===!0&&t.push("null")}return t}Re.getSchemaTypes=x2;function T_(e){let t=Array.isArray(e)?e:e?[e]:[];if(t.every(R2.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}Re.getJSONTypes=T_;function P2(e,t){let{gen:r,data:n,opts:o}=e,i=O2(t,o.coerceTypes),a=t.length>0&&!(i.length===0&&t.length===1&&(0,A2.schemaHasRulesForType)(e,t[0]));if(a){let l=Lf(t,n,o.strictNumbers,In.Wrong);r.if(l,()=>{i.length?B2(e,t,i):qf(e)})}return a}Re.coerceAndCheckDataType=P2;var R_=new Set(["string","number","integer","boolean","null"]);function O2(e,t){return t?e.filter(r=>R_.has(r)||t==="array"&&r==="array"):[]}function B2(e,t,r){let{gen:n,data:o,opts:i}=e,a=n.let("dataType",(0,U._)`typeof ${o}`),l=n.let("coerced",(0,U._)`undefined`);i.coerceTypes==="array"&&n.if((0,U._)`${a} == 'object' && Array.isArray(${o}) && ${o}.length == 1`,()=>n.assign(o,(0,U._)`${o}[0]`).assign(a,(0,U._)`typeof ${o}`).if(Lf(t,o,i.strictNumbers),()=>n.assign(l,o))),n.if((0,U._)`${l} !== undefined`);for(let d of r)(R_.has(d)||d==="array"&&i.coerceTypes==="array")&&c(d);n.else(),qf(e),n.endIf(),n.if((0,U._)`${l} !== undefined`,()=>{n.assign(o,l),I2(e,l)});function c(d){switch(d){case"string":n.elseIf((0,U._)`${a} == "number" || ${a} == "boolean"`).assign(l,(0,U._)`"" + ${o}`).elseIf((0,U._)`${o} === null`).assign(l,(0,U._)`""`);return;case"number":n.elseIf((0,U._)`${a} == "boolean" || ${o} === null
              || (${a} == "string" && ${o} && ${o} == +${o})`).assign(l,(0,U._)`+${o}`);return;case"integer":n.elseIf((0,U._)`${a} === "boolean" || ${o} === null
              || (${a} === "string" && ${o} && ${o} == +${o} && !(${o} % 1))`).assign(l,(0,U._)`+${o}`);return;case"boolean":n.elseIf((0,U._)`${o} === "false" || ${o} === 0 || ${o} === null`).assign(l,!1).elseIf((0,U._)`${o} === "true" || ${o} === 1`).assign(l,!0);return;case"null":n.elseIf((0,U._)`${o} === "" || ${o} === 0 || ${o} === false`),n.assign(l,null);return;case"array":n.elseIf((0,U._)`${a} === "string" || ${a} === "number"
              || ${a} === "boolean" || ${o} === null`).assign(l,(0,U._)`[${o}]`)}}}function I2({gen:e,parentData:t,parentDataProperty:r},n){e.if((0,U._)`${t} !== undefined`,()=>e.assign((0,U._)`${t}[${r}]`,n))}function jf(e,t,r,n=In.Correct){let o=n===In.Correct?U.operators.EQ:U.operators.NEQ,i;switch(e){case"null":return(0,U._)`${t} ${o} null`;case"array":i=(0,U._)`Array.isArray(${t})`;break;case"object":i=(0,U._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":i=a((0,U._)`!(${t} % 1) && !isNaN(${t})`);break;case"number":i=a();break;default:return(0,U._)`typeof ${t} ${o} ${e}`}return n===In.Correct?i:(0,U.not)(i);function a(l=U.nil){return(0,U.and)((0,U._)`typeof ${t} == "number"`,l,r?(0,U._)`isFinite(${t})`:U.nil)}}Re.checkDataType=jf;function Lf(e,t,r,n){if(e.length===1)return jf(e[0],t,r,n);let o,i=(0,C_.toHash)(e);if(i.array&&i.object){let a=(0,U._)`typeof ${t} != "object"`;o=i.null?a:(0,U._)`!${t} || ${a}`,delete i.null,delete i.array,delete i.object}else o=U.nil;i.number&&delete i.integer;for(let a in i)o=(0,U.and)(o,jf(a,t,r,n));return o}Re.checkDataTypes=Lf;var k2={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>typeof e=="string"?(0,U._)`{type: ${e}}`:(0,U._)`{type: ${t}}`};function qf(e){let t=M2(e);(0,$2.reportError)(t,k2)}Re.reportTypeError=qf;function M2(e){let{gen:t,data:r,schema:n}=e,o=(0,C_.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:o,schemaValue:o,parentSchema:n,params:{},it:e}}});var $_=C(Va=>{"use strict";Object.defineProperty(Va,"__esModule",{value:!0});Va.assignDefaults=void 0;var kn=G(),N2=Z();function j2(e,t){let{properties:r,items:n}=e.schema;if(t==="object"&&r)for(let o in r)A_(e,o,r[o].default);else t==="array"&&Array.isArray(n)&&n.forEach((o,i)=>A_(e,i,o.default))}Va.assignDefaults=j2;function A_(e,t,r){let{gen:n,compositeRule:o,data:i,opts:a}=e;if(r===void 0)return;let l=(0,kn._)`${i}${(0,kn.getProperty)(t)}`;if(o){(0,N2.checkStrictMode)(e,`default is ignored for: ${l}`);return}let c=(0,kn._)`${l} === undefined`;a.useDefaults==="empty"&&(c=(0,kn._)`${c} || ${l} === null || ${l} === ""`),n.if(c,(0,kn._)`${l} = ${(0,kn.stringify)(r)}`)}});var Ge=C(ne=>{"use strict";Object.defineProperty(ne,"__esModule",{value:!0});ne.validateUnion=ne.validateArray=ne.usePattern=ne.callValidateCode=ne.schemaProperties=ne.allSchemaProperties=ne.noPropertyInData=ne.propertyInData=ne.isOwnProperty=ne.hasPropFunc=ne.reportMissingProp=ne.checkMissingProp=ne.checkReportMissingProp=void 0;var le=G(),Uf=Z(),hr=Wt(),L2=Z();function q2(e,t){let{gen:r,data:n,it:o}=e;r.if(Wf(r,n,t,o.opts.ownProperties),()=>{e.setParams({missingProperty:(0,le._)`${t}`},!0),e.error()})}ne.checkReportMissingProp=q2;function U2({gen:e,data:t,it:{opts:r}},n,o){return(0,le.or)(...n.map(i=>(0,le.and)(Wf(e,t,i,r.ownProperties),(0,le._)`${o} = ${i}`)))}ne.checkMissingProp=U2;function z2(e,t){e.setParams({missingProperty:t},!0),e.error()}ne.reportMissingProp=z2;function x_(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:(0,le._)`Object.prototype.hasOwnProperty`})}ne.hasPropFunc=x_;function zf(e,t,r){return(0,le._)`${x_(e)}.call(${t}, ${r})`}ne.isOwnProperty=zf;function W2(e,t,r,n){let o=(0,le._)`${t}${(0,le.getProperty)(r)} !== undefined`;return n?(0,le._)`${o} && ${zf(e,t,r)}`:o}ne.propertyInData=W2;function Wf(e,t,r,n){let o=(0,le._)`${t}${(0,le.getProperty)(r)} === undefined`;return n?(0,le.or)(o,(0,le.not)(zf(e,t,r))):o}ne.noPropertyInData=Wf;function P_(e){return e?Object.keys(e).filter(t=>t!=="__proto__"):[]}ne.allSchemaProperties=P_;function V2(e,t){return P_(t).filter(r=>!(0,Uf.alwaysValidSchema)(e,t[r]))}ne.schemaProperties=V2;function G2({schemaCode:e,data:t,it:{gen:r,topSchemaRef:n,schemaPath:o,errorPath:i},it:a},l,c,d){let p=d?(0,le._)`${e}, ${t}, ${n}${o}`:t,m=[[hr.default.instancePath,(0,le.strConcat)(hr.default.instancePath,i)],[hr.default.parentData,a.parentData],[hr.default.parentDataProperty,a.parentDataProperty],[hr.default.rootData,hr.default.rootData]];a.opts.dynamicRef&&m.push([hr.default.dynamicAnchors,hr.default.dynamicAnchors]);let b=(0,le._)`${p}, ${r.object(...m)}`;return c!==le.nil?(0,le._)`${l}.call(${c}, ${b})`:(0,le._)`${l}(${b})`}ne.callValidateCode=G2;var H2=(0,le._)`new RegExp`;function Y2({gen:e,it:{opts:t}},r){let n=t.unicodeRegExp?"u":"",{regExp:o}=t.code,i=o(r,n);return e.scopeValue("pattern",{key:i.toString(),ref:i,code:(0,le._)`${o.code==="new RegExp"?H2:(0,L2.useFunc)(e,o)}(${r}, ${n})`})}ne.usePattern=Y2;function K2(e){let{gen:t,data:r,keyword:n,it:o}=e,i=t.name("valid");if(o.allErrors){let l=t.let("valid",!0);return a(()=>t.assign(l,!1)),l}return t.var(i,!0),a(()=>t.break()),i;function a(l){let c=t.const("len",(0,le._)`${r}.length`);t.forRange("i",0,c,d=>{e.subschema({keyword:n,dataProp:d,dataPropType:Uf.Type.Num},i),t.if((0,le.not)(i),l)})}}ne.validateArray=K2;function J2(e){let{gen:t,schema:r,keyword:n,it:o}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(c=>(0,Uf.alwaysValidSchema)(o,c))&&!o.opts.unevaluated)return;let a=t.let("valid",!1),l=t.name("_valid");t.block(()=>r.forEach((c,d)=>{let p=e.subschema({keyword:n,schemaProp:d,compositeRule:!0},l);t.assign(a,(0,le._)`${a} || ${l}`),e.mergeValidEvaluated(p,l)||t.if((0,le.not)(a))})),e.result(a,()=>e.reset(),()=>e.error(!0))}ne.validateUnion=J2});var I_=C(Tt=>{"use strict";Object.defineProperty(Tt,"__esModule",{value:!0});Tt.validateKeywordUsage=Tt.validSchemaType=Tt.funcKeywordCode=Tt.macroKeywordCode=void 0;var xe=G(),jr=Wt(),Q2=Ge(),X2=Zo();function Z2(e,t){let{gen:r,keyword:n,schema:o,parentSchema:i,it:a}=e,l=t.macro.call(a.self,o,i,a),c=B_(r,n,l);a.opts.validateSchema!==!1&&a.self.validateSchema(l,!0);let d=r.name("valid");e.subschema({schema:l,schemaPath:xe.nil,errSchemaPath:`${a.errSchemaPath}/${n}`,topSchemaRef:c,compositeRule:!0},d),e.pass(d,()=>e.error(!0))}Tt.macroKeywordCode=Z2;function eP(e,t){var r;let{gen:n,keyword:o,schema:i,parentSchema:a,$data:l,it:c}=e;rP(c,t);let d=!l&&t.compile?t.compile.call(c.self,i,a,c):t.validate,p=B_(n,o,d),m=n.let("valid");e.block$data(m,b),e.ok((r=t.valid)!==null&&r!==void 0?r:m);function b(){if(t.errors===!1)y(),t.modifying&&O_(e),S(()=>e.error());else{let w=t.async?D():g();t.modifying&&O_(e),S(()=>tP(e,w))}}function D(){let w=n.let("ruleErrs",null);return n.try(()=>y((0,xe._)`await `),T=>n.assign(m,!1).if((0,xe._)`${T} instanceof ${c.ValidationError}`,()=>n.assign(w,(0,xe._)`${T}.errors`),()=>n.throw(T))),w}function g(){let w=(0,xe._)`${p}.errors`;return n.assign(w,null),y(xe.nil),w}function y(w=t.async?(0,xe._)`await `:xe.nil){let T=c.opts.passContext?jr.default.this:jr.default.self,R=!("compile"in t&&!l||t.schema===!1);n.assign(m,(0,xe._)`${w}${(0,Q2.callValidateCode)(e,p,T,R)}`,t.modifying)}function S(w){var T;n.if((0,xe.not)((T=t.valid)!==null&&T!==void 0?T:m),w)}}Tt.funcKeywordCode=eP;function O_(e){let{gen:t,data:r,it:n}=e;t.if(n.parentData,()=>t.assign(r,(0,xe._)`${n.parentData}[${n.parentDataProperty}]`))}function tP(e,t){let{gen:r}=e;r.if((0,xe._)`Array.isArray(${t})`,()=>{r.assign(jr.default.vErrors,(0,xe._)`${jr.default.vErrors} === null ? ${t} : ${jr.default.vErrors}.concat(${t})`).assign(jr.default.errors,(0,xe._)`${jr.default.vErrors}.length`),(0,X2.extendErrors)(e)},()=>e.error())}function rP({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}function B_(e,t,r){if(r===void 0)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword",typeof r=="function"?{ref:r}:{ref:r,code:(0,xe.stringify)(r)})}function nP(e,t,r=!1){return!t.length||t.some(n=>n==="array"?Array.isArray(e):n==="object"?e&&typeof e=="object"&&!Array.isArray(e):typeof e==n||r&&typeof e>"u")}Tt.validSchemaType=nP;function oP({schema:e,opts:t,self:r,errSchemaPath:n},o,i){if(Array.isArray(o.keyword)?!o.keyword.includes(i):o.keyword!==i)throw new Error("ajv implementation error");let a=o.dependencies;if(a?.some(l=>!Object.prototype.hasOwnProperty.call(e,l)))throw new Error(`parent schema must have dependencies of ${i}: ${a.join(",")}`);if(o.validateSchema&&!o.validateSchema(e[i])){let c=`keyword "${i}" value is invalid at path "${n}": `+r.errorsText(o.validateSchema.errors);if(t.validateSchema==="log")r.logger.error(c);else throw new Error(c)}}Tt.validateKeywordUsage=oP});var M_=C(gr=>{"use strict";Object.defineProperty(gr,"__esModule",{value:!0});gr.extendSubschemaMode=gr.extendSubschemaData=gr.getSubschema=void 0;var Rt=G(),k_=Z();function iP(e,{keyword:t,schemaProp:r,schema:n,schemaPath:o,errSchemaPath:i,topSchemaRef:a}){if(t!==void 0&&n!==void 0)throw new Error('both "keyword" and "schema" passed, only one allowed');if(t!==void 0){let l=e.schema[t];return r===void 0?{schema:l,schemaPath:(0,Rt._)`${e.schemaPath}${(0,Rt.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:l[r],schemaPath:(0,Rt._)`${e.schemaPath}${(0,Rt.getProperty)(t)}${(0,Rt.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,k_.escapeFragment)(r)}`}}if(n!==void 0){if(o===void 0||i===void 0||a===void 0)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:n,schemaPath:o,topSchemaRef:a,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')}gr.getSubschema=iP;function sP(e,t,{dataProp:r,dataPropType:n,data:o,dataTypes:i,propertyName:a}){if(o!==void 0&&r!==void 0)throw new Error('both "data" and "dataProp" passed, only one allowed');let{gen:l}=t;if(r!==void 0){let{errorPath:d,dataPathArr:p,opts:m}=t,b=l.let("data",(0,Rt._)`${t.data}${(0,Rt.getProperty)(r)}`,!0);c(b),e.errorPath=(0,Rt.str)`${d}${(0,k_.getErrorPath)(r,n,m.jsPropertySyntax)}`,e.parentDataProperty=(0,Rt._)`${r}`,e.dataPathArr=[...p,e.parentDataProperty]}if(o!==void 0){let d=o instanceof Rt.Name?o:l.let("data",o,!0);c(d),a!==void 0&&(e.propertyName=a)}i&&(e.dataTypes=i);function c(d){e.data=d,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,d]}}gr.extendSubschemaData=sP;function aP(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:o,allErrors:i}){n!==void 0&&(e.compositeRule=n),o!==void 0&&(e.createErrors=o),i!==void 0&&(e.allErrors=i),e.jtdDiscriminator=t,e.jtdMetadata=r}gr.extendSubschemaMode=aP});var Vf=C((C4,N_)=>{"use strict";N_.exports=function e(t,r){if(t===r)return!0;if(t&&r&&typeof t=="object"&&typeof r=="object"){if(t.constructor!==r.constructor)return!1;var n,o,i;if(Array.isArray(t)){if(n=t.length,n!=r.length)return!1;for(o=n;o--!==0;)if(!e(t[o],r[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if(i=Object.keys(t),n=i.length,n!==Object.keys(r).length)return!1;for(o=n;o--!==0;)if(!Object.prototype.hasOwnProperty.call(r,i[o]))return!1;for(o=n;o--!==0;){var a=i[o];if(!e(t[a],r[a]))return!1}return!0}return t!==t&&r!==r}});var L_=C((T4,j_)=>{"use strict";var yr=j_.exports=function(e,t,r){typeof t=="function"&&(r=t,t={}),r=t.cb||r;var n=typeof r=="function"?r:r.pre||function(){},o=r.post||function(){};Ga(t,n,o,e,"",e)};yr.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0};yr.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0};yr.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0};yr.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function Ga(e,t,r,n,o,i,a,l,c,d){if(n&&typeof n=="object"&&!Array.isArray(n)){t(n,o,i,a,l,c,d);for(var p in n){var m=n[p];if(Array.isArray(m)){if(p in yr.arrayKeywords)for(var b=0;b<m.length;b++)Ga(e,t,r,m[b],o+"/"+p+"/"+b,i,o,p,n,b)}else if(p in yr.propsKeywords){if(m&&typeof m=="object")for(var D in m)Ga(e,t,r,m[D],o+"/"+p+"/"+uP(D),i,o,p,n,D)}else(p in yr.keywords||e.allKeys&&!(p in yr.skipKeywords))&&Ga(e,t,r,m,o+"/"+p,i,o,p,n)}r(n,o,i,a,l,c,d)}}function uP(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}});var ti=C(ke=>{"use strict";Object.defineProperty(ke,"__esModule",{value:!0});ke.getSchemaRefs=ke.resolveUrl=ke.normalizeId=ke._getFullPath=ke.getFullPath=ke.inlineRef=void 0;var lP=Z(),cP=Vf(),fP=L_(),dP=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);function pP(e,t=!0){return typeof e=="boolean"?!0:t===!0?!Gf(e):t?q_(e)<=t:!1}ke.inlineRef=pP;var mP=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function Gf(e){for(let t in e){if(mP.has(t))return!0;let r=e[t];if(Array.isArray(r)&&r.some(Gf)||typeof r=="object"&&Gf(r))return!0}return!1}function q_(e){let t=0;for(let r in e){if(r==="$ref")return 1/0;if(t++,!dP.has(r)&&(typeof e[r]=="object"&&(0,lP.eachItem)(e[r],n=>t+=q_(n)),t===1/0))return 1/0}return t}function U_(e,t="",r){r!==!1&&(t=Mn(t));let n=e.parse(t);return z_(e,n)}ke.getFullPath=U_;function z_(e,t){return e.serialize(t).split("#")[0]+"#"}ke._getFullPath=z_;var hP=/#\/?$/;function Mn(e){return e?e.replace(hP,""):""}ke.normalizeId=Mn;function gP(e,t,r){return r=Mn(r),e.resolve(t,r)}ke.resolveUrl=gP;var yP=/^[a-z_][-a-z0-9._]*$/i;function DP(e,t){if(typeof e=="boolean")return{};let{schemaId:r,uriResolver:n}=this.opts,o=Mn(e[r]||t),i={"":o},a=U_(n,o,!1),l={},c=new Set;return fP(e,{allKeys:!0},(m,b,D,g)=>{if(g===void 0)return;let y=a+b,S=i[g];typeof m[r]=="string"&&(S=w.call(this,m[r])),T.call(this,m.$anchor),T.call(this,m.$dynamicAnchor),i[b]=S;function w(R){let A=this.opts.uriResolver.resolve;if(R=Mn(S?A(S,R):R),c.has(R))throw p(R);c.add(R);let v=this.refs[R];return typeof v=="string"&&(v=this.refs[v]),typeof v=="object"?d(m,v.schema,R):R!==Mn(y)&&(R[0]==="#"?(d(m,l[R],R),l[R]=m):this.refs[R]=y),R}function T(R){if(typeof R=="string"){if(!yP.test(R))throw new Error(`invalid anchor "${R}"`);w.call(this,`#${R}`)}}}),l;function d(m,b,D){if(b!==void 0&&!cP(m,b))throw p(D)}function p(m){return new Error(`reference "${m}" resolves to more than one schema`)}}ke.getSchemaRefs=DP});var oi=C(Dr=>{"use strict";Object.defineProperty(Dr,"__esModule",{value:!0});Dr.getData=Dr.KeywordCxt=Dr.validateFunctionCode=void 0;var Y_=E_(),W_=ei(),Yf=Nf(),Ha=ei(),bP=$_(),ni=I_(),Hf=M_(),O=G(),M=Wt(),_P=ti(),Vt=Z(),ri=Zo();function wP(e){if(Q_(e)&&(X_(e),J_(e))){vP(e);return}K_(e,()=>(0,Y_.topBoolOrEmptySchema)(e))}Dr.validateFunctionCode=wP;function K_({gen:e,validateName:t,schema:r,schemaEnv:n,opts:o},i){o.code.es5?e.func(t,(0,O._)`${M.default.data}, ${M.default.valCxt}`,n.$async,()=>{e.code((0,O._)`"use strict"; ${V_(r,o)}`),EP(e,o),e.code(i)}):e.func(t,(0,O._)`${M.default.data}, ${SP(o)}`,n.$async,()=>e.code(V_(r,o)).code(i))}function SP(e){return(0,O._)`{${M.default.instancePath}="", ${M.default.parentData}, ${M.default.parentDataProperty}, ${M.default.rootData}=${M.default.data}${e.dynamicRef?(0,O._)`, ${M.default.dynamicAnchors}={}`:O.nil}}={}`}function EP(e,t){e.if(M.default.valCxt,()=>{e.var(M.default.instancePath,(0,O._)`${M.default.valCxt}.${M.default.instancePath}`),e.var(M.default.parentData,(0,O._)`${M.default.valCxt}.${M.default.parentData}`),e.var(M.default.parentDataProperty,(0,O._)`${M.default.valCxt}.${M.default.parentDataProperty}`),e.var(M.default.rootData,(0,O._)`${M.default.valCxt}.${M.default.rootData}`),t.dynamicRef&&e.var(M.default.dynamicAnchors,(0,O._)`${M.default.valCxt}.${M.default.dynamicAnchors}`)},()=>{e.var(M.default.instancePath,(0,O._)`""`),e.var(M.default.parentData,(0,O._)`undefined`),e.var(M.default.parentDataProperty,(0,O._)`undefined`),e.var(M.default.rootData,M.default.data),t.dynamicRef&&e.var(M.default.dynamicAnchors,(0,O._)`{}`)})}function vP(e){let{schema:t,opts:r,gen:n}=e;K_(e,()=>{r.$comment&&t.$comment&&ew(e),AP(e),n.let(M.default.vErrors,null),n.let(M.default.errors,0),r.unevaluated&&FP(e),Z_(e),PP(e)})}function FP(e){let{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",(0,O._)`${r}.evaluated`),t.if((0,O._)`${e.evaluated}.dynamicProps`,()=>t.assign((0,O._)`${e.evaluated}.props`,(0,O._)`undefined`)),t.if((0,O._)`${e.evaluated}.dynamicItems`,()=>t.assign((0,O._)`${e.evaluated}.items`,(0,O._)`undefined`))}function V_(e,t){let r=typeof e=="object"&&e[t.schemaId];return r&&(t.code.source||t.code.process)?(0,O._)`/*# sourceURL=${r} */`:O.nil}function CP(e,t){if(Q_(e)&&(X_(e),J_(e))){TP(e,t);return}(0,Y_.boolOrEmptySchema)(e,t)}function J_({schema:e,self:t}){if(typeof e=="boolean")return!e;for(let r in e)if(t.RULES.all[r])return!0;return!1}function Q_(e){return typeof e.schema!="boolean"}function TP(e,t){let{schema:r,gen:n,opts:o}=e;o.$comment&&r.$comment&&ew(e),$P(e),xP(e);let i=n.const("_errs",M.default.errors);Z_(e,i),n.var(t,(0,O._)`${i} === ${M.default.errors}`)}function X_(e){(0,Vt.checkUnknownRules)(e),RP(e)}function Z_(e,t){if(e.opts.jtd)return G_(e,[],!1,t);let r=(0,W_.getSchemaTypes)(e.schema),n=(0,W_.coerceAndCheckDataType)(e,r);G_(e,r,!n,t)}function RP(e){let{schema:t,errSchemaPath:r,opts:n,self:o}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,Vt.schemaHasRulesButRef)(t,o.RULES)&&o.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}function AP(e){let{schema:t,opts:r}=e;t.default!==void 0&&r.useDefaults&&r.strictSchema&&(0,Vt.checkStrictMode)(e,"default is ignored in the schema root")}function $P(e){let t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,_P.resolveUrl)(e.opts.uriResolver,e.baseId,t))}function xP(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}function ew({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:o}){let i=r.$comment;if(o.$comment===!0)e.code((0,O._)`${M.default.self}.logger.log(${i})`);else if(typeof o.$comment=="function"){let a=(0,O.str)`${n}/$comment`,l=e.scopeValue("root",{ref:t.root});e.code((0,O._)`${M.default.self}.opts.$comment(${i}, ${a}, ${l}.schema)`)}}function PP(e){let{gen:t,schemaEnv:r,validateName:n,ValidationError:o,opts:i}=e;r.$async?t.if((0,O._)`${M.default.errors} === 0`,()=>t.return(M.default.data),()=>t.throw((0,O._)`new ${o}(${M.default.vErrors})`)):(t.assign((0,O._)`${n}.errors`,M.default.vErrors),i.unevaluated&&OP(e),t.return((0,O._)`${M.default.errors} === 0`))}function OP({gen:e,evaluated:t,props:r,items:n}){r instanceof O.Name&&e.assign((0,O._)`${t}.props`,r),n instanceof O.Name&&e.assign((0,O._)`${t}.items`,n)}function G_(e,t,r,n){let{gen:o,schema:i,data:a,allErrors:l,opts:c,self:d}=e,{RULES:p}=d;if(i.$ref&&(c.ignoreKeywordsWithRef||!(0,Vt.schemaHasRulesButRef)(i,p))){o.block(()=>rw(e,"$ref",p.all.$ref.definition));return}c.jtd||BP(e,t),o.block(()=>{for(let b of p.rules)m(b);m(p.post)});function m(b){(0,Yf.shouldUseGroup)(i,b)&&(b.type?(o.if((0,Ha.checkDataType)(b.type,a,c.strictNumbers)),H_(e,b),t.length===1&&t[0]===b.type&&r&&(o.else(),(0,Ha.reportTypeError)(e)),o.endIf()):H_(e,b),l||o.if((0,O._)`${M.default.errors} === ${n||0}`))}}function H_(e,t){let{gen:r,schema:n,opts:{useDefaults:o}}=e;o&&(0,bP.assignDefaults)(e,t.type),r.block(()=>{for(let i of t.rules)(0,Yf.shouldUseRule)(n,i)&&rw(e,i.keyword,i.definition,t.type)})}function BP(e,t){e.schemaEnv.meta||!e.opts.strictTypes||(IP(e,t),e.opts.allowUnionTypes||kP(e,t),MP(e,e.dataTypes))}function IP(e,t){if(t.length){if(!e.dataTypes.length){e.dataTypes=t;return}t.forEach(r=>{tw(e.dataTypes,r)||Kf(e,`type "${r}" not allowed by context "${e.dataTypes.join(",")}"`)}),jP(e,t)}}function kP(e,t){t.length>1&&!(t.length===2&&t.includes("null"))&&Kf(e,"use allowUnionTypes to allow union type keyword")}function MP(e,t){let r=e.self.RULES.all;for(let n in r){let o=r[n];if(typeof o=="object"&&(0,Yf.shouldUseRule)(e.schema,o)){let{type:i}=o.definition;i.length&&!i.some(a=>NP(t,a))&&Kf(e,`missing type "${i.join(",")}" for keyword "${n}"`)}}}function NP(e,t){return e.includes(t)||t==="number"&&e.includes("integer")}function tw(e,t){return e.includes(t)||t==="integer"&&e.includes("number")}function jP(e,t){let r=[];for(let n of e.dataTypes)tw(t,n)?r.push(n):t.includes("integer")&&n==="number"&&r.push("integer");e.dataTypes=r}function Kf(e,t){let r=e.schemaEnv.baseId+e.errSchemaPath;t+=` at "${r}" (strictTypes)`,(0,Vt.checkStrictMode)(e,t,e.opts.strictTypes)}var Ya=class{constructor(t,r,n){if((0,ni.validateKeywordUsage)(t,r,n),this.gen=t.gen,this.allErrors=t.allErrors,this.keyword=n,this.data=t.data,this.schema=t.schema[n],this.$data=r.$data&&t.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,Vt.schemaRefOrVal)(t,this.schema,n,this.$data),this.schemaType=r.schemaType,this.parentSchema=t.schema,this.params={},this.it=t,this.def=r,this.$data)this.schemaCode=t.gen.const("vSchema",nw(this.$data,t));else if(this.schemaCode=this.schemaValue,!(0,ni.validSchemaType)(this.schema,r.schemaType,r.allowUndefined))throw new Error(`${n} value must be ${JSON.stringify(r.schemaType)}`);("code"in r?r.trackErrors:r.errors!==!1)&&(this.errsCount=t.gen.const("_errs",M.default.errors))}result(t,r,n){this.failResult((0,O.not)(t),r,n)}failResult(t,r,n){this.gen.if(t),n?n():this.error(),r?(this.gen.else(),r(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(t,r){this.failResult((0,O.not)(t),void 0,r)}fail(t){if(t===void 0){this.error(),this.allErrors||this.gen.if(!1);return}this.gen.if(t),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(t){if(!this.$data)return this.fail(t);let{schemaCode:r}=this;this.fail((0,O._)`${r} !== undefined && (${(0,O.or)(this.invalid$data(),t)})`)}error(t,r,n){if(r){this.setParams(r),this._error(t,n),this.setParams({});return}this._error(t,n)}_error(t,r){(t?ri.reportExtraError:ri.reportError)(this,this.def.error,r)}$dataError(){(0,ri.reportError)(this,this.def.$dataError||ri.keyword$DataError)}reset(){if(this.errsCount===void 0)throw new Error('add "trackErrors" to keyword definition');(0,ri.resetErrorsCount)(this.gen,this.errsCount)}ok(t){this.allErrors||this.gen.if(t)}setParams(t,r){r?Object.assign(this.params,t):this.params=t}block$data(t,r,n=O.nil){this.gen.block(()=>{this.check$data(t,n),r()})}check$data(t=O.nil,r=O.nil){if(!this.$data)return;let{gen:n,schemaCode:o,schemaType:i,def:a}=this;n.if((0,O.or)((0,O._)`${o} === undefined`,r)),t!==O.nil&&n.assign(t,!0),(i.length||a.validateSchema)&&(n.elseIf(this.invalid$data()),this.$dataError(),t!==O.nil&&n.assign(t,!1)),n.else()}invalid$data(){let{gen:t,schemaCode:r,schemaType:n,def:o,it:i}=this;return(0,O.or)(a(),l());function a(){if(n.length){if(!(r instanceof O.Name))throw new Error("ajv implementation error");let c=Array.isArray(n)?n:[n];return(0,O._)`${(0,Ha.checkDataTypes)(c,r,i.opts.strictNumbers,Ha.DataType.Wrong)}`}return O.nil}function l(){if(o.validateSchema){let c=t.scopeValue("validate$data",{ref:o.validateSchema});return(0,O._)`!${c}(${r})`}return O.nil}}subschema(t,r){let n=(0,Hf.getSubschema)(this.it,t);(0,Hf.extendSubschemaData)(n,this.it,t),(0,Hf.extendSubschemaMode)(n,t);let o={...this.it,...n,items:void 0,props:void 0};return CP(o,r),o}mergeEvaluated(t,r){let{it:n,gen:o}=this;n.opts.unevaluated&&(n.props!==!0&&t.props!==void 0&&(n.props=Vt.mergeEvaluated.props(o,t.props,n.props,r)),n.items!==!0&&t.items!==void 0&&(n.items=Vt.mergeEvaluated.items(o,t.items,n.items,r)))}mergeValidEvaluated(t,r){let{it:n,gen:o}=this;if(n.opts.unevaluated&&(n.props!==!0||n.items!==!0))return o.if(r,()=>this.mergeEvaluated(t,O.Name)),!0}};Dr.KeywordCxt=Ya;function rw(e,t,r,n){let o=new Ya(e,r,t);"code"in r?r.code(o,n):o.$data&&r.validate?(0,ni.funcKeywordCode)(o,r):"macro"in r?(0,ni.macroKeywordCode)(o,r):(r.compile||r.validate)&&(0,ni.funcKeywordCode)(o,r)}var LP=/^\/(?:[^~]|~0|~1)*$/,qP=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function nw(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let o,i;if(e==="")return M.default.rootData;if(e[0]==="/"){if(!LP.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);o=e,i=M.default.rootData}else{let d=qP.exec(e);if(!d)throw new Error(`Invalid JSON-pointer: ${e}`);let p=+d[1];if(o=d[2],o==="#"){if(p>=t)throw new Error(c("property/index",p));return n[t-p]}if(p>t)throw new Error(c("data",p));if(i=r[t-p],!o)return i}let a=i,l=o.split("/");for(let d of l)d&&(i=(0,O._)`${i}${(0,O.getProperty)((0,Vt.unescapeJsonPointer)(d))}`,a=(0,O._)`${a} && ${i}`);return a;function c(d,p){return`Cannot access ${d} ${p} levels up, current level is ${t}`}}Dr.getData=nw});var Ka=C(Qf=>{"use strict";Object.defineProperty(Qf,"__esModule",{value:!0});var Jf=class extends Error{constructor(t){super("validation failed"),this.errors=t,this.ajv=this.validation=!0}};Qf.default=Jf});var ii=C(ed=>{"use strict";Object.defineProperty(ed,"__esModule",{value:!0});var Xf=ti(),Zf=class extends Error{constructor(t,r,n,o){super(o||`can't resolve reference ${n} from id ${r}`),this.missingRef=(0,Xf.resolveUrl)(t,r,n),this.missingSchema=(0,Xf.normalizeId)((0,Xf.getFullPath)(t,this.missingRef))}};ed.default=Zf});var Qa=C(He=>{"use strict";Object.defineProperty(He,"__esModule",{value:!0});He.resolveSchema=He.getCompilingSchema=He.resolveRef=He.compileSchema=He.SchemaEnv=void 0;var ft=G(),UP=Ka(),Lr=Wt(),dt=ti(),ow=Z(),zP=oi(),Nn=class{constructor(t){var r;this.refs={},this.dynamicAnchors={};let n;typeof t.schema=="object"&&(n=t.schema),this.schema=t.schema,this.schemaId=t.schemaId,this.root=t.root||this,this.baseId=(r=t.baseId)!==null&&r!==void 0?r:(0,dt.normalizeId)(n?.[t.schemaId||"$id"]),this.schemaPath=t.schemaPath,this.localRefs=t.localRefs,this.meta=t.meta,this.$async=n?.$async,this.refs={}}};He.SchemaEnv=Nn;function rd(e){let t=iw.call(this,e);if(t)return t;let r=(0,dt.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:n,lines:o}=this.opts.code,{ownProperties:i}=this.opts,a=new ft.CodeGen(this.scope,{es5:n,lines:o,ownProperties:i}),l;e.$async&&(l=a.scopeValue("Error",{ref:UP.default,code:(0,ft._)`require("ajv/dist/runtime/validation_error").default`}));let c=a.scopeName("validate");e.validateName=c;let d={gen:a,allErrors:this.opts.allErrors,data:Lr.default.data,parentData:Lr.default.parentData,parentDataProperty:Lr.default.parentDataProperty,dataNames:[Lr.default.data],dataPathArr:[ft.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:a.scopeValue("schema",this.opts.code.source===!0?{ref:e.schema,code:(0,ft.stringify)(e.schema)}:{ref:e.schema}),validateName:c,ValidationError:l,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:ft.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:(0,ft._)`""`,opts:this.opts,self:this},p;try{this._compilations.add(e),(0,zP.validateFunctionCode)(d),a.optimize(this.opts.code.optimize);let m=a.toString();p=`${a.scopeRefs(Lr.default.scope)}return ${m}`,this.opts.code.process&&(p=this.opts.code.process(p,e));let D=new Function(`${Lr.default.self}`,`${Lr.default.scope}`,p)(this,this.scope.get());if(this.scope.value(c,{ref:D}),D.errors=null,D.schema=e.schema,D.schemaEnv=e,e.$async&&(D.$async=!0),this.opts.code.source===!0&&(D.source={validateName:c,validateCode:m,scopeValues:a._values}),this.opts.unevaluated){let{props:g,items:y}=d;D.evaluated={props:g instanceof ft.Name?void 0:g,items:y instanceof ft.Name?void 0:y,dynamicProps:g instanceof ft.Name,dynamicItems:y instanceof ft.Name},D.source&&(D.source.evaluated=(0,ft.stringify)(D.evaluated))}return e.validate=D,e}catch(m){throw delete e.validate,delete e.validateName,p&&this.logger.error("Error compiling schema, function code:",p),m}finally{this._compilations.delete(e)}}He.compileSchema=rd;function WP(e,t,r){var n;r=(0,dt.resolveUrl)(this.opts.uriResolver,t,r);let o=e.refs[r];if(o)return o;let i=HP.call(this,e,r);if(i===void 0){let a=(n=e.localRefs)===null||n===void 0?void 0:n[r],{schemaId:l}=this.opts;a&&(i=new Nn({schema:a,schemaId:l,root:e,baseId:t}))}if(i!==void 0)return e.refs[r]=VP.call(this,i)}He.resolveRef=WP;function VP(e){return(0,dt.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:rd.call(this,e)}function iw(e){for(let t of this._compilations)if(GP(t,e))return t}He.getCompilingSchema=iw;function GP(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function HP(e,t){let r;for(;typeof(r=this.refs[t])=="string";)t=r;return r||this.schemas[t]||Ja.call(this,e,t)}function Ja(e,t){let r=this.opts.uriResolver.parse(t),n=(0,dt._getFullPath)(this.opts.uriResolver,r),o=(0,dt.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===o)return td.call(this,r,e);let i=(0,dt.normalizeId)(n),a=this.refs[i]||this.schemas[i];if(typeof a=="string"){let l=Ja.call(this,e,a);return typeof l?.schema!="object"?void 0:td.call(this,r,l)}if(typeof a?.schema=="object"){if(a.validate||rd.call(this,a),i===(0,dt.normalizeId)(t)){let{schema:l}=a,{schemaId:c}=this.opts,d=l[c];return d&&(o=(0,dt.resolveUrl)(this.opts.uriResolver,o,d)),new Nn({schema:l,schemaId:c,root:e,baseId:o})}return td.call(this,r,a)}}He.resolveSchema=Ja;var YP=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function td(e,{baseId:t,schema:r,root:n}){var o;if(((o=e.fragment)===null||o===void 0?void 0:o[0])!=="/")return;for(let l of e.fragment.slice(1).split("/")){if(typeof r=="boolean")return;let c=r[(0,ow.unescapeFragment)(l)];if(c===void 0)return;r=c;let d=typeof r=="object"&&r[this.opts.schemaId];!YP.has(l)&&d&&(t=(0,dt.resolveUrl)(this.opts.uriResolver,t,d))}let i;if(typeof r!="boolean"&&r.$ref&&!(0,ow.schemaHasRulesButRef)(r,this.RULES)){let l=(0,dt.resolveUrl)(this.opts.uriResolver,t,r.$ref);i=Ja.call(this,n,l)}let{schemaId:a}=this.opts;if(i=i||new Nn({schema:r,schemaId:a,root:n,baseId:t}),i.schema!==i.root.schema)return i}});var sw=C((O4,KP)=>{KP.exports={$id:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",description:"Meta-schema for $data reference (JSON AnySchema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1}});var uw=C((B4,aw)=>{"use strict";var JP={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};aw.exports={HEX:JP}});var gw=C((I4,hw)=>{"use strict";var{HEX:QP}=uw();function dw(e){if(mw(e,".")<3)return{host:e,isIPV4:!1};let t=e.match(/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/u)||[],[r]=t;return r?{host:ZP(r,"."),isIPV4:!0}:{host:e,isIPV4:!1}}function nd(e,t=!1){let r="",n=!0;for(let o of e){if(QP[o]===void 0)return;o!=="0"&&n===!0&&(n=!1),n||(r+=o)}return t&&r.length===0&&(r="0"),r}function XP(e){let t=0,r={error:!1,address:"",zone:""},n=[],o=[],i=!1,a=!1,l=!1;function c(){if(o.length){if(i===!1){let d=nd(o);if(d!==void 0)n.push(d);else return r.error=!0,!1}o.length=0}return!0}for(let d=0;d<e.length;d++){let p=e[d];if(!(p==="["||p==="]"))if(p===":"){if(a===!0&&(l=!0),!c())break;if(t++,n.push(":"),t>7){r.error=!0;break}d-1>=0&&e[d-1]===":"&&(a=!0);continue}else if(p==="%"){if(!c())break;i=!0}else{o.push(p);continue}}return o.length&&(i?r.zone=o.join(""):l?n.push(o.join("")):n.push(nd(o))),r.address=n.join(""),r}function pw(e,t={}){if(mw(e,":")<2)return{host:e,isIPV6:!1};let r=XP(e);if(r.error)return{host:e,isIPV6:!1};{let n=r.address,o=r.address;return r.zone&&(n+="%"+r.zone,o+="%25"+r.zone),{host:n,escapedHost:o,isIPV6:!0}}}function ZP(e,t){let r="",n=!0,o=e.length;for(let i=0;i<o;i++){let a=e[i];a==="0"&&n?(i+1<=o&&e[i+1]===t||i+1===o)&&(r+=a,n=!1):(a===t?n=!0:n=!1,r+=a)}return r}function mw(e,t){let r=0;for(let n=0;n<e.length;n++)e[n]===t&&r++;return r}var lw=/^\.\.?\//u,cw=/^\/\.(?:\/|$)/u,fw=/^\/\.\.(?:\/|$)/u,eO=/^\/?(?:.|\n)*?(?=\/|$)/u;function tO(e){let t=[];for(;e.length;)if(e.match(lw))e=e.replace(lw,"");else if(e.match(cw))e=e.replace(cw,"/");else if(e.match(fw))e=e.replace(fw,"/"),t.pop();else if(e==="."||e==="..")e="";else{let r=e.match(eO);if(r){let n=r[0];e=e.slice(n.length),t.push(n)}else throw new Error("Unexpected dot segment condition")}return t.join("")}function rO(e,t){let r=t!==!0?escape:unescape;return e.scheme!==void 0&&(e.scheme=r(e.scheme)),e.userinfo!==void 0&&(e.userinfo=r(e.userinfo)),e.host!==void 0&&(e.host=r(e.host)),e.path!==void 0&&(e.path=r(e.path)),e.query!==void 0&&(e.query=r(e.query)),e.fragment!==void 0&&(e.fragment=r(e.fragment)),e}function nO(e,t){let r=[];if(e.userinfo!==void 0&&(r.push(e.userinfo),r.push("@")),e.host!==void 0){let n=unescape(e.host),o=dw(n);if(o.isIPV4)n=o.host;else{let i=pw(o.host,{isIPV4:!1});i.isIPV6===!0?n=`[${i.escapedHost}]`:n=e.host}r.push(n)}return(typeof e.port=="number"||typeof e.port=="string")&&(r.push(":"),r.push(String(e.port))),r.length?r.join(""):void 0}hw.exports={recomposeAuthority:nO,normalizeComponentEncoding:rO,removeDotSegments:tO,normalizeIPv4:dw,normalizeIPv6:pw,stringArrayToHexStripped:nd}});var Sw=C((k4,ww)=>{"use strict";var oO=/^[\da-f]{8}\b-[\da-f]{4}\b-[\da-f]{4}\b-[\da-f]{4}\b-[\da-f]{12}$/iu,iO=/([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu;function yw(e){return typeof e.secure=="boolean"?e.secure:String(e.scheme).toLowerCase()==="wss"}function Dw(e){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e}function bw(e){let t=String(e.scheme).toLowerCase()==="https";return(e.port===(t?443:80)||e.port==="")&&(e.port=void 0),e.path||(e.path="/"),e}function sO(e){return e.secure=yw(e),e.resourceName=(e.path||"/")+(e.query?"?"+e.query:""),e.path=void 0,e.query=void 0,e}function aO(e){if((e.port===(yw(e)?443:80)||e.port==="")&&(e.port=void 0),typeof e.secure=="boolean"&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){let[t,r]=e.resourceName.split("?");e.path=t&&t!=="/"?t:void 0,e.query=r,e.resourceName=void 0}return e.fragment=void 0,e}function uO(e,t){if(!e.path)return e.error="URN can not be parsed",e;let r=e.path.match(iO);if(r){let n=t.scheme||e.scheme||"urn";e.nid=r[1].toLowerCase(),e.nss=r[2];let o=`${n}:${t.nid||e.nid}`,i=od[o];e.path=void 0,i&&(e=i.parse(e,t))}else e.error=e.error||"URN can not be parsed.";return e}function lO(e,t){let r=t.scheme||e.scheme||"urn",n=e.nid.toLowerCase(),o=`${r}:${t.nid||n}`,i=od[o];i&&(e=i.serialize(e,t));let a=e,l=e.nss;return a.path=`${n||t.nid}:${l}`,t.skipEscape=!0,a}function cO(e,t){let r=e;return r.uuid=r.nss,r.nss=void 0,!t.tolerant&&(!r.uuid||!oO.test(r.uuid))&&(r.error=r.error||"UUID is not valid."),r}function fO(e){let t=e;return t.nss=(e.uuid||"").toLowerCase(),t}var _w={scheme:"http",domainHost:!0,parse:Dw,serialize:bw},dO={scheme:"https",domainHost:_w.domainHost,parse:Dw,serialize:bw},Xa={scheme:"ws",domainHost:!0,parse:sO,serialize:aO},pO={scheme:"wss",domainHost:Xa.domainHost,parse:Xa.parse,serialize:Xa.serialize},mO={scheme:"urn",parse:uO,serialize:lO,skipNormalize:!0},hO={scheme:"urn:uuid",parse:cO,serialize:fO,skipNormalize:!0},od={http:_w,https:dO,ws:Xa,wss:pO,urn:mO,"urn:uuid":hO};ww.exports=od});var vw=C((M4,eu)=>{"use strict";var{normalizeIPv6:gO,normalizeIPv4:yO,removeDotSegments:si,recomposeAuthority:DO,normalizeComponentEncoding:Za}=gw(),id=Sw();function bO(e,t){return typeof e=="string"?e=At(Gt(e,t),t):typeof e=="object"&&(e=Gt(At(e,t),t)),e}function _O(e,t,r){let n=Object.assign({scheme:"null"},r),o=Ew(Gt(e,n),Gt(t,n),n,!0);return At(o,{...n,skipEscape:!0})}function Ew(e,t,r,n){let o={};return n||(e=Gt(At(e,r),r),t=Gt(At(t,r),r)),r=r||{},!r.tolerant&&t.scheme?(o.scheme=t.scheme,o.userinfo=t.userinfo,o.host=t.host,o.port=t.port,o.path=si(t.path||""),o.query=t.query):(t.userinfo!==void 0||t.host!==void 0||t.port!==void 0?(o.userinfo=t.userinfo,o.host=t.host,o.port=t.port,o.path=si(t.path||""),o.query=t.query):(t.path?(t.path.charAt(0)==="/"?o.path=si(t.path):((e.userinfo!==void 0||e.host!==void 0||e.port!==void 0)&&!e.path?o.path="/"+t.path:e.path?o.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:o.path=t.path,o.path=si(o.path)),o.query=t.query):(o.path=e.path,t.query!==void 0?o.query=t.query:o.query=e.query),o.userinfo=e.userinfo,o.host=e.host,o.port=e.port),o.scheme=e.scheme),o.fragment=t.fragment,o}function wO(e,t,r){return typeof e=="string"?(e=unescape(e),e=At(Za(Gt(e,r),!0),{...r,skipEscape:!0})):typeof e=="object"&&(e=At(Za(e,!0),{...r,skipEscape:!0})),typeof t=="string"?(t=unescape(t),t=At(Za(Gt(t,r),!0),{...r,skipEscape:!0})):typeof t=="object"&&(t=At(Za(t,!0),{...r,skipEscape:!0})),e.toLowerCase()===t.toLowerCase()}function At(e,t){let r={host:e.host,scheme:e.scheme,userinfo:e.userinfo,port:e.port,path:e.path,query:e.query,nid:e.nid,nss:e.nss,uuid:e.uuid,fragment:e.fragment,reference:e.reference,resourceName:e.resourceName,secure:e.secure,error:""},n=Object.assign({},t),o=[],i=id[(n.scheme||r.scheme||"").toLowerCase()];i&&i.serialize&&i.serialize(r,n),r.path!==void 0&&(n.skipEscape?r.path=unescape(r.path):(r.path=escape(r.path),r.scheme!==void 0&&(r.path=r.path.split("%3A").join(":")))),n.reference!=="suffix"&&r.scheme&&o.push(r.scheme,":");let a=DO(r,n);if(a!==void 0&&(n.reference!=="suffix"&&o.push("//"),o.push(a),r.path&&r.path.charAt(0)!=="/"&&o.push("/")),r.path!==void 0){let l=r.path;!n.absolutePath&&(!i||!i.absolutePath)&&(l=si(l)),a===void 0&&(l=l.replace(/^\/\//u,"/%2F")),o.push(l)}return r.query!==void 0&&o.push("?",r.query),r.fragment!==void 0&&o.push("#",r.fragment),o.join("")}var SO=Array.from({length:127},(e,t)=>/[^!"$&'()*+,\-.;=_`a-z{}~]/u.test(String.fromCharCode(t)));function EO(e){let t=0;for(let r=0,n=e.length;r<n;++r)if(t=e.charCodeAt(r),t>126||SO[t])return!0;return!1}var vO=/^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;function Gt(e,t){let r=Object.assign({},t),n={scheme:void 0,userinfo:void 0,host:"",port:void 0,path:"",query:void 0,fragment:void 0},o=e.indexOf("%")!==-1,i=!1;r.reference==="suffix"&&(e=(r.scheme?r.scheme+":":"")+"//"+e);let a=e.match(vO);if(a){if(n.scheme=a[1],n.userinfo=a[3],n.host=a[4],n.port=parseInt(a[5],10),n.path=a[6]||"",n.query=a[7],n.fragment=a[8],isNaN(n.port)&&(n.port=a[5]),n.host){let c=yO(n.host);if(c.isIPV4===!1){let d=gO(c.host,{isIPV4:!1});n.host=d.host.toLowerCase(),i=d.isIPV6}else n.host=c.host,i=!0}n.scheme===void 0&&n.userinfo===void 0&&n.host===void 0&&n.port===void 0&&!n.path&&n.query===void 0?n.reference="same-document":n.scheme===void 0?n.reference="relative":n.fragment===void 0?n.reference="absolute":n.reference="uri",r.reference&&r.reference!=="suffix"&&r.reference!==n.reference&&(n.error=n.error||"URI is not a "+r.reference+" reference.");let l=id[(r.scheme||n.scheme||"").toLowerCase()];if(!r.unicodeSupport&&(!l||!l.unicodeSupport)&&n.host&&(r.domainHost||l&&l.domainHost)&&i===!1&&EO(n.host))try{n.host=URL.domainToASCII(n.host.toLowerCase())}catch(c){n.error=n.error||"Host's domain name can not be converted to ASCII: "+c}(!l||l&&!l.skipNormalize)&&(o&&n.scheme!==void 0&&(n.scheme=unescape(n.scheme)),o&&n.host!==void 0&&(n.host=unescape(n.host)),n.path!==void 0&&n.path.length&&(n.path=escape(unescape(n.path))),n.fragment!==void 0&&n.fragment.length&&(n.fragment=encodeURI(decodeURIComponent(n.fragment)))),l&&l.parse&&l.parse(n,r)}else n.error=n.error||"URI can not be parsed.";return n}var sd={SCHEMES:id,normalize:bO,resolve:_O,resolveComponents:Ew,equal:wO,serialize:At,parse:Gt};eu.exports=sd;eu.exports.default=sd;eu.exports.fastUri=sd});var Cw=C(ad=>{"use strict";Object.defineProperty(ad,"__esModule",{value:!0});var Fw=vw();Fw.code='require("ajv/dist/runtime/uri").default';ad.default=Fw});var Bw=C(ve=>{"use strict";Object.defineProperty(ve,"__esModule",{value:!0});ve.CodeGen=ve.Name=ve.nil=ve.stringify=ve.str=ve._=ve.KeywordCxt=void 0;var FO=oi();Object.defineProperty(ve,"KeywordCxt",{enumerable:!0,get:function(){return FO.KeywordCxt}});var jn=G();Object.defineProperty(ve,"_",{enumerable:!0,get:function(){return jn._}});Object.defineProperty(ve,"str",{enumerable:!0,get:function(){return jn.str}});Object.defineProperty(ve,"stringify",{enumerable:!0,get:function(){return jn.stringify}});Object.defineProperty(ve,"nil",{enumerable:!0,get:function(){return jn.nil}});Object.defineProperty(ve,"Name",{enumerable:!0,get:function(){return jn.Name}});Object.defineProperty(ve,"CodeGen",{enumerable:!0,get:function(){return jn.CodeGen}});var CO=Ka(),xw=ii(),TO=Mf(),ai=Qa(),RO=G(),ui=ti(),tu=ei(),ld=Z(),Tw=sw(),AO=Cw(),Pw=(e,t)=>new RegExp(e,t);Pw.code="new RegExp";var $O=["removeAdditional","useDefaults","coerceTypes"],xO=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),PO={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},OO={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'},Rw=200;function BO(e){var t,r,n,o,i,a,l,c,d,p,m,b,D,g,y,S,w,T,R,A,v,B,W,ae,P;let x=e.strict,k=(t=e.code)===null||t===void 0?void 0:t.optimize,oe=k===!0||k===void 0?1:k||0,re=(n=(r=e.code)===null||r===void 0?void 0:r.regExp)!==null&&n!==void 0?n:Pw,De=(o=e.uriResolver)!==null&&o!==void 0?o:AO.default;return{strictSchema:(a=(i=e.strictSchema)!==null&&i!==void 0?i:x)!==null&&a!==void 0?a:!0,strictNumbers:(c=(l=e.strictNumbers)!==null&&l!==void 0?l:x)!==null&&c!==void 0?c:!0,strictTypes:(p=(d=e.strictTypes)!==null&&d!==void 0?d:x)!==null&&p!==void 0?p:"log",strictTuples:(b=(m=e.strictTuples)!==null&&m!==void 0?m:x)!==null&&b!==void 0?b:"log",strictRequired:(g=(D=e.strictRequired)!==null&&D!==void 0?D:x)!==null&&g!==void 0?g:!1,code:e.code?{...e.code,optimize:oe,regExp:re}:{optimize:oe,regExp:re},loopRequired:(y=e.loopRequired)!==null&&y!==void 0?y:Rw,loopEnum:(S=e.loopEnum)!==null&&S!==void 0?S:Rw,meta:(w=e.meta)!==null&&w!==void 0?w:!0,messages:(T=e.messages)!==null&&T!==void 0?T:!0,inlineRefs:(R=e.inlineRefs)!==null&&R!==void 0?R:!0,schemaId:(A=e.schemaId)!==null&&A!==void 0?A:"$id",addUsedSchema:(v=e.addUsedSchema)!==null&&v!==void 0?v:!0,validateSchema:(B=e.validateSchema)!==null&&B!==void 0?B:!0,validateFormats:(W=e.validateFormats)!==null&&W!==void 0?W:!0,unicodeRegExp:(ae=e.unicodeRegExp)!==null&&ae!==void 0?ae:!0,int32range:(P=e.int32range)!==null&&P!==void 0?P:!0,uriResolver:De}}var li=class{constructor(t={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,t=this.opts={...t,...BO(t)};let{es5:r,lines:n}=this.opts.code;this.scope=new RO.ValueScope({scope:{},prefixes:xO,es5:r,lines:n}),this.logger=LO(t.logger);let o=t.validateFormats;t.validateFormats=!1,this.RULES=(0,TO.getRules)(),Aw.call(this,PO,t,"NOT SUPPORTED"),Aw.call(this,OO,t,"DEPRECATED","warn"),this._metaOpts=NO.call(this),t.formats&&kO.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),t.keywords&&MO.call(this,t.keywords),typeof t.meta=="object"&&this.addMetaSchema(t.meta),IO.call(this),t.validateFormats=o}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){let{$data:t,meta:r,schemaId:n}=this.opts,o=Tw;n==="id"&&(o={...Tw},o.id=o.$id,delete o.$id),r&&t&&this.addMetaSchema(o,o[n],!1)}defaultMeta(){let{meta:t,schemaId:r}=this.opts;return this.opts.defaultMeta=typeof t=="object"?t[r]||t:void 0}validate(t,r){let n;if(typeof t=="string"){if(n=this.getSchema(t),!n)throw new Error(`no schema with key or ref "${t}"`)}else n=this.compile(t);let o=n(r);return"$async"in n||(this.errors=n.errors),o}compile(t,r){let n=this._addSchema(t,r);return n.validate||this._compileSchemaEnv(n)}compileAsync(t,r){if(typeof this.opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");let{loadSchema:n}=this.opts;return o.call(this,t,r);async function o(p,m){await i.call(this,p.$schema);let b=this._addSchema(p,m);return b.validate||a.call(this,b)}async function i(p){p&&!this.getSchema(p)&&await o.call(this,{$ref:p},!0)}async function a(p){try{return this._compileSchemaEnv(p)}catch(m){if(!(m instanceof xw.default))throw m;return l.call(this,m),await c.call(this,m.missingSchema),a.call(this,p)}}function l({missingSchema:p,missingRef:m}){if(this.refs[p])throw new Error(`AnySchema ${p} is loaded but ${m} cannot be resolved`)}async function c(p){let m=await d.call(this,p);this.refs[p]||await i.call(this,m.$schema),this.refs[p]||this.addSchema(m,p,r)}async function d(p){let m=this._loading[p];if(m)return m;try{return await(this._loading[p]=n(p))}finally{delete this._loading[p]}}}addSchema(t,r,n,o=this.opts.validateSchema){if(Array.isArray(t)){for(let a of t)this.addSchema(a,void 0,n,o);return this}let i;if(typeof t=="object"){let{schemaId:a}=this.opts;if(i=t[a],i!==void 0&&typeof i!="string")throw new Error(`schema ${a} must be string`)}return r=(0,ui.normalizeId)(r||i),this._checkUnique(r),this.schemas[r]=this._addSchema(t,n,r,o,!0),this}addMetaSchema(t,r,n=this.opts.validateSchema){return this.addSchema(t,r,!0,n),this}validateSchema(t,r){if(typeof t=="boolean")return!0;let n;if(n=t.$schema,n!==void 0&&typeof n!="string")throw new Error("$schema must be a string");if(n=n||this.opts.defaultMeta||this.defaultMeta(),!n)return this.logger.warn("meta-schema not available"),this.errors=null,!0;let o=this.validate(n,t);if(!o&&r){let i="schema is invalid: "+this.errorsText();if(this.opts.validateSchema==="log")this.logger.error(i);else throw new Error(i)}return o}getSchema(t){let r;for(;typeof(r=$w.call(this,t))=="string";)t=r;if(r===void 0){let{schemaId:n}=this.opts,o=new ai.SchemaEnv({schema:{},schemaId:n});if(r=ai.resolveSchema.call(this,o,t),!r)return;this.refs[t]=r}return r.validate||this._compileSchemaEnv(r)}removeSchema(t){if(t instanceof RegExp)return this._removeAllSchemas(this.schemas,t),this._removeAllSchemas(this.refs,t),this;switch(typeof t){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{let r=$w.call(this,t);return typeof r=="object"&&this._cache.delete(r.schema),delete this.schemas[t],delete this.refs[t],this}case"object":{let r=t;this._cache.delete(r);let n=t[this.opts.schemaId];return n&&(n=(0,ui.normalizeId)(n),delete this.schemas[n],delete this.refs[n]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(t){for(let r of t)this.addKeyword(r);return this}addKeyword(t,r){let n;if(typeof t=="string")n=t,typeof r=="object"&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),r.keyword=n);else if(typeof t=="object"&&r===void 0){if(r=t,n=r.keyword,Array.isArray(n)&&!n.length)throw new Error("addKeywords: keyword must be string or non-empty array")}else throw new Error("invalid addKeywords parameters");if(UO.call(this,n,r),!r)return(0,ld.eachItem)(n,i=>ud.call(this,i)),this;WO.call(this,r);let o={...r,type:(0,tu.getJSONTypes)(r.type),schemaType:(0,tu.getJSONTypes)(r.schemaType)};return(0,ld.eachItem)(n,o.type.length===0?i=>ud.call(this,i,o):i=>o.type.forEach(a=>ud.call(this,i,o,a))),this}getKeyword(t){let r=this.RULES.all[t];return typeof r=="object"?r.definition:!!r}removeKeyword(t){let{RULES:r}=this;delete r.keywords[t],delete r.all[t];for(let n of r.rules){let o=n.rules.findIndex(i=>i.keyword===t);o>=0&&n.rules.splice(o,1)}return this}addFormat(t,r){return typeof r=="string"&&(r=new RegExp(r)),this.formats[t]=r,this}errorsText(t=this.errors,{separator:r=", ",dataVar:n="data"}={}){return!t||t.length===0?"No errors":t.map(o=>`${n}${o.instancePath} ${o.message}`).reduce((o,i)=>o+r+i)}$dataMetaSchema(t,r){let n=this.RULES.all;t=JSON.parse(JSON.stringify(t));for(let o of r){let i=o.split("/").slice(1),a=t;for(let l of i)a=a[l];for(let l in n){let c=n[l];if(typeof c!="object")continue;let{$data:d}=c.definition,p=a[l];d&&p&&(a[l]=Ow(p))}}return t}_removeAllSchemas(t,r){for(let n in t){let o=t[n];(!r||r.test(n))&&(typeof o=="string"?delete t[n]:o&&!o.meta&&(this._cache.delete(o.schema),delete t[n]))}}_addSchema(t,r,n,o=this.opts.validateSchema,i=this.opts.addUsedSchema){let a,{schemaId:l}=this.opts;if(typeof t=="object")a=t[l];else{if(this.opts.jtd)throw new Error("schema must be object");if(typeof t!="boolean")throw new Error("schema must be object or boolean")}let c=this._cache.get(t);if(c!==void 0)return c;n=(0,ui.normalizeId)(a||n);let d=ui.getSchemaRefs.call(this,t,n);return c=new ai.SchemaEnv({schema:t,schemaId:l,meta:r,baseId:n,localRefs:d}),this._cache.set(c.schema,c),i&&!n.startsWith("#")&&(n&&this._checkUnique(n),this.refs[n]=c),o&&this.validateSchema(t,!0),c}_checkUnique(t){if(this.schemas[t]||this.refs[t])throw new Error(`schema with key or id "${t}" already exists`)}_compileSchemaEnv(t){if(t.meta?this._compileMetaSchema(t):ai.compileSchema.call(this,t),!t.validate)throw new Error("ajv implementation error");return t.validate}_compileMetaSchema(t){let r=this.opts;this.opts=this._metaOpts;try{ai.compileSchema.call(this,t)}finally{this.opts=r}}};li.ValidationError=CO.default;li.MissingRefError=xw.default;ve.default=li;function Aw(e,t,r,n="error"){for(let o in e){let i=o;i in t&&this.logger[n](`${r}: option ${o}. ${e[i]}`)}}function $w(e){return e=(0,ui.normalizeId)(e),this.schemas[e]||this.refs[e]}function IO(){let e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(let t in e)this.addSchema(e[t],t)}function kO(){for(let e in this.opts.formats){let t=this.opts.formats[e];t&&this.addFormat(e,t)}}function MO(e){if(Array.isArray(e)){this.addVocabulary(e);return}this.logger.warn("keywords option as map is deprecated, pass array");for(let t in e){let r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}function NO(){let e={...this.opts};for(let t of $O)delete e[t];return e}var jO={log(){},warn(){},error(){}};function LO(e){if(e===!1)return jO;if(e===void 0)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}var qO=/^[a-z_$][a-z0-9_$:-]*$/i;function UO(e,t){let{RULES:r}=this;if((0,ld.eachItem)(e,n=>{if(r.keywords[n])throw new Error(`Keyword ${n} is already defined`);if(!qO.test(n))throw new Error(`Keyword ${n} has invalid name`)}),!!t&&t.$data&&!("code"in t||"validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function ud(e,t,r){var n;let o=t?.post;if(r&&o)throw new Error('keyword with "post" flag cannot have "type"');let{RULES:i}=this,a=o?i.post:i.rules.find(({type:c})=>c===r);if(a||(a={type:r,rules:[]},i.rules.push(a)),i.keywords[e]=!0,!t)return;let l={keyword:e,definition:{...t,type:(0,tu.getJSONTypes)(t.type),schemaType:(0,tu.getJSONTypes)(t.schemaType)}};t.before?zO.call(this,a,l,t.before):a.rules.push(l),i.all[e]=l,(n=t.implements)===null||n===void 0||n.forEach(c=>this.addKeyword(c))}function zO(e,t,r){let n=e.rules.findIndex(o=>o.keyword===r);n>=0?e.rules.splice(n,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function WO(e){let{metaSchema:t}=e;t!==void 0&&(e.$data&&this.opts.$data&&(t=Ow(t)),e.validateSchema=this.compile(t,!0))}var VO={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function Ow(e){return{anyOf:[e,VO]}}});var Iw=C(cd=>{"use strict";Object.defineProperty(cd,"__esModule",{value:!0});var GO={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};cd.default=GO});var jw=C(qr=>{"use strict";Object.defineProperty(qr,"__esModule",{value:!0});qr.callRef=qr.getValidate=void 0;var HO=ii(),kw=Ge(),Me=G(),Ln=Wt(),Mw=Qa(),ru=Z(),YO={keyword:"$ref",schemaType:"string",code(e){let{gen:t,schema:r,it:n}=e,{baseId:o,schemaEnv:i,validateName:a,opts:l,self:c}=n,{root:d}=i;if((r==="#"||r==="#/")&&o===d.baseId)return m();let p=Mw.resolveRef.call(c,d,o,r);if(p===void 0)throw new HO.default(n.opts.uriResolver,o,r);if(p instanceof Mw.SchemaEnv)return b(p);return D(p);function m(){if(i===d)return nu(e,a,i,i.$async);let g=t.scopeValue("root",{ref:d});return nu(e,(0,Me._)`${g}.validate`,d,d.$async)}function b(g){let y=Nw(e,g);nu(e,y,g,g.$async)}function D(g){let y=t.scopeValue("schema",l.code.source===!0?{ref:g,code:(0,Me.stringify)(g)}:{ref:g}),S=t.name("valid"),w=e.subschema({schema:g,dataTypes:[],schemaPath:Me.nil,topSchemaRef:y,errSchemaPath:r},S);e.mergeEvaluated(w),e.ok(S)}}};function Nw(e,t){let{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):(0,Me._)`${r.scopeValue("wrapper",{ref:t})}.validate`}qr.getValidate=Nw;function nu(e,t,r,n){let{gen:o,it:i}=e,{allErrors:a,schemaEnv:l,opts:c}=i,d=c.passContext?Ln.default.this:Me.nil;n?p():m();function p(){if(!l.$async)throw new Error("async schema referenced by sync schema");let g=o.let("valid");o.try(()=>{o.code((0,Me._)`await ${(0,kw.callValidateCode)(e,t,d)}`),D(t),a||o.assign(g,!0)},y=>{o.if((0,Me._)`!(${y} instanceof ${i.ValidationError})`,()=>o.throw(y)),b(y),a||o.assign(g,!1)}),e.ok(g)}function m(){e.result((0,kw.callValidateCode)(e,t,d),()=>D(t),()=>b(t))}function b(g){let y=(0,Me._)`${g}.errors`;o.assign(Ln.default.vErrors,(0,Me._)`${Ln.default.vErrors} === null ? ${y} : ${Ln.default.vErrors}.concat(${y})`),o.assign(Ln.default.errors,(0,Me._)`${Ln.default.vErrors}.length`)}function D(g){var y;if(!i.opts.unevaluated)return;let S=(y=r?.validate)===null||y===void 0?void 0:y.evaluated;if(i.props!==!0)if(S&&!S.dynamicProps)S.props!==void 0&&(i.props=ru.mergeEvaluated.props(o,S.props,i.props));else{let w=o.var("props",(0,Me._)`${g}.evaluated.props`);i.props=ru.mergeEvaluated.props(o,w,i.props,Me.Name)}if(i.items!==!0)if(S&&!S.dynamicItems)S.items!==void 0&&(i.items=ru.mergeEvaluated.items(o,S.items,i.items));else{let w=o.var("items",(0,Me._)`${g}.evaluated.items`);i.items=ru.mergeEvaluated.items(o,w,i.items,Me.Name)}}}qr.callRef=nu;qr.default=YO});var Lw=C(fd=>{"use strict";Object.defineProperty(fd,"__esModule",{value:!0});var KO=Iw(),JO=jw(),QO=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",KO.default,JO.default];fd.default=QO});var qw=C(dd=>{"use strict";Object.defineProperty(dd,"__esModule",{value:!0});var ou=G(),br=ou.operators,iu={maximum:{okStr:"<=",ok:br.LTE,fail:br.GT},minimum:{okStr:">=",ok:br.GTE,fail:br.LT},exclusiveMaximum:{okStr:"<",ok:br.LT,fail:br.GTE},exclusiveMinimum:{okStr:">",ok:br.GT,fail:br.LTE}},XO={message:({keyword:e,schemaCode:t})=>(0,ou.str)`must be ${iu[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>(0,ou._)`{comparison: ${iu[e].okStr}, limit: ${t}}`},ZO={keyword:Object.keys(iu),type:"number",schemaType:"number",$data:!0,error:XO,code(e){let{keyword:t,data:r,schemaCode:n}=e;e.fail$data((0,ou._)`${r} ${iu[t].fail} ${n} || isNaN(${r})`)}};dd.default=ZO});var Uw=C(pd=>{"use strict";Object.defineProperty(pd,"__esModule",{value:!0});var ci=G(),eB={message:({schemaCode:e})=>(0,ci.str)`must be multiple of ${e}`,params:({schemaCode:e})=>(0,ci._)`{multipleOf: ${e}}`},tB={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:eB,code(e){let{gen:t,data:r,schemaCode:n,it:o}=e,i=o.opts.multipleOfPrecision,a=t.let("res"),l=i?(0,ci._)`Math.abs(Math.round(${a}) - ${a}) > 1e-${i}`:(0,ci._)`${a} !== parseInt(${a})`;e.fail$data((0,ci._)`(${n} === 0 || (${a} = ${r}/${n}, ${l}))`)}};pd.default=tB});var Ww=C(md=>{"use strict";Object.defineProperty(md,"__esModule",{value:!0});function zw(e){let t=e.length,r=0,n=0,o;for(;n<t;)r++,o=e.charCodeAt(n++),o>=55296&&o<=56319&&n<t&&(o=e.charCodeAt(n),(o&64512)===56320&&n++);return r}md.default=zw;zw.code='require("ajv/dist/runtime/ucs2length").default'});var Vw=C(hd=>{"use strict";Object.defineProperty(hd,"__esModule",{value:!0});var Ur=G(),rB=Z(),nB=Ww(),oB={message({keyword:e,schemaCode:t}){let r=e==="maxLength"?"more":"fewer";return(0,Ur.str)`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>(0,Ur._)`{limit: ${e}}`},iB={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:oB,code(e){let{keyword:t,data:r,schemaCode:n,it:o}=e,i=t==="maxLength"?Ur.operators.GT:Ur.operators.LT,a=o.opts.unicode===!1?(0,Ur._)`${r}.length`:(0,Ur._)`${(0,rB.useFunc)(e.gen,nB.default)}(${r})`;e.fail$data((0,Ur._)`${a} ${i} ${n}`)}};hd.default=iB});var Gw=C(gd=>{"use strict";Object.defineProperty(gd,"__esModule",{value:!0});var sB=Ge(),su=G(),aB={message:({schemaCode:e})=>(0,su.str)`must match pattern "${e}"`,params:({schemaCode:e})=>(0,su._)`{pattern: ${e}}`},uB={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:aB,code(e){let{data:t,$data:r,schema:n,schemaCode:o,it:i}=e,a=i.opts.unicodeRegExp?"u":"",l=r?(0,su._)`(new RegExp(${o}, ${a}))`:(0,sB.usePattern)(e,n);e.fail$data((0,su._)`!${l}.test(${t})`)}};gd.default=uB});var Hw=C(yd=>{"use strict";Object.defineProperty(yd,"__esModule",{value:!0});var fi=G(),lB={message({keyword:e,schemaCode:t}){let r=e==="maxProperties"?"more":"fewer";return(0,fi.str)`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>(0,fi._)`{limit: ${e}}`},cB={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:lB,code(e){let{keyword:t,data:r,schemaCode:n}=e,o=t==="maxProperties"?fi.operators.GT:fi.operators.LT;e.fail$data((0,fi._)`Object.keys(${r}).length ${o} ${n}`)}};yd.default=cB});var Yw=C(Dd=>{"use strict";Object.defineProperty(Dd,"__esModule",{value:!0});var di=Ge(),pi=G(),fB=Z(),dB={message:({params:{missingProperty:e}})=>(0,pi.str)`must have required property '${e}'`,params:({params:{missingProperty:e}})=>(0,pi._)`{missingProperty: ${e}}`},pB={keyword:"required",type:"object",schemaType:"array",$data:!0,error:dB,code(e){let{gen:t,schema:r,schemaCode:n,data:o,$data:i,it:a}=e,{opts:l}=a;if(!i&&r.length===0)return;let c=r.length>=l.loopRequired;if(a.allErrors?d():p(),l.strictRequired){let D=e.parentSchema.properties,{definedProperties:g}=e.it;for(let y of r)if(D?.[y]===void 0&&!g.has(y)){let S=a.schemaEnv.baseId+a.errSchemaPath,w=`required property "${y}" is not defined at "${S}" (strictRequired)`;(0,fB.checkStrictMode)(a,w,a.opts.strictRequired)}}function d(){if(c||i)e.block$data(pi.nil,m);else for(let D of r)(0,di.checkReportMissingProp)(e,D)}function p(){let D=t.let("missing");if(c||i){let g=t.let("valid",!0);e.block$data(g,()=>b(D,g)),e.ok(g)}else t.if((0,di.checkMissingProp)(e,r,D)),(0,di.reportMissingProp)(e,D),t.else()}function m(){t.forOf("prop",n,D=>{e.setParams({missingProperty:D}),t.if((0,di.noPropertyInData)(t,o,D,l.ownProperties),()=>e.error())})}function b(D,g){e.setParams({missingProperty:D}),t.forOf(D,n,()=>{t.assign(g,(0,di.propertyInData)(t,o,D,l.ownProperties)),t.if((0,pi.not)(g),()=>{e.error(),t.break()})},pi.nil)}}};Dd.default=pB});var Kw=C(bd=>{"use strict";Object.defineProperty(bd,"__esModule",{value:!0});var mi=G(),mB={message({keyword:e,schemaCode:t}){let r=e==="maxItems"?"more":"fewer";return(0,mi.str)`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>(0,mi._)`{limit: ${e}}`},hB={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:mB,code(e){let{keyword:t,data:r,schemaCode:n}=e,o=t==="maxItems"?mi.operators.GT:mi.operators.LT;e.fail$data((0,mi._)`${r}.length ${o} ${n}`)}};bd.default=hB});var au=C(_d=>{"use strict";Object.defineProperty(_d,"__esModule",{value:!0});var Jw=Vf();Jw.code='require("ajv/dist/runtime/equal").default';_d.default=Jw});var Qw=C(Sd=>{"use strict";Object.defineProperty(Sd,"__esModule",{value:!0});var wd=ei(),Fe=G(),gB=Z(),yB=au(),DB={message:({params:{i:e,j:t}})=>(0,Fe.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>(0,Fe._)`{i: ${e}, j: ${t}}`},bB={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:DB,code(e){let{gen:t,data:r,$data:n,schema:o,parentSchema:i,schemaCode:a,it:l}=e;if(!n&&!o)return;let c=t.let("valid"),d=i.items?(0,wd.getSchemaTypes)(i.items):[];e.block$data(c,p,(0,Fe._)`${a} === false`),e.ok(c);function p(){let g=t.let("i",(0,Fe._)`${r}.length`),y=t.let("j");e.setParams({i:g,j:y}),t.assign(c,!0),t.if((0,Fe._)`${g} > 1`,()=>(m()?b:D)(g,y))}function m(){return d.length>0&&!d.some(g=>g==="object"||g==="array")}function b(g,y){let S=t.name("item"),w=(0,wd.checkDataTypes)(d,S,l.opts.strictNumbers,wd.DataType.Wrong),T=t.const("indices",(0,Fe._)`{}`);t.for((0,Fe._)`;${g}--;`,()=>{t.let(S,(0,Fe._)`${r}[${g}]`),t.if(w,(0,Fe._)`continue`),d.length>1&&t.if((0,Fe._)`typeof ${S} == "string"`,(0,Fe._)`${S} += "_"`),t.if((0,Fe._)`typeof ${T}[${S}] == "number"`,()=>{t.assign(y,(0,Fe._)`${T}[${S}]`),e.error(),t.assign(c,!1).break()}).code((0,Fe._)`${T}[${S}] = ${g}`)})}function D(g,y){let S=(0,gB.useFunc)(t,yB.default),w=t.name("outer");t.label(w).for((0,Fe._)`;${g}--;`,()=>t.for((0,Fe._)`${y} = ${g}; ${y}--;`,()=>t.if((0,Fe._)`${S}(${r}[${g}], ${r}[${y}])`,()=>{e.error(),t.assign(c,!1).break(w)})))}}};Sd.default=bB});var Xw=C(vd=>{"use strict";Object.defineProperty(vd,"__esModule",{value:!0});var Ed=G(),_B=Z(),wB=au(),SB={message:"must be equal to constant",params:({schemaCode:e})=>(0,Ed._)`{allowedValue: ${e}}`},EB={keyword:"const",$data:!0,error:SB,code(e){let{gen:t,data:r,$data:n,schemaCode:o,schema:i}=e;n||i&&typeof i=="object"?e.fail$data((0,Ed._)`!${(0,_B.useFunc)(t,wB.default)}(${r}, ${o})`):e.fail((0,Ed._)`${i} !== ${r}`)}};vd.default=EB});var Zw=C(Fd=>{"use strict";Object.defineProperty(Fd,"__esModule",{value:!0});var hi=G(),vB=Z(),FB=au(),CB={message:"must be equal to one of the allowed values",params:({schemaCode:e})=>(0,hi._)`{allowedValues: ${e}}`},TB={keyword:"enum",schemaType:"array",$data:!0,error:CB,code(e){let{gen:t,data:r,$data:n,schema:o,schemaCode:i,it:a}=e;if(!n&&o.length===0)throw new Error("enum must have non-empty array");let l=o.length>=a.opts.loopEnum,c,d=()=>c??(c=(0,vB.useFunc)(t,FB.default)),p;if(l||n)p=t.let("valid"),e.block$data(p,m);else{if(!Array.isArray(o))throw new Error("ajv implementation error");let D=t.const("vSchema",i);p=(0,hi.or)(...o.map((g,y)=>b(D,y)))}e.pass(p);function m(){t.assign(p,!1),t.forOf("v",i,D=>t.if((0,hi._)`${d()}(${r}, ${D})`,()=>t.assign(p,!0).break()))}function b(D,g){let y=o[g];return typeof y=="object"&&y!==null?(0,hi._)`${d()}(${r}, ${D}[${g}])`:(0,hi._)`${r} === ${y}`}}};Fd.default=TB});var eS=C(Cd=>{"use strict";Object.defineProperty(Cd,"__esModule",{value:!0});var RB=qw(),AB=Uw(),$B=Vw(),xB=Gw(),PB=Hw(),OB=Yw(),BB=Kw(),IB=Qw(),kB=Xw(),MB=Zw(),NB=[RB.default,AB.default,$B.default,xB.default,PB.default,OB.default,BB.default,IB.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},kB.default,MB.default];Cd.default=NB});var Rd=C(gi=>{"use strict";Object.defineProperty(gi,"__esModule",{value:!0});gi.validateAdditionalItems=void 0;var zr=G(),Td=Z(),jB={message:({params:{len:e}})=>(0,zr.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,zr._)`{limit: ${e}}`},LB={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:jB,code(e){let{parentSchema:t,it:r}=e,{items:n}=t;if(!Array.isArray(n)){(0,Td.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas');return}tS(e,n)}};function tS(e,t){let{gen:r,schema:n,data:o,keyword:i,it:a}=e;a.items=!0;let l=r.const("len",(0,zr._)`${o}.length`);if(n===!1)e.setParams({len:t.length}),e.pass((0,zr._)`${l} <= ${t.length}`);else if(typeof n=="object"&&!(0,Td.alwaysValidSchema)(a,n)){let d=r.var("valid",(0,zr._)`${l} <= ${t.length}`);r.if((0,zr.not)(d),()=>c(d)),e.ok(d)}function c(d){r.forRange("i",t.length,l,p=>{e.subschema({keyword:i,dataProp:p,dataPropType:Td.Type.Num},d),a.allErrors||r.if((0,zr.not)(d),()=>r.break())})}}gi.validateAdditionalItems=tS;gi.default=LB});var Ad=C(yi=>{"use strict";Object.defineProperty(yi,"__esModule",{value:!0});yi.validateTuple=void 0;var rS=G(),uu=Z(),qB=Ge(),UB={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){let{schema:t,it:r}=e;if(Array.isArray(t))return nS(e,"additionalItems",t);r.items=!0,!(0,uu.alwaysValidSchema)(r,t)&&e.ok((0,qB.validateArray)(e))}};function nS(e,t,r=e.schema){let{gen:n,parentSchema:o,data:i,keyword:a,it:l}=e;p(o),l.opts.unevaluated&&r.length&&l.items!==!0&&(l.items=uu.mergeEvaluated.items(n,r.length,l.items));let c=n.name("valid"),d=n.const("len",(0,rS._)`${i}.length`);r.forEach((m,b)=>{(0,uu.alwaysValidSchema)(l,m)||(n.if((0,rS._)`${d} > ${b}`,()=>e.subschema({keyword:a,schemaProp:b,dataProp:b},c)),e.ok(c))});function p(m){let{opts:b,errSchemaPath:D}=l,g=r.length,y=g===m.minItems&&(g===m.maxItems||m[t]===!1);if(b.strictTuples&&!y){let S=`"${a}" is ${g}-tuple, but minItems or maxItems/${t} are not specified or different at path "${D}"`;(0,uu.checkStrictMode)(l,S,b.strictTuples)}}}yi.validateTuple=nS;yi.default=UB});var oS=C($d=>{"use strict";Object.defineProperty($d,"__esModule",{value:!0});var zB=Ad(),WB={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,zB.validateTuple)(e,"items")};$d.default=WB});var sS=C(xd=>{"use strict";Object.defineProperty(xd,"__esModule",{value:!0});var iS=G(),VB=Z(),GB=Ge(),HB=Rd(),YB={message:({params:{len:e}})=>(0,iS.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,iS._)`{limit: ${e}}`},KB={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:YB,code(e){let{schema:t,parentSchema:r,it:n}=e,{prefixItems:o}=r;n.items=!0,!(0,VB.alwaysValidSchema)(n,t)&&(o?(0,HB.validateAdditionalItems)(e,o):e.ok((0,GB.validateArray)(e)))}};xd.default=KB});var aS=C(Pd=>{"use strict";Object.defineProperty(Pd,"__esModule",{value:!0});var Ye=G(),lu=Z(),JB={message:({params:{min:e,max:t}})=>t===void 0?(0,Ye.str)`must contain at least ${e} valid item(s)`:(0,Ye.str)`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>t===void 0?(0,Ye._)`{minContains: ${e}}`:(0,Ye._)`{minContains: ${e}, maxContains: ${t}}`},QB={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:JB,code(e){let{gen:t,schema:r,parentSchema:n,data:o,it:i}=e,a,l,{minContains:c,maxContains:d}=n;i.opts.next?(a=c===void 0?1:c,l=d):a=1;let p=t.const("len",(0,Ye._)`${o}.length`);if(e.setParams({min:a,max:l}),l===void 0&&a===0){(0,lu.checkStrictMode)(i,'"minContains" == 0 without "maxContains": "contains" keyword ignored');return}if(l!==void 0&&a>l){(0,lu.checkStrictMode)(i,'"minContains" > "maxContains" is always invalid'),e.fail();return}if((0,lu.alwaysValidSchema)(i,r)){let y=(0,Ye._)`${p} >= ${a}`;l!==void 0&&(y=(0,Ye._)`${y} && ${p} <= ${l}`),e.pass(y);return}i.items=!0;let m=t.name("valid");l===void 0&&a===1?D(m,()=>t.if(m,()=>t.break())):a===0?(t.let(m,!0),l!==void 0&&t.if((0,Ye._)`${o}.length > 0`,b)):(t.let(m,!1),b()),e.result(m,()=>e.reset());function b(){let y=t.name("_valid"),S=t.let("count",0);D(y,()=>t.if(y,()=>g(S)))}function D(y,S){t.forRange("i",0,p,w=>{e.subschema({keyword:"contains",dataProp:w,dataPropType:lu.Type.Num,compositeRule:!0},y),S()})}function g(y){t.code((0,Ye._)`${y}++`),l===void 0?t.if((0,Ye._)`${y} >= ${a}`,()=>t.assign(m,!0).break()):(t.if((0,Ye._)`${y} > ${l}`,()=>t.assign(m,!1).break()),a===1?t.assign(m,!0):t.if((0,Ye._)`${y} >= ${a}`,()=>t.assign(m,!0)))}}};Pd.default=QB});var cS=C($t=>{"use strict";Object.defineProperty($t,"__esModule",{value:!0});$t.validateSchemaDeps=$t.validatePropertyDeps=$t.error=void 0;var Od=G(),XB=Z(),Di=Ge();$t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{let n=t===1?"property":"properties";return(0,Od.str)`must have ${n} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:n}})=>(0,Od._)`{property: ${e},
    missingProperty: ${n},
    depsCount: ${t},
    deps: ${r}}`};var ZB={keyword:"dependencies",type:"object",schemaType:"object",error:$t.error,code(e){let[t,r]=eI(e);uS(e,t),lS(e,r)}};function eI({schema:e}){let t={},r={};for(let n in e){if(n==="__proto__")continue;let o=Array.isArray(e[n])?t:r;o[n]=e[n]}return[t,r]}function uS(e,t=e.schema){let{gen:r,data:n,it:o}=e;if(Object.keys(t).length===0)return;let i=r.let("missing");for(let a in t){let l=t[a];if(l.length===0)continue;let c=(0,Di.propertyInData)(r,n,a,o.opts.ownProperties);e.setParams({property:a,depsCount:l.length,deps:l.join(", ")}),o.allErrors?r.if(c,()=>{for(let d of l)(0,Di.checkReportMissingProp)(e,d)}):(r.if((0,Od._)`${c} && (${(0,Di.checkMissingProp)(e,l,i)})`),(0,Di.reportMissingProp)(e,i),r.else())}}$t.validatePropertyDeps=uS;function lS(e,t=e.schema){let{gen:r,data:n,keyword:o,it:i}=e,a=r.name("valid");for(let l in t)(0,XB.alwaysValidSchema)(i,t[l])||(r.if((0,Di.propertyInData)(r,n,l,i.opts.ownProperties),()=>{let c=e.subschema({keyword:o,schemaProp:l},a);e.mergeValidEvaluated(c,a)},()=>r.var(a,!0)),e.ok(a))}$t.validateSchemaDeps=lS;$t.default=ZB});var dS=C(Bd=>{"use strict";Object.defineProperty(Bd,"__esModule",{value:!0});var fS=G(),tI=Z(),rI={message:"property name must be valid",params:({params:e})=>(0,fS._)`{propertyName: ${e.propertyName}}`},nI={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:rI,code(e){let{gen:t,schema:r,data:n,it:o}=e;if((0,tI.alwaysValidSchema)(o,r))return;let i=t.name("valid");t.forIn("key",n,a=>{e.setParams({propertyName:a}),e.subschema({keyword:"propertyNames",data:a,dataTypes:["string"],propertyName:a,compositeRule:!0},i),t.if((0,fS.not)(i),()=>{e.error(!0),o.allErrors||t.break()})}),e.ok(i)}};Bd.default=nI});var kd=C(Id=>{"use strict";Object.defineProperty(Id,"__esModule",{value:!0});var cu=Ge(),pt=G(),oI=Wt(),fu=Z(),iI={message:"must NOT have additional properties",params:({params:e})=>(0,pt._)`{additionalProperty: ${e.additionalProperty}}`},sI={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:iI,code(e){let{gen:t,schema:r,parentSchema:n,data:o,errsCount:i,it:a}=e;if(!i)throw new Error("ajv implementation error");let{allErrors:l,opts:c}=a;if(a.props=!0,c.removeAdditional!=="all"&&(0,fu.alwaysValidSchema)(a,r))return;let d=(0,cu.allSchemaProperties)(n.properties),p=(0,cu.allSchemaProperties)(n.patternProperties);m(),e.ok((0,pt._)`${i} === ${oI.default.errors}`);function m(){t.forIn("key",o,S=>{!d.length&&!p.length?g(S):t.if(b(S),()=>g(S))})}function b(S){let w;if(d.length>8){let T=(0,fu.schemaRefOrVal)(a,n.properties,"properties");w=(0,cu.isOwnProperty)(t,T,S)}else d.length?w=(0,pt.or)(...d.map(T=>(0,pt._)`${S} === ${T}`)):w=pt.nil;return p.length&&(w=(0,pt.or)(w,...p.map(T=>(0,pt._)`${(0,cu.usePattern)(e,T)}.test(${S})`))),(0,pt.not)(w)}function D(S){t.code((0,pt._)`delete ${o}[${S}]`)}function g(S){if(c.removeAdditional==="all"||c.removeAdditional&&r===!1){D(S);return}if(r===!1){e.setParams({additionalProperty:S}),e.error(),l||t.break();return}if(typeof r=="object"&&!(0,fu.alwaysValidSchema)(a,r)){let w=t.name("valid");c.removeAdditional==="failing"?(y(S,w,!1),t.if((0,pt.not)(w),()=>{e.reset(),D(S)})):(y(S,w),l||t.if((0,pt.not)(w),()=>t.break()))}}function y(S,w,T){let R={keyword:"additionalProperties",dataProp:S,dataPropType:fu.Type.Str};T===!1&&Object.assign(R,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(R,w)}}};Id.default=sI});var hS=C(Nd=>{"use strict";Object.defineProperty(Nd,"__esModule",{value:!0});var aI=oi(),pS=Ge(),Md=Z(),mS=kd(),uI={keyword:"properties",type:"object",schemaType:"object",code(e){let{gen:t,schema:r,parentSchema:n,data:o,it:i}=e;i.opts.removeAdditional==="all"&&n.additionalProperties===void 0&&mS.default.code(new aI.KeywordCxt(i,mS.default,"additionalProperties"));let a=(0,pS.allSchemaProperties)(r);for(let m of a)i.definedProperties.add(m);i.opts.unevaluated&&a.length&&i.props!==!0&&(i.props=Md.mergeEvaluated.props(t,(0,Md.toHash)(a),i.props));let l=a.filter(m=>!(0,Md.alwaysValidSchema)(i,r[m]));if(l.length===0)return;let c=t.name("valid");for(let m of l)d(m)?p(m):(t.if((0,pS.propertyInData)(t,o,m,i.opts.ownProperties)),p(m),i.allErrors||t.else().var(c,!0),t.endIf()),e.it.definedProperties.add(m),e.ok(c);function d(m){return i.opts.useDefaults&&!i.compositeRule&&r[m].default!==void 0}function p(m){e.subschema({keyword:"properties",schemaProp:m,dataProp:m},c)}}};Nd.default=uI});var bS=C(jd=>{"use strict";Object.defineProperty(jd,"__esModule",{value:!0});var gS=Ge(),du=G(),yS=Z(),DS=Z(),lI={keyword:"patternProperties",type:"object",schemaType:"object",code(e){let{gen:t,schema:r,data:n,parentSchema:o,it:i}=e,{opts:a}=i,l=(0,gS.allSchemaProperties)(r),c=l.filter(y=>(0,yS.alwaysValidSchema)(i,r[y]));if(l.length===0||c.length===l.length&&(!i.opts.unevaluated||i.props===!0))return;let d=a.strictSchema&&!a.allowMatchingProperties&&o.properties,p=t.name("valid");i.props!==!0&&!(i.props instanceof du.Name)&&(i.props=(0,DS.evaluatedPropsToName)(t,i.props));let{props:m}=i;b();function b(){for(let y of l)d&&D(y),i.allErrors?g(y):(t.var(p,!0),g(y),t.if(p))}function D(y){for(let S in d)new RegExp(y).test(S)&&(0,yS.checkStrictMode)(i,`property ${S} matches pattern ${y} (use allowMatchingProperties)`)}function g(y){t.forIn("key",n,S=>{t.if((0,du._)`${(0,gS.usePattern)(e,y)}.test(${S})`,()=>{let w=c.includes(y);w||e.subschema({keyword:"patternProperties",schemaProp:y,dataProp:S,dataPropType:DS.Type.Str},p),i.opts.unevaluated&&m!==!0?t.assign((0,du._)`${m}[${S}]`,!0):!w&&!i.allErrors&&t.if((0,du.not)(p),()=>t.break())})})}}};jd.default=lI});var _S=C(Ld=>{"use strict";Object.defineProperty(Ld,"__esModule",{value:!0});var cI=Z(),fI={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){let{gen:t,schema:r,it:n}=e;if((0,cI.alwaysValidSchema)(n,r)){e.fail();return}let o=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},o),e.failResult(o,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}};Ld.default=fI});var wS=C(qd=>{"use strict";Object.defineProperty(qd,"__esModule",{value:!0});var dI=Ge(),pI={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:dI.validateUnion,error:{message:"must match a schema in anyOf"}};qd.default=pI});var SS=C(Ud=>{"use strict";Object.defineProperty(Ud,"__esModule",{value:!0});var pu=G(),mI=Z(),hI={message:"must match exactly one schema in oneOf",params:({params:e})=>(0,pu._)`{passingSchemas: ${e.passing}}`},gI={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:hI,code(e){let{gen:t,schema:r,parentSchema:n,it:o}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(o.opts.discriminator&&n.discriminator)return;let i=r,a=t.let("valid",!1),l=t.let("passing",null),c=t.name("_valid");e.setParams({passing:l}),t.block(d),e.result(a,()=>e.reset(),()=>e.error(!0));function d(){i.forEach((p,m)=>{let b;(0,mI.alwaysValidSchema)(o,p)?t.var(c,!0):b=e.subschema({keyword:"oneOf",schemaProp:m,compositeRule:!0},c),m>0&&t.if((0,pu._)`${c} && ${a}`).assign(a,!1).assign(l,(0,pu._)`[${l}, ${m}]`).else(),t.if(c,()=>{t.assign(a,!0),t.assign(l,m),b&&e.mergeEvaluated(b,pu.Name)})})}}};Ud.default=gI});var ES=C(zd=>{"use strict";Object.defineProperty(zd,"__esModule",{value:!0});var yI=Z(),DI={keyword:"allOf",schemaType:"array",code(e){let{gen:t,schema:r,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");let o=t.name("valid");r.forEach((i,a)=>{if((0,yI.alwaysValidSchema)(n,i))return;let l=e.subschema({keyword:"allOf",schemaProp:a},o);e.ok(o),e.mergeEvaluated(l)})}};zd.default=DI});var CS=C(Wd=>{"use strict";Object.defineProperty(Wd,"__esModule",{value:!0});var mu=G(),FS=Z(),bI={message:({params:e})=>(0,mu.str)`must match "${e.ifClause}" schema`,params:({params:e})=>(0,mu._)`{failingKeyword: ${e.ifClause}}`},_I={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:bI,code(e){let{gen:t,parentSchema:r,it:n}=e;r.then===void 0&&r.else===void 0&&(0,FS.checkStrictMode)(n,'"if" without "then" and "else" is ignored');let o=vS(n,"then"),i=vS(n,"else");if(!o&&!i)return;let a=t.let("valid",!0),l=t.name("_valid");if(c(),e.reset(),o&&i){let p=t.let("ifClause");e.setParams({ifClause:p}),t.if(l,d("then",p),d("else",p))}else o?t.if(l,d("then")):t.if((0,mu.not)(l),d("else"));e.pass(a,()=>e.error(!0));function c(){let p=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},l);e.mergeEvaluated(p)}function d(p,m){return()=>{let b=e.subschema({keyword:p},l);t.assign(a,l),e.mergeValidEvaluated(b,a),m?t.assign(m,(0,mu._)`${p}`):e.setParams({ifClause:p})}}}};function vS(e,t){let r=e.schema[t];return r!==void 0&&!(0,FS.alwaysValidSchema)(e,r)}Wd.default=_I});var TS=C(Vd=>{"use strict";Object.defineProperty(Vd,"__esModule",{value:!0});var wI=Z(),SI={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){t.if===void 0&&(0,wI.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};Vd.default=SI});var RS=C(Gd=>{"use strict";Object.defineProperty(Gd,"__esModule",{value:!0});var EI=Rd(),vI=oS(),FI=Ad(),CI=sS(),TI=aS(),RI=cS(),AI=dS(),$I=kd(),xI=hS(),PI=bS(),OI=_S(),BI=wS(),II=SS(),kI=ES(),MI=CS(),NI=TS();function jI(e=!1){let t=[OI.default,BI.default,II.default,kI.default,MI.default,NI.default,AI.default,$I.default,RI.default,xI.default,PI.default];return e?t.push(vI.default,CI.default):t.push(EI.default,FI.default),t.push(TI.default),t}Gd.default=jI});var AS=C(Hd=>{"use strict";Object.defineProperty(Hd,"__esModule",{value:!0});var ye=G(),LI={message:({schemaCode:e})=>(0,ye.str)`must match format "${e}"`,params:({schemaCode:e})=>(0,ye._)`{format: ${e}}`},qI={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:LI,code(e,t){let{gen:r,data:n,$data:o,schema:i,schemaCode:a,it:l}=e,{opts:c,errSchemaPath:d,schemaEnv:p,self:m}=l;if(!c.validateFormats)return;o?b():D();function b(){let g=r.scopeValue("formats",{ref:m.formats,code:c.code.formats}),y=r.const("fDef",(0,ye._)`${g}[${a}]`),S=r.let("fType"),w=r.let("format");r.if((0,ye._)`typeof ${y} == "object" && !(${y} instanceof RegExp)`,()=>r.assign(S,(0,ye._)`${y}.type || "string"`).assign(w,(0,ye._)`${y}.validate`),()=>r.assign(S,(0,ye._)`"string"`).assign(w,y)),e.fail$data((0,ye.or)(T(),R()));function T(){return c.strictSchema===!1?ye.nil:(0,ye._)`${a} && !${w}`}function R(){let A=p.$async?(0,ye._)`(${y}.async ? await ${w}(${n}) : ${w}(${n}))`:(0,ye._)`${w}(${n})`,v=(0,ye._)`(typeof ${w} == "function" ? ${A} : ${w}.test(${n}))`;return(0,ye._)`${w} && ${w} !== true && ${S} === ${t} && !${v}`}}function D(){let g=m.formats[i];if(!g){T();return}if(g===!0)return;let[y,S,w]=R(g);y===t&&e.pass(A());function T(){if(c.strictSchema===!1){m.logger.warn(v());return}throw new Error(v());function v(){return`unknown format "${i}" ignored in schema at path "${d}"`}}function R(v){let B=v instanceof RegExp?(0,ye.regexpCode)(v):c.code.formats?(0,ye._)`${c.code.formats}${(0,ye.getProperty)(i)}`:void 0,W=r.scopeValue("formats",{key:i,ref:v,code:B});return typeof v=="object"&&!(v instanceof RegExp)?[v.type||"string",v.validate,(0,ye._)`${W}.validate`]:["string",v,W]}function A(){if(typeof g=="object"&&!(g instanceof RegExp)&&g.async){if(!p.$async)throw new Error("async format in sync schema");return(0,ye._)`await ${w}(${n})`}return typeof S=="function"?(0,ye._)`${w}(${n})`:(0,ye._)`${w}.test(${n})`}}}};Hd.default=qI});var $S=C(Yd=>{"use strict";Object.defineProperty(Yd,"__esModule",{value:!0});var UI=AS(),zI=[UI.default];Yd.default=zI});var xS=C(qn=>{"use strict";Object.defineProperty(qn,"__esModule",{value:!0});qn.contentVocabulary=qn.metadataVocabulary=void 0;qn.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"];qn.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]});var OS=C(Kd=>{"use strict";Object.defineProperty(Kd,"__esModule",{value:!0});var WI=Lw(),VI=eS(),GI=RS(),HI=$S(),PS=xS(),YI=[WI.default,VI.default,(0,GI.default)(),HI.default,PS.metadataVocabulary,PS.contentVocabulary];Kd.default=YI});var IS=C(hu=>{"use strict";Object.defineProperty(hu,"__esModule",{value:!0});hu.DiscrError=void 0;var BS;(function(e){e.Tag="tag",e.Mapping="mapping"})(BS||(hu.DiscrError=BS={}))});var MS=C(Qd=>{"use strict";Object.defineProperty(Qd,"__esModule",{value:!0});var Un=G(),Jd=IS(),kS=Qa(),KI=ii(),JI=Z(),QI={message:({params:{discrError:e,tagName:t}})=>e===Jd.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>(0,Un._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`},XI={keyword:"discriminator",type:"object",schemaType:"object",error:QI,code(e){let{gen:t,data:r,schema:n,parentSchema:o,it:i}=e,{oneOf:a}=o;if(!i.opts.discriminator)throw new Error("discriminator: requires discriminator option");let l=n.propertyName;if(typeof l!="string")throw new Error("discriminator: requires propertyName");if(n.mapping)throw new Error("discriminator: mapping is not supported");if(!a)throw new Error("discriminator: requires oneOf keyword");let c=t.let("valid",!1),d=t.const("tag",(0,Un._)`${r}${(0,Un.getProperty)(l)}`);t.if((0,Un._)`typeof ${d} == "string"`,()=>p(),()=>e.error(!1,{discrError:Jd.DiscrError.Tag,tag:d,tagName:l})),e.ok(c);function p(){let D=b();t.if(!1);for(let g in D)t.elseIf((0,Un._)`${d} === ${g}`),t.assign(c,m(D[g]));t.else(),e.error(!1,{discrError:Jd.DiscrError.Mapping,tag:d,tagName:l}),t.endIf()}function m(D){let g=t.name("valid"),y=e.subschema({keyword:"oneOf",schemaProp:D},g);return e.mergeEvaluated(y,Un.Name),g}function b(){var D;let g={},y=w(o),S=!0;for(let A=0;A<a.length;A++){let v=a[A];if(v?.$ref&&!(0,JI.schemaHasRulesButRef)(v,i.self.RULES)){let W=v.$ref;if(v=kS.resolveRef.call(i.self,i.schemaEnv.root,i.baseId,W),v instanceof kS.SchemaEnv&&(v=v.schema),v===void 0)throw new KI.default(i.opts.uriResolver,i.baseId,W)}let B=(D=v?.properties)===null||D===void 0?void 0:D[l];if(typeof B!="object")throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${l}"`);S=S&&(y||w(v)),T(B,A)}if(!S)throw new Error(`discriminator: "${l}" must be required`);return g;function w({required:A}){return Array.isArray(A)&&A.includes(l)}function T(A,v){if(A.const)R(A.const,v);else if(A.enum)for(let B of A.enum)R(B,v);else throw new Error(`discriminator: "properties/${l}" must have "const" or "enum"`)}function R(A,v){if(typeof A!="string"||A in g)throw new Error(`discriminator: "${l}" values must be unique strings`);g[A]=v}}}};Qd.default=XI});var NS=C((Fz,ZI)=>{ZI.exports={$schema:"http://json-schema.org/draft-07/schema#",$id:"http://json-schema.org/draft-07/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}});var LS=C((ce,Xd)=>{"use strict";Object.defineProperty(ce,"__esModule",{value:!0});ce.MissingRefError=ce.ValidationError=ce.CodeGen=ce.Name=ce.nil=ce.stringify=ce.str=ce._=ce.KeywordCxt=ce.Ajv=void 0;var ek=Bw(),tk=OS(),rk=MS(),jS=NS(),nk=["/properties"],gu="http://json-schema.org/draft-07/schema",zn=class extends ek.default{_addVocabularies(){super._addVocabularies(),tk.default.forEach(t=>this.addVocabulary(t)),this.opts.discriminator&&this.addKeyword(rk.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;let t=this.opts.$data?this.$dataMetaSchema(jS,nk):jS;this.addMetaSchema(t,gu,!1),this.refs["http://json-schema.org/schema"]=gu}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(gu)?gu:void 0)}};ce.Ajv=zn;Xd.exports=ce=zn;Xd.exports.Ajv=zn;Object.defineProperty(ce,"__esModule",{value:!0});ce.default=zn;var ok=oi();Object.defineProperty(ce,"KeywordCxt",{enumerable:!0,get:function(){return ok.KeywordCxt}});var Wn=G();Object.defineProperty(ce,"_",{enumerable:!0,get:function(){return Wn._}});Object.defineProperty(ce,"str",{enumerable:!0,get:function(){return Wn.str}});Object.defineProperty(ce,"stringify",{enumerable:!0,get:function(){return Wn.stringify}});Object.defineProperty(ce,"nil",{enumerable:!0,get:function(){return Wn.nil}});Object.defineProperty(ce,"Name",{enumerable:!0,get:function(){return Wn.Name}});Object.defineProperty(ce,"CodeGen",{enumerable:!0,get:function(){return Wn.CodeGen}});var ik=Ka();Object.defineProperty(ce,"ValidationError",{enumerable:!0,get:function(){return ik.default}});var sk=ii();Object.defineProperty(ce,"MissingRefError",{enumerable:!0,get:function(){return sk.default}})});var qS=C(yu=>{"use strict";Object.defineProperty(yu,"__esModule",{value:!0});yu.titleCase=void 0;function ak(e){let t={and:!0,but:!0,or:!0,nor:!0,for:!0,yet:!0,so:!0,to:!0,as:!0,at:!0,by:!0,from:!0,in:!0,into:!0,of:!0,off:!0,on:!0,onto:!0,out:!0,over:!0,up:!0,with:!0},r={a:!0,an:!0,the:!0},n={npm:!0,"crates.io":!0,dbt:!0,"pub.dev":!0,kubectx:!0,"monday.com":!0,"ray.so":!0,flomo:!0,iterm:!0,xkcd:!0,macos:!0,iphone:!0,github:!0,ide:!0,url:!0,vs:!0,ai:!0},o={npm:"npm","crates.io":"crates.io",dbt:"dbt","pub.dev":"pub.dev",kubectx:"kubectx","monday.com":"monday.com","ray.so":"ray.so",flomo:"flomo",iterm:"iTerm",xkcd:"xkcd",macos:"macOS",iphone:"iPhone",github:"GitHub",ide:"IDE",url:"URL",vs:"VS",ai:"AI"};e=e.replace(/\.\.\./g,"\u2026");let i=e.split(" ");for(let a=0;a<i.length;a++){let l=i[a],c=l.toLowerCase(),d=t[c],p=r[c];n[c]?i[a]=o[c]:!d&&!p||a===0||a===i.length-1||p&&i[a-1].endsWith(":")?i[a]=l.charAt(0).toUpperCase()+l.slice(1).toLowerCase():i[a]=l.toLowerCase()}return i.join(" ")}yu.titleCase=ak});var zS=C((Tz,US)=>{US.exports.titleCase=qS().titleCase});var sE=C((Gz,$k)=>{$k.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},binary:{interval:80,frames:["010010","001100","100101","111010","111101","010111","101011","111000","110011","110101"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[====]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]},dwarfFortress:{interval:80,frames:[" \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A\u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","\u263A \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A\u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u263A \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A\u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u263A \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2593\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2592\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A\u2591\u2588\u2588\xA3\xA3\xA3  ","   \u263A \u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2588\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2593\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2592\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A\u2591\u2588\xA3\xA3\xA3  ","    \u263A \u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2588\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2593\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2592\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A\u2591\xA3\xA3\xA3  ","     \u263A \xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\xA3\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2593\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2592\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A\u2591\xA3\xA3  ","      \u263A \xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\xA3\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2593\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2592\xA3  ","       \u263A\u2591\xA3  ","       \u263A\u2591\xA3  ","       \u263A \xA3  ","        \u263A\xA3  ","        \u263A\xA3  ","        \u263A\u2593  ","        \u263A\u2593  ","        \u263A\u2592  ","        \u263A\u2592  ","        \u263A\u2591  ","        \u263A\u2591  ","        \u263A   ","        \u263A  &","        \u263A \u263C&","       \u263A \u263C &","       \u263A\u263C  &","      \u263A\u263C  & ","      \u203C   & ","     \u263A   &  ","    \u203C    &  ","   \u263A    &   ","  \u203C     &   "," \u263A     &    ","\u203C      &    ","      &     ","      &     ","     &   \u2591  ","     &   \u2592  ","    &    \u2593  ","    &    \xA3  ","   &    \u2591\xA3  ","   &    \u2592\xA3  ","  &     \u2593\xA3  ","  &     \xA3\xA3  "," &     \u2591\xA3\xA3  "," &     \u2592\xA3\xA3  ","&      \u2593\xA3\xA3  ","&      \xA3\xA3\xA3  ","      \u2591\xA3\xA3\xA3  ","      \u2592\xA3\xA3\xA3  ","      \u2593\xA3\xA3\xA3  ","      \u2588\xA3\xA3\xA3  ","     \u2591\u2588\xA3\xA3\xA3  ","     \u2592\u2588\xA3\xA3\xA3  ","     \u2593\u2588\xA3\xA3\xA3  ","     \u2588\u2588\xA3\xA3\xA3  ","    \u2591\u2588\u2588\xA3\xA3\xA3  ","    \u2592\u2588\u2588\xA3\xA3\xA3  ","    \u2593\u2588\u2588\xA3\xA3\xA3  ","    \u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2591\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2592\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2593\u2588\u2588\u2588\xA3\xA3\xA3  ","   \u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2591\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2592\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2593\u2588\u2588\u2588\u2588\xA3\xA3\xA3  ","  \u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2591\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2592\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2593\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "," \u2588\u2588\u2588\u2588\u2588\u2588\xA3\xA3\xA3  "]}}});var sp=C((Hz,uE)=>{"use strict";var Eu=Object.assign({},sE()),aE=Object.keys(Eu);Object.defineProperty(Eu,"random",{get(){let e=Math.floor(Math.random()*aE.length),t=aE[e];return Eu[t]}});uE.exports=Eu});var FE=C((hW,vE)=>{vE.exports=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g});var Ep=C((jW,kE)=>{"use strict";var IE=require("fs"),Sp;function o3(){try{return IE.statSync("/.dockerenv"),!0}catch{return!1}}function i3(){try{return IE.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}kE.exports=()=>(Sp===void 0&&(Sp=o3()||i3()),Sp)});var jE=C((LW,vp)=>{"use strict";var s3=require("os"),a3=require("fs"),ME=Ep(),NE=()=>{if(process.platform!=="linux")return!1;if(s3.release().toLowerCase().includes("microsoft"))return!ME();try{return a3.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!ME():!1}catch{return!1}};process.env.__IS_WSL_TEST__?vp.exports=NE:vp.exports=NE()});var qE=C((qW,LE)=>{"use strict";LE.exports=(e,t,r)=>{let n=o=>Object.defineProperty(e,t,{value:o,enumerable:!0,writable:!0});return Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get(){let o=r();return n(o),o},set(o){n(o)}}),e}});var YE=C((UW,HE)=>{var u3=require("path"),l3=require("child_process"),{promises:Au,constants:GE}=require("fs"),Ru=jE(),c3=Ep(),Cp=qE(),UE=u3.join(__dirname,"xdg-open"),{platform:Xn,arch:zE}=process,f3=()=>{try{return Au.statSync("/run/.containerenv"),!0}catch{return!1}},Fp;function d3(){return Fp===void 0&&(Fp=f3()||c3()),Fp}var p3=(()=>{let e="/mnt/",t;return async function(){if(t)return t;let r="/etc/wsl.conf",n=!1;try{await Au.access(r,GE.F_OK),n=!0}catch{}if(!n)return e;let o=await Au.readFile(r,{encoding:"utf8"}),i=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(o);return i?(t=i.groups.mountPoint.trim(),t=t.endsWith("/")?t:`${t}/`,t):e}})(),WE=async(e,t)=>{let r;for(let n of e)try{return await t(n)}catch(o){r=o}throw r},$u=async e=>{if(e={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...e},Array.isArray(e.app))return WE(e.app,l=>$u({...e,app:l}));let{name:t,arguments:r=[]}=e.app||{};if(r=[...r],Array.isArray(t))return WE(t,l=>$u({...e,app:{name:l,arguments:r}}));let n,o=[],i={};if(Xn==="darwin")n="open",e.wait&&o.push("--wait-apps"),e.background&&o.push("--background"),e.newInstance&&o.push("--new"),t&&o.push("-a",t);else if(Xn==="win32"||Ru&&!d3()&&!t){let l=await p3();n=Ru?`${l}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,o.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Ru||(i.windowsVerbatimArguments=!0);let c=["Start"];e.wait&&c.push("-Wait"),t?(c.push(`"\`"${t}\`""`,"-ArgumentList"),e.target&&r.unshift(e.target)):e.target&&c.push(`"${e.target}"`),r.length>0&&(r=r.map(d=>`"\`"${d}\`""`),c.push(r.join(","))),e.target=Buffer.from(c.join(" "),"utf16le").toString("base64")}else{if(t)n=t;else{let l=!__dirname||__dirname==="/",c=!1;try{await Au.access(UE,GE.X_OK),c=!0}catch{}n=process.versions.electron||Xn==="android"||l||!c?"xdg-open":UE}r.length>0&&o.push(...r),e.wait||(i.stdio="ignore",i.detached=!0)}e.target&&o.push(e.target),Xn==="darwin"&&r.length>0&&o.push("--args",...r);let a=l3.spawn(n,o,i);return e.wait?new Promise((l,c)=>{a.once("error",c),a.once("close",d=>{if(!e.allowNonzeroExitCode&&d>0){c(new Error(`Exited with code ${d}`));return}l(a)})}):(a.unref(),a)},Tp=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a `target`");return $u({...t,target:e})},m3=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a `name`");let{arguments:r=[]}=t||{};if(r!=null&&!Array.isArray(r))throw new TypeError("Expected `appArguments` as Array type");return $u({...t,app:{name:e,arguments:r}})};function VE(e){if(typeof e=="string"||Array.isArray(e))return e;let{[zE]:t}=e;if(!t)throw new Error(`${zE} is not supported`);return t}function Rp({[Xn]:e},{wsl:t}){if(t&&Ru)return VE(t);if(!e)throw new Error(`${Xn} is not supported`);return VE(e)}var xu={};Cp(xu,"chrome",()=>Rp({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));Cp(xu,"firefox",()=>Rp({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));Cp(xu,"edge",()=>Rp({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));Tp.apps=xu;Tp.openApp=m3;HE.exports=Tp});var v3={};AF(v3,{default:()=>Bu,lint:()=>ev});module.exports=$F(v3);var Ou=require("@oclif/core");function ge(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var Wm=require("node:url"),sn=(e,t)=>{let r=dl(xF(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},xF=e=>fl(e)?e.toString():e,fl=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,dl=e=>e instanceof URL?(0,Wm.fileURLToPath)(e):e;var ls=(e,t=[],r={})=>{let n=sn(e,"First argument"),[o,i]=ge(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(c=>typeof c=="object"&&c!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let a=o.map(String),l=a.find(c=>c.includes("\0"));if(l!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${l}`);if(!ge(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,a,i]};var Zm=require("node:child_process");var Vm=require("node:string_decoder"),{toString:Gm}=Object.prototype,Hm=e=>Gm.call(e)==="[object ArrayBuffer]",we=e=>Gm.call(e)==="[object Uint8Array]",kt=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),PF=new TextEncoder,Ym=e=>PF.encode(e),OF=new TextDecoder,cs=e=>OF.decode(e),Km=(e,t)=>BF(e,t).join(""),BF=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new Vm.StringDecoder(t),n=e.map(i=>typeof i=="string"?Ym(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},yo=e=>e.length===1&&we(e[0])?e[0]:pl(IF(e)),IF=e=>e.map(t=>typeof t=="string"?Ym(t):t),pl=e=>{let t=new Uint8Array(kF(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},kF=e=>{let t=0;for(let r of e)t+=r.length;return t};var eh=e=>Array.isArray(e)&&Array.isArray(e.raw),th=(e,t)=>{let r=[];for(let[i,a]of e.entries())r=MF({templates:e,expressions:t,tokens:r,index:i,template:a});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},MF=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:a,trailingWhitespaces:l}=NF(o,e.raw[n]),c=Qm(r,i,a);if(n===t.length)return c;let d=t[n],p=Array.isArray(d)?d.map(m=>Xm(m)):[Xm(d)];return Qm(c,p,l)},NF=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=Jm.has(t[0]);for(let a=0,l=0;a<e.length;a+=1,l+=1){let c=t[l];if(Jm.has(c))n!==a&&r.push(e.slice(n,a)),n=a+1;else if(c==="\\"){let d=t[l+1];d==="u"&&t[l+2]==="{"?l=t.indexOf("}",l+3):l+=jF[d]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},Jm=new Set([" ","	","\r",`
`]),jF={x:3,u:5},Qm=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],Xm=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(ge(e)&&("stdout"in e||"isMaxBuffer"in e))return LF(e);throw e instanceof Zm.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},LF=({stdout:e})=>{if(typeof e=="string")return e;if(we(e))return cs(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var fD=require("node:child_process");var nh=require("node:util");var fs=N(require("node:process"),1),nt=e=>ds.includes(e),ds=[fs.default.stdin,fs.default.stdout,fs.default.stderr],Ue=["stdin","stdout","stderr"],ps=e=>Ue[e]??`stdio[${e}]`;var oh=e=>{let t={...e};for(let r of gl)t[r]=ml(e,r);return t},ml=(e,t)=>{let r=Array.from({length:qF(e)+1}),n=UF(e[t],r,t);return HF(n,t)},qF=({stdio:e})=>Array.isArray(e)?Math.max(e.length,Ue.length):Ue.length,UF=(e,t,r)=>ge(e)?zF(e,t,r):t.fill(e),zF=(e,t,r)=>{for(let n of Object.keys(e).sort(WF))for(let o of VF(n,r,t))t[o]=e[n];return t},WF=(e,t)=>rh(e)<rh(t)?1:-1,rh=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,VF=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=hl(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},hl=e=>{if(e==="all")return e;if(Ue.includes(e))return Ue.indexOf(e);let t=GF.exec(e);if(t!==null)return Number(t[1])},GF=/^fd(\d+)$/,HF=(e,t)=>e.map(r=>r===void 0?KF[t]:r),YF=(0,nh.debuglog)("execa").enabled?"full":"none",KF={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:YF,stripFinalNewline:!0},gl=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],Mt=(e,t)=>t==="ipc"?e.at(-1):e[t];var an=({verbose:e},t)=>yl(e,t)!=="none",un=({verbose:e},t)=>!["none","short"].includes(yl(e,t)),ih=({verbose:e},t)=>{let r=yl(e,t);return ms(r)?r:void 0},yl=(e,t)=>t===void 0?JF(e):Mt(e,t),JF=e=>e.find(t=>ms(t))??hs.findLast(t=>e.includes(t)),ms=e=>typeof e=="function",hs=["none","short","full"];var _h=require("node:util");var sh=require("node:process"),ah=require("node:util"),uh=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>rC(lh(i))).join(" ");return{command:n,escapedCommand:o}},Do=e=>(0,ah.stripVTControlCharacters)(e).split(`
`).map(t=>lh(t)).join(`
`),lh=e=>e.replaceAll(ZF,t=>QF(t)),QF=e=>{let t=eC[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=tC?`\\u${n.padStart(4,"0")}`:`\\U${n}`},XF=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},ZF=XF(),eC={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},tC=65535,rC=e=>nC.test(e)?e:sh.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,nC=/^[\w./-]+$/;var Dl=N(require("node:process"),1);function bo(){let{env:e}=Dl.default,{TERM:t,TERM_PROGRAM:r}=e;return Dl.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var ch={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},fh={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},oC={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},iC={...ch,...fh},sC={...ch,...oC},aC=bo(),uC=aC?iC:sC,gs=uC,V3=Object.entries(fh);var dh=N(require("node:tty"),1),lC=dh.default?.WriteStream?.prototype?.hasColors?.()??!1,L=(e,t)=>{if(!lC)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",a=i.indexOf(n);if(a===-1)return r+i+n;let l=r,c=0;for(;a!==-1;)l+=i.slice(c,a)+r,c=a+n.length,a=i.indexOf(n,c);return l+=i.slice(c)+n,l}},H3=L(0,0),ph=L(1,22),Y3=L(2,22),K3=L(3,23),J3=L(4,24),Q3=L(53,55),X3=L(7,27),Z3=L(8,28),eM=L(9,29),tM=L(30,39),rM=L(31,39),nM=L(32,39),oM=L(33,39),iM=L(34,39),sM=L(35,39),aM=L(36,39),uM=L(37,39),ys=L(90,39),lM=L(40,49),cM=L(41,49),fM=L(42,49),dM=L(43,49),pM=L(44,49),mM=L(45,49),hM=L(46,49),gM=L(47,49),yM=L(100,49),mh=L(91,39),DM=L(92,39),hh=L(93,39),bM=L(94,39),_M=L(95,39),wM=L(96,39),SM=L(97,39),EM=L(101,49),vM=L(102,49),FM=L(103,49),CM=L(104,49),TM=L(105,49),RM=L(106,49),AM=L(107,49);var Dh=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:a=!0}})=>{let l=cC(r),c=fC[e]({failed:i,reject:a,piped:n}),d=dC[e]({reject:a});return`${ys(`[${l}]`)} ${ys(`[${o}]`)} ${d(c)} ${d(t)}`},cC=e=>`${Ds(e.getHours(),2)}:${Ds(e.getMinutes(),2)}:${Ds(e.getSeconds(),2)}.${Ds(e.getMilliseconds(),3)}`,Ds=(e,t)=>String(e).padStart(t,"0"),gh=({failed:e,reject:t})=>e?t?gs.cross:gs.warning:gs.tick,fC={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:gh,duration:gh},yh=e=>e,dC={command:()=>ph,output:()=>yh,ipc:()=>yh,error:({reject:e})=>e?mh:hh,duration:()=>ys};var bh=(e,t,r)=>{let n=ih(t,r);return e.map(({verboseLine:o,verboseObject:i})=>pC(o,i,n)).filter(o=>o!==void 0).map(o=>mC(o)).join("")},pC=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},mC=e=>e.endsWith(`
`)?e:`${e}
`;var bt=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=hC({type:e,result:o,verboseInfo:n}),a=gC(t,i),l=bh(a,n,r);l!==""&&console.warn(l.slice(0,-1))},hC=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),gC=(e,t)=>e.split(`
`).map(r=>yC({...t,message:r})),yC=e=>({verboseLine:Dh(e),verboseObject:e}),bs=e=>{let t=typeof e=="string"?e:(0,_h.inspect)(e);return Do(t).replaceAll("	"," ".repeat(DC))},DC=2;var wh=(e,t)=>{an(t)&&bt({type:"command",verboseMessage:e,verboseInfo:t})};var Sh=(e,t,r)=>{wC(e);let n=bC(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},bC=e=>an({verbose:e})?_C++:void 0,_C=0n,wC=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!hs.includes(t)&&!ms(t)){let r=hs.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var bl=require("node:process"),_s=()=>bl.hrtime.bigint(),_l=e=>Number(bl.hrtime.bigint()-e)/1e6;var ws=(e,t,r)=>{let n=_s(),{command:o,escapedCommand:i}=uh(e,t),a=ml(r,"verbose"),l=Sh(a,i,{...r});return wh(i,l),{command:o,escapedCommand:i,startTime:n,verboseInfo:l}};var ky=N(require("node:path"),1),Wl=N(require("node:process"),1),My=N(cg(),1);var _o=N(require("node:process"),1),ir=N(require("node:path"),1);function Es(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var fg=require("node:util"),xl=require("node:child_process"),$l=N(require("node:path"),1),dg=require("node:url"),pN=(0,fg.promisify)(xl.execFile);function vs(e){return e instanceof URL?(0,dg.fileURLToPath)(e):e}function pg(e){return{*[Symbol.iterator](){let t=$l.default.resolve(vs(e)),r;for(;r!==t;)yield t,r=t,t=$l.default.resolve(t,"..")}}}var mN=10*1024*1024;var YC=({cwd:e=_o.default.cwd(),path:t=_o.default.env[Es()],preferLocal:r=!0,execPath:n=_o.default.execPath,addExecPath:o=!0}={})=>{let i=ir.default.resolve(vs(e)),a=[],l=t.split(ir.default.delimiter);return r&&KC(a,l,i),o&&JC(a,l,n,i),t===""||t===ir.default.delimiter?`${a.join(ir.default.delimiter)}${t}`:[...a,t].join(ir.default.delimiter)},KC=(e,t,r)=>{for(let n of pg(r)){let o=ir.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},JC=(e,t,r,n)=>{let o=ir.default.resolve(n,vs(r),"..");t.includes(o)||e.push(o)},mg=({env:e=_o.default.env,...t}={})=>{e={...e};let r=Es({env:e});return t.path=e[r],e[r]=YC(t),e};var $g=require("node:timers/promises");var hg=(e,t,r)=>{let n=r?So:wo,o=e instanceof ot?{}:{cause:e};return new n(t,o)},ot=class extends Error{},gg=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Dg,{value:!0,writable:!1,enumerable:!1,configurable:!1})},yg=e=>Fs(e)&&Dg in e,Dg=Symbol("isExecaError"),Fs=e=>Object.prototype.toString.call(e)==="[object Error]",wo=class extends Error{};gg(wo,wo.name);var So=class extends Error{};gg(So,So.name);var fn=require("node:os");var vg=require("node:os");var bg=()=>{let e=wg-_g+1;return Array.from({length:e},QC)},QC=(e,t)=>({name:`SIGRT${t+1}`,number:_g+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),_g=34,wg=64;var Eg=require("node:os");var Sg=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var Pl=()=>{let e=bg();return[...Sg,...e].map(XC)},XC=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:a}}=Eg.constants,l=a!==void 0;return{name:e,number:l?a:t,description:r,supported:l,action:n,forced:o,standard:i}};var ZC=()=>{let e=Pl();return Object.fromEntries(e.map(eT))},eT=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:a})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:a}],Fg=ZC(),tT=()=>{let e=Pl(),t=65,r=Array.from({length:t},(n,o)=>rT(o,e));return Object.assign({},...r)},rT=(e,t)=>{let r=nT(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:a,forced:l,standard:c}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:a,forced:l,standard:c}}},nT=(e,t)=>{let r=t.find(({name:n})=>vg.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},TN=tT();var Tg=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return Ag(e,t)},Rg=e=>e===0?e:Ag(e,"`subprocess.kill()`'s argument"),Ag=(e,t)=>{if(Number.isInteger(e))return oT(e,t);if(typeof e=="string")return sT(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${Ol()}`)},oT=(e,t)=>{if(Cg.has(e))return Cg.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${Ol()}`)},iT=()=>new Map(Object.entries(fn.constants.signals).reverse().map(([e,t])=>[t,e])),Cg=iT(),sT=(e,t)=>{if(e in fn.constants.signals)return e;throw e.toUpperCase()in fn.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${Ol()}`)},Ol=()=>`Available signal names: ${aT()}.
Available signal numbers: ${uT()}.`,aT=()=>Object.keys(fn.constants.signals).sort().map(e=>`'${e}'`).join(", "),uT=()=>[...new Set(Object.values(fn.constants.signals).sort((e,t)=>e-t))].join(", "),Cs=e=>Fg[e].description;var xg=e=>{if(e===!1)return e;if(e===!0)return lT;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},lT=1e3*5,Pg=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},a,l)=>{let{signal:c,error:d}=cT(a,l,r);fT(d,n);let p=e(c);return dT({kill:e,signal:c,forceKillAfterDelay:t,killSignal:r,killResult:p,context:o,controller:i}),p},cT=(e,t,r)=>{let[n=r,o]=Fs(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!Fs(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Rg(n),error:o}},fT=(e,t)=>{e!==void 0&&t.reject(e)},dT=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:a})=>{t===n&&o&&Bl({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:a.signal})},Bl=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,$g.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var Og=require("node:events"),Ts=async(e,t)=>{e.aborted||await(0,Og.once)(e,"abort",{signal:t})};var Bg=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},Ig=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[pT(e,t,n,o)],pT=async(e,t,r,{signal:n})=>{throw await Ts(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var my=require("node:timers/promises");var dy=require("node:util");var dn=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{mT(e,t,r),Il(e,t,n)},mT=(e,t,r)=>{if(!r)throw new Error(`${it(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},Il=(e,t,r)=>{if(!r)throw new Error(`${it(e,t)} cannot be used: the ${sr(t)} has already exited or disconnected.`)},kg=e=>{throw new Error(`${it("getOneMessage",e)} could not complete: the ${sr(e)} exited or disconnected.`)},Mg=e=>{throw new Error(`${it("sendMessage",e)} failed: the ${sr(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${it("getOneMessage",e)},
	${it("sendMessage",e,"message, {strict: true}")},
]);`)},Rs=(e,t)=>new Error(`${it("sendMessage",t)} failed when sending an acknowledgment response to the ${sr(t)}.`,{cause:e}),Ng=e=>{throw new Error(`${it("sendMessage",e)} failed: the ${sr(e)} is not listening to incoming messages.`)},jg=e=>{throw new Error(`${it("sendMessage",e)} failed: the ${sr(e)} exited without listening to incoming messages.`)},Lg=()=>new Error(`\`cancelSignal\` aborted: the ${sr(!0)} disconnected.`),qg=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},Ug=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${it(t,r)} cannot be used: the ${sr(r)} is disconnecting.`,{cause:e})},zg=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(hT(e))throw new Error(`${it(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},hT=({code:e,message:t})=>gT.has(e)||yT.some(r=>t.includes(r)),gT=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),yT=["could not be cloned","circular structure","call stack size exceeded"],it=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${DT(t)}${e}(${r})`,DT=e=>e?"":"subprocess.",sr=e=>e?"parent process":"subprocess",pn=e=>{e.connected&&e.disconnect()};var _t=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var $s=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=wt.get(e),i=Wg(o,t,!0),a=e.stdio[i];if(a===null)throw new TypeError(Vg(i,t,n,!0));return a},mn=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=wt.get(e),i=Wg(o,t,!1),a=i==="all"?e.all:e.stdio[i];if(a==null)throw new TypeError(Vg(i,t,n,!1));return a},wt=new WeakMap,Wg=(e,t,r)=>{let n=bT(t,r);return _T(n,t,r,e),n},bT=(e,t)=>{let r=hl(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${Eo(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},_T=(e,t,r,n)=>{let o=n[Gg(e)];if(o===void 0)throw new TypeError(`"${Eo(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${Eo(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${Eo(r)}" must not be ${t}. It must be a writable stream, not readable.`)},Vg=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=wT(e,r);return`The "${o}: ${As(i)}" option is incompatible with using "${Eo(n)}: ${As(t)}".
Please set this option with "pipe" instead.`},wT=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=Gg(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},Gg=e=>e==="all"?1:e,Eo=e=>e?"to":"from",As=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var iy=require("node:events");var Hg=require("node:events"),Rr=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,Hg.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var oy=require("node:events");var Jg=require("node:events"),Qg=require("node:timers/promises");var xs=(e,t)=>{t&&kl(e)},kl=e=>{e.refCounted()},Ps=(e,t)=>{t&&Ml(e)},Ml=e=>{e.unrefCounted()},Yg=(e,t)=>{t&&(Ml(e),Ml(e))},Kg=(e,t)=>{t&&(kl(e),kl(e))};var Xg=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(ty(o)||ny(o))return;Os.has(e)||Os.set(e,[]);let i=Os.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await ry(e,n,o),await Qg.scheduler.yield();let a=await ey({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",a),n.emit("message:done")}},Zg=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{Nl();let i=Os.get(e);for(;i?.length>0;)await(0,Jg.once)(n,"message:done");e.removeListener("message",o),Kg(t,r),n.connected=!1,n.emit("disconnect")},Os=new WeakMap;var ar=(e,t,r)=>{if(Bs.has(e))return Bs.get(e);let n=new oy.EventEmitter;return n.connected=!0,Bs.set(e,n),ST({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},Bs=new WeakMap,ST=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=Xg.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",Zg.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),Yg(r,n)},Is=e=>{let t=Bs.get(e);return t===void 0?e.channel!==null:t.connected};var sy=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=ar(e,t,r),a=Ns(e,i);return{id:ET++,type:Ms,message:n,hasListeners:a}},ET=0n,ay=(e,t)=>{if(!(t?.type!==Ms||t.hasListeners))for(let{id:r}of e)r!==void 0&&ks[r].resolve({isDeadlock:!0,hasListeners:!1})},ey=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==Ms||!t.connected)return e;let{id:i,message:a}=e,l={id:i,type:ly,message:Ns(t,o)};try{await js({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},l)}catch(c){o.emit("strict:error",c)}return a},ty=e=>{if(e?.type!==ly)return!1;let{id:t,message:r}=e;return ks[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},uy=async(e,t,r)=>{if(e?.type!==Ms)return;let n=_t();ks[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:a}=await Promise.race([n,vT(t,r,o)]);i&&Mg(r),a||Ng(r)}finally{o.abort(),delete ks[e.id]}},ks={},vT=async(e,t,{signal:r})=>{Rr(e,1,r),await(0,iy.once)(e,"disconnect",{signal:r}),jg(t)},Ms="execa:ipc:request",ly="execa:ipc:response";var cy=(e,t,r)=>{vo.has(e)||vo.set(e,new Set);let n=vo.get(e),o=_t(),i=r?t.id:void 0,a={onMessageSent:o,id:i};return n.add(a),{outgoingMessages:n,outgoingMessage:a}},fy=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},ry=async(e,t,r)=>{for(;!Ns(e,t)&&vo.get(e)?.size>0;){let n=[...vo.get(e)];ay(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},vo=new WeakMap,Ns=(e,t)=>t.listenerCount("message")>FT(e),FT=e=>wt.has(e)&&!Mt(wt.get(e).options.buffer,"ipc")?1:0;var js=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let a="sendMessage";return dn({methodName:a,isSubprocess:r,ipc:n,isConnected:e.connected}),CT({anyProcess:e,channel:t,methodName:a,isSubprocess:r,message:o,strict:i})},CT=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let a=sy({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),l=cy(e,a,i);try{await Ll({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:a,message:o})}catch(c){throw pn(e),c}finally{fy(l)}},Ll=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=TT(e);try{await Promise.all([uy(n,e,r),i(n)])}catch(a){throw Ug({error:a,methodName:t,isSubprocess:r}),zg({error:a,methodName:t,isSubprocess:r,message:o}),a}},TT=e=>{if(jl.has(e))return jl.get(e);let t=(0,dy.promisify)(e.send.bind(e));return jl.set(e,t),t},jl=new WeakMap;var hy=(e,t)=>{let r="cancelSignal";return Il(r,!1,e.connected),Ll({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:yy,message:t},message:t})},gy=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await RT({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),ql.signal),RT=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!py){if(py=!0,!n){qg();return}if(t===null){Nl();return}ar(e,t,r),await my.scheduler.yield()}},py=!1,ny=e=>e?.type!==yy?!1:(ql.abort(e.message),!0),yy="execa:ipc:cancel",Nl=()=>{ql.abort(Lg())},ql=new AbortController;var Dy=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},by=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[AT({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],AT=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await Ts(t,o);let i=$T(t);throw await hy(e,i),Bl({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},$T=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var _y=require("node:timers/promises");var wy=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},Sy=(e,t,r,n)=>t===0||t===void 0?[]:[xT(e,t,r,n)],xT=async(e,t,r,{signal:n})=>{throw await(0,_y.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new ot};var Ls=require("node:process"),Ul=N(require("node:path"),1);var Ey=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},vy=(e,t,{node:r=!1,nodePath:n=Ls.execPath,nodeOptions:o=Ls.execArgv.filter(c=>!c.startsWith("--inspect")),cwd:i,execPath:a,...l})=>{if(a!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let c=sn(n,'The "nodePath" option'),d=Ul.default.resolve(i,c),p={...l,nodePath:d,node:r,cwd:i};if(!r)return[e,t,p];if(Ul.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[d,[...o,e,...t],{ipc:!0,...p,shell:!1}]};var Fy=require("node:v8"),Cy=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");BT[r](e)}},PT=e=>{try{(0,Fy.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},OT=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},BT={advanced:PT,json:OT},Ty=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var Ay=({encoding:e})=>{if(zl.has(e))return;let t=kT(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${qs(e)}\`.
Please rename it to ${qs(t)}.`);let r=[...zl].map(n=>qs(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${qs(e)}\`.
Please rename it to one of: ${r}.`)},IT=new Set(["utf8","utf16le"]),je=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),zl=new Set([...IT,...je]),kT=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Ry)return Ry[t];if(zl.has(t))return t},Ry={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},qs=e=>typeof e=="string"?`"${e}"`:String(e);var $y=require("node:fs"),xy=N(require("node:path"),1),Py=N(require("node:process"),1);var Oy=(e=By())=>{let t=sn(e,'The "cwd" option');return xy.default.resolve(t)},By=()=>{try{return Py.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},Iy=(e,t)=>{if(t===By())return e;let r;try{r=(0,$y.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var Us=(e,t,r)=>{r.cwd=Oy(r.cwd);let[n,o,i]=vy(e,t,r),{command:a,args:l,options:c}=My.default._parse(n,o,i),d=oh(c),p=MT(d);return wy(p),Ay(p),Cy(p),Bg(p),Dy(p),p.shell=dl(p.shell),p.env=NT(p),p.killSignal=Tg(p.killSignal),p.forceKillAfterDelay=xg(p.forceKillAfterDelay),p.lines=p.lines.map((m,b)=>m&&!je.has(p.encoding)&&p.buffer[b]),Wl.default.platform==="win32"&&ky.default.basename(a,".exe")==="cmd"&&l.unshift("/q"),{file:a,commandArguments:l,options:p}},MT=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:a=!0,all:l=!1,windowsHide:c=!0,killSignal:d="SIGTERM",forceKillAfterDelay:p=!0,gracefulCancel:m=!1,ipcInput:b,ipc:D=b!==void 0||m,serialization:g="advanced",...y})=>({...y,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:a,all:l,windowsHide:c,killSignal:d,forceKillAfterDelay:p,gracefulCancel:m,ipcInput:b,ipc:D,serialization:g}),NT=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let a=t?{...Wl.default.env,...e}:e;return r||n?mg({env:a,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):a};var i0=require("node:util");function hn(e){if(typeof e=="string")return jT(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return LT(e)}var jT=e=>e.at(-1)===Ny?e.slice(0,e.at(-2)===jy?-2:-1):e,LT=e=>e.at(-1)===qT?e.subarray(0,e.at(-2)===UT?-2:-1):e,Ny=`
`,qT=Ny.codePointAt(0),jy="\r",UT=jy.codePointAt(0);var Qy=require("node:events"),Xy=require("node:stream/promises");function st(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function Vl(e,{checkOpen:t=!0}={}){return st(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Ar(e,{checkOpen:t=!0}={}){return st(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function Gl(e,t){return Vl(e,t)&&Ar(e,t)}var zT=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),Hl=class{#e;#r;#t=!1;#n=void 0;constructor(t,r){this.#e=t,this.#r=r}next(){let t=()=>this.#a();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#o(t);return this.#n?this.#n.then(r,r):r()}async#a(){if(this.#t)return{done:!0,value:void 0};let t;try{t=await this.#e.read()}catch(r){throw this.#n=void 0,this.#t=!0,this.#e.releaseLock(),r}return t.done&&(this.#n=void 0,this.#t=!0,this.#e.releaseLock()),t}async#o(t){if(this.#t)return{done:!0,value:t};if(this.#t=!0,!this.#r){let r=this.#e.cancel(t);return this.#e.releaseLock(),await r,{done:!0,value:t}}return this.#e.releaseLock(),{done:!0,value:t}}},Yl=Symbol();function Ly(){return this[Yl].next()}Object.defineProperty(Ly,"name",{value:"next"});function qy(e){return this[Yl].return(e)}Object.defineProperty(qy,"name",{value:"return"});var WT=Object.create(zT,{next:{enumerable:!0,configurable:!0,writable:!0,value:Ly},return:{enumerable:!0,configurable:!0,writable:!0,value:qy}});function Kl({preventCancel:e=!1}={}){let t=this.getReader(),r=new Hl(t,e),n=Object.create(WT);return n[Yl]=r,n}var Uy=e=>{if(Ar(e,{checkOpen:!1})&&Fo.on!==void 0)return GT(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(VT.call(e)==="[object ReadableStream]")return Kl.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:VT}=Object.prototype,GT=async function*(e){let t=new AbortController,r={};HT(e,t,r);try{for await(let[n]of Fo.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},HT=async(e,t,r)=>{try{await Fo.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},Fo={};var gn=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:a,finalize:l},{maxBuffer:c=Number.POSITIVE_INFINITY}={})=>{let d=Uy(e),p=t();p.length=0;try{for await(let m of d){let b=KT(m),D=r[b](m,p);Vy({convertedChunk:D,state:p,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:c})}return YT({state:p,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:a,maxBuffer:c}),l(p)}catch(m){let b=typeof m=="object"&&m!==null?m:new Error(m);throw b.bufferedData=l(p),b}},YT=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let a=o(e);a!==void 0&&Vy({convertedChunk:a,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},Vy=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let a=r(e),l=t.length+a;if(l<=i){zy(e,t,o,l);return}let c=n(e,i-t.length);throw c!==void 0&&zy(c,t,o,i),new St},zy=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},KT=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=Wy.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&Wy.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:Wy}=Object.prototype,St=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var Nt=e=>e,Co=()=>{},zs=({contents:e})=>e,Ws=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},Vs=e=>e.length;async function Gs(e,t){return gn(e,ZT,t)}var JT=()=>({contents:[]}),QT=()=>1,XT=(e,{contents:t})=>(t.push(e),t),ZT={init:JT,convertChunk:{string:Nt,buffer:Nt,arrayBuffer:Nt,dataView:Nt,typedArray:Nt,others:Nt},getSize:QT,truncateChunk:Co,addChunk:XT,getFinalChunk:Co,finalize:zs};async function Hs(e,t){return gn(e,uR,t)}var eR=()=>({contents:new ArrayBuffer(0)}),tR=e=>rR.encode(e),rR=new TextEncoder,Gy=e=>new Uint8Array(e),Hy=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),nR=(e,t)=>e.slice(0,t),oR=(e,{contents:t,length:r},n)=>{let o=Jy()?sR(t,n):iR(t,n);return new Uint8Array(o).set(e,r),o},iR=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(Ky(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},sR=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:Ky(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},Ky=e=>Yy**Math.ceil(Math.log(e)/Math.log(Yy)),Yy=2,aR=({contents:e,length:t})=>Jy()?e:e.slice(0,t),Jy=()=>"resize"in ArrayBuffer.prototype,uR={init:eR,convertChunk:{string:tR,buffer:Gy,arrayBuffer:Gy,dataView:Hy,typedArray:Hy,others:Ws},getSize:Vs,truncateChunk:nR,addChunk:oR,getFinalChunk:Co,finalize:aR};async function Ks(e,t){return gn(e,pR,t)}var lR=()=>({contents:"",textDecoder:new TextDecoder}),Ys=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),cR=(e,{contents:t})=>t+e,fR=(e,t)=>e.slice(0,t),dR=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},pR={init:lR,convertChunk:{string:Nt,buffer:Ys,arrayBuffer:Ys,dataView:Ys,typedArray:Ys,others:Ws},getSize:Vs,truncateChunk:fR,addChunk:cR,getFinalChunk:dR,finalize:zs};Object.assign(Fo,{on:Qy.on,finished:Xy.finished});var Zy=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof St))throw e;if(i==="all")return e;let a=mR(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:a},t.destroy(),e},mR=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",e0=(e,t,r)=>{if(t.length!==r)return;let n=new St;throw n.maxBufferInfo={fdNumber:"ipc"},n},t0=(e,t)=>{let{streamName:r,threshold:n,unit:o}=hR(e,t);return`Command's ${r} was larger than ${n} ${o}`},hR=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=Mt(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:ps(r),threshold:o,unit:n}},r0=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>Js(r)),n0=(e,t,r)=>{if(!t)return e;let n=Js(r);return e.length>n?e.slice(0,n):e},Js=([,e])=>e;var s0=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:a,escapedCommand:l,timedOut:c,isCanceled:d,isGracefullyCanceled:p,isMaxBuffer:m,isForcefullyTerminated:b,forceKillAfterDelay:D,killSignal:g,maxBuffer:y,timeout:S,cwd:w})=>{let T=n?.code,R=gR({originalError:n,timedOut:c,timeout:S,isMaxBuffer:m,maxBuffer:y,errorCode:T,signal:o,signalDescription:i,exitCode:a,isCanceled:d,isGracefullyCanceled:p,isForcefullyTerminated:b,forceKillAfterDelay:D,killSignal:g}),A=DR(n,w),v=A===void 0?"":`
${A}`,B=`${R}: ${l}${v}`,W=t===void 0?[e[2],e[1]]:[t],ae=[B,...W,...e.slice(3),r.map(P=>bR(P)).join(`
`)].map(P=>Do(hn(_R(P)))).filter(Boolean).join(`

`);return{originalMessage:A,shortMessage:B,message:ae}},gR=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:a,signalDescription:l,exitCode:c,isCanceled:d,isGracefullyCanceled:p,isForcefullyTerminated:m,forceKillAfterDelay:b,killSignal:D})=>{let g=yR(m,b);return t?`Command timed out after ${r} milliseconds${g}`:p?a===void 0?`Command was gracefully canceled with exit code ${c}`:m?`Command was gracefully canceled${g}`:`Command was gracefully canceled with ${a} (${l})`:d?`Command was canceled${g}`:n?`${t0(e,o)}${g}`:i!==void 0?`Command failed with ${i}${g}`:m?`Command was killed with ${D} (${Cs(D)})${g}`:a!==void 0?`Command was killed with ${a} (${l})`:c!==void 0?`Command failed with exit code ${c}`:"Command failed"},yR=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",DR=(e,t)=>{if(e instanceof ot)return;let r=yg(e)?e.originalMessage:String(e?.message??e),n=Do(Iy(r,t));return n===""?void 0:n},bR=e=>typeof e=="string"?e:(0,i0.inspect)(e),_R=e=>Array.isArray(e)?e.map(t=>hn(o0(t))).filter(Boolean).join(`
`):o0(e),o0=e=>typeof e=="string"?e:we(e)?cs(e):"";var Qs=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:a})=>a0({command:e,escapedCommand:t,cwd:i,durationMs:_l(a),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),yn=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:a})=>To({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:a}),To=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:d,signal:p,stdio:m,all:b,ipcOutput:D,options:{timeoutDuration:g,timeout:y=g,forceKillAfterDelay:S,killSignal:w,cwd:T,maxBuffer:R},isSync:A})=>{let{exitCode:v,signal:B,signalDescription:W}=SR(d,p),{originalMessage:ae,shortMessage:P,message:x}=s0({stdio:m,all:b,ipcOutput:D,originalError:e,signal:B,signalDescription:W,exitCode:v,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,forceKillAfterDelay:S,killSignal:w,maxBuffer:R,timeout:y,cwd:T}),k=hg(e,x,A);return Object.assign(k,wR({error:k,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:v,signal:B,signalDescription:W,stdio:m,all:b,ipcOutput:D,cwd:T,originalMessage:ae,shortMessage:P})),k},wR=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:d,signal:p,signalDescription:m,stdio:b,all:D,ipcOutput:g,cwd:y,originalMessage:S,shortMessage:w})=>a0({shortMessage:w,originalMessage:S,command:t,escapedCommand:r,cwd:y,durationMs:_l(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:a,isTerminated:p!==void 0,isMaxBuffer:l,isForcefullyTerminated:c,exitCode:d,signal:p,signalDescription:m,code:e.cause?.code,stdout:b[1],stderr:b[2],all:D,stdio:b,ipcOutput:g,pipedFrom:[]}),a0=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),SR=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:Cs(t);return{exitCode:r,signal:n,signalDescription:o}};var u0=e=>Number.isFinite(e)?e:0;function ER(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(u0(e*1e3)%1e3),nanoseconds:Math.trunc(u0(e*1e6)%1e3)}}function vR(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function Jl(e){switch(typeof e){case"number":{if(Number.isFinite(e))return ER(e);break}case"bigint":return vR(e)}throw new TypeError("Expected a finite number or bigint")}var FR=e=>e===0||e===0n,CR=(e,t)=>t===1||t===1n?e:`${e}s`,TR=1e-7,RR=24n*60n*60n*1000n;function Ql(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(p,m)=>{let b=Math.floor(p*10**m+TR);return(Math.round(b)/10**m).toFixed(m)},a=(p,m,b,D)=>{if(!((o.length===0||!t.colonNotation)&&FR(p)&&!(t.colonNotation&&b==="m"))){if(D??=String(p),t.colonNotation){let g=D.includes(".")?D.split(".")[0].length:D.length,y=o.length>0?2:1;D="0".repeat(Math.max(0,y-g))+D}else D+=t.verbose?" "+CR(m,p):b;o.push(D)}},l=Jl(e),c=BigInt(l.days);if(a(c/365n,"year","y"),a(c%365n,"day","d"),a(Number(l.hours),"hour","h"),a(Number(l.minutes),"minute","m"),t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let p=Number(l.seconds),m=Number(l.milliseconds),b=Number(l.microseconds),D=Number(l.nanoseconds);if(a(p,"second","s"),t.formatSubMilliseconds)a(m,"millisecond","ms"),a(b,"microsecond","\xB5s"),a(D,"nanosecond","ns");else{let g=m+b/1e3+D/1e6,y=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,S=g>=1?Math.round(g):Math.ceil(g),w=y?g.toFixed(y):S;a(Number.parseFloat(w),"millisecond","ms",w)}}else{let p=(r?Number(e%RR):e)/1e3%60,m=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,b=i(p,m),D=t.keepDecimalsOnWholeSeconds?b:b.replace(/\.0+$/,"");a(Number.parseFloat(D),"second","s",D)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let d=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(d)}var l0=(e,t)=>{e.failed&&bt({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var c0=(e,t)=>{an(t)&&(l0(e,t),AR(e,t))},AR=(e,t)=>{let r=`(done in ${Ql(e.durationMs)})`;bt({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var Dn=(e,t,{reject:r})=>{if(c0(e,t),e.failed&&r)throw e;return e};var ic=require("node:fs");var p0=(e,t)=>$r(e)?"asyncGenerator":g0(e)?"generator":Xs(e)?"fileUrl":BR(e)?"filePath":MR(e)?"webStream":st(e,{checkOpen:!1})?"native":we(e)?"uint8Array":NR(e)?"asyncIterable":jR(e)?"iterable":ec(e)?m0({transform:e},t):OR(e)?$R(e,t):"native",$R=(e,t)=>Gl(e.transform,{checkOpen:!1})?xR(e,t):ec(e.transform)?m0(e,t):PR(e,t),xR=(e,t)=>(h0(e,t,"Duplex stream"),"duplex"),m0=(e,t)=>(h0(e,t,"web TransformStream"),"webTransform"),h0=({final:e,binary:t,objectMode:r},n,o)=>{f0(e,`${n}.final`,o),f0(t,`${n}.binary`,o),Xl(r,`${n}.objectMode`)},f0=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},PR=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!d0(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(Gl(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(ec(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!d0(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return Xl(r,`${o}.binary`),Xl(n,`${o}.objectMode`),$r(e)||$r(t)?"asyncGenerator":"generator"},Xl=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},d0=e=>$r(e)||g0(e),$r=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",g0=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",OR=e=>ge(e)&&(e.transform!==void 0||e.final!==void 0),Xs=e=>Object.prototype.toString.call(e)==="[object URL]",y0=e=>Xs(e)&&e.protocol!=="file:",BR=e=>ge(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>IR.has(t))&&Zl(e.file),IR=new Set(["file","append"]),Zl=e=>typeof e=="string",D0=(e,t)=>e==="native"&&typeof t=="string"&&!kR.has(t),kR=new Set(["ipc","ignore","inherit","overlapped","pipe"]),b0=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",Zs=e=>Object.prototype.toString.call(e)==="[object WritableStream]",MR=e=>b0(e)||Zs(e),ec=e=>b0(e?.readable)&&Zs(e?.writable),NR=e=>_0(e)&&typeof e[Symbol.asyncIterator]=="function",jR=e=>_0(e)&&typeof e[Symbol.iterator]=="function",_0=e=>typeof e=="object"&&e!==null,ze=new Set(["generator","asyncGenerator","duplex","webTransform"]),ea=new Set(["fileUrl","filePath","fileNumber"]),tc=new Set(["fileUrl","filePath"]),w0=new Set([...tc,"webStream","nodeStream"]),S0=new Set(["webTransform","duplex"]),ur={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var rc=(e,t,r,n)=>n==="output"?LR(e,t,r):qR(e,t,r),LR=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},qR=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},E0=(e,t)=>{let r=e.findLast(({type:n})=>ze.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var v0=(e,t,r,n)=>[...e.filter(({type:o})=>!ze.has(o)),...UR(e,t,r,n)],UR=(e,t,r,{encoding:n})=>{let o=e.filter(({type:a})=>ze.has(a)),i=Array.from({length:o.length});for(let[a,l]of Object.entries(o))i[a]=zR({stdioItem:l,index:Number(a),newTransforms:i,optionName:t,direction:r,encoding:n});return HR(i,r)},zR=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:a})=>t==="duplex"?WR({stdioItem:e,optionName:o}):t==="webTransform"?VR({stdioItem:e,index:r,newTransforms:n,direction:i}):GR({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:a}),WR=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},VR=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:a}=ge(t)?t:{transform:t},{writableObjectMode:l,readableObjectMode:c}=rc(a,r,n,o);return{...e,value:{transform:i,writableObjectMode:l,readableObjectMode:c}}},GR=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:a,final:l,binary:c=!1,preserveNewlines:d=!1,objectMode:p}=ge(t)?t:{transform:t},m=c||je.has(i),{writableObjectMode:b,readableObjectMode:D}=rc(p,r,n,o);return{...e,value:{transform:a,final:l,binary:m,preserveNewlines:d,writableObjectMode:b,readableObjectMode:D}}},HR=(e,t)=>t==="input"?e.reverse():e;var ta=N(require("node:process"),1);var F0=(e,t,r)=>{let n=e.map(o=>YR(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??QR},YR=({type:e,value:t},r)=>KR[r]??C0[e](t),KR=["input","output","output"],bn=()=>{},nc=()=>"input",C0={generator:bn,asyncGenerator:bn,fileUrl:bn,filePath:bn,iterable:nc,asyncIterable:nc,uint8Array:nc,webStream:e=>Zs(e)?"output":"input",nodeStream(e){return Ar(e,{checkOpen:!1})?Vl(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:bn,duplex:bn,native(e){let t=JR(e);if(t!==void 0)return t;if(st(e,{checkOpen:!1}))return C0.nodeStream(e)}},JR=e=>{if([0,ta.default.stdin].includes(e))return"input";if([1,2,ta.default.stdout,ta.default.stderr].includes(e))return"output"},QR="output";var T0=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var R0=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let a=XR(e,n).map((l,c)=>A0(l,c));return i?eA(a,r,o):T0(a,t)},XR=(e,t)=>{if(e===void 0)return Ue.map(n=>t[n]);if(ZR(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${Ue.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,Ue.length);return Array.from({length:r},(n,o)=>e[o])},ZR=e=>Ue.some(t=>e[t]!==void 0),A0=(e,t)=>Array.isArray(e)?e.map(r=>A0(r,t)):e??(t>=Ue.length?"ignore":"pipe"),eA=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!un(r,o)&&tA(n)?"ignore":n),tA=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var x0=require("node:fs"),P0=N(require("node:tty"),1);var O0=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?rA({stdioItem:e,fdNumber:n,direction:o}):iA({stdioItem:e,fdNumber:n}),rA=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=nA({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if(st(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},nA=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=oA(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(P0.default.isatty(o))throw new TypeError(`The \`${t}: ${As(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:kt((0,x0.readFileSync)(o)),optionName:t}}},oA=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=ds.indexOf(e);if(r!==-1)return r},iA=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:$0(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:$0(t,t,r),optionName:r}:st(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,$0=(e,t,r)=>{let n=ds[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var B0=({input:e,inputFile:t},r)=>r===0?[...sA(e),...uA(t)]:[],sA=e=>e===void 0?[]:[{type:aA(e),value:e,optionName:"input"}],aA=e=>{if(Ar(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(we(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},uA=e=>e===void 0?[]:[{...lA(e),optionName:"inputFile"}],lA=e=>{if(Xs(e))return{type:"fileUrl",value:e};if(Zl(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var I0=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),k0=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let a=cA(o,e);if(a.length!==0){if(i){fA({otherStdioItems:a,type:e,value:t,optionName:r,direction:n});return}if(w0.has(e))return M0({otherStdioItems:a,type:e,value:t,optionName:r,direction:n});S0.has(e)&&pA({otherStdioItems:a,type:e,value:t,optionName:r})}},cA=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),fA=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{tc.has(t)&&M0({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},M0=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(l=>dA(l,r));if(i.length===0)return;let a=i.find(l=>l.direction!==o);return N0(a,n,t),o==="output"?i[0].stream:void 0},dA=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,pA=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);N0(o,n,t)},N0=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${ur[r]} that is the same.`)};var ra=(e,t,r,n)=>{let i=R0(t,r,n).map((l,c)=>mA({stdioOption:l,fdNumber:c,options:t,isSync:n})),a=SA({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=a.map(({stdioItems:l})=>FA(l)),a},mA=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=ps(t),{stdioItems:i,isStdioArray:a}=hA({stdioOption:e,fdNumber:t,options:r,optionName:o}),l=F0(i,t,o),c=i.map(m=>O0({stdioItem:m,isStdioArray:a,fdNumber:t,direction:l,isSync:n})),d=v0(c,o,l,r),p=E0(d,l);return wA(d,p),{direction:l,objectMode:p,stdioItems:d}},hA=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(c=>gA(c,n)),...B0(r,t)],a=I0(i),l=a.length>1;return yA(a,l,n),bA(a),{stdioItems:a,isStdioArray:l}},gA=(e,t)=>({type:p0(e,t),value:e,optionName:t}),yA=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(DA.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},DA=new Set(["ignore","ipc"]),bA=e=>{for(let t of e)_A(t)},_A=({type:e,value:t,optionName:r})=>{if(y0(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(D0(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},wA=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>ea.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},SA=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(EA({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw oc(o),i}},EA=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:a})=>{let l=r.map(c=>vA({stdioItem:c,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:a}));return{direction:e,objectMode:t,stdioItems:l}},vA=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let a=k0({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return a!==void 0?{...e,stream:a}:{...e,...t[r][e.type](e,n)}},oc=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!nt(r)&&r.destroy()},FA=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var L0=(e,t)=>ra(TA,e,t,!0),Et=({type:e,optionName:t})=>{q0(t,ur[e])},CA=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&q0(e,`"${t}"`),{}),q0=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},j0={generator(){},asyncGenerator:Et,webStream:Et,nodeStream:Et,webTransform:Et,duplex:Et,asyncIterable:Et,native:CA},TA={input:{...j0,fileUrl:({value:e})=>({contents:[kt((0,ic.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[kt((0,ic.readFileSync)(e))]}),fileNumber:Et,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...j0,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:Et,string:Et,uint8Array:Et}};var jt=(e,{stripFinalNewline:t},r)=>sc(t,r)&&e!==void 0&&!Array.isArray(e)?hn(e):e,sc=(e,t)=>t==="all"?e[1]||e[2]:e[t];var Ao=require("node:stream");var na=(e,t,r,n)=>e||r?void 0:z0(t,n),uc=(e,t,r)=>r?e.flatMap(n=>U0(n,t)):U0(e,t),U0=(e,t)=>{let{transform:r,final:n}=z0(t,{});return[...r(e),...n()]},z0=(e,t)=>(t.previousChunks="",{transform:RA.bind(void 0,t,e),final:$A.bind(void 0,t)}),RA=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let a=AA(r,i,t,e),l=r.slice(o+1,i+1-a);n.length>0&&(l=ac(n,l),n=""),yield l,o=i}o!==r.length-1&&(n=ac(n,r.slice(o+1))),e.previousChunks=n},AA=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),$A=function*({previousChunks:e}){e.length>0&&(yield e)},W0=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:xA.bind(void 0,n)},xA=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?PA:BA;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},ac=(e,t)=>`${e}${t}`,PA={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:ac},OA=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},BA={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:OA};var V0=require("node:buffer");var G0=(e,t)=>e?void 0:IA.bind(void 0,t),IA=function*(e,t){if(typeof t!="string"&&!we(t)&&!V0.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},H0=(e,t)=>e?kA.bind(void 0,t):MA.bind(void 0,t),kA=function*(e,t){Y0(e,t),yield t},MA=function*(e,t){if(Y0(e,t),typeof t!="string"&&!we(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},Y0=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var K0=require("node:buffer"),J0=require("node:string_decoder");var oa=(e,t,r)=>{if(r)return;if(e)return{transform:NA.bind(void 0,new TextEncoder)};let n=new J0.StringDecoder(t);return{transform:jA.bind(void 0,n),final:LA.bind(void 0,n)}},NA=function*(e,t){K0.Buffer.isBuffer(t)?yield kt(t):typeof t=="string"?yield e.encode(t):yield t},jA=function*(e,t){yield we(t)?e.write(t):t},LA=function*(e){let t=e.end();t!==""&&(yield t)};var lc=require("node:util"),cc=(0,lc.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),ia=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=UA}=t[r];for await(let o of n(e))yield*ia(o,t,r+1)},Q0=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*qA(r,Number(t),e)},qA=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*ia(n,r,t+1)},X0=(0,lc.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),UA=function*(e){yield e};var fc=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},Z0=(e,t)=>[...t.flatMap(r=>[...xr(r,e,0)]),...Ro(e)],xr=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=WA}=t[r];for(let o of n(e))yield*xr(o,t,r+1)},Ro=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*zA(r,Number(t),e)},zA=function*(e,t,r){if(e!==void 0)for(let n of e())yield*xr(n,r,t+1)},WA=function*(e){yield e};var dc=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:a})=>{let l={},c=eD(e,a,i),d=$r(t),p=$r(r),m=d?cc.bind(void 0,ia,l):fc.bind(void 0,xr),b=d||p?cc.bind(void 0,Q0,l):fc.bind(void 0,Ro),D=d||p?X0.bind(void 0,l):void 0;return{stream:new Ao.Transform({writableObjectMode:n,writableHighWaterMark:(0,Ao.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,Ao.getDefaultHighWaterMark)(o),transform(y,S,w){m([y,c,0],this,w)},flush(y){b([c],this,y)},destroy:D})}},sa=(e,t,r,n)=>{let o=t.filter(({type:a})=>a==="generator"),i=n?o.reverse():o;for(let{value:a,optionName:l}of i){let c=eD(a,r,l);e=Z0(c,e)}return e},eD=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},a,l)=>{let c={};return[{transform:G0(n,l)},oa(r,a,n),na(r,i,n,c),{transform:e,final:t},{transform:H0(o,l)},W0({binary:r,preserveNewlines:i,readableObjectMode:o,state:c})].filter(Boolean)};var tD=(e,t)=>{for(let r of VA(e))GA(e,r,t)},VA=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),GA=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:l})=>l!==void 0);if(o.length===0)return;if(t!==0){let[{type:l,optionName:c}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${c}\`, can be ${ur[l]} with synchronous methods.`)}let a=o.map(({contents:l})=>l).map(l=>HA(l,n));r.input=yo(a)},HA=(e,t)=>{let r=sa(e,t,"utf8",!0);return YA(r),yo(r)},YA=e=>{let t=e.find(r=>typeof r!="string"&&!we(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var ua=require("node:fs");var aa=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&un(r,n)&&!je.has(t)&&KA(n)&&(e.some(({type:o,value:i})=>o==="native"&&JA.has(i))||e.every(({type:o})=>ze.has(o))),KA=e=>e===1||e===2,JA=new Set(["pipe","overlapped"]),rD=async(e,t,r,n)=>{for await(let o of e)QA(t)||oD(o,r,n)},nD=(e,t,r)=>{for(let n of e)oD(n,t,r)},QA=e=>e._readableState.pipes.length>0,oD=(e,t,r)=>{let n=bs(e);bt({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var iD=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},a=new Set([]);return{output:t.map((c,d)=>XA({result:c,fileDescriptors:e,fdNumber:d,state:i,outputFiles:a,isMaxBuffer:n,verboseInfo:o},r)),...i}},XA=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:a},{buffer:l,encoding:c,lines:d,stripFinalNewline:p,maxBuffer:m})=>{if(e===null)return;let b=n0(e,i,m),D=kt(b),{stdioItems:g,objectMode:y}=t[r],S=ZA([D],g,c,n),{serializedResult:w,finalResult:T=w}=e$({chunks:S,objectMode:y,encoding:c,lines:d,stripFinalNewline:p,fdNumber:r});t$({serializedResult:w,fdNumber:r,state:n,verboseInfo:a,encoding:c,stdioItems:g,objectMode:y});let R=l[r]?T:void 0;try{return n.error===void 0&&r$(w,g,o),R}catch(A){return n.error=A,R}},ZA=(e,t,r,n)=>{try{return sa(e,t,r,!1)}catch(o){return n.error=o,e}},e$=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:yo(e)};let a=Km(e,r);return n[i]?{serializedResult:a,finalResult:uc(a,!o[i],t)}:{serializedResult:a}},t$=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:a})=>{if(!aa({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let l=uc(e,!1,a);try{nD(l,t,n)}catch(c){r.error??=c}},r$=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>ea.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,ua.appendFileSync)(n,e):(r.add(i),(0,ua.writeFileSync)(n,e))}};var sD=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,jt(t,r,"all")]:Array.isArray(t)?[jt(e,r,"all"),...t]:we(e)&&we(t)?pl([e,t]):`${e}${t}`};var la=require("node:events");var aD=async(e,t)=>{let[r,n]=await n$(e);return t.isForcefullyTerminated??=!1,[r,n]},n$=async e=>{let[t,r]=await Promise.allSettled([(0,la.once)(e,"spawn"),(0,la.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?uD(e):r.value},uD=async e=>{try{return await(0,la.once)(e,"exit")}catch{return uD(e)}},lD=async e=>{let[t,r]=await e;if(!o$(t,r)&&pc(t,r))throw new ot;return[t,r]},o$=(e,t)=>e===void 0&&t===void 0,pc=(e,t)=>e!==0||t!==null;var cD=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=i$(e,t,r),a=i?.code==="ETIMEDOUT",l=r0(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:a,isMaxBuffer:l}},i$=(e,t,r)=>e!==void 0?e:pc(t,r)?new ot:void 0;var dD=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:a,startTime:l,verboseInfo:c,options:d,fileDescriptors:p}=s$(e,t,r),m=l$({file:n,commandArguments:o,options:d,command:i,escapedCommand:a,verboseInfo:c,fileDescriptors:p,startTime:l});return Dn(m,c,d)},s$=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:a}=ws(e,t,r),l=a$(r),{file:c,commandArguments:d,options:p}=Us(e,t,l);u$(p);let m=L0(p,a);return{file:c,commandArguments:d,command:n,escapedCommand:o,startTime:i,verboseInfo:a,options:p,fileDescriptors:m}},a$=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,u$=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&ca("ipcInput"),e&&ca("ipc: true"),r&&ca("detached: true"),n&&ca("cancelSignal")},ca=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},l$=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:a,startTime:l})=>{let c=c$({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:a,startTime:l});if(c.failed)return c;let{resultError:d,exitCode:p,signal:m,timedOut:b,isMaxBuffer:D}=cD(c,r),{output:g,error:y=d}=iD({fileDescriptors:a,syncResult:c,options:r,isMaxBuffer:D,verboseInfo:i}),S=g.map((T,R)=>jt(T,r,R)),w=jt(sD(g,r),r,"all");return d$({error:y,exitCode:p,signal:m,timedOut:b,isMaxBuffer:D,stdio:S,all:w,options:r,command:n,escapedCommand:o,startTime:l})},c$=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:a})=>{try{tD(i,r);let l=f$(r);return(0,fD.spawnSync)(e,t,l)}catch(l){return yn({error:l,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:a,isSync:!0})}},f$=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:Js(t)}),d$=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:a,options:l,command:c,escapedCommand:d,startTime:p})=>e===void 0?Qs({command:c,escapedCommand:d,stdio:i,all:a,ipcOutput:[],options:l,startTime:p}):To({error:e,command:c,escapedCommand:d,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:a,ipcOutput:[],options:l,startTime:p,isSync:!0});var bb=require("node:events"),_b=require("node:child_process");var hc=N(require("node:process"),1);var _n=require("node:events");var pD=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(dn({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:Is(e)}),p$({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),p$=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{xs(t,o);let i=ar(e,t,r),a=new AbortController;try{return await Promise.race([m$(i,n,a),h$(i,r,a),g$(i,r,a)])}catch(l){throw pn(e),l}finally{a.abort(),Ps(t,o)}},m$=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,_n.once)(e,"message",{signal:r});return n}for await(let[n]of(0,_n.on)(e,"message",{signal:r}))if(t(n))return n},h$=async(e,t,{signal:r})=>{await(0,_n.once)(e,"disconnect",{signal:r}),kg(t)},g$=async(e,t,{signal:r})=>{let[n]=await(0,_n.once)(e,"strict:error",{signal:r});throw Rs(n,t)};var $o=require("node:events");var hD=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>mc({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),mc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{dn({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:Is(e)}),xs(t,i);let a=ar(e,t,r),l=new AbortController,c={};return y$(e,a,l),D$({ipcEmitter:a,isSubprocess:r,controller:l,state:c}),b$({anyProcess:e,channel:t,ipcEmitter:a,isSubprocess:r,shouldAwait:o,controller:l,state:c,reference:i})},y$=async(e,t,r)=>{try{await(0,$o.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},D$=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,$o.once)(e,"strict:error",{signal:r.signal});n.error=Rs(o,t),r.abort()}catch{}},b$=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:a,reference:l}){try{for await(let[c]of(0,$o.on)(r,"message",{signal:i.signal}))mD(a),yield c}catch{mD(a)}finally{i.abort(),Ps(t,l),n||pn(e),o&&await e}},mD=({error:e})=>{if(e)throw e};var gD=(e,{ipc:t})=>{Object.assign(e,DD(e,!1,t))},yD=()=>{let e=hc.default,t=!0,r=hc.default.channel!==void 0;return{...DD(e,t,r),getCancelSignal:gy.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},DD=(e,t,r)=>({sendMessage:js.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:pD.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:hD.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var bD=require("node:child_process"),lr=require("node:stream");var _D=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:a})=>{oc(n);let l=new bD.ChildProcess;_$(l,n),Object.assign(l,{readable:w$,writable:S$,duplex:E$});let c=yn({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),d=v$(c,a,o);return{subprocess:l,promise:d}},_$=(e,t)=>{let r=xo(),n=xo(),o=xo(),i=Array.from({length:t.length-3},xo),a=xo(),l=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:a,stdio:l})},xo=()=>{let e=new lr.PassThrough;return e.end(),e},w$=()=>new lr.Readable({read(){}}),S$=()=>new lr.Writable({write(){}}),E$=()=>new lr.Duplex({read(){},write(){}}),v$=async(e,t,r)=>Dn(e,t,r);var wn=require("node:fs"),SD=require("node:buffer"),vt=require("node:stream");var ED=(e,t)=>ra(F$,e,t,!1),Po=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${ur[e]}.`)},wD={fileNumber:Po,generator:dc,asyncGenerator:dc,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:vt.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},F$={input:{...wD,fileUrl:({value:e})=>({stream:(0,wn.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,wn.createReadStream)(e)}),webStream:({value:e})=>({stream:vt.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:vt.Readable.from(e)}),asyncIterable:({value:e})=>({stream:vt.Readable.from(e)}),string:({value:e})=>({stream:vt.Readable.from(e)}),uint8Array:({value:e})=>({stream:vt.Readable.from(SD.Buffer.from(e))})},output:{...wD,fileUrl:({value:e})=>({stream:(0,wn.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,wn.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:vt.Writable.fromWeb(e)}),iterable:Po,asyncIterable:Po,string:Po,uint8Array:Po}};var Oo=require("node:events"),da=require("node:stream"),Dc=require("node:stream/promises");function Pr(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)yc(o);let t=e.some(({readableObjectMode:o})=>o),r=C$(e,t),n=new gc({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var C$=(e,t)=>{if(e.length===0)return(0,da.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},gc=class extends da.PassThrough{#e=new Set([]);#r=new Set([]);#t=new Set([]);#n;#a=Symbol("unpipe");#o=new WeakMap;add(t){if(yc(t),this.#e.has(t))return;this.#e.add(t),this.#n??=T$(this,this.#e,this.#a);let r=$$({passThroughStream:this,stream:t,streams:this.#e,ended:this.#r,aborted:this.#t,onFinished:this.#n,unpipeEvent:this.#a});this.#o.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(yc(t),!this.#e.has(t))return!1;let r=this.#o.get(t);return r===void 0?!1:(this.#o.delete(t),t.unpipe(this),await r,!0)}},T$=async(e,t,r)=>{fa(e,vD);let n=new AbortController;try{await Promise.race([R$(e,n),A$(e,t,r,n)])}finally{n.abort(),fa(e,-vD)}},R$=async(e,{signal:t})=>{try{await(0,Dc.finished)(e,{signal:t,cleanup:!0})}catch(r){throw CD(e,r),r}},A$=async(e,t,r,{signal:n})=>{for await(let[o]of(0,Oo.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},yc=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},$$=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:a})=>{fa(e,FD);let l=new AbortController;try{await Promise.race([x$(i,t,l),P$({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:l}),O$({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:a,controller:l})])}finally{l.abort(),fa(e,-FD)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?bc(e):B$(e))},x$=async(e,t,{signal:r})=>{try{await e,r.aborted||bc(t)}catch(n){r.aborted||CD(t,n)}},P$=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,Dc.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(a){if(i.aborted||!r.has(t))return;TD(a)?o.add(t):RD(e,a)}},O$=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,Oo.once)(e,o,{signal:i}),!e.readable)return(0,Oo.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},B$=e=>{e.writable&&e.end()},CD=(e,t)=>{TD(t)?bc(e):RD(e,t)},TD=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",bc=e=>{(e.readable||e.writable)&&e.destroy()},RD=(e,t)=>{e.destroyed||(e.once("error",I$),e.destroy(t))},I$=()=>{},fa=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},vD=2,FD=1;var _c=require("node:stream/promises");var Sn=(e,t)=>{e.pipe(t),k$(e,t),M$(e,t)},k$=async(e,t)=>{if(!(nt(e)||nt(t))){try{await(0,_c.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}wc(t)}},wc=e=>{e.writable&&e.end()},M$=async(e,t)=>{if(!(nt(e)||nt(t))){try{await(0,_c.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}Sc(e)}},Sc=e=>{e.readable&&e.destroy()};var AD=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:a}]of Object.entries(t)){for(let{stream:l}of i.filter(({type:c})=>ze.has(c)))N$(e,l,a,o);for(let{stream:l}of i.filter(({type:c})=>!ze.has(c)))L$({subprocess:e,stream:l,direction:a,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let a=i.length===1?i[0]:Pr(i);Sn(a,o)}},N$=(e,t,r,n)=>{r==="output"?Sn(e.stdio[n],t):Sn(t,e.stdio[n]);let o=j$[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},j$=["stdin","stdout","stderr"],L$=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;q$(t,i);let[a,l]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],c=o.get(a)??[];o.set(a,[...c,l])},q$=(e,{signal:t})=>{nt(e)&&Rr(e,U$,t)},U$=2;var $D=require("node:events");var Or=[];Or.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&Or.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&Or.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var pa=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",Ec=Symbol.for("signal-exit emitter"),vc=globalThis,z$=Object.defineProperty.bind(Object),Fc=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(vc[Ec])return vc[Ec];z$(vc,Ec,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},ma=class{},W$=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),Cc=class extends ma{onExit(){return()=>{}}load(){}unload(){}},Tc=class extends ma{#e=Rc.platform==="win32"?"SIGINT":"SIGHUP";#r=new Fc;#t;#n;#a;#o={};#s=!1;constructor(t){super(),this.#t=t,this.#o={};for(let r of Or)this.#o[r]=()=>{let n=this.#t.listeners(r),{count:o}=this.#r,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let a=this.#r.emit("exit",null,r),l=r==="SIGHUP"?this.#e:r;a||t.kill(t.pid,l)}};this.#a=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!pa(this.#t))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#r.on(n,t),()=>{this.#r.removeListener(n,t),this.#r.listeners.exit.length===0&&this.#r.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#r.count+=1;for(let t of Or)try{let r=this.#o[t];r&&this.#t.on(t,r)}catch{}this.#t.emit=(t,...r)=>this.#p(t,...r),this.#t.reallyExit=t=>this.#i(t)}}unload(){this.#s&&(this.#s=!1,Or.forEach(t=>{let r=this.#o[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#t.removeListener(t,r)}catch{}}),this.#t.emit=this.#n,this.#t.reallyExit=this.#a,this.#r.count-=1)}#i(t){return pa(this.#t)?(this.#t.exitCode=t||0,this.#r.emit("exit",this.#t.exitCode,null),this.#a.call(this.#t,this.#t.exitCode)):0}#p(t,...r){let n=this.#n;if(t==="exit"&&pa(this.#t)){typeof r[0]=="number"&&(this.#t.exitCode=r[0]);let o=n.call(this.#t,t,...r);return this.#r.emit("exit",this.#t.exitCode,null),o}else return n.call(this.#t,t,...r)}},Rc=globalThis.process,{onExit:ha,load:gq,unload:yq}=W$(pa(Rc)?new Tc(Rc):new Cc);var xD=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=ha(()=>{e.kill()});(0,$D.addAbortListener)(n,()=>{o()})};var OD=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=_s(),{destination:a,destinationStream:l,destinationError:c,from:d,unpipeSignal:p}=V$(r,n,o),{sourceStream:m,sourceError:b}=H$(e,d),{options:D,fileDescriptors:g}=wt.get(e);return{sourcePromise:t,sourceStream:m,sourceOptions:D,sourceError:b,destination:a,destinationStream:l,destinationError:c,unpipeSignal:p,fileDescriptors:g,startTime:i}},V$=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:a}={}}=G$(e,t,...r),l=$s(n,i);return{destination:n,destinationStream:l,from:o,unpipeSignal:a}}catch(n){return{destinationError:n}}},G$=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(PD,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||fl(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,a]=ls(r,...n);return{destination:t(PD)(o,i,a),pipeOptions:a}}if(wt.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},PD=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),H$=(e,t)=>{try{return{sourceStream:mn(e,t)}}catch(r){return{sourceError:r}}};var ID=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:a})=>{let l=Y$({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(l!==void 0)throw Ac({error:l,fileDescriptors:o,sourceOptions:i,startTime:a})},Y$=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return Sc(e),n;if(t!==void 0)return wc(r),t},Ac=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>yn({error:e,command:BD,escapedCommand:BD,fileDescriptors:t,options:r,startTime:n,isSync:!1}),BD="source.pipe(destination)";var kD=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:a=i}]=await e;if(a.pipedFrom.includes(n)||a.pipedFrom.push(n),o==="rejected")throw a;if(t==="rejected")throw n;return a};var MD=require("node:stream/promises");var ND=(e,t,r)=>{let n=ga.has(t)?J$(e,t):K$(e,t);return Rr(e,X$,r.signal),Rr(t,Z$,r.signal),Q$(t),n},K$=(e,t)=>{let r=Pr([e]);return Sn(r,t),ga.set(t,r),r},J$=(e,t)=>{let r=ga.get(t);return r.add(e),r},Q$=async e=>{try{await(0,MD.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}ga.delete(e)},ga=new WeakMap,X$=2,Z$=1;var jD=require("node:util");var LD=(e,t)=>e===void 0?[]:[e1(e,t)],e1=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,jD.aborted)(e,t),await r.remove(t);let a=new Error("Pipe canceled by `unpipeSignal` option.");throw Ac({error:a,fileDescriptors:n,sourceOptions:o,startTime:i})};var ya=(e,...t)=>{if(ge(t[0]))return ya.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=OD(e,...t),o=t1({...n,destination:r});return o.pipe=ya.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},t1=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:a,unpipeSignal:l,fileDescriptors:c,startTime:d})=>{let p=r1(e,o);ID({sourceStream:t,sourceError:n,destinationStream:i,destinationError:a,fileDescriptors:c,sourceOptions:r,startTime:d});let m=new AbortController;try{let b=ND(t,i,m);return await Promise.race([kD(p),...LD(l,{sourceStream:t,mergedStream:b,sourceOptions:r,fileDescriptors:c,startTime:d})])}finally{m.abort()}},r1=(e,t)=>Promise.allSettled([e,t]);var VD=require("node:timers/promises");var UD=require("node:events"),zD=require("node:stream");var Da=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let a=new AbortController;return n1(t,a),WD({stream:e,controller:a,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},n1=async(e,t)=>{try{await e}catch{}finally{t.abort()}},$c=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let a=new AbortController;o1(t,a,e);let l=e.readableObjectMode&&!i;return WD({stream:e,controller:a,binary:n==="buffer",shouldEncode:!l,encoding:n,shouldSplit:!l&&r,preserveNewlines:!o})},o1=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},WD=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a})=>{let l=(0,UD.on)(e,"data",{signal:t.signal,highWaterMark:qD,highWatermark:qD});return i1({onStdoutChunk:l,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a})},xc=(0,zD.getDefaultHighWaterMark)(!0),qD=xc,i1=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a}){let l=s1({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:a});try{for await(let[c]of e)yield*xr(c,l,0)}catch(c){if(!t.signal.aborted)throw c}finally{yield*Ro(l)}},s1=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[oa(e,r,!t),na(e,o,!n,{})].filter(Boolean);var GD=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:a,allMixed:l,stripFinalNewline:c,verboseInfo:d,streamInfo:p})=>{let m=a1({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:l,verboseInfo:d,streamInfo:p});if(!o){await Promise.all([u1(e),m]);return}let b=sc(c,r),D=$c({stream:e,onStreamEnd:t,lines:a,encoding:n,stripFinalNewline:b,allMixed:l}),[g]=await Promise.all([l1({stream:e,iterable:D,fdNumber:r,encoding:n,maxBuffer:i,lines:a}),m]);return g},a1=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:a}})=>{if(!aa({stdioItems:a[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let l=$c({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await rD(l,e,r,i)},u1=async e=>{await(0,VD.setImmediate)(),e.readableFlowing===null&&e.resume()},l1=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:a})=>{try{return t||a?await Gs(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await Hs(r,{maxBuffer:i})):await Ks(r,{maxBuffer:i})}catch(l){return HD(Zy({error:l,stream:e,readableObjectMode:t,lines:a,encoding:o,fdNumber:n}))}},Pc=async e=>{try{return await e}catch(t){return HD(t)}},HD=({bufferedData:e})=>Hm(e)?new Uint8Array(e):e;var KD=require("node:stream/promises"),Bo=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=c1(e,r),a=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,KD.finished)(e,{cleanup:!0,signal:a.signal})])}catch(l){i.stdinCleanedUp||p1(l,t,r,n)}finally{a.abort()}},c1=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&f1(e,r,n),n},f1=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{d1(t,r),n.call(e,...o)}},d1=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},p1=(e,t,r,n)=>{if(!m1(e,t,r,n))throw e},m1=(e,t,r,n=!0)=>r.propagating?YD(e)||ba(e):(r.propagating=!0,Oc(r,t)===n?YD(e):ba(e)),Oc=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",ba=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",YD=e=>e?.code==="EPIPE";var JD=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:a,streamInfo:l})=>e.stdio.map((c,d)=>Bc({stream:c,fdNumber:d,encoding:t,buffer:r[d],maxBuffer:n[d],lines:o[d],allMixed:!1,stripFinalNewline:i,verboseInfo:a,streamInfo:l})),Bc=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:a,stripFinalNewline:l,verboseInfo:c,streamInfo:d})=>{if(!e)return;let p=Bo(e,t,d);if(Oc(d,t)){await p;return}let[m]=await Promise.all([GD({stream:e,onStreamEnd:p,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:a,stripFinalNewline:l,verboseInfo:c,streamInfo:d}),p]);return m};var QD=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?Pr([e,t].filter(Boolean)):void 0,XD=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:a,streamInfo:l})=>Bc({...h1(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:g1(e),stripFinalNewline:i,verboseInfo:a,streamInfo:l}),h1=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},g1=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var nb=require("node:events");var ZD=e=>un(e,"ipc"),eb=(e,t)=>{let r=bs(e);bt({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var tb=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let a=ZD(i),l=Mt(t,"ipc"),c=Mt(r,"ipc");for await(let d of mc({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))l&&(e0(e,o,c),o.push(d)),a&&eb(d,i);return o},rb=async(e,t)=>(await Promise.allSettled([e]),t);var ob=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:a,gracefulCancel:l,forceKillAfterDelay:c,stripFinalNewline:d,ipc:p,ipcInput:m},context:b,verboseInfo:D,fileDescriptors:g,originalStreams:y,onInternalError:S,controller:w})=>{let T=aD(e,b),R={originalStreams:y,fileDescriptors:g,subprocess:e,exitPromise:T,propagating:!1},A=JD({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:d,verboseInfo:D,streamInfo:R}),v=XD({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:d,verboseInfo:D,streamInfo:R}),B=[],W=tb({subprocess:e,buffer:r,maxBuffer:n,ipc:p,ipcOutput:B,verboseInfo:D}),ae=y1(y,e,R),P=D1(g,R);try{return await Promise.race([Promise.all([{},lD(T),Promise.all(A),v,W,Ty(e,m),...ae,...P]),S,b1(e,w),...Sy(e,i,b,w),...Ig({subprocess:e,cancelSignal:a,gracefulCancel:l,context:b,controller:w}),...by({subprocess:e,cancelSignal:a,gracefulCancel:l,forceKillAfterDelay:c,context:b,controller:w})])}catch(x){return b.terminationReason??="other",Promise.all([{error:x},T,Promise.all(A.map(k=>Pc(k))),Pc(v),rb(W,B),Promise.allSettled(ae),Promise.allSettled(P)])}},y1=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:Bo(n,o,r)),D1=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>st(i,{checkOpen:!1})&&!nt(i)).map(({type:o,value:i,stream:a=i})=>Bo(a,n,t,{isSameDirection:ze.has(o),stopOnExit:o==="native"}))),b1=async(e,{signal:t})=>{let[r]=await(0,nb.once)(e,"error",{signal:t});throw r};var ib=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),Io=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=_t();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},En=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var ab=require("node:stream"),ub=require("node:util");var Ic=require("node:stream/promises");var kc=async e=>{if(e!==void 0)try{await Mc(e)}catch{}},sb=async e=>{if(e!==void 0)try{await Nc(e)}catch{}},Mc=async e=>{await(0,Ic.finished)(e,{cleanup:!0,readable:!1,writable:!0})},Nc=async e=>{await(0,Ic.finished)(e,{cleanup:!0,readable:!0,writable:!1})},_a=async(e,t)=>{if(await e,t)throw t},wa=(e,t,r)=>{r&&!ba(r)?e.destroy(r):t&&e.destroy()};var lb=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let a=o||je.has(r),{subprocessStdout:l,waitReadableDestroy:c}=jc(e,n,t),{readableEncoding:d,readableObjectMode:p,readableHighWaterMark:m}=Lc(l,a),{read:b,onStdoutDataDone:D}=qc({subprocessStdout:l,subprocess:e,binary:a,encoding:r,preserveNewlines:i}),g=new ab.Readable({read:b,destroy:(0,ub.callbackify)(zc.bind(void 0,{subprocessStdout:l,subprocess:e,waitReadableDestroy:c})),highWaterMark:m,objectMode:p,encoding:d});return Uc({subprocessStdout:l,onStdoutDataDone:D,readable:g,subprocess:e}),g},jc=(e,t,r)=>{let n=mn(e,t),o=Io(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},Lc=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:xc},qc=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=_t(),a=Da({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){_1(this,a,i)},onStdoutDataDone:i}},_1=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},Uc=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await Nc(e),await n,await kc(o),await t,r.readable&&r.push(null)}catch(i){await kc(o),cb(r,i)}},zc=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await En(r,t)&&(cb(e,n),await _a(t,n))},cb=(e,t)=>{wa(e,e.readable,t)};var fb=require("node:stream"),Wc=require("node:util");var db=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Vc(e,r,t),a=new fb.Writable({...Gc(n,e,o),destroy:(0,Wc.callbackify)(Yc.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return Hc(n,a),a},Vc=(e,t,r)=>{let n=$s(e,t),o=Io(r,n,"writableFinal"),i=Io(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Gc=(e,t,r)=>({write:w1.bind(void 0,e),final:(0,Wc.callbackify)(S1.bind(void 0,e,t,r))}),w1=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},S1=async(e,t,r)=>{await En(r,t)&&(e.writable&&e.end(),await t)},Hc=async(e,t,r)=>{try{await Mc(e),t.writable&&t.end()}catch(n){await sb(r),pb(t,n)}},Yc=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await En(r,t),await En(n,t)&&(pb(e,o),await _a(t,o))},pb=(e,t)=>{wa(e,e.writable,t)};var mb=require("node:stream"),hb=require("node:util");var gb=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:a=!0}={})=>{let l=i||je.has(r),{subprocessStdout:c,waitReadableDestroy:d}=jc(e,n,t),{subprocessStdin:p,waitWritableFinal:m,waitWritableDestroy:b}=Vc(e,o,t),{readableEncoding:D,readableObjectMode:g,readableHighWaterMark:y}=Lc(c,l),{read:S,onStdoutDataDone:w}=qc({subprocessStdout:c,subprocess:e,binary:l,encoding:r,preserveNewlines:a}),T=new mb.Duplex({read:S,...Gc(p,e,m),destroy:(0,hb.callbackify)(E1.bind(void 0,{subprocessStdout:c,subprocessStdin:p,subprocess:e,waitReadableDestroy:d,waitWritableFinal:m,waitWritableDestroy:b})),readableHighWaterMark:y,writableHighWaterMark:p.writableHighWaterMark,readableObjectMode:g,writableObjectMode:p.writableObjectMode,encoding:D});return Uc({subprocessStdout:c,onStdoutDataDone:w,readable:T,subprocess:e,subprocessStdin:p}),Hc(p,T,c),T},E1=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},a)=>{await Promise.all([zc({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},a),Yc({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},a)])};var Kc=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||je.has(t),a=mn(e,r),l=Da({subprocessStdout:a,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return v1(l,a,e)},v1=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var yb=(e,{encoding:t})=>{let r=ib();e.readable=lb.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=db.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=gb.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=Kc.bind(void 0,e,t),e[Symbol.asyncIterator]=Kc.bind(void 0,e,t,{})};var Db=(e,t)=>{for(let[r,n]of C1){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},F1=(async()=>{})().constructor.prototype,C1=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(F1,e)]);var wb=(e,t,r,n)=>{let{file:o,commandArguments:i,command:a,escapedCommand:l,startTime:c,verboseInfo:d,options:p,fileDescriptors:m}=T1(e,t,r),{subprocess:b,promise:D}=A1({file:o,commandArguments:i,options:p,startTime:c,verboseInfo:d,command:a,escapedCommand:l,fileDescriptors:m});return b.pipe=ya.bind(void 0,{source:b,sourcePromise:D,boundOptions:{},createNested:n}),Db(b,D),wt.set(b,{options:p,fileDescriptors:m}),b},T1=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:a}=ws(e,t,r),{file:l,commandArguments:c,options:d}=Us(e,t,r),p=R1(d),m=ED(p,a);return{file:l,commandArguments:c,command:n,escapedCommand:o,startTime:i,verboseInfo:a,options:p,fileDescriptors:m}},R1=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},A1=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:a,fileDescriptors:l})=>{let c;try{c=(0,_b.spawn)(e,t,r)}catch(g){return _D({error:g,command:i,escapedCommand:a,fileDescriptors:l,options:r,startTime:n,verboseInfo:o})}let d=new AbortController;(0,bb.setMaxListeners)(Number.POSITIVE_INFINITY,d.signal);let p=[...c.stdio];AD(c,l,d),xD(c,r,d);let m={},b=_t();c.kill=Pg.bind(void 0,{kill:c.kill.bind(c),options:r,onInternalError:b,context:m,controller:d}),c.all=QD(c,r),yb(c,r),gD(c,r);let D=$1({subprocess:c,options:r,startTime:n,verboseInfo:o,fileDescriptors:l,originalStreams:p,command:i,escapedCommand:a,context:m,onInternalError:b,controller:d});return{subprocess:c,promise:D}},$1=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:a,escapedCommand:l,context:c,onInternalError:d,controller:p})=>{let[m,[b,D],g,y,S]=await ob({subprocess:e,options:t,context:c,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:d,controller:p});p.abort(),d.resolve();let w=g.map((A,v)=>jt(A,t,v)),T=jt(y,t,"all"),R=x1({errorInfo:m,exitCode:b,signal:D,stdio:w,all:T,ipcOutput:S,context:c,options:t,command:a,escapedCommand:l,startTime:r});return Dn(R,n,t)},x1=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:a,options:l,command:c,escapedCommand:d,startTime:p})=>"error"in e?To({error:e.error,command:c,escapedCommand:d,timedOut:a.terminationReason==="timeout",isCanceled:a.terminationReason==="cancel"||a.terminationReason==="gracefulCancel",isGracefullyCanceled:a.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof St,isForcefullyTerminated:a.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:l,startTime:p,isSync:!1}):Qs({command:c,escapedCommand:d,stdio:n,all:o,ipcOutput:i,options:l,startTime:p});var Sa=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,P1(n,e[n],o)]));return{...e,...r}},P1=(e,t,r)=>O1.has(e)&&ge(t)&&ge(r)?{...t,...r}:r,O1=new Set(["env",...gl]);var cr=(e,t,r,n)=>{let o=(a,l,c)=>cr(a,l,r,c),i=(...a)=>B1({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...a);return n!==void 0&&n(i,o,t),i},B1=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...a)=>{if(ge(i))return o(e,Sa(r,i),n);let{file:l,commandArguments:c,options:d,isSync:p}=I1({mapArguments:e,firstArgument:i,nextArguments:a,deepOptions:t,boundOptions:r});return p?dD(l,c,d):wb(l,c,d,o)},I1=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=eh(t)?th(t,r):[t,...r],[a,l,c]=ls(...i),d=Sa(Sa(n,o),c),{file:p=a,commandArguments:m=l,options:b=d,isSync:D=!1}=e({file:a,commandArguments:l,options:d});return{file:p,commandArguments:m,options:b,isSync:D}};var Sb=({file:e,commandArguments:t})=>vb(e,t),Eb=({file:e,commandArguments:t})=>({...vb(e,t),isSync:!0}),vb=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=k1(e);return{file:r,commandArguments:n}},k1=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(M1)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},M1=/ +/g;var Fb=(e,t,r)=>{e.sync=t(N1,r),e.s=e.sync},Cb=({options:e})=>Tb(e),N1=({options:e})=>({...Tb(e),isSync:!0}),Tb=e=>({options:{...j1(e),...e}}),j1=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Rb={preferLocal:!0};var Ea=cr(()=>({})),U9=cr(()=>({isSync:!0})),z9=cr(Sb),W9=cr(Eb),V9=cr(Ey),G9=cr(Cb,{},Rb,Fb),{sendMessage:H9,getOneMessage:Y9,getEachMessage:K9,getCancelSignal:J9}=yD();var _e=N(require("node:path")),Xe=N(require("node:fs")),QE=N($b()),Iu=N(Xc()),XE=N(hf()),ZE=N(LS()),Vr=N(zS());var fe=N(Xc());var Ci=N(require("node:process"),1);var WS=(e=0)=>t=>`\x1B[${t+e}m`,VS=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,GS=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,de={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},Rz=Object.keys(de.modifier),uk=Object.keys(de.color),lk=Object.keys(de.bgColor),Az=[...uk,...lk];function ck(){let e=new Map;for(let[t,r]of Object.entries(de)){for(let[n,o]of Object.entries(r))de[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=de[n],e.set(o[0],o[1]);Object.defineProperty(de,t,{value:r,enumerable:!1})}return Object.defineProperty(de,"codes",{value:e,enumerable:!1}),de.color.close="\x1B[39m",de.bgColor.close="\x1B[49m",de.color.ansi=WS(),de.color.ansi256=VS(),de.color.ansi16m=GS(),de.bgColor.ansi=WS(10),de.bgColor.ansi256=VS(10),de.bgColor.ansi16m=GS(10),Object.defineProperties(de,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>de.rgbToAnsi256(...de.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let l=t%36;r=Math.floor(t/36)/5,n=Math.floor(l/6)/5,o=l%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let a=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(a+=60),a},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>de.ansi256ToAnsi(de.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>de.ansi256ToAnsi(de.hexToAnsi256(t)),enumerable:!1}}),de}var fk=ck(),mt=fk;var bu=N(require("node:process"),1),YS=N(require("node:os"),1),Zd=N(require("node:tty"),1);function Ke(e,t=globalThis.Deno?globalThis.Deno.args:bu.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:me}=bu.default,Du;Ke("no-color")||Ke("no-colors")||Ke("color=false")||Ke("color=never")?Du=0:(Ke("color")||Ke("colors")||Ke("color=true")||Ke("color=always"))&&(Du=1);function dk(){if("FORCE_COLOR"in me)return me.FORCE_COLOR==="true"?1:me.FORCE_COLOR==="false"?0:me.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(me.FORCE_COLOR,10),3)}function pk(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function mk(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=dk();n!==void 0&&(Du=n);let o=r?Du:n;if(o===0)return 0;if(r){if(Ke("color=16m")||Ke("color=full")||Ke("color=truecolor"))return 3;if(Ke("color=256"))return 2}if("TF_BUILD"in me&&"AGENT_NAME"in me)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(me.TERM==="dumb")return i;if(bu.default.platform==="win32"){let a=YS.default.release().split(".");return Number(a[0])>=10&&Number(a[2])>=10586?Number(a[2])>=14931?3:2:1}if("CI"in me)return"GITHUB_ACTIONS"in me||"GITEA_ACTIONS"in me?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(a=>a in me)||me.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in me)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(me.TEAMCITY_VERSION)?1:0;if(me.COLORTERM==="truecolor"||me.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in me){let a=Number.parseInt((me.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(me.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(me.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(me.TERM)||"COLORTERM"in me?1:i}function HS(e,t={}){let r=mk(e,{streamIsTTY:e&&e.isTTY,...t});return pk(r)}var hk={stdout:HS({isTTY:Zd.default.isatty(1)}),stderr:HS({isTTY:Zd.default.isatty(2)})},KS=hk;function JS(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,a="";do a+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return a+=e.slice(i),a}function QS(e,t,r,n){let o=0,i="";do{let a=e[n-1]==="\r";i+=e.slice(o,a?n-1:n)+t+(a?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:XS,stderr:ZS}=KS,ep=Symbol("GENERATOR"),Vn=Symbol("STYLER"),bi=Symbol("IS_EMPTY"),eE=["ansi","ansi","ansi256","ansi16m"],Gn=Object.create(null),gk=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=XS?XS.level:0;e.level=t.level===void 0?r:t.level};var yk=e=>{let t=(...r)=>r.join(" ");return gk(t,e),Object.setPrototypeOf(t,_i.prototype),t};function _i(e){return yk(e)}Object.setPrototypeOf(_i.prototype,Function.prototype);for(let[e,t]of Object.entries(mt))Gn[e]={get(){let r=_u(this,rp(t.open,t.close,this[Vn]),this[bi]);return Object.defineProperty(this,e,{value:r}),r}};Gn.visible={get(){let e=_u(this,this[Vn],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var tp=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?mt[r].ansi16m(...n):t==="ansi256"?mt[r].ansi256(mt.rgbToAnsi256(...n)):mt[r].ansi(mt.rgbToAnsi(...n)):e==="hex"?tp("rgb",t,r,...mt.hexToRgb(...n)):mt[r][e](...n),Dk=["rgb","hex","ansi256"];for(let e of Dk){Gn[e]={get(){let{level:r}=this;return function(...n){let o=rp(tp(e,eE[r],"color",...n),mt.color.close,this[Vn]);return _u(this,o,this[bi])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);Gn[t]={get(){let{level:r}=this;return function(...n){let o=rp(tp(e,eE[r],"bgColor",...n),mt.bgColor.close,this[Vn]);return _u(this,o,this[bi])}}}}var bk=Object.defineProperties(()=>{},{...Gn,level:{enumerable:!0,get(){return this[ep].level},set(e){this[ep].level=e}}}),rp=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},_u=(e,t,r)=>{let n=(...o)=>_k(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,bk),n[ep]=e,n[Vn]=t,n[bi]=r,n},_k=(e,t)=>{if(e.level<=0||!t)return e[bi]?"":t;let r=e[Vn];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=JS(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=QS(t,o,n,i)),n+t+o};Object.defineProperties(_i.prototype,Gn);var wk=_i(),kz=_i({level:ZS?ZS.level:0});var tE=wk;var op=N(require("node:process"),1);var wi=N(require("node:process"),1);var Sk=(e,t,r,n)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;let o=Object.getOwnPropertyDescriptor(e,r),i=Object.getOwnPropertyDescriptor(t,r);!Ek(o,i)&&n||Object.defineProperty(e,r,i)},Ek=function(e,t){return e===void 0||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},vk=(e,t)=>{let r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},Fk=(e,t)=>`/* Wrapped ${e}*/
${t}`,Ck=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),Tk=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),Rk=(e,t,r)=>{let n=r===""?"":`with ${r.trim()}() `,o=Fk.bind(null,n,t.toString());Object.defineProperty(o,"name",Tk);let{writable:i,enumerable:a,configurable:l}=Ck;Object.defineProperty(e,"toString",{value:o,writable:i,enumerable:a,configurable:l})};function np(e,t,{ignoreNonConfigurable:r=!1}={}){let{name:n}=e;for(let o of Reflect.ownKeys(t))Sk(e,t,o,r);return vk(e,t),Rk(e,t,n),e}var wu=new WeakMap,rE=(e,t={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let r,n=0,o=e.displayName||e.name||"<anonymous>",i=function(...a){if(wu.set(i,++n),n===1)r=e.apply(this,a),e=void 0;else if(t.throw===!0)throw new Error(`Function \`${o}\` can only be called once`);return r};return np(i,e),wu.set(i,n),i};rE.callCount=e=>{if(!wu.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return wu.get(e)};var nE=rE;var oE=wi.default.stderr.isTTY?wi.default.stderr:wi.default.stdout.isTTY?wi.default.stdout:void 0,Ak=oE?nE(()=>{ha(()=>{oE.write("\x1B[?25h")},{alwaysLast:!0})}):()=>{},iE=Ak;var Su=!1,Hn={};Hn.show=(e=op.default.stderr)=>{e.isTTY&&(Su=!1,e.write("\x1B[?25h"))};Hn.hide=(e=op.default.stderr)=>{e.isTTY&&(iE(),Su=!0,e.write("\x1B[?25l"))};Hn.toggle=(e,t)=>{e!==void 0&&(Su=e),Su?Hn.show(t):Hn.hide(t)};var ip=Hn;var Ti=N(sp(),1);var lE=(e=0)=>t=>`\x1B[${t+e}m`,cE=(e=0)=>t=>`\x1B[${38+e};5;${t}m`,fE=(e=0)=>(t,r,n)=>`\x1B[${38+e};2;${t};${r};${n}m`,pe={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},Yz=Object.keys(pe.modifier),xk=Object.keys(pe.color),Pk=Object.keys(pe.bgColor),Kz=[...xk,...Pk];function Ok(){let e=new Map;for(let[t,r]of Object.entries(pe)){for(let[n,o]of Object.entries(r))pe[n]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},r[n]=pe[n],e.set(o[0],o[1]);Object.defineProperty(pe,t,{value:r,enumerable:!1})}return Object.defineProperty(pe,"codes",{value:e,enumerable:!1}),pe.color.close="\x1B[39m",pe.bgColor.close="\x1B[49m",pe.color.ansi=lE(),pe.color.ansi256=cE(),pe.color.ansi16m=fE(),pe.bgColor.ansi=lE(10),pe.bgColor.ansi256=cE(10),pe.bgColor.ansi16m=fE(10),Object.defineProperties(pe,{rgbToAnsi256:{value(t,r,n){return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},enumerable:!1},hexToRgb:{value(t){let r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));let o=Number.parseInt(n,16);return[o>>16&255,o>>8&255,o&255]},enumerable:!1},hexToAnsi256:{value:t=>pe.rgbToAnsi256(...pe.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value(t){if(t<8)return 30+t;if(t<16)return 90+(t-8);let r,n,o;if(t>=232)r=((t-232)*10+8)/255,n=r,o=r;else{t-=16;let l=t%36;r=Math.floor(t/36)/5,n=Math.floor(l/6)/5,o=l%6/5}let i=Math.max(r,n,o)*2;if(i===0)return 30;let a=30+(Math.round(o)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(a+=60),a},enumerable:!1},rgbToAnsi:{value:(t,r,n)=>pe.ansi256ToAnsi(pe.rgbToAnsi256(t,r,n)),enumerable:!1},hexToAnsi:{value:t=>pe.ansi256ToAnsi(pe.hexToAnsi256(t)),enumerable:!1}}),pe}var Bk=Ok(),ht=Bk;var Fu=N(require("node:process"),1),pE=N(require("node:os"),1),ap=N(require("node:tty"),1);function Je(e,t=globalThis.Deno?globalThis.Deno.args:Fu.default.argv){let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),o=t.indexOf("--");return n!==-1&&(o===-1||n<o)}var{env:he}=Fu.default,vu;Je("no-color")||Je("no-colors")||Je("color=false")||Je("color=never")?vu=0:(Je("color")||Je("colors")||Je("color=true")||Je("color=always"))&&(vu=1);function Ik(){if("FORCE_COLOR"in he)return he.FORCE_COLOR==="true"?1:he.FORCE_COLOR==="false"?0:he.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(he.FORCE_COLOR,10),3)}function kk(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Mk(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=Ik();n!==void 0&&(vu=n);let o=r?vu:n;if(o===0)return 0;if(r){if(Je("color=16m")||Je("color=full")||Je("color=truecolor"))return 3;if(Je("color=256"))return 2}if("TF_BUILD"in he&&"AGENT_NAME"in he)return 1;if(e&&!t&&o===void 0)return 0;let i=o||0;if(he.TERM==="dumb")return i;if(Fu.default.platform==="win32"){let a=pE.default.release().split(".");return Number(a[0])>=10&&Number(a[2])>=10586?Number(a[2])>=14931?3:2:1}if("CI"in he)return"GITHUB_ACTIONS"in he||"GITEA_ACTIONS"in he?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(a=>a in he)||he.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in he)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(he.TEAMCITY_VERSION)?1:0;if(he.COLORTERM==="truecolor"||he.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in he){let a=Number.parseInt((he.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(he.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(he.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(he.TERM)||"COLORTERM"in he?1:i}function dE(e,t={}){let r=Mk(e,{streamIsTTY:e&&e.isTTY,...t});return kk(r)}var Nk={stdout:dE({isTTY:ap.default.isatty(1)}),stderr:dE({isTTY:ap.default.isatty(2)})},mE=Nk;function hE(e,t,r){let n=e.indexOf(t);if(n===-1)return e;let o=t.length,i=0,a="";do a+=e.slice(i,n)+t+r,i=n+o,n=e.indexOf(t,i);while(n!==-1);return a+=e.slice(i),a}function gE(e,t,r,n){let o=0,i="";do{let a=e[n-1]==="\r";i+=e.slice(o,a?n-1:n)+t+(a?`\r
`:`
`)+r,o=n+1,n=e.indexOf(`
`,o)}while(n!==-1);return i+=e.slice(o),i}var{stdout:yE,stderr:DE}=mE,up=Symbol("GENERATOR"),Yn=Symbol("STYLER"),Si=Symbol("IS_EMPTY"),bE=["ansi","ansi","ansi256","ansi16m"],Kn=Object.create(null),jk=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");let r=yE?yE.level:0;e.level=t.level===void 0?r:t.level};var Lk=e=>{let t=(...r)=>r.join(" ");return jk(t,e),Object.setPrototypeOf(t,Ei.prototype),t};function Ei(e){return Lk(e)}Object.setPrototypeOf(Ei.prototype,Function.prototype);for(let[e,t]of Object.entries(ht))Kn[e]={get(){let r=Cu(this,cp(t.open,t.close,this[Yn]),this[Si]);return Object.defineProperty(this,e,{value:r}),r}};Kn.visible={get(){let e=Cu(this,this[Yn],!0);return Object.defineProperty(this,"visible",{value:e}),e}};var lp=(e,t,r,...n)=>e==="rgb"?t==="ansi16m"?ht[r].ansi16m(...n):t==="ansi256"?ht[r].ansi256(ht.rgbToAnsi256(...n)):ht[r].ansi(ht.rgbToAnsi(...n)):e==="hex"?lp("rgb",t,r,...ht.hexToRgb(...n)):ht[r][e](...n),qk=["rgb","hex","ansi256"];for(let e of qk){Kn[e]={get(){let{level:r}=this;return function(...n){let o=cp(lp(e,bE[r],"color",...n),ht.color.close,this[Yn]);return Cu(this,o,this[Si])}}};let t="bg"+e[0].toUpperCase()+e.slice(1);Kn[t]={get(){let{level:r}=this;return function(...n){let o=cp(lp(e,bE[r],"bgColor",...n),ht.bgColor.close,this[Yn]);return Cu(this,o,this[Si])}}}}var Uk=Object.defineProperties(()=>{},{...Kn,level:{enumerable:!0,get(){return this[up].level},set(e){this[up].level=e}}}),cp=(e,t,r)=>{let n,o;return r===void 0?(n=e,o=t):(n=r.openAll+e,o=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:o,parent:r}},Cu=(e,t,r)=>{let n=(...o)=>zk(n,o.length===1?""+o[0]:o.join(" "));return Object.setPrototypeOf(n,Uk),n[up]=e,n[Yn]=t,n[Si]=r,n},zk=(e,t)=>{if(e.level<=0||!t)return e[Si]?"":t;let r=e[Yn];if(r===void 0)return t;let{openAll:n,closeAll:o}=r;if(t.includes("\x1B"))for(;r!==void 0;)t=hE(t,r.close,r.open),r=r.parent;let i=t.indexOf(`
`);return i!==-1&&(t=gE(t,o,n,i)),n+t+o};Object.defineProperties(Ei.prototype,Kn);var Wk=Ei(),rW=Ei({level:DE?DE.level:0});var Ht=Wk;var Qe=N(require("node:process"),1);function fp(){return Qe.default.platform!=="win32"?Qe.default.env.TERM!=="linux":!!Qe.default.env.CI||!!Qe.default.env.WT_SESSION||!!Qe.default.env.TERMINUS_SUBLIME||Qe.default.env.ConEmuTask==="{cmd::Cmder}"||Qe.default.env.TERM_PROGRAM==="Terminus-Sublime"||Qe.default.env.TERM_PROGRAM==="vscode"||Qe.default.env.TERM==="xterm-256color"||Qe.default.env.TERM==="alacritty"||Qe.default.env.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var Vk={info:Ht.blue("\u2139"),success:Ht.green("\u2714"),warning:Ht.yellow("\u26A0"),error:Ht.red("\u2716")},Gk={info:Ht.blue("i"),success:Ht.green("\u221A"),warning:Ht.yellow("\u203C"),error:Ht.red("\xD7")},Hk=fp()?Vk:Gk,vi=Hk;function dp({onlyFirst:e=!1}={}){let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var Yk=dp();function Fi(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(Yk,"")}function _E(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function wE(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function SE(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function Kk(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function EE(e,{ambiguousAsWide:t=!1}={}){return Kk(e),wE(e)||SE(e)||t&&_E(e)?2:1}var CE=N(FE(),1),Jk=new Intl.Segmenter,Qk=/^\p{Default_Ignorable_Code_Point}$/u;function pp(e,t={}){if(typeof e!="string"||e.length===0)return 0;let{ambiguousIsNarrow:r=!0,countAnsiEscapeCodes:n=!1}=t;if(n||(e=Fi(e)),e.length===0)return 0;let o=0,i={ambiguousAsWide:!r};for(let{segment:a}of Jk.segment(e)){let l=a.codePointAt(0);if(!(l<=31||l>=127&&l<=159)&&!(l>=8203&&l<=8207||l===65279)&&!(l>=768&&l<=879||l>=6832&&l<=6911||l>=7616&&l<=7679||l>=8400&&l<=8447||l>=65056&&l<=65071)&&!(l>=55296&&l<=57343)&&!(l>=65024&&l<=65039)&&!Qk.test(a)){if((0,CE.default)().test(a)){o+=2;continue}o+=EE(l,i)}}return o}function mp({stream:e=process.stdout}={}){return!!(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))}var gt=N(require("node:process"),1),Xk=3,hp=class{#e=0;start(){this.#e++,this.#e===1&&this.#r()}stop(){if(this.#e<=0)throw new Error("`stop` called more times than `start`");this.#e--,this.#e===0&&this.#t()}#r(){gt.default.platform==="win32"||!gt.default.stdin.isTTY||(gt.default.stdin.setRawMode(!0),gt.default.stdin.on("data",this.#n),gt.default.stdin.resume())}#t(){gt.default.stdin.isTTY&&(gt.default.stdin.off("data",this.#n),gt.default.stdin.pause(),gt.default.stdin.setRawMode(!1))}#n(t){t[0]===Xk&&gt.default.emit("SIGINT")}},Zk=new hp,gp=Zk;var e3=N(sp(),1),yp=class{#e=0;#r=!1;#t=0;#n=-1;#a=0;#o;#s;#i;#p;#h;#c;#f;#d;#g;#u;#l;color;constructor(t){typeof t=="string"&&(t={text:t}),this.#o={color:"cyan",stream:Ci.default.stderr,discardStdin:!0,hideCursor:!0,...t},this.color=this.#o.color,this.spinner=this.#o.spinner,this.#h=this.#o.interval,this.#i=this.#o.stream,this.#c=typeof this.#o.isEnabled=="boolean"?this.#o.isEnabled:mp({stream:this.#i}),this.#f=typeof this.#o.isSilent=="boolean"?this.#o.isSilent:!1,this.text=this.#o.text,this.prefixText=this.#o.prefixText,this.suffixText=this.#o.suffixText,this.indent=this.#o.indent,Ci.default.env.NODE_ENV==="test"&&(this._stream=this.#i,this._isEnabled=this.#c,Object.defineProperty(this,"_linesToClear",{get(){return this.#e},set(r){this.#e=r}}),Object.defineProperty(this,"_frameIndex",{get(){return this.#n}}),Object.defineProperty(this,"_lineCount",{get(){return this.#t}}))}get indent(){return this.#d}set indent(t=0){if(!(t>=0&&Number.isInteger(t)))throw new Error("The `indent` option must be an integer from 0 and up");this.#d=t,this.#m()}get interval(){return this.#h??this.#s.interval??100}get spinner(){return this.#s}set spinner(t){if(this.#n=-1,this.#h=void 0,typeof t=="object"){if(t.frames===void 0)throw new Error("The given spinner must have a `frames` property");this.#s=t}else if(!bo())this.#s=Ti.default.line;else if(t===void 0)this.#s=Ti.default.dots;else if(t!=="default"&&Ti.default[t])this.#s=Ti.default[t];else throw new Error(`There is no built-in spinner named '${t}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}get text(){return this.#g}set text(t=""){this.#g=t,this.#m()}get prefixText(){return this.#u}set prefixText(t=""){this.#u=t,this.#m()}get suffixText(){return this.#l}set suffixText(t=""){this.#l=t,this.#m()}get isSpinning(){return this.#p!==void 0}#y(t=this.#u,r=" "){return typeof t=="string"&&t!==""?t+r:typeof t=="function"?t()+r:""}#D(t=this.#l,r=" "){return typeof t=="string"&&t!==""?r+t:typeof t=="function"?r+t():""}#m(){let t=this.#i.columns??80,r=this.#y(this.#u,"-"),n=this.#D(this.#l,"-"),o=" ".repeat(this.#d)+r+"--"+this.#g+"--"+n;this.#t=0;for(let i of Fi(o).split(`
`))this.#t+=Math.max(1,Math.ceil(pp(i,{countAnsiEscapeCodes:!0})/t))}get isEnabled(){return this.#c&&!this.#f}set isEnabled(t){if(typeof t!="boolean")throw new TypeError("The `isEnabled` option must be a boolean");this.#c=t}get isSilent(){return this.#f}set isSilent(t){if(typeof t!="boolean")throw new TypeError("The `isSilent` option must be a boolean");this.#f=t}frame(){let t=Date.now();(this.#n===-1||t-this.#a>=this.interval)&&(this.#n=++this.#n%this.#s.frames.length,this.#a=t);let{frames:r}=this.#s,n=r[this.#n];this.color&&(n=tE[this.color](n));let o=typeof this.#u=="string"&&this.#u!==""?this.#u+" ":"",i=typeof this.text=="string"?" "+this.text:"",a=typeof this.#l=="string"&&this.#l!==""?" "+this.#l:"";return o+n+i+a}clear(){if(!this.#c||!this.#i.isTTY)return this;this.#i.cursorTo(0);for(let t=0;t<this.#e;t++)t>0&&this.#i.moveCursor(0,-1),this.#i.clearLine(1);return(this.#d||this.lastIndent!==this.#d)&&this.#i.cursorTo(this.#d),this.lastIndent=this.#d,this.#e=0,this}render(){return this.#f?this:(this.clear(),this.#i.write(this.frame()),this.#e=this.#t,this)}start(t){return t&&(this.text=t),this.#f?this:this.#c?this.isSpinning?this:(this.#o.hideCursor&&ip.hide(this.#i),this.#o.discardStdin&&Ci.default.stdin.isTTY&&(this.#r=!0,gp.start()),this.render(),this.#p=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.#i.write(`- ${this.text}
`),this)}stop(){return this.#c?(clearInterval(this.#p),this.#p=void 0,this.#n=0,this.clear(),this.#o.hideCursor&&ip.show(this.#i),this.#o.discardStdin&&Ci.default.stdin.isTTY&&this.#r&&(gp.stop(),this.#r=!1),this):this}succeed(t){return this.stopAndPersist({symbol:vi.success,text:t})}fail(t){return this.stopAndPersist({symbol:vi.error,text:t})}warn(t){return this.stopAndPersist({symbol:vi.warning,text:t})}info(t){return this.stopAndPersist({symbol:vi.info,text:t})}stopAndPersist(t={}){if(this.#f)return this;let r=t.prefixText??this.#u,n=this.#y(r," "),o=t.symbol??" ",i=t.text??this.text,l=typeof i=="string"?(o?" ":"")+i:"",c=t.suffixText??this.#l,d=this.#D(c," "),p=n+o+l+d+`
`;return this.stop(),this.#i.write(p),this}};function Dp(e){return new yp(e)}var TE=(0,fe.blue)((0,fe.dim)("internal only"));function Jn(e,t,r){console.log(Yt[e]+t),typeof r?.exit<"u"&&process.exit(r.exit)}async function _r(e,t,r){if(!RE){Jn("wait",e);try{let o=await t();o&&console.log(o),Jn("success",e);return}catch(o){return Jn("error",e),r?.printError!==!1&&console.log((0,fe.red)(o.message)),o}}let n=Dp({spinner:"simpleDots",prefixText:Yt.wait+e}).start();try{let o=await t();n.stop(),Jn("success",e),o&&console.log(o)}catch(o){return n.stop(),Jn("error",e),r?.printError!==!1&&console.error(o.message),o}}var Yt={wait:`\u{1F550}${(0,fe.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,fe.cyan)("info")}  - `,success:`\u2705${(0,fe.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,fe.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,fe.red)("error")}  - `,event:`\u26A1\uFE0F${(0,fe.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,fe.yellowBright)("plan")}  - `},RE=!0;function AE(e,t){e||(Yt.wait=`${(0,fe.blue)("wait")}  - `,Yt.info=`${(0,fe.cyan)("info")}  - `,Yt.success=`${(0,fe.green)("ready")}  - `,Yt.warn=`${(0,fe.yellow)("warn")}  - `,Yt.error=`${(0,fe.red)("error")}  - `,Yt.event=`${(0,fe.magenta)("event")}  - `,Yt.paymentPrompt=`${(0,fe.yellowBright)("plan")}  - `),t&&(RE=!1)}var wr=require("@oclif/core");var Ri=N(require("node:fs")),wp=N(require("node:path")),PE=N(require("node:os"));var $E=N(require("node:path")),xE=N(require("node:fs"));function bp(){let e;try{e=$E.resolve("package.json")}catch(r){throw new Error(`cannot resolve package manifest path: ${r}`)}let t;try{t=JSON.parse(xE.readFileSync(e,"utf8"))}catch(r){throw new Error(`cannot read package manifest: ${r}`)}return t.name=t.name.replace(/^@workaround/g,""),t}var Wr={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],t3="e69bae0ec90f5e838555",Kt={},OE;function BE(e){OE=e;try{Kt=JSON.parse(Ri.readFileSync(wp.join(n3(),"config.json"),"utf8"))}catch(t){if(t instanceof Error&&t.code==="ENOENT")return;throw new Error(`Failed to read config file: ${t}`)}}function Qn(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||Kt.APIURL||Wr.url;case"raycastAccessToken":return process.env.RAY_TOKEN||Kt.Token||Kt.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||Kt.ClientID||Wr.clientID;case"githubClientId":return process.env.RAY_GithubClientID||Kt.GithubClientID||t3;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||Kt.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof Kt.Target<"u"?Kt.Target:_p(process.platform==="win32"?"x":"release")}}function _p(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return Qn("flavorName")}}function r3(){let e=_p(OE);return e==""?"raycast":`raycast-${e}`}function n3(){let e=wp.join(PE.default.homedir(),".config",r3());return Ri.mkdirSync(e,{recursive:!0}),e}var Tu=class extends wr.Command{static baseFlags={"exit-on-error":wr.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:wr.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:wr.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":wr.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:wr.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:t,flags:r}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=r,this.args=t,BE(this.flags.target),AE(this.flags.emoji,this.flags["non-interactive"])}error(t,r){return r?.message&&t instanceof Error&&(t.message=`${r.message} (${t.message})`,delete r.message),super.error(t,r)}async catch(t){return super.catch(t)}async finally(t){return super.finally(t)}};var y3=require("@oclif/core");var h3=require("@oclif/core"),KE=N(hf()),g3=N(YE());async function Ap(e,t){let r;try{r=await(0,KE.default)(e,{method:t.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...t.token?{Authorization:`Bearer ${t.token}`}:void 0},body:t.body})}catch(n){throw new Error(`HTTP request: ${n.message}`)}if(!r.ok){switch(r.status){case 401:throw new Jt(r,"not authorized - please log in first using `npx ray login`");case 403:throw new Jt(r,"forbidden - you don't have permissions to perform the request");case 402:throw new Jt(r,"the limit of free commands has been reached")}let n=await r.text(),o;try{o=JSON.parse(n)}catch{throw new Jt(r,`HTTP error: ${r.status} - ${n}`)}throw Array.isArray(o.errors)&&o.errors.length>0?new Jt(r,`error: ${o.errors[0].status} - ${o.errors[0].title}`):new Jt(r,`HTTP error: ${r.status} - ${n}`)}return await r.json()}var Jt=class extends Error{constructor(t,r){let n=t.headers.get("X-Request-Id");n?super(`${r} (${t.url} RequestID: ${n})`):super(r),this.name="HTTPError"}};var JW=`${Wr.url}/sessions/success`,QW=`${Wr.url}/sessions/failure`;function Pu(e){let t=Qn("raycastApiURL"),r=Qn("raycastAccessToken");return Ap(`${t}/api/v1/users/${e}`,{token:r})}function JE(e){let t=Qn("raycastApiURL"),r=Qn("raycastAccessToken");return Ap(`${t}/api/v1/organizations/${e}`,{token:r})}var Bu=class e extends Tu{static description="Validate the extension manifest and metadata, and lint its source code";static flags={fix:Ou.Flags.boolean({char:"f",default:!1,description:"Attempt to fix linting issues"}),relaxed:Ou.Flags.boolean({char:"r",description:"Use relaxed linting mode to skip validation of: package.json schema, icons and metadata.",default:!1}),schema:Ou.Flags.string({char:"s",description:"Path to JSON schema for package.json validation "+TE,default:`${Wr.url}/schemas/extension.json`,hidden:!0})};async run(){let{flags:t}=await this.parse(e);await ev({skipOwner:!1,fix:t.fix,relaxed:t.relaxed,skipLockFiles:process.env.CI?.toLowerCase()!=="true",schema:t.schema})||this.error("linting issues found (tip: try re-running with '--fix')")}};async function ev(e){let t=!0;try{let r=bp();t=await D3(r,e),t=await b3(r,e)&&t,t=await _3(r,e)&&t,t=await w3(r,e)&&t}catch(r){console.error("error reading manifest:",r),t=!1}return t=await S3(e)&&t,t=await E3(e)&&t,t}async function D3(e,t){return await _r("validate package.json file",async()=>{let n=[],o=[];try{await Pu(e.author)}catch(i){i.message.includes("Couldn't find User")?n.push({pointer:"/author",message:`Invalid author "${e.author}". Use the Raycast username`,level:"error"}):n.push({pointer:"/author",message:`Invalid author "${e.author}". ${i.message}`,level:"error"})}if(await Promise.all((e.contributors||[]).map(async(i,a)=>{try{await Pu(i)}catch(l){l.message.includes("Couldn't find User")?n.push({pointer:"/contributors/"+a,message:`Invalid contributor "${i}". Use the Raycast username`,level:"error"}):n.push({pointer:"/contributors/"+a,message:`Invalid contributor "${i}". ${l.message}`,level:"error"})}i===e.author&&n.push({pointer:"/contributors/"+a,message:`Author "${i}" should not be added as a contributor`,level:"error"})})),await Promise.all((e.pastContributors||[]).map(async(i,a)=>{try{await Pu(i)}catch(l){l.message.includes("Couldn't find User")?n.push({pointer:"/pastContributors/"+a,message:`Invalid contributor "${i}". Use the Raycast username`,level:"error"}):n.push({pointer:"/pastContributors/"+a,message:`Invalid contributor "${i}". ${l.message}`,level:"error"})}})),e.owner&&!t.skipOwner)try{await JE(e.owner)}catch(i){i.message.includes("Couldn't find Organization")?n.push({pointer:"/owner",message:`Invalid owner "${e.owner}". Use the Raycast organization handle`,level:"error"}):n.push({pointer:"/owner",message:`Invalid owner "${e.owner}". ${i.message}`,level:"error"})}if(!t.relaxed){let i=new ZE.default({allErrors:!0});try{let a=await(await(0,XE.default)(t.schema)).json();delete a.$schema;let l=i.compile(a);l(e)||n.push(...(l.errors||[]).map(c=>({pointer:c.instancePath,message:c.message||"",level:"error"})))}catch(a){n.push({message:`Failed compiling JSON schema: ${a.message}`,level:"error"})}e.name.startsWith("@workaround/")&&o.push({pointer:"/name",message:`"@workaround/" is a reserved prefix for internal extensions in companies that have a policy regarding namespaced package.json names. This isn't valid for public extensions`,level:"warning"}),(0,Vr.titleCase)(e.title)!==e.title&&o.push({pointer:"/title",message:`Extension's title has to be Title Cased. Expected "${(0,Vr.titleCase)(e.title)}"`,level:"warning"}),Zn(e.title)||o.push({pointer:"/title",message:'Raycast is spelled "Raycast"',level:"warning"}),Zn(e.description)||o.push({pointer:"/description",message:'Raycast is spelled "Raycast"',level:"warning"}),e.commands.forEach((a,l)=>{(0,Vr.titleCase)(a.title)!==a.title&&o.push({pointer:`/commands/${l}/title`,message:`Command's title has to be Title Cased. Expected "${(0,Vr.titleCase)(a.title)}"`,level:"warning"}),Zn(a.title)||o.push({pointer:`/commands/${l}/title`,message:'Raycast is spelled "Raycast"',level:"warning"}),Zn(a.description)||o.push({pointer:`/commands/${l}/description`,message:'Raycast is spelled "Raycast"',level:"warning"})}),e.tools?.forEach((a,l)=>{(0,Vr.titleCase)(a.title)!==a.title&&o.push({pointer:`/tools/${l}/title`,message:`Tool's title has to be Title Cased. Expected "${(0,Vr.titleCase)(a.title)}"`,level:"warning"}),Zn(a.title)||o.push({pointer:`/tools/${l}/title`,message:'Raycast is spelled "Raycast"',level:"warning"}),Zn(a.description)||o.push({pointer:`/tools/${l}/description`,message:'Raycast is spelled "Raycast"',level:"warning"})})}if(n.length>0)throw new Error(`${_e.resolve("package.json")}
  ${Qt(n.concat(o))}`);o.length>0&&console.log((0,Iu.yellow)(`${_e.resolve("package.json")}
  ${Qt(n.concat(o))}`))})===void 0}async function b3(e,t){if(t.relaxed||t.skipLockFiles)return!0;let r=await _r("validate package-lock.json",async()=>{let n=_e.resolve("package-lock.json"),o;try{o=require(n)}catch{throw new Error("missing package-lock.json. Run 'npm install' to generate the file")}if(o.lockfileVersion===1)throw new Error(`${n}
  4:20  error  Wrong 'lockfileVersion'. Version '${o.lockfileVersion}' is unsupported. Update to npm v7 or higher`)});return r?!1:(r=await _r("validate other lock files",async()=>{if(Xe.existsSync("yarn.lock"))throw new Error("yarn is not supported. Remove the `yarn.lock` file and run `npm install` instead");if(Xe.existsSync("pnpm-lock.yaml"))throw new Error("pnpm is not supported. Remove the `pnpm-lock.yaml` file and run `npm install` instead");if(Xe.existsSync("shrinkwrap.json"))throw new Error("shrinkwrap is not supported. Remove the `shrinkwrap.json` file and run `npm install` instead");if(Xe.existsSync("npm-shrinkwrap.json"))throw new Error("shrinkwrap is not supported. Remove the `npm-shrinkwrap.json` file and run `npm install` instead")}),r===void 0)}async function _3(e,t){return t.relaxed?!0:await _r("validate extension icons",async()=>{let n=[],o=_e.resolve(_e.join("assets",e.icon)),i=Gr(o,{type:"icon"});if(i&&n.push(`${o}
  ${Qt([i])}`),o=_e.resolve(_e.join("assets",e.icon.replace(/.png$/,"@dark.png"))),i=Gr(o,{type:"icon",isOptional:!0}),i&&n.push(`${o}
  ${Qt([i])}`),e.commands.forEach(a=>{a.icon&&(o=_e.resolve(_e.join("assets",a.icon)),i=Gr(o,{type:"icon"}),i&&n.push(`${o}
  ${Qt([i])}`),o=_e.resolve(_e.join("assets",a.icon.replace(/.png$/,"@dark.png"))),i=Gr(o,{type:"icon",isOptional:!0}),i&&n.push(`${o}
  ${Qt([i])}`))}),e.tools?.forEach(a=>{a.icon&&(o=_e.resolve(_e.join("assets",a.icon)),i=Gr(o,{type:"icon"}),i&&n.push(`${o}
  ${Qt([i])}`),o=_e.resolve(_e.join("assets",a.icon.replace(/.png$/,"@dark.png"))),i=Gr(o,{type:"icon",isOptional:!0}),i&&n.push(`${o}
  ${Qt([i])}`))}),n.length)throw new Error(n.join(`

`))})===void 0}async function w3(e,t){return t.relaxed||!Xe.existsSync("metadata")?!0:await _r("validate extension metadata",async()=>{Xe.readdirSync("metadata").forEach(n=>{let o=_e.resolve(_e.join("metadata",n));if(n.endsWith(".png")){let i=Gr(o,{type:"metadata"});if(i)throw new Error(`${o}
  ${Qt([i])}`)}})})===void 0}function Gr(e,t){if(!Xe.existsSync(e))return t.isOptional?void 0:{message:"Missing file in assets folder",level:"error"};if(!e.endsWith(".png"))return{message:"Use png format for icon",level:"error"};try{let r=Xe.readFileSync(e),n=Buffer.from([137,80,78,71,13,10,26,10]);if(r.compare(n,0,8,0,8)!==0)return{message:"Wrong image format, use a proper png file",level:"error"};let o=r.subarray(8,33);if(o.toString("ascii",4,8)!=="IHDR")return{message:"Could not read png file dimensions",level:"error"};let i=o.readUInt32BE(8),a=o.readUInt32BE(12);if(t.type==="icon"){if(i!==512||a!==512)return{message:`Wrong image size: ${i} x ${a} pixels. Required size is 512 x 512 pixels`,level:"error"}}else if(i!==2e3||a!==1250)return i===2e3/2&&a===1250/2?{message:`Wrong image size: ${i} x ${a} pixels. Required size is 2000 x 1250 pixels. Make sure to use a retina screen when taking the screenshot`,level:"error"}:{message:`Wrong image size: ${i} x ${a} pixels. Required size is 2000 x 1250 pixels`,level:"error"}}catch{return{message:"could not read file",level:"error"}}}async function S3(e){return await _r("run ESLint",async()=>{let r=await Ea("eslint",["src/**"].concat(e.fix?["--fix"]:[]),{preferLocal:!0,reject:!1,all:!0});if(r.failed)throw r.all?new Error(r.all):new Error(r.message);if(r.all.trim())return(0,Iu.yellow)(r.all.trim())})===void 0}async function E3(e){let t=await Ea("prettier",["-v"],{preferLocal:!0,reject:!1});return await _r(`run Prettier ${t.stdout}`,async()=>{let n=await Ea("prettier",["src/**","!**/.DS_Store"].concat(e.fix?["--loglevel","warn","--write"]:["--list-different"]),{preferLocal:!0,reject:!1,all:!0});if(n.failed){if(!n.all)throw new Error(n.message);if(e.fix)throw new Error(n.all.trim());let i=n.all.trim().split(`
`).filter(Boolean).map(a=>`${_e.resolve(a)}
  error  Code style issues found. Please run Prettier ${t.stdout} (ray lint --fix).`);throw new Error(i.join(`
`))}})===void 0}function Qt(e){if(e.every(i=>!i.pointer))return e.map(i=>`  ${i.level}  ${i.message}`).join(`
  `);let t=QE.default.parse(Xe.readFileSync("package.json","utf8")).pointers,r=0,n=0;e.forEach(i=>{if(!i.pointer||!t[i.pointer])return;let a=t[i.pointer].value;(a.line+1).toString().length>r&&(r=(a.line+1).toString().length),a.column.toString().length>n&&(n=a.column.toString().length)});let o=[];return e.forEach(i=>{let a="";if(!i.pointer||!t[i.pointer])a=`${"".padStart(r+n+1)}  ${i.level}  ${i.message}`;else{let l=t[i.pointer].value;a=`${(l.line+1).toString().padStart(r)}:${l.column.toString().padStart(n)}  ${i.level}  ${i.message}`}i.level==="warning"?o.push((0,Iu.yellow)(a)):o.push(a)}),o.join(`
  `)}function Zn(e){return!e.includes("RayCast")&&!e.includes("raycast")}0&&(module.exports={lint});
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
