"use strict";var $=Object.create;var m=Object.defineProperty;var G=Object.getOwnPropertyDescriptor;var D=Object.getOwnPropertyNames;var O=Object.getPrototypeOf,L=Object.prototype.hasOwnProperty;var A=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),R=(t,n)=>{for(var i in n)m(t,i,{get:n[i],enumerable:!0})},I=(t,n,i,c)=>{if(n&&typeof n=="object"||typeof n=="function")for(let o of D(n))!L.call(t,o)&&o!==i&&m(t,o,{get:()=>n[o],enumerable:!(c=G(n,o))||c.enumerable});return t};var h=(t,n,i)=>(i=t!=null?$(O(t)):{},I(n||!t||!t.__esModule?m(i,"default",{value:t,enumerable:!0}):i,t)),S=t=>I(m({},"__esModule",{value:!0}),t);var B=A((Q,k)=>{var Y=require("node:tty"),J=Y?.WriteStream?.prototype?.hasColors?.()??!1,r=(t,n)=>{if(!J)return o=>o;let i=`\x1B[${t}m`,c=`\x1B[${n}m`;return o=>{let u=o+"",d=u.indexOf(c);if(d===-1)return i+u+c;let x=i,g=0;for(;d!==-1;)x+=u.slice(g,d)+i,g=d+c.length,d=u.indexOf(c,g);return x+=u.slice(g)+c,x}},e={};e.reset=r(0,0);e.bold=r(1,22);e.dim=r(2,22);e.italic=r(3,23);e.underline=r(4,24);e.overline=r(53,55);e.inverse=r(7,27);e.hidden=r(8,28);e.strikethrough=r(9,29);e.black=r(30,39);e.red=r(31,39);e.green=r(32,39);e.yellow=r(33,39);e.blue=r(34,39);e.magenta=r(35,39);e.cyan=r(36,39);e.white=r(37,39);e.gray=r(90,39);e.bgBlack=r(40,49);e.bgRed=r(41,49);e.bgGreen=r(42,49);e.bgYellow=r(43,49);e.bgBlue=r(44,49);e.bgMagenta=r(45,49);e.bgCyan=r(46,49);e.bgWhite=r(47,49);e.bgGray=r(100,49);e.redBright=r(91,39);e.greenBright=r(92,39);e.yellowBright=r(93,39);e.blueBright=r(94,39);e.magentaBright=r(95,39);e.cyanBright=r(96,39);e.whiteBright=r(97,39);e.bgRedBright=r(101,49);e.bgGreenBright=r(102,49);e.bgYellowBright=r(103,49);e.bgBlueBright=r(104,49);e.bgMagentaBright=r(105,49);e.bgCyanBright=r(106,49);e.bgWhiteBright=r(107,49);k.exports=e});var P=A((oe,H)=>{H.exports={name:"ray",description:"Build extensions for Raycast with React and Node.js.",version:"1.100.3",author:"mathieudutour",private:!0,bin:{ray:"./bin/run.js"},dependencies:{"@oclif/core":"^4.0.33","@oclif/plugin-autocomplete":"^3.2.10","@oclif/plugin-help":"^6.2.18","@oclif/plugin-not-found":"^3.2.28","@raycast/eslint-plugin":"^1.0.15","@raycast/extract-tools-info":"file:../extract-tools-info",ajv:"^8.17.1",archiver:"^7.0.1",camelcase:"^8.0.0",chokidar:"^4.0.1","cli-progress":"^3.12.0",clipboardy:"^2.3.0",execa:"^9.5.1","json-source-map":"^0.6.1",json5:"^2.2.3","node-fetch-cjs":"^3.3.2",open:"^8.4.2",ora:"^8.1.1",tail:"^2.2.6","terminal-link":"^3.0.0",yaml:"^2.6.1","yoctocolors-cjs":"^2.1.2"},devDependencies:{"@raycast/eslint-config":"^1.0.11","@types/archiver":"^6.0.3","@types/cli-progress":"^3.11.6","@types/node":"20.8.4","@types/tail":"^2.2.3",esbuild:"^0.25.1",eslint:"^8","json-schema-to-typescript":"^15.0.3",oclif:"^4.15.28",typescript:"^5.6.3"},files:["/bin","/dist","/oclif.manifest.json"],license:"MIT",main:"dist/index.js",oclif:{bin:"ray",dirname:"ray",commands:"./dist/commands",plugins:["@oclif/plugin-autocomplete","@oclif/plugin-help","@oclif/plugin-not-found"]},scripts:{"json-schema-to-ts":"json2ts ../../api/schema.json -y > src/types/manifest-schema.d.ts","build-scripts":'tsc "./scripts/build.ts" "./scripts/build-command.ts" "./scripts/dev-command.ts" --outDir ./scripts/dist --esModuleInterop',prebuild:"npm run json-schema-to-ts && npm run build-scripts",build:"node ./scripts/dist/build-command.js",predev:"npm run json-schema-to-ts && npm run build-scripts",dev:"node ./scripts/dist/dev-command.js",lint:"eslint . --ext .ts"},types:"dist/index.d.ts"}});var K={};R(K,{default:()=>b});module.exports=S(K);var l=require("@oclif/core");var f=h(require("node:fs")),v=h(require("node:path")),E=h(require("node:os"));var T={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],F="e69bae0ec90f5e838555",a={},C;function N(t){C=t;try{a=JSON.parse(f.readFileSync(v.join(M(),"config.json"),"utf8"))}catch(n){if(n instanceof Error&&n.code==="ENOENT")return;throw new Error(`Failed to read config file: ${n}`)}}function U(t){switch(t){case"raycastApiURL":return process.env.RAY_APIURL||a.APIURL||T.url;case"raycastAccessToken":return process.env.RAY_TOKEN||a.Token||a.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||a.ClientID||T.clientID;case"githubClientId":return process.env.RAY_GithubClientID||a.GithubClientID||F;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||a.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof a.Target<"u"?a.Target:w(process.platform==="win32"?"x":"release")}}function w(t){switch(t){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return U("flavorName")}}function _(){let t=w(C);return t==""?"raycast":`raycast-${t}`}function M(){let t=v.join(E.default.homedir(),".config",_());return f.mkdirSync(t,{recursive:!0}),t}var s=h(B());var ee=(0,s.blue)((0,s.dim)("internal only"));var p={wait:`\u{1F550}${(0,s.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,s.cyan)("info")}  - `,success:`\u2705${(0,s.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,s.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,s.red)("error")}  - `,event:`\u26A1\uFE0F${(0,s.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,s.yellowBright)("plan")}  - `},q=!0;function j(t,n){t||(p.wait=`${(0,s.blue)("wait")}  - `,p.info=`${(0,s.cyan)("info")}  - `,p.success=`${(0,s.green)("ready")}  - `,p.warn=`${(0,s.yellow)("warn")}  - `,p.error=`${(0,s.red)("error")}  - `,p.event=`${(0,s.magenta)("event")}  - `,p.paymentPrompt=`${(0,s.yellowBright)("plan")}  - `),n&&(q=!1)}var y=class extends l.Command{static baseFlags={"exit-on-error":l.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:l.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:l.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":l.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:l.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:n,flags:i}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=i,this.args=n,N(this.flags.target),j(this.flags.emoji,this.flags["non-interactive"])}error(n,i){return i?.message&&n instanceof Error&&(n.message=`${i.message} (${n.message})`,delete i.message),super.error(n,i)}async catch(n){return super.catch(n)}async finally(n){return super.finally(n)}};var b=class t extends y{static description="Print the version number";async run(){await this.parse(t),this.log(`${P().version}`)}};
