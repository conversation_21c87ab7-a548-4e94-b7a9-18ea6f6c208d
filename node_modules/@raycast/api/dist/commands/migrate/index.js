"use strict";var Ql=Object.create;var Xe=Object.defineProperty;var ef=Object.getOwnPropertyDescriptor;var tf=Object.getOwnPropertyNames;var rf=Object.getPrototypeOf,nf=Object.prototype.hasOwnProperty;var D=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),of=(e,t)=>{for(var r in t)Xe(e,r,{get:t[r],enumerable:!0})},ro=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of tf(t))!nf.call(e,o)&&o!==r&&Xe(e,o,{get:()=>t[o],enumerable:!(n=ef(t,o))||n.enumerable});return e};var T=(e,t,r)=>(r=e!=null?Ql(rf(e)):{},ro(t||!e||!e.__esModule?Xe(r,"default",{value:e,enumerable:!0}):r,e)),sf=e=>ro(Xe({},"__esModule",{value:!0}),e);var co=D((Cg,ao)=>{var df=require("node:tty"),pf=df?.WriteStream?.prototype?.hasColors?.()??!1,g=(e,t)=>{if(!pf)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,c=0;for(;s!==-1;)a+=i.slice(c,s)+r,c=s+n.length,s=i.indexOf(n,c);return a+=i.slice(c)+n,a}},h={};h.reset=g(0,0);h.bold=g(1,22);h.dim=g(2,22);h.italic=g(3,23);h.underline=g(4,24);h.overline=g(53,55);h.inverse=g(7,27);h.hidden=g(8,28);h.strikethrough=g(9,29);h.black=g(30,39);h.red=g(31,39);h.green=g(32,39);h.yellow=g(33,39);h.blue=g(34,39);h.magenta=g(35,39);h.cyan=g(36,39);h.white=g(37,39);h.gray=g(90,39);h.bgBlack=g(40,49);h.bgRed=g(41,49);h.bgGreen=g(42,49);h.bgYellow=g(43,49);h.bgBlue=g(44,49);h.bgMagenta=g(45,49);h.bgCyan=g(46,49);h.bgWhite=g(47,49);h.bgGray=g(100,49);h.redBright=g(91,39);h.greenBright=g(92,39);h.yellowBright=g(93,39);h.blueBright=g(94,39);h.magentaBright=g(95,39);h.cyanBright=g(96,39);h.whiteBright=g(97,39);h.bgRedBright=g(101,49);h.bgGreenBright=g(102,49);h.bgYellowBright=g(103,49);h.bgBlueBright=g(104,49);h.bgMagentaBright=g(105,49);h.bgCyanBright=g(106,49);h.bgWhiteBright=g(107,49);ao.exports=h});var Ko=D((pS,Ho)=>{Ho.exports=qo;qo.sync=cd;var Vo=require("fs");function ad(e,t){var r=t.pathExt!==void 0?t.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}function Yo(e,t,r){return!e.isSymbolicLink()&&!e.isFile()?!1:ad(t,r)}function qo(e,t,r){Vo.stat(e,function(n,o){r(n,n?!1:Yo(o,e,t))})}function cd(e,t){return Yo(Vo.statSync(e),e,t)}});var ei=D((uS,Qo)=>{Qo.exports=Jo;Jo.sync=ld;var Xo=require("fs");function Jo(e,t,r){Xo.stat(e,function(n,o){r(n,n?!1:Zo(o,t))})}function ld(e,t){return Zo(Xo.statSync(e),t)}function Zo(e,t){return e.isFile()&&fd(e,t)}function fd(e,t){var r=e.mode,n=e.uid,o=e.gid,i=t.uid!==void 0?t.uid:process.getuid&&process.getuid(),s=t.gid!==void 0?t.gid:process.getgid&&process.getgid(),a=parseInt("100",8),c=parseInt("010",8),f=parseInt("001",8),l=a|c,d=r&f||r&c&&o===s||r&a&&n===i||r&l&&i===0;return d}});var ri=D((hS,ti)=>{var mS=require("fs"),ut;process.platform==="win32"||global.TESTING_WINDOWS?ut=Ko():ut=ei();ti.exports=Br;Br.sync=dd;function Br(e,t,r){if(typeof t=="function"&&(r=t,t={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,o){Br(e,t||{},function(i,s){i?o(i):n(s)})})}ut(e,t||{},function(n,o){n&&(n.code==="EACCES"||t&&t.ignoreErrors)&&(n=null,o=!1),r(n,o)})}function dd(e,t){try{return ut.sync(e,t||{})}catch(r){if(t&&t.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var li=D((gS,ci)=>{var he=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",ni=require("path"),pd=he?";":":",oi=ri(),ii=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"}),si=(e,t)=>{let r=t.colon||pd,n=e.match(/\//)||he&&e.match(/\\/)?[""]:[...he?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(r)],o=he?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",i=he?o.split(r):[""];return he&&e.indexOf(".")!==-1&&i[0]!==""&&i.unshift(""),{pathEnv:n,pathExt:i,pathExtExe:o}},ai=(e,t,r)=>{typeof t=="function"&&(r=t,t={}),t||(t={});let{pathEnv:n,pathExt:o,pathExtExe:i}=si(e,t),s=[],a=f=>new Promise((l,d)=>{if(f===n.length)return t.all&&s.length?l(s):d(ii(e));let p=n[f],u=/^".*"$/.test(p)?p.slice(1,-1):p,m=ni.join(u,e),S=!u&&/^\.[\\\/]/.test(e)?e.slice(0,2)+m:m;l(c(S,f,0))}),c=(f,l,d)=>new Promise((p,u)=>{if(d===o.length)return p(a(l+1));let m=o[d];oi(f+m,{pathExt:i},(S,E)=>{if(!S&&E)if(t.all)s.push(f+m);else return p(f+m);return p(c(f,l,d+1))})});return r?a(0).then(f=>r(null,f),r):a(0)},ud=(e,t)=>{t=t||{};let{pathEnv:r,pathExt:n,pathExtExe:o}=si(e,t),i=[];for(let s=0;s<r.length;s++){let a=r[s],c=/^".*"$/.test(a)?a.slice(1,-1):a,f=ni.join(c,e),l=!c&&/^\.[\\\/]/.test(e)?e.slice(0,2)+f:f;for(let d=0;d<n.length;d++){let p=l+n[d];try{if(oi.sync(p,{pathExt:o}))if(t.all)i.push(p);else return p}catch{}}}if(t.all&&i.length)return i;if(t.nothrow)return null;throw ii(e)};ci.exports=ai;ai.sync=ud});var di=D((yS,Lr)=>{"use strict";var fi=(e={})=>{let t=e.env||process.env;return(e.platform||process.platform)!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};Lr.exports=fi;Lr.exports.default=fi});var hi=D((SS,mi)=>{"use strict";var pi=require("path"),md=li(),hd=di();function ui(e,t){let r=e.options.env||process.env,n=process.cwd(),o=e.options.cwd!=null,i=o&&process.chdir!==void 0&&!process.chdir.disabled;if(i)try{process.chdir(e.options.cwd)}catch{}let s;try{s=md.sync(e.command,{path:r[hd({env:r})],pathExt:t?pi.delimiter:void 0})}catch{}finally{i&&process.chdir(n)}return s&&(s=pi.resolve(o?e.options.cwd:"",s)),s}function gd(e){return ui(e)||ui(e,!0)}mi.exports=gd});var gi=D((bS,$r)=>{"use strict";var Fr=/([()\][%!^"`<>&|;, *?])/g;function yd(e){return e=e.replace(Fr,"^$1"),e}function Sd(e,t){return e=`${e}`,e=e.replace(/(\\*)"/g,'$1$1\\"'),e=e.replace(/(\\*)$/,"$1$1"),e=`"${e}"`,e=e.replace(Fr,"^$1"),t&&(e=e.replace(Fr,"^$1")),e}$r.exports.command=yd;$r.exports.argument=Sd});var Si=D((wS,yi)=>{"use strict";yi.exports=/^#!(.*)/});var wi=D((xS,bi)=>{"use strict";var bd=Si();bi.exports=(e="")=>{let t=e.match(bd);if(!t)return null;let[r,n]=t[0].replace(/#! ?/,"").split(" "),o=r.split("/").pop();return o==="env"?n:n?`${o} ${n}`:o}});var Ei=D((ES,xi)=>{"use strict";var vr=require("fs"),wd=wi();function xd(e){let r=Buffer.alloc(150),n;try{n=vr.openSync(e,"r"),vr.readSync(n,r,0,150,0),vr.closeSync(n)}catch{}return wd(r.toString())}xi.exports=xd});var Ii=D((TS,Oi)=>{"use strict";var Ed=require("path"),Ti=hi(),Ai=gi(),Td=Ei(),Ad=process.platform==="win32",Od=/\.(?:com|exe)$/i,Id=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Dd(e){e.file=Ti(e);let t=e.file&&Td(e.file);return t?(e.args.unshift(e.file),e.command=t,Ti(e)):e.file}function Rd(e){if(!Ad)return e;let t=Dd(e),r=!Od.test(t);if(e.options.forceShell||r){let n=Id.test(t);e.command=Ed.normalize(e.command),e.command=Ai.command(e.command),e.args=e.args.map(i=>Ai.argument(i,n));let o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}function Cd(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null),t=t?t.slice(0):[],r=Object.assign({},r);let n={command:e,args:t,options:r,file:void 0,original:{command:e,args:t}};return r.shell?n:Rd(n)}Oi.exports=Cd});var Ci=D((AS,Ri)=>{"use strict";var Ur=process.platform==="win32";function Nr(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function Md(e,t){if(!Ur)return;let r=e.emit;e.emit=function(n,o){if(n==="exit"){let i=Di(o,t,"spawn");if(i)return r.call(e,"error",i)}return r.apply(e,arguments)}}function Di(e,t){return Ur&&e===1&&!t.file?Nr(t.original,"spawn"):null}function Pd(e,t){return Ur&&e===1&&!t.file?Nr(t.original,"spawnSync"):null}Ri.exports={hookChildProcess:Md,verifyENOENT:Di,verifyENOENTSync:Pd,notFoundError:Nr}});var Bi=D((OS,ge)=>{"use strict";var Mi=require("child_process"),_r=Ii(),jr=Ci();function Pi(e,t,r){let n=_r(e,t,r),o=Mi.spawn(n.command,n.args,n.options);return jr.hookChildProcess(o,n),o}function Bd(e,t,r){let n=_r(e,t,r),o=Mi.spawnSync(n.command,n.args,n.options);return o.error=o.error||jr.verifyENOENTSync(o.status,n),o}ge.exports=Pi;ge.exports.spawn=Pi;ge.exports.sync=Bd;ge.exports._parse=_r;ge.exports._enoent=jr});var Tg={};of(Tg,{default:()=>dr});module.exports=sf(Tg);var Zl=require("@oclif/core");var X=require("@oclif/core");var Pe=T(require("node:fs")),mr=T(require("node:path")),oo=T(require("node:os"));var no={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],af="e69bae0ec90f5e838555",z={},io;function so(e){io=e;try{z=JSON.parse(Pe.readFileSync(mr.join(ff(),"config.json"),"utf8"))}catch(t){if(t instanceof Error&&t.code==="ENOENT")return;throw new Error(`Failed to read config file: ${t}`)}}function cf(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||z.APIURL||no.url;case"raycastAccessToken":return process.env.RAY_TOKEN||z.Token||z.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||z.ClientID||no.clientID;case"githubClientId":return process.env.RAY_GithubClientID||z.GithubClientID||af;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||z.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof z.Target<"u"?z.Target:ur(process.platform==="win32"?"x":"release")}}function ur(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return cf("flavorName")}}function lf(){let e=ur(io);return e==""?"raycast":`raycast-${e}`}function ff(){let e=mr.join(oo.default.homedir(),".config",lf());return Pe.mkdirSync(e,{recursive:!0}),e}var w=T(co());var oe=[];oe.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&oe.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&oe.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var Je=e=>!!e&&typeof e=="object"&&typeof e.removeListener=="function"&&typeof e.emit=="function"&&typeof e.reallyExit=="function"&&typeof e.listeners=="function"&&typeof e.kill=="function"&&typeof e.pid=="number"&&typeof e.on=="function",hr=Symbol.for("signal-exit emitter"),gr=globalThis,uf=Object.defineProperty.bind(Object),yr=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(gr[hr])return gr[hr];uf(gr,hr,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,r){this.listeners[t].push(r)}removeListener(t,r){let n=this.listeners[t],o=n.indexOf(r);o!==-1&&(o===0&&n.length===1?n.length=0:n.splice(o,1))}emit(t,r,n){if(this.emitted[t])return!1;this.emitted[t]=!0;let o=!1;for(let i of this.listeners[t])o=i(r,n)===!0||o;return t==="exit"&&(o=this.emit("afterExit",r,n)||o),o}},Ze=class{},mf=e=>({onExit(t,r){return e.onExit(t,r)},load(){return e.load()},unload(){return e.unload()}}),Sr=class extends Ze{onExit(){return()=>{}}load(){}unload(){}},br=class extends Ze{#t=wr.platform==="win32"?"SIGINT":"SIGHUP";#r=new yr;#e;#n;#i;#o={};#s=!1;constructor(t){super(),this.#e=t,this.#o={};for(let r of oe)this.#o[r]=()=>{let n=this.#e.listeners(r),{count:o}=this.#r,i=t;if(typeof i.__signal_exit_emitter__=="object"&&typeof i.__signal_exit_emitter__.count=="number"&&(o+=i.__signal_exit_emitter__.count),n.length===o){this.unload();let s=this.#r.emit("exit",null,r),a=r==="SIGHUP"?this.#t:r;s||t.kill(t.pid,a)}};this.#i=t.reallyExit,this.#n=t.emit}onExit(t,r){if(!Je(this.#e))return()=>{};this.#s===!1&&this.load();let n=r?.alwaysLast?"afterExit":"exit";return this.#r.on(n,t),()=>{this.#r.removeListener(n,t),this.#r.listeners.exit.length===0&&this.#r.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#s){this.#s=!0,this.#r.count+=1;for(let t of oe)try{let r=this.#o[t];r&&this.#e.on(t,r)}catch{}this.#e.emit=(t,...r)=>this.#c(t,...r),this.#e.reallyExit=t=>this.#a(t)}}unload(){this.#s&&(this.#s=!1,oe.forEach(t=>{let r=this.#o[t];if(!r)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,r)}catch{}}),this.#e.emit=this.#n,this.#e.reallyExit=this.#i,this.#r.count-=1)}#a(t){return Je(this.#e)?(this.#e.exitCode=t||0,this.#r.emit("exit",this.#e.exitCode,null),this.#i.call(this.#e,this.#e.exitCode)):0}#c(t,...r){let n=this.#n;if(t==="exit"&&Je(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let o=n.call(this.#e,t,...r);return this.#r.emit("exit",this.#e.exitCode,null),o}else return n.call(this.#e,t,...r)}},wr=globalThis.process,{onExit:lo,load:Bg,unload:Lg}=mf(Je(wr)?new br(wr):new Sr);var xr=T(require("node:process"),1);function Er(){let{env:e}=xr.default,{TERM:t,TERM_PROGRAM:r}=e;return xr.default.platform!=="win32"?t!=="linux":!!e.WT_SESSION||!!e.TERMINUS_SUBLIME||e.ConEmuTask==="{cmd::Cmder}"||r==="Terminus-Sublime"||r==="vscode"||t==="xterm-256color"||t==="alacritty"||t==="rxvt-unicode"||t==="rxvt-unicode-256color"||e.TERMINAL_EMULATOR==="JetBrains-JediTerm"}var vg=(0,w.blue)((0,w.dim)("internal only"));var ie={wait:`\u{1F550}${(0,w.blue)("wait")}  - `,info:`${process.env.GITHUB_ACTIONS==="true"?"\u2139\uFE0F":"\u2139\uFE0F "}${(0,w.cyan)("info")}  - `,success:`\u2705${(0,w.green)("ready")}  - `,warn:`${process.env.GITHUB_ACTIONS==="true"?"\u26A0\uFE0F":"\u26A0\uFE0F "}${(0,w.yellow)("warn")}  - `,error:`\u{1F4A5}${(0,w.red)("error")}  - `,event:`\u26A1\uFE0F${(0,w.magenta)("event")}  - `,paymentPrompt:`\u{1F4B0}${(0,w.yellowBright)("plan")}  - `},hf=!0;function fo(e,t){e||(ie.wait=`${(0,w.blue)("wait")}  - `,ie.info=`${(0,w.cyan)("info")}  - `,ie.success=`${(0,w.green)("ready")}  - `,ie.warn=`${(0,w.yellow)("warn")}  - `,ie.error=`${(0,w.red)("error")}  - `,ie.event=`${(0,w.magenta)("event")}  - `,ie.paymentPrompt=`${(0,w.yellowBright)("plan")}  - `),t&&(hf=!1)}var Qe=class extends X.Command{static baseFlags={"exit-on-error":X.Flags.boolean({default:!0,helpGroup:"GLOBAL",aliases:["exitOnError"],deprecateAliases:!0,summary:"Always exit with non-zero code on error",allowNo:!0}),emoji:X.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Prefix output with emojis \u{1F308}"}),help:X.Flags.boolean({default:!1,helpGroup:"GLOBAL",summary:"Show the help message for the command"}),"non-interactive":X.Flags.boolean({char:"I",default:!1,helpGroup:"GLOBAL",summary:"Disable interactive outputs, useful for CI"}),target:X.Flags.option({char:"t",description:"Raycast app target",helpGroup:"GLOBAL",multiple:!1,options:["debug","internal","release","x","x-development","x-internal"],hidden:!0})()};flags;args;async init(){await super.init(),process.on("SIGINT",()=>process.exit(1));let{args:t,flags:r}=await this.parse({flags:this.ctor.flags,baseFlags:super.ctor.baseFlags,enableJsonFlag:this.ctor.enableJsonFlag,args:this.ctor.args,strict:this.ctor.strict});this.flags=r,this.args=t,so(this.flags.target),fo(this.flags.emoji,this.flags["non-interactive"])}error(t,r){return r?.message&&t instanceof Error&&(t.message=`${r.message} (${t.message})`,delete r.message),super.error(t,r)}async catch(t){return super.catch(t)}async finally(t){return super.finally(t)}};function x(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var po=require("node:url"),pe=(e,t)=>{let r=Ar(gf(e));if(typeof r!="string")throw new TypeError(`${t} must be a string or a file URL: ${r}.`);return r},gf=e=>Tr(e)?e.toString():e,Tr=e=>typeof e!="string"&&e&&Object.getPrototypeOf(e)===String.prototype,Ar=e=>e instanceof URL?(0,po.fileURLToPath)(e):e;var et=(e,t=[],r={})=>{let n=pe(e,"First argument"),[o,i]=x(t)?[[],t]:[t,r];if(!Array.isArray(o))throw new TypeError(`Second argument must be either an array of arguments or an options object: ${o}`);if(o.some(c=>typeof c=="object"&&c!==null))throw new TypeError(`Second argument must be an array of strings: ${o}`);let s=o.map(String),a=s.find(c=>c.includes("\0"));if(a!==void 0)throw new TypeError(`Arguments cannot contain null bytes ("\\0"): ${a}`);if(!x(i))throw new TypeError(`Last argument must be an options object: ${i}`);return[n,s,i]};var xo=require("node:child_process");var uo=require("node:string_decoder"),{toString:mo}=Object.prototype,ho=e=>mo.call(e)==="[object ArrayBuffer]",A=e=>mo.call(e)==="[object Uint8Array]",W=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),yf=new TextEncoder,go=e=>yf.encode(e),Sf=new TextDecoder,tt=e=>Sf.decode(e),yo=(e,t)=>bf(e,t).join(""),bf=(e,t)=>{if(t==="utf8"&&e.every(i=>typeof i=="string"))return e;let r=new uo.StringDecoder(t),n=e.map(i=>typeof i=="string"?go(i):i).map(i=>r.write(i)),o=r.end();return o===""?n:[...n,o]},Be=e=>e.length===1&&A(e[0])?e[0]:Or(wf(e)),wf=e=>e.map(t=>typeof t=="string"?go(t):t),Or=e=>{let t=new Uint8Array(xf(e)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t},xf=e=>{let t=0;for(let r of e)t+=r.length;return t};var Eo=e=>Array.isArray(e)&&Array.isArray(e.raw),To=(e,t)=>{let r=[];for(let[i,s]of e.entries())r=Ef({templates:e,expressions:t,tokens:r,index:i,template:s});if(r.length===0)throw new TypeError("Template script must not be empty");let[n,...o]=r;return[n,o,{}]},Ef=({templates:e,expressions:t,tokens:r,index:n,template:o})=>{if(o===void 0)throw new TypeError(`Invalid backslash sequence: ${e.raw[n]}`);let{nextTokens:i,leadingWhitespaces:s,trailingWhitespaces:a}=Tf(o,e.raw[n]),c=bo(r,i,s);if(n===t.length)return c;let f=t[n],l=Array.isArray(f)?f.map(d=>wo(d)):[wo(f)];return bo(c,l,a)},Tf=(e,t)=>{if(t.length===0)return{nextTokens:[],leadingWhitespaces:!1,trailingWhitespaces:!1};let r=[],n=0,o=So.has(t[0]);for(let s=0,a=0;s<e.length;s+=1,a+=1){let c=t[a];if(So.has(c))n!==s&&r.push(e.slice(n,s)),n=s+1;else if(c==="\\"){let f=t[a+1];f==="u"&&t[a+2]==="{"?a=t.indexOf("}",a+3):a+=Af[f]??1}}let i=n===e.length;return i||r.push(e.slice(n)),{nextTokens:r,leadingWhitespaces:o,trailingWhitespaces:i}},So=new Set([" ","	","\r",`
`]),Af={x:3,u:5},bo=(e,t,r)=>r||e.length===0||t.length===0?[...e,...t]:[...e.slice(0,-1),`${e.at(-1)}${t[0]}`,...t.slice(1)],wo=e=>{let t=typeof e;if(t==="string")return e;if(t==="number")return String(e);if(x(e)&&("stdout"in e||"isMaxBuffer"in e))return Of(e);throw e instanceof xo.ChildProcess||Object.prototype.toString.call(e)==="[object Promise]"?new TypeError("Unexpected subprocess in template expression. Please use ${await subprocess} instead of ${subprocess}."):new TypeError(`Unexpected "${t}" in template expression`)},Of=({stdout:e})=>{if(typeof e=="string")return e;if(A(e))return tt(e);throw e===void 0?new TypeError(`Missing result.stdout in template expression. This is probably due to the previous subprocess' "stdout" option.`):new TypeError(`Unexpected "${typeof e}" stdout in template expression`)};var Lc=require("node:child_process");var Oo=require("node:util");var rt=T(require("node:process"),1),B=e=>nt.includes(e),nt=[rt.default.stdin,rt.default.stdout,rt.default.stderr],M=["stdin","stdout","stderr"],ot=e=>M[e]??`stdio[${e}]`;var Io=e=>{let t={...e};for(let r of Rr)t[r]=Ir(e,r);return t},Ir=(e,t)=>{let r=Array.from({length:If(e)+1}),n=Df(e[t],r,t);return Bf(n,t)},If=({stdio:e})=>Array.isArray(e)?Math.max(e.length,M.length):M.length,Df=(e,t,r)=>x(e)?Rf(e,t,r):t.fill(e),Rf=(e,t,r)=>{for(let n of Object.keys(e).sort(Cf))for(let o of Mf(n,r,t))t[o]=e[n];return t},Cf=(e,t)=>Ao(e)<Ao(t)?1:-1,Ao=e=>e==="stdout"||e==="stderr"?0:e==="all"?2:1,Mf=(e,t,r)=>{if(e==="ipc")return[r.length-1];let n=Dr(e);if(n===void 0||n===0)throw new TypeError(`"${t}.${e}" is invalid.
It must be "${t}.stdout", "${t}.stderr", "${t}.all", "${t}.ipc", or "${t}.fd3", "${t}.fd4" (and so on).`);if(n>=r.length)throw new TypeError(`"${t}.${e}" is invalid: that file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);return n==="all"?[1,2]:[n]},Dr=e=>{if(e==="all")return e;if(M.includes(e))return M.indexOf(e);let t=Pf.exec(e);if(t!==null)return Number(t[1])},Pf=/^fd(\d+)$/,Bf=(e,t)=>e.map(r=>r===void 0?Ff[t]:r),Lf=(0,Oo.debuglog)("execa").enabled?"full":"none",Ff={lines:!1,buffer:!0,maxBuffer:1e3*1e3*100,verbose:Lf,stripFinalNewline:!0},Rr=["lines","buffer","maxBuffer","verbose","stripFinalNewline"],V=(e,t)=>t==="ipc"?e.at(-1):e[t];var ue=({verbose:e},t)=>Cr(e,t)!=="none",me=({verbose:e},t)=>!["none","short"].includes(Cr(e,t)),Do=({verbose:e},t)=>{let r=Cr(e,t);return it(r)?r:void 0},Cr=(e,t)=>t===void 0?$f(e):V(e,t),$f=e=>e.find(t=>it(t))??st.findLast(t=>e.includes(t)),it=e=>typeof e=="function",st=["none","short","full"];var Go=require("node:util");var Ro=require("node:process"),Co=require("node:util"),Mo=(e,t)=>{let r=[e,...t],n=r.join(" "),o=r.map(i=>kf(Po(i))).join(" ");return{command:n,escapedCommand:o}},Le=e=>(0,Co.stripVTControlCharacters)(e).split(`
`).map(t=>Po(t)).join(`
`),Po=e=>e.replaceAll(Nf,t=>vf(t)),vf=e=>{let t=_f[e];if(t!==void 0)return t;let r=e.codePointAt(0),n=r.toString(16);return r<=jf?`\\u${n.padStart(4,"0")}`:`\\U${n}`},Uf=()=>{try{return new RegExp("\\p{Separator}|\\p{Other}","gu")}catch{return/[\s\u0000-\u001F\u007F-\u009F\u00AD]/g}},Nf=Uf(),_f={" ":" ","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},jf=65535,kf=e=>Gf.test(e)?e:Ro.platform==="win32"?`"${e.replaceAll('"','""')}"`:`'${e.replaceAll("'","'\\''")}'`,Gf=/^[\w./-]+$/;var Bo={circleQuestionMark:"(?)",questionMarkPrefix:"(?)",square:"\u2588",squareDarkShade:"\u2593",squareMediumShade:"\u2592",squareLightShade:"\u2591",squareTop:"\u2580",squareBottom:"\u2584",squareLeft:"\u258C",squareRight:"\u2590",squareCenter:"\u25A0",bullet:"\u25CF",dot:"\u2024",ellipsis:"\u2026",pointerSmall:"\u203A",triangleUp:"\u25B2",triangleUpSmall:"\u25B4",triangleDown:"\u25BC",triangleDownSmall:"\u25BE",triangleLeftSmall:"\u25C2",triangleRightSmall:"\u25B8",home:"\u2302",heart:"\u2665",musicNote:"\u266A",musicNoteBeamed:"\u266B",arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",arrowLeftRight:"\u2194",arrowUpDown:"\u2195",almostEqual:"\u2248",notEqual:"\u2260",lessOrEqual:"\u2264",greaterOrEqual:"\u2265",identical:"\u2261",infinity:"\u221E",subscriptZero:"\u2080",subscriptOne:"\u2081",subscriptTwo:"\u2082",subscriptThree:"\u2083",subscriptFour:"\u2084",subscriptFive:"\u2085",subscriptSix:"\u2086",subscriptSeven:"\u2087",subscriptEight:"\u2088",subscriptNine:"\u2089",oneHalf:"\xBD",oneThird:"\u2153",oneQuarter:"\xBC",oneFifth:"\u2155",oneSixth:"\u2159",oneEighth:"\u215B",twoThirds:"\u2154",twoFifths:"\u2156",threeQuarters:"\xBE",threeFifths:"\u2157",threeEighths:"\u215C",fourFifths:"\u2158",fiveSixths:"\u215A",fiveEighths:"\u215D",sevenEighths:"\u215E",line:"\u2500",lineBold:"\u2501",lineDouble:"\u2550",lineDashed0:"\u2504",lineDashed1:"\u2505",lineDashed2:"\u2508",lineDashed3:"\u2509",lineDashed4:"\u254C",lineDashed5:"\u254D",lineDashed6:"\u2574",lineDashed7:"\u2576",lineDashed8:"\u2578",lineDashed9:"\u257A",lineDashed10:"\u257C",lineDashed11:"\u257E",lineDashed12:"\u2212",lineDashed13:"\u2013",lineDashed14:"\u2010",lineDashed15:"\u2043",lineVertical:"\u2502",lineVerticalBold:"\u2503",lineVerticalDouble:"\u2551",lineVerticalDashed0:"\u2506",lineVerticalDashed1:"\u2507",lineVerticalDashed2:"\u250A",lineVerticalDashed3:"\u250B",lineVerticalDashed4:"\u254E",lineVerticalDashed5:"\u254F",lineVerticalDashed6:"\u2575",lineVerticalDashed7:"\u2577",lineVerticalDashed8:"\u2579",lineVerticalDashed9:"\u257B",lineVerticalDashed10:"\u257D",lineVerticalDashed11:"\u257F",lineDownLeft:"\u2510",lineDownLeftArc:"\u256E",lineDownBoldLeftBold:"\u2513",lineDownBoldLeft:"\u2512",lineDownLeftBold:"\u2511",lineDownDoubleLeftDouble:"\u2557",lineDownDoubleLeft:"\u2556",lineDownLeftDouble:"\u2555",lineDownRight:"\u250C",lineDownRightArc:"\u256D",lineDownBoldRightBold:"\u250F",lineDownBoldRight:"\u250E",lineDownRightBold:"\u250D",lineDownDoubleRightDouble:"\u2554",lineDownDoubleRight:"\u2553",lineDownRightDouble:"\u2552",lineUpLeft:"\u2518",lineUpLeftArc:"\u256F",lineUpBoldLeftBold:"\u251B",lineUpBoldLeft:"\u251A",lineUpLeftBold:"\u2519",lineUpDoubleLeftDouble:"\u255D",lineUpDoubleLeft:"\u255C",lineUpLeftDouble:"\u255B",lineUpRight:"\u2514",lineUpRightArc:"\u2570",lineUpBoldRightBold:"\u2517",lineUpBoldRight:"\u2516",lineUpRightBold:"\u2515",lineUpDoubleRightDouble:"\u255A",lineUpDoubleRight:"\u2559",lineUpRightDouble:"\u2558",lineUpDownLeft:"\u2524",lineUpBoldDownBoldLeftBold:"\u252B",lineUpBoldDownBoldLeft:"\u2528",lineUpDownLeftBold:"\u2525",lineUpBoldDownLeftBold:"\u2529",lineUpDownBoldLeftBold:"\u252A",lineUpDownBoldLeft:"\u2527",lineUpBoldDownLeft:"\u2526",lineUpDoubleDownDoubleLeftDouble:"\u2563",lineUpDoubleDownDoubleLeft:"\u2562",lineUpDownLeftDouble:"\u2561",lineUpDownRight:"\u251C",lineUpBoldDownBoldRightBold:"\u2523",lineUpBoldDownBoldRight:"\u2520",lineUpDownRightBold:"\u251D",lineUpBoldDownRightBold:"\u2521",lineUpDownBoldRightBold:"\u2522",lineUpDownBoldRight:"\u251F",lineUpBoldDownRight:"\u251E",lineUpDoubleDownDoubleRightDouble:"\u2560",lineUpDoubleDownDoubleRight:"\u255F",lineUpDownRightDouble:"\u255E",lineDownLeftRight:"\u252C",lineDownBoldLeftBoldRightBold:"\u2533",lineDownLeftBoldRightBold:"\u252F",lineDownBoldLeftRight:"\u2530",lineDownBoldLeftBoldRight:"\u2531",lineDownBoldLeftRightBold:"\u2532",lineDownLeftRightBold:"\u252E",lineDownLeftBoldRight:"\u252D",lineDownDoubleLeftDoubleRightDouble:"\u2566",lineDownDoubleLeftRight:"\u2565",lineDownLeftDoubleRightDouble:"\u2564",lineUpLeftRight:"\u2534",lineUpBoldLeftBoldRightBold:"\u253B",lineUpLeftBoldRightBold:"\u2537",lineUpBoldLeftRight:"\u2538",lineUpBoldLeftBoldRight:"\u2539",lineUpBoldLeftRightBold:"\u253A",lineUpLeftRightBold:"\u2536",lineUpLeftBoldRight:"\u2535",lineUpDoubleLeftDoubleRightDouble:"\u2569",lineUpDoubleLeftRight:"\u2568",lineUpLeftDoubleRightDouble:"\u2567",lineUpDownLeftRight:"\u253C",lineUpBoldDownBoldLeftBoldRightBold:"\u254B",lineUpDownBoldLeftBoldRightBold:"\u2548",lineUpBoldDownLeftBoldRightBold:"\u2547",lineUpBoldDownBoldLeftRightBold:"\u254A",lineUpBoldDownBoldLeftBoldRight:"\u2549",lineUpBoldDownLeftRight:"\u2540",lineUpDownBoldLeftRight:"\u2541",lineUpDownLeftBoldRight:"\u253D",lineUpDownLeftRightBold:"\u253E",lineUpBoldDownBoldLeftRight:"\u2542",lineUpDownLeftBoldRightBold:"\u253F",lineUpBoldDownLeftBoldRight:"\u2543",lineUpBoldDownLeftRightBold:"\u2544",lineUpDownBoldLeftBoldRight:"\u2545",lineUpDownBoldLeftRightBold:"\u2546",lineUpDoubleDownDoubleLeftDoubleRightDouble:"\u256C",lineUpDoubleDownDoubleLeftRight:"\u256B",lineUpDownLeftDoubleRightDouble:"\u256A",lineCross:"\u2573",lineBackslash:"\u2572",lineSlash:"\u2571"},Lo={tick:"\u2714",info:"\u2139",warning:"\u26A0",cross:"\u2718",squareSmall:"\u25FB",squareSmallFilled:"\u25FC",circle:"\u25EF",circleFilled:"\u25C9",circleDotted:"\u25CC",circleDouble:"\u25CE",circleCircle:"\u24DE",circleCross:"\u24E7",circlePipe:"\u24BE",radioOn:"\u25C9",radioOff:"\u25EF",checkboxOn:"\u2612",checkboxOff:"\u2610",checkboxCircleOn:"\u24E7",checkboxCircleOff:"\u24BE",pointer:"\u276F",triangleUpOutline:"\u25B3",triangleLeft:"\u25C0",triangleRight:"\u25B6",lozenge:"\u25C6",lozengeOutline:"\u25C7",hamburger:"\u2630",smiley:"\u32E1",mustache:"\u0DF4",star:"\u2605",play:"\u25B6",nodejs:"\u2B22",oneSeventh:"\u2150",oneNinth:"\u2151",oneTenth:"\u2152"},zf={tick:"\u221A",info:"i",warning:"\u203C",cross:"\xD7",squareSmall:"\u25A1",squareSmallFilled:"\u25A0",circle:"( )",circleFilled:"(*)",circleDotted:"( )",circleDouble:"( )",circleCircle:"(\u25CB)",circleCross:"(\xD7)",circlePipe:"(\u2502)",radioOn:"(*)",radioOff:"( )",checkboxOn:"[\xD7]",checkboxOff:"[ ]",checkboxCircleOn:"(\xD7)",checkboxCircleOff:"( )",pointer:">",triangleUpOutline:"\u2206",triangleLeft:"\u25C4",triangleRight:"\u25BA",lozenge:"\u2666",lozengeOutline:"\u25CA",hamburger:"\u2261",smiley:"\u263A",mustache:"\u250C\u2500\u2510",star:"\u2736",play:"\u25BA",nodejs:"\u2666",oneSeventh:"1/7",oneNinth:"1/9",oneTenth:"1/10"},Wf={...Bo,...Lo},Vf={...Bo,...zf},Yf=Er(),qf=Yf?Wf:Vf,at=qf,iy=Object.entries(Lo);var Fo=T(require("node:tty"),1),Hf=Fo.default?.WriteStream?.prototype?.hasColors?.()??!1,y=(e,t)=>{if(!Hf)return o=>o;let r=`\x1B[${e}m`,n=`\x1B[${t}m`;return o=>{let i=o+"",s=i.indexOf(n);if(s===-1)return r+i+n;let a=r,c=0;for(;s!==-1;)a+=i.slice(c,s)+r,c=s+n.length,s=i.indexOf(n,c);return a+=i.slice(c)+n,a}},ay=y(0,0),$o=y(1,22),cy=y(2,22),ly=y(3,23),fy=y(4,24),dy=y(53,55),py=y(7,27),uy=y(8,28),my=y(9,29),hy=y(30,39),gy=y(31,39),yy=y(32,39),Sy=y(33,39),by=y(34,39),wy=y(35,39),xy=y(36,39),Ey=y(37,39),ct=y(90,39),Ty=y(40,49),Ay=y(41,49),Oy=y(42,49),Iy=y(43,49),Dy=y(44,49),Ry=y(45,49),Cy=y(46,49),My=y(47,49),Py=y(100,49),vo=y(91,39),By=y(92,39),Uo=y(93,39),Ly=y(94,39),Fy=y(95,39),$y=y(96,39),vy=y(97,39),Uy=y(101,49),Ny=y(102,49),_y=y(103,49),jy=y(104,49),ky=y(105,49),Gy=y(106,49),zy=y(107,49);var jo=({type:e,message:t,timestamp:r,piped:n,commandId:o,result:{failed:i=!1}={},options:{reject:s=!0}})=>{let a=Kf(r),c=Xf[e]({failed:i,reject:s,piped:n}),f=Jf[e]({reject:s});return`${ct(`[${a}]`)} ${ct(`[${o}]`)} ${f(c)} ${f(t)}`},Kf=e=>`${lt(e.getHours(),2)}:${lt(e.getMinutes(),2)}:${lt(e.getSeconds(),2)}.${lt(e.getMilliseconds(),3)}`,lt=(e,t)=>String(e).padStart(t,"0"),No=({failed:e,reject:t})=>e?t?at.cross:at.warning:at.tick,Xf={command:({piped:e})=>e?"|":"$",output:()=>" ",ipc:()=>"*",error:No,duration:No},_o=e=>e,Jf={command:()=>$o,output:()=>_o,ipc:()=>_o,error:({reject:e})=>e?vo:Uo,duration:()=>ct};var ko=(e,t,r)=>{let n=Do(t,r);return e.map(({verboseLine:o,verboseObject:i})=>Zf(o,i,n)).filter(o=>o!==void 0).map(o=>Qf(o)).join("")},Zf=(e,t,r)=>{if(r===void 0)return e;let n=r(e,t);if(typeof n=="string")return n},Qf=e=>e.endsWith(`
`)?e:`${e}
`;var v=({type:e,verboseMessage:t,fdNumber:r,verboseInfo:n,result:o})=>{let i=ed({type:e,result:o,verboseInfo:n}),s=td(t,i),a=ko(s,n,r);a!==""&&console.warn(a.slice(0,-1))},ed=({type:e,result:t,verboseInfo:{escapedCommand:r,commandId:n,rawOptions:{piped:o=!1,...i}}})=>({type:e,escapedCommand:r,commandId:`${n}`,timestamp:new Date,piped:o,result:t,options:i}),td=(e,t)=>e.split(`
`).map(r=>rd({...t,message:r})),rd=e=>({verboseLine:jo(e),verboseObject:e}),ft=e=>{let t=typeof e=="string"?e:(0,Go.inspect)(e);return Le(t).replaceAll("	"," ".repeat(nd))},nd=2;var zo=(e,t)=>{ue(t)&&v({type:"command",verboseMessage:e,verboseInfo:t})};var Wo=(e,t,r)=>{sd(e);let n=od(e);return{verbose:e,escapedCommand:t,commandId:n,rawOptions:r}},od=e=>ue({verbose:e})?id++:void 0,id=0n,sd=e=>{for(let t of e){if(t===!1)throw new TypeError(`The "verbose: false" option was renamed to "verbose: 'none'".`);if(t===!0)throw new TypeError(`The "verbose: true" option was renamed to "verbose: 'short'".`);if(!st.includes(t)&&!it(t)){let r=st.map(n=>`'${n}'`).join(", ");throw new TypeError(`The "verbose" option must not be ${t}. Allowed values are: ${r} or a function.`)}}};var Mr=require("node:process"),dt=()=>Mr.hrtime.bigint(),Pr=e=>Number(Mr.hrtime.bigint()-e)/1e6;var pt=(e,t,r)=>{let n=dt(),{command:o,escapedCommand:i}=Mo(e,t),s=Ir(r,"verbose"),a=Wo(s,i,{...r});return zo(i,a),{command:o,escapedCommand:i,startTime:n,verboseInfo:a}};var oa=T(require("node:path"),1),tn=T(require("node:process"),1),ia=T(Bi(),1);var Fe=T(require("node:process"),1),J=T(require("node:path"),1);function mt(e={}){let{env:t=process.env,platform:r=process.platform}=e;return r!=="win32"?"PATH":Object.keys(t).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"}var Li=require("node:util"),Gr=require("node:child_process"),kr=T(require("node:path"),1),Fi=require("node:url"),DS=(0,Li.promisify)(Gr.execFile);function ht(e){return e instanceof URL?(0,Fi.fileURLToPath)(e):e}function $i(e){return{*[Symbol.iterator](){let t=kr.default.resolve(ht(e)),r;for(;r!==t;)yield t,r=t,t=kr.default.resolve(t,"..")}}}var RS=10*1024*1024;var Ld=({cwd:e=Fe.default.cwd(),path:t=Fe.default.env[mt()],preferLocal:r=!0,execPath:n=Fe.default.execPath,addExecPath:o=!0}={})=>{let i=J.default.resolve(ht(e)),s=[],a=t.split(J.default.delimiter);return r&&Fd(s,a,i),o&&$d(s,a,n,i),t===""||t===J.default.delimiter?`${s.join(J.default.delimiter)}${t}`:[...s,t].join(J.default.delimiter)},Fd=(e,t,r)=>{for(let n of $i(r)){let o=J.default.join(n,"node_modules/.bin");t.includes(o)||e.push(o)}},$d=(e,t,r,n)=>{let o=J.default.resolve(n,ht(r),"..");t.includes(o)||e.push(o)},vi=({env:e=Fe.default.env,...t}={})=>{e={...e};let r=mt({env:e});return t.path=e[r],e[r]=Ld(t),e};var Zi=require("node:timers/promises");var Ui=(e,t,r)=>{let n=r?ve:$e,o=e instanceof L?{}:{cause:e};return new n(t,o)},L=class extends Error{},Ni=(e,t)=>{Object.defineProperty(e.prototype,"name",{value:t,writable:!0,enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,ji,{value:!0,writable:!1,enumerable:!1,configurable:!1})},_i=e=>gt(e)&&ji in e,ji=Symbol("isExecaError"),gt=e=>Object.prototype.toString.call(e)==="[object Error]",$e=class extends Error{};Ni($e,$e.name);var ve=class extends Error{};Ni(ve,ve.name);var ye=require("node:os");var Yi=require("node:os");var ki=()=>{let e=zi-Gi+1;return Array.from({length:e},vd)},vd=(e,t)=>({name:`SIGRT${t+1}`,number:Gi+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),Gi=34,zi=64;var Vi=require("node:os");var Wi=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var zr=()=>{let e=ki();return[...Wi,...e].map(Ud)},Ud=({name:e,number:t,description:r,action:n,forced:o=!1,standard:i})=>{let{signals:{[e]:s}}=Vi.constants,a=s!==void 0;return{name:e,number:a?s:t,description:r,supported:a,action:n,forced:o,standard:i}};var Nd=()=>{let e=zr();return Object.fromEntries(e.map(_d))},_d=({name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s})=>[e,{name:e,number:t,description:r,supported:n,action:o,forced:i,standard:s}],qi=Nd(),jd=()=>{let e=zr(),t=65,r=Array.from({length:t},(n,o)=>kd(o,e));return Object.assign({},...r)},kd=(e,t)=>{let r=Gd(e,t);if(r===void 0)return{};let{name:n,description:o,supported:i,action:s,forced:a,standard:c}=r;return{[e]:{name:n,number:e,description:o,supported:i,action:s,forced:a,standard:c}}},Gd=(e,t)=>{let r=t.find(({name:n})=>Yi.constants.signals[n]===e);return r!==void 0?r:t.find(n=>n.number===e)},kS=jd();var Ki=e=>{let t="option `killSignal`";if(e===0)throw new TypeError(`Invalid ${t}: 0 cannot be used.`);return Ji(e,t)},Xi=e=>e===0?e:Ji(e,"`subprocess.kill()`'s argument"),Ji=(e,t)=>{if(Number.isInteger(e))return zd(e,t);if(typeof e=="string")return Vd(e,t);throw new TypeError(`Invalid ${t} ${String(e)}: it must be a string or an integer.
${Wr()}`)},zd=(e,t)=>{if(Hi.has(e))return Hi.get(e);throw new TypeError(`Invalid ${t} ${e}: this signal integer does not exist.
${Wr()}`)},Wd=()=>new Map(Object.entries(ye.constants.signals).reverse().map(([e,t])=>[t,e])),Hi=Wd(),Vd=(e,t)=>{if(e in ye.constants.signals)return e;throw e.toUpperCase()in ye.constants.signals?new TypeError(`Invalid ${t} '${e}': please rename it to '${e.toUpperCase()}'.`):new TypeError(`Invalid ${t} '${e}': this signal name does not exist.
${Wr()}`)},Wr=()=>`Available signal names: ${Yd()}.
Available signal numbers: ${qd()}.`,Yd=()=>Object.keys(ye.constants.signals).sort().map(e=>`'${e}'`).join(", "),qd=()=>[...new Set(Object.values(ye.constants.signals).sort((e,t)=>e-t))].join(", "),yt=e=>qi[e].description;var Qi=e=>{if(e===!1)return e;if(e===!0)return Hd;if(!Number.isFinite(e)||e<0)throw new TypeError(`Expected the \`forceKillAfterDelay\` option to be a non-negative integer, got \`${e}\` (${typeof e})`);return e},Hd=1e3*5,es=({kill:e,options:{forceKillAfterDelay:t,killSignal:r},onInternalError:n,context:o,controller:i},s,a)=>{let{signal:c,error:f}=Kd(s,a,r);Xd(f,n);let l=e(c);return Jd({kill:e,signal:c,forceKillAfterDelay:t,killSignal:r,killResult:l,context:o,controller:i}),l},Kd=(e,t,r)=>{let[n=r,o]=gt(e)?[void 0,e]:[e,t];if(typeof n!="string"&&!Number.isInteger(n))throw new TypeError(`The first argument must be an error instance or a signal name string/integer: ${String(n)}`);if(o!==void 0&&!gt(o))throw new TypeError(`The second argument is optional. If specified, it must be an error instance: ${o}`);return{signal:Xi(n),error:o}},Xd=(e,t)=>{e!==void 0&&t.reject(e)},Jd=async({kill:e,signal:t,forceKillAfterDelay:r,killSignal:n,killResult:o,context:i,controller:s})=>{t===n&&o&&Vr({kill:e,forceKillAfterDelay:r,context:i,controllerSignal:s.signal})},Vr=async({kill:e,forceKillAfterDelay:t,context:r,controllerSignal:n})=>{if(t!==!1)try{await(0,Zi.setTimeout)(t,void 0,{signal:n}),e("SIGKILL")&&(r.isForcefullyTerminated??=!0)}catch{}};var ts=require("node:events"),St=async(e,t)=>{e.aborted||await(0,ts.once)(e,"abort",{signal:t})};var rs=({cancelSignal:e})=>{if(e!==void 0&&Object.prototype.toString.call(e)!=="[object AbortSignal]")throw new Error(`The \`cancelSignal\` option must be an AbortSignal: ${String(e)}`)},ns=({subprocess:e,cancelSignal:t,gracefulCancel:r,context:n,controller:o})=>t===void 0||r?[]:[Zd(e,t,n,o)],Zd=async(e,t,r,{signal:n})=>{throw await St(t,n),r.terminationReason??="cancel",e.kill(),t.reason};var vs=require("node:timers/promises");var Fs=require("node:util");var Se=({methodName:e,isSubprocess:t,ipc:r,isConnected:n})=>{Qd(e,t,r),Yr(e,t,n)},Qd=(e,t,r)=>{if(!r)throw new Error(`${F(e,t)} can only be used if the \`ipc\` option is \`true\`.`)},Yr=(e,t,r)=>{if(!r)throw new Error(`${F(e,t)} cannot be used: the ${Z(t)} has already exited or disconnected.`)},os=e=>{throw new Error(`${F("getOneMessage",e)} could not complete: the ${Z(e)} exited or disconnected.`)},is=e=>{throw new Error(`${F("sendMessage",e)} failed: the ${Z(e)} is sending a message too, instead of listening to incoming messages.
This can be fixed by both sending a message and listening to incoming messages at the same time:

const [receivedMessage] = await Promise.all([
	${F("getOneMessage",e)},
	${F("sendMessage",e,"message, {strict: true}")},
]);`)},bt=(e,t)=>new Error(`${F("sendMessage",t)} failed when sending an acknowledgment response to the ${Z(t)}.`,{cause:e}),ss=e=>{throw new Error(`${F("sendMessage",e)} failed: the ${Z(e)} is not listening to incoming messages.`)},as=e=>{throw new Error(`${F("sendMessage",e)} failed: the ${Z(e)} exited without listening to incoming messages.`)},cs=()=>new Error(`\`cancelSignal\` aborted: the ${Z(!0)} disconnected.`),ls=()=>{throw new Error("`getCancelSignal()` cannot be used without setting the `cancelSignal` subprocess option.")},fs=({error:e,methodName:t,isSubprocess:r})=>{if(e.code==="EPIPE")throw new Error(`${F(t,r)} cannot be used: the ${Z(r)} is disconnecting.`,{cause:e})},ds=({error:e,methodName:t,isSubprocess:r,message:n})=>{if(ep(e))throw new Error(`${F(t,r)}'s argument type is invalid: the message cannot be serialized: ${String(n)}.`,{cause:e})},ep=({code:e,message:t})=>tp.has(e)||rp.some(r=>t.includes(r)),tp=new Set(["ERR_MISSING_ARGS","ERR_INVALID_ARG_TYPE"]),rp=["could not be cloned","circular structure","call stack size exceeded"],F=(e,t,r="")=>e==="cancelSignal"?"`cancelSignal`'s `controller.abort()`":`${np(t)}${e}(${r})`,np=e=>e?"":"subprocess.",Z=e=>e?"parent process":"subprocess",be=e=>{e.connected&&e.disconnect()};var U=()=>{let e={},t=new Promise((r,n)=>{Object.assign(e,{resolve:r,reject:n})});return Object.assign(t,e)};var xt=(e,t="stdin")=>{let{options:n,fileDescriptors:o}=N.get(e),i=ps(o,t,!0),s=e.stdio[i];if(s===null)throw new TypeError(us(i,t,n,!0));return s},we=(e,t="stdout")=>{let{options:n,fileDescriptors:o}=N.get(e),i=ps(o,t,!1),s=i==="all"?e.all:e.stdio[i];if(s==null)throw new TypeError(us(i,t,n,!1));return s},N=new WeakMap,ps=(e,t,r)=>{let n=op(t,r);return ip(n,t,r,e),n},op=(e,t)=>{let r=Dr(e);if(r!==void 0)return r;let{validOptions:n,defaultValue:o}=t?{validOptions:'"stdin"',defaultValue:"stdin"}:{validOptions:'"stdout", "stderr", "all"',defaultValue:"stdout"};throw new TypeError(`"${Ue(t)}" must not be "${e}".
It must be ${n} or "fd3", "fd4" (and so on).
It is optional and defaults to "${o}".`)},ip=(e,t,r,n)=>{let o=n[ms(e)];if(o===void 0)throw new TypeError(`"${Ue(r)}" must not be ${t}. That file descriptor does not exist.
Please set the "stdio" option to ensure that file descriptor exists.`);if(o.direction==="input"&&!r)throw new TypeError(`"${Ue(r)}" must not be ${t}. It must be a readable stream, not writable.`);if(o.direction!=="input"&&r)throw new TypeError(`"${Ue(r)}" must not be ${t}. It must be a writable stream, not readable.`)},us=(e,t,r,n)=>{if(e==="all"&&!r.all)return`The "all" option must be true to use "from: 'all'".`;let{optionName:o,optionValue:i}=sp(e,r);return`The "${o}: ${wt(i)}" option is incompatible with using "${Ue(n)}: ${wt(t)}".
Please set this option with "pipe" instead.`},sp=(e,{stdin:t,stdout:r,stderr:n,stdio:o})=>{let i=ms(e);return i===0&&t!==void 0?{optionName:"stdin",optionValue:t}:i===1&&r!==void 0?{optionName:"stdout",optionValue:r}:i===2&&n!==void 0?{optionName:"stderr",optionValue:n}:{optionName:`stdio[${i}]`,optionValue:o[i]}},ms=e=>e==="all"?1:e,Ue=e=>e?"to":"from",wt=e=>typeof e=="string"?`'${e}'`:typeof e=="number"?`${e}`:"Stream";var Ds=require("node:events");var hs=require("node:events"),se=(e,t,r)=>{let n=e.getMaxListeners();n===0||n===Number.POSITIVE_INFINITY||(e.setMaxListeners(n+t),(0,hs.addAbortListener)(r,()=>{e.setMaxListeners(e.getMaxListeners()-t)}))};var Is=require("node:events");var Ss=require("node:events"),bs=require("node:timers/promises");var Et=(e,t)=>{t&&qr(e)},qr=e=>{e.refCounted()},Tt=(e,t)=>{t&&Hr(e)},Hr=e=>{e.unrefCounted()},gs=(e,t)=>{t&&(Hr(e),Hr(e))},ys=(e,t)=>{t&&(qr(e),qr(e))};var ws=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n},o)=>{if(Ts(o)||Os(o))return;At.has(e)||At.set(e,[]);let i=At.get(e);if(i.push(o),!(i.length>1))for(;i.length>0;){await As(e,n,o),await bs.scheduler.yield();let s=await Es({wrappedMessage:i[0],anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n});i.shift(),n.emit("message",s),n.emit("message:done")}},xs=async({anyProcess:e,channel:t,isSubprocess:r,ipcEmitter:n,boundOnMessage:o})=>{Kr();let i=At.get(e);for(;i?.length>0;)await(0,Ss.once)(n,"message:done");e.removeListener("message",o),ys(t,r),n.connected=!1,n.emit("disconnect")},At=new WeakMap;var Q=(e,t,r)=>{if(Ot.has(e))return Ot.get(e);let n=new Is.EventEmitter;return n.connected=!0,Ot.set(e,n),ap({ipcEmitter:n,anyProcess:e,channel:t,isSubprocess:r}),n},Ot=new WeakMap,ap=({ipcEmitter:e,anyProcess:t,channel:r,isSubprocess:n})=>{let o=ws.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e});t.on("message",o),t.once("disconnect",xs.bind(void 0,{anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:e,boundOnMessage:o})),gs(r,n)},It=e=>{let t=Ot.get(e);return t===void 0?e.channel!==null:t.connected};var Rs=({anyProcess:e,channel:t,isSubprocess:r,message:n,strict:o})=>{if(!o)return n;let i=Q(e,t,r),s=Ct(e,i);return{id:cp++,type:Rt,message:n,hasListeners:s}},cp=0n,Cs=(e,t)=>{if(!(t?.type!==Rt||t.hasListeners))for(let{id:r}of e)r!==void 0&&Dt[r].resolve({isDeadlock:!0,hasListeners:!1})},Es=async({wrappedMessage:e,anyProcess:t,channel:r,isSubprocess:n,ipcEmitter:o})=>{if(e?.type!==Rt||!t.connected)return e;let{id:i,message:s}=e,a={id:i,type:Ps,message:Ct(t,o)};try{await Mt({anyProcess:t,channel:r,isSubprocess:n,ipc:!0},a)}catch(c){o.emit("strict:error",c)}return s},Ts=e=>{if(e?.type!==Ps)return!1;let{id:t,message:r}=e;return Dt[t]?.resolve({isDeadlock:!1,hasListeners:r}),!0},Ms=async(e,t,r)=>{if(e?.type!==Rt)return;let n=U();Dt[e.id]=n;let o=new AbortController;try{let{isDeadlock:i,hasListeners:s}=await Promise.race([n,lp(t,r,o)]);i&&is(r),s||ss(r)}finally{o.abort(),delete Dt[e.id]}},Dt={},lp=async(e,t,{signal:r})=>{se(e,1,r),await(0,Ds.once)(e,"disconnect",{signal:r}),as(t)},Rt="execa:ipc:request",Ps="execa:ipc:response";var Bs=(e,t,r)=>{Ne.has(e)||Ne.set(e,new Set);let n=Ne.get(e),o=U(),i=r?t.id:void 0,s={onMessageSent:o,id:i};return n.add(s),{outgoingMessages:n,outgoingMessage:s}},Ls=({outgoingMessages:e,outgoingMessage:t})=>{e.delete(t),t.onMessageSent.resolve()},As=async(e,t,r)=>{for(;!Ct(e,t)&&Ne.get(e)?.size>0;){let n=[...Ne.get(e)];Cs(n,r),await Promise.all(n.map(({onMessageSent:o})=>o))}},Ne=new WeakMap,Ct=(e,t)=>t.listenerCount("message")>fp(e),fp=e=>N.has(e)&&!V(N.get(e).options.buffer,"ipc")?1:0;var Mt=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},o,{strict:i=!1}={})=>{let s="sendMessage";return Se({methodName:s,isSubprocess:r,ipc:n,isConnected:e.connected}),dp({anyProcess:e,channel:t,methodName:s,isSubprocess:r,message:o,strict:i})},dp=async({anyProcess:e,channel:t,methodName:r,isSubprocess:n,message:o,strict:i})=>{let s=Rs({anyProcess:e,channel:t,isSubprocess:n,message:o,strict:i}),a=Bs(e,s,i);try{await Jr({anyProcess:e,methodName:r,isSubprocess:n,wrappedMessage:s,message:o})}catch(c){throw be(e),c}finally{Ls(a)}},Jr=async({anyProcess:e,methodName:t,isSubprocess:r,wrappedMessage:n,message:o})=>{let i=pp(e);try{await Promise.all([Ms(n,e,r),i(n)])}catch(s){throw fs({error:s,methodName:t,isSubprocess:r}),ds({error:s,methodName:t,isSubprocess:r,message:o}),s}},pp=e=>{if(Xr.has(e))return Xr.get(e);let t=(0,Fs.promisify)(e.send.bind(e));return Xr.set(e,t),t},Xr=new WeakMap;var Us=(e,t)=>{let r="cancelSignal";return Yr(r,!1,e.connected),Jr({anyProcess:e,methodName:r,isSubprocess:!1,wrappedMessage:{type:_s,message:t},message:t})},Ns=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>(await up({anyProcess:e,channel:t,isSubprocess:r,ipc:n}),Zr.signal),up=async({anyProcess:e,channel:t,isSubprocess:r,ipc:n})=>{if(!$s){if($s=!0,!n){ls();return}if(t===null){Kr();return}Q(e,t,r),await vs.scheduler.yield()}},$s=!1,Os=e=>e?.type!==_s?!1:(Zr.abort(e.message),!0),_s="execa:ipc:cancel",Kr=()=>{Zr.abort(cs())},Zr=new AbortController;var js=({gracefulCancel:e,cancelSignal:t,ipc:r,serialization:n})=>{if(e){if(t===void 0)throw new Error("The `cancelSignal` option must be defined when setting the `gracefulCancel` option.");if(!r)throw new Error("The `ipc` option cannot be false when setting the `gracefulCancel` option.");if(n==="json")throw new Error("The `serialization` option cannot be 'json' when setting the `gracefulCancel` option.")}},ks=({subprocess:e,cancelSignal:t,gracefulCancel:r,forceKillAfterDelay:n,context:o,controller:i})=>r?[mp({subprocess:e,cancelSignal:t,forceKillAfterDelay:n,context:o,controller:i})]:[],mp=async({subprocess:e,cancelSignal:t,forceKillAfterDelay:r,context:n,controller:{signal:o}})=>{await St(t,o);let i=hp(t);throw await Us(e,i),Vr({kill:e.kill,forceKillAfterDelay:r,context:n,controllerSignal:o}),n.terminationReason??="gracefulCancel",t.reason},hp=({reason:e})=>{if(!(e instanceof DOMException))return e;let t=new Error(e.message);return Object.defineProperty(t,"stack",{value:e.stack,enumerable:!1,configurable:!0,writable:!0}),t};var Gs=require("node:timers/promises");var zs=({timeout:e})=>{if(e!==void 0&&(!Number.isFinite(e)||e<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)},Ws=(e,t,r,n)=>t===0||t===void 0?[]:[gp(e,t,r,n)],gp=async(e,t,r,{signal:n})=>{throw await(0,Gs.setTimeout)(t,void 0,{signal:n}),r.terminationReason??="timeout",e.kill(),new L};var Pt=require("node:process"),Qr=T(require("node:path"),1);var Vs=({options:e})=>{if(e.node===!1)throw new TypeError('The "node" option cannot be false with `execaNode()`.');return{options:{...e,node:!0}}},Ys=(e,t,{node:r=!1,nodePath:n=Pt.execPath,nodeOptions:o=Pt.execArgv.filter(c=>!c.startsWith("--inspect")),cwd:i,execPath:s,...a})=>{if(s!==void 0)throw new TypeError('The "execPath" option has been removed. Please use the "nodePath" option instead.');let c=pe(n,'The "nodePath" option'),f=Qr.default.resolve(i,c),l={...a,nodePath:f,node:r,cwd:i};if(!r)return[e,t,l];if(Qr.default.basename(e,".exe")==="node")throw new TypeError('When the "node" option is true, the first argument does not need to be "node".');return[f,[...o,e,...t],{ipc:!0,...l,shell:!1}]};var qs=require("node:v8"),Hs=({ipcInput:e,ipc:t,serialization:r})=>{if(e!==void 0){if(!t)throw new Error("The `ipcInput` option cannot be set unless the `ipc` option is `true`.");bp[r](e)}},yp=e=>{try{(0,qs.serialize)(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with a structured clone.",{cause:t})}},Sp=e=>{try{JSON.stringify(e)}catch(t){throw new Error("The `ipcInput` option is not serializable with JSON.",{cause:t})}},bp={advanced:yp,json:Sp},Ks=async(e,t)=>{t!==void 0&&await e.sendMessage(t)};var Js=({encoding:e})=>{if(en.has(e))return;let t=xp(e);if(t!==void 0)throw new TypeError(`Invalid option \`encoding: ${Bt(e)}\`.
Please rename it to ${Bt(t)}.`);let r=[...en].map(n=>Bt(n)).join(", ");throw new TypeError(`Invalid option \`encoding: ${Bt(e)}\`.
Please rename it to one of: ${r}.`)},wp=new Set(["utf8","utf16le"]),R=new Set(["buffer","hex","base64","base64url","latin1","ascii"]),en=new Set([...wp,...R]),xp=e=>{if(e===null)return"buffer";if(typeof e!="string")return;let t=e.toLowerCase();if(t in Xs)return Xs[t];if(en.has(t))return t},Xs={"utf-8":"utf8","utf-16le":"utf16le","ucs-2":"utf16le",ucs2:"utf16le",binary:"latin1"},Bt=e=>typeof e=="string"?`"${e}"`:String(e);var Zs=require("node:fs"),Qs=T(require("node:path"),1),ea=T(require("node:process"),1);var ta=(e=ra())=>{let t=pe(e,'The "cwd" option');return Qs.default.resolve(t)},ra=()=>{try{return ea.default.cwd()}catch(e){throw e.message=`The current directory does not exist.
${e.message}`,e}},na=(e,t)=>{if(t===ra())return e;let r;try{r=(0,Zs.statSync)(t)}catch(n){return`The "cwd" option is invalid: ${t}.
${n.message}
${e}`}return r.isDirectory()?e:`The "cwd" option is not a directory: ${t}.
${e}`};var Lt=(e,t,r)=>{r.cwd=ta(r.cwd);let[n,o,i]=Ys(e,t,r),{command:s,args:a,options:c}=ia.default._parse(n,o,i),f=Io(c),l=Ep(f);return zs(l),Js(l),Hs(l),rs(l),js(l),l.shell=Ar(l.shell),l.env=Tp(l),l.killSignal=Ki(l.killSignal),l.forceKillAfterDelay=Qi(l.forceKillAfterDelay),l.lines=l.lines.map((d,p)=>d&&!R.has(l.encoding)&&l.buffer[p]),tn.default.platform==="win32"&&oa.default.basename(s,".exe")==="cmd"&&a.unshift("/q"),{file:s,commandArguments:a,options:l}},Ep=({extendEnv:e=!0,preferLocal:t=!1,cwd:r,localDir:n=r,encoding:o="utf8",reject:i=!0,cleanup:s=!0,all:a=!1,windowsHide:c=!0,killSignal:f="SIGTERM",forceKillAfterDelay:l=!0,gracefulCancel:d=!1,ipcInput:p,ipc:u=p!==void 0||d,serialization:m="advanced",...S})=>({...S,extendEnv:e,preferLocal:t,cwd:r,localDirectory:n,encoding:o,reject:i,cleanup:s,all:a,windowsHide:c,killSignal:f,forceKillAfterDelay:l,gracefulCancel:d,ipcInput:p,ipc:u,serialization:m}),Tp=({env:e,extendEnv:t,preferLocal:r,node:n,localDirectory:o,nodePath:i})=>{let s=t?{...tn.default.env,...e}:e;return r||n?vi({env:s,cwd:o,execPath:i,preferLocal:r,addExecPath:n}):s};var Da=require("node:util");function xe(e){if(typeof e=="string")return Ap(e);if(!(ArrayBuffer.isView(e)&&e.BYTES_PER_ELEMENT===1))throw new Error("Input must be a string or a Uint8Array");return Op(e)}var Ap=e=>e.at(-1)===sa?e.slice(0,e.at(-2)===aa?-2:-1):e,Op=e=>e.at(-1)===Ip?e.subarray(0,e.at(-2)===Dp?-2:-1):e,sa=`
`,Ip=sa.codePointAt(0),aa="\r",Dp=aa.codePointAt(0);var ba=require("node:events"),wa=require("node:stream/promises");function $(e,{checkOpen:t=!0}={}){return e!==null&&typeof e=="object"&&(e.writable||e.readable||!t||e.writable===void 0&&e.readable===void 0)&&typeof e.pipe=="function"}function rn(e,{checkOpen:t=!0}={}){return $(e,{checkOpen:t})&&(e.writable||!t)&&typeof e.write=="function"&&typeof e.end=="function"&&typeof e.writable=="boolean"&&typeof e.writableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function ae(e,{checkOpen:t=!0}={}){return $(e,{checkOpen:t})&&(e.readable||!t)&&typeof e.read=="function"&&typeof e.readable=="boolean"&&typeof e.readableObjectMode=="boolean"&&typeof e.destroy=="function"&&typeof e.destroyed=="boolean"}function nn(e,t){return rn(e,t)&&ae(e,t)}var Rp=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),on=class{#t;#r;#e=!1;#n=void 0;constructor(t,r){this.#t=t,this.#r=r}next(){let t=()=>this.#i();return this.#n=this.#n?this.#n.then(t,t):t(),this.#n}return(t){let r=()=>this.#o(t);return this.#n?this.#n.then(r,r):r()}async#i(){if(this.#e)return{done:!0,value:void 0};let t;try{t=await this.#t.read()}catch(r){throw this.#n=void 0,this.#e=!0,this.#t.releaseLock(),r}return t.done&&(this.#n=void 0,this.#e=!0,this.#t.releaseLock()),t}async#o(t){if(this.#e)return{done:!0,value:t};if(this.#e=!0,!this.#r){let r=this.#t.cancel(t);return this.#t.releaseLock(),await r,{done:!0,value:t}}return this.#t.releaseLock(),{done:!0,value:t}}},sn=Symbol();function ca(){return this[sn].next()}Object.defineProperty(ca,"name",{value:"next"});function la(e){return this[sn].return(e)}Object.defineProperty(la,"name",{value:"return"});var Cp=Object.create(Rp,{next:{enumerable:!0,configurable:!0,writable:!0,value:ca},return:{enumerable:!0,configurable:!0,writable:!0,value:la}});function an({preventCancel:e=!1}={}){let t=this.getReader(),r=new on(t,e),n=Object.create(Cp);return n[sn]=r,n}var fa=e=>{if(ae(e,{checkOpen:!1})&&_e.on!==void 0)return Pp(e);if(typeof e?.[Symbol.asyncIterator]=="function")return e;if(Mp.call(e)==="[object ReadableStream]")return an.call(e);throw new TypeError("The first argument must be a Readable, a ReadableStream, or an async iterable.")},{toString:Mp}=Object.prototype,Pp=async function*(e){let t=new AbortController,r={};Bp(e,t,r);try{for await(let[n]of _e.on(e,"data",{signal:t.signal}))yield n}catch(n){if(r.error!==void 0)throw r.error;if(!t.signal.aborted)throw n}finally{e.destroy()}},Bp=async(e,t,r)=>{try{await _e.finished(e,{cleanup:!0,readable:!0,writable:!1,error:!1})}catch(n){r.error=n}finally{t.abort()}},_e={};var Ee=async(e,{init:t,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,finalize:a},{maxBuffer:c=Number.POSITIVE_INFINITY}={})=>{let f=fa(e),l=t();l.length=0;try{for await(let d of f){let p=Fp(d),u=r[p](d,l);ua({convertedChunk:u,state:l,getSize:n,truncateChunk:o,addChunk:i,maxBuffer:c})}return Lp({state:l,convertChunk:r,getSize:n,truncateChunk:o,addChunk:i,getFinalChunk:s,maxBuffer:c}),a(l)}catch(d){let p=typeof d=="object"&&d!==null?d:new Error(d);throw p.bufferedData=a(l),p}},Lp=({state:e,getSize:t,truncateChunk:r,addChunk:n,getFinalChunk:o,maxBuffer:i})=>{let s=o(e);s!==void 0&&ua({convertedChunk:s,state:e,getSize:t,truncateChunk:r,addChunk:n,maxBuffer:i})},ua=({convertedChunk:e,state:t,getSize:r,truncateChunk:n,addChunk:o,maxBuffer:i})=>{let s=r(e),a=t.length+s;if(a<=i){da(e,t,o,a);return}let c=n(e,i-t.length);throw c!==void 0&&da(c,t,o,i),new _},da=(e,t,r,n)=>{t.contents=r(e,t,n),t.length=n},Fp=e=>{let t=typeof e;if(t==="string")return"string";if(t!=="object"||e===null)return"others";if(globalThis.Buffer?.isBuffer(e))return"buffer";let r=pa.call(e);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(e.byteLength)&&Number.isInteger(e.byteOffset)&&pa.call(e.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:pa}=Object.prototype,_=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded")}};var Y=e=>e,je=()=>{},Ft=({contents:e})=>e,$t=e=>{throw new Error(`Streams in object mode are not supported: ${String(e)}`)},vt=e=>e.length;async function Ut(e,t){return Ee(e,Np,t)}var $p=()=>({contents:[]}),vp=()=>1,Up=(e,{contents:t})=>(t.push(e),t),Np={init:$p,convertChunk:{string:Y,buffer:Y,arrayBuffer:Y,dataView:Y,typedArray:Y,others:Y},getSize:vp,truncateChunk:je,addChunk:Up,getFinalChunk:je,finalize:Ft};async function Nt(e,t){return Ee(e,qp,t)}var _p=()=>({contents:new ArrayBuffer(0)}),jp=e=>kp.encode(e),kp=new TextEncoder,ma=e=>new Uint8Array(e),ha=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),Gp=(e,t)=>e.slice(0,t),zp=(e,{contents:t,length:r},n)=>{let o=Sa()?Vp(t,n):Wp(t,n);return new Uint8Array(o).set(e,r),o},Wp=(e,t)=>{if(t<=e.byteLength)return e;let r=new ArrayBuffer(ya(t));return new Uint8Array(r).set(new Uint8Array(e),0),r},Vp=(e,t)=>{if(t<=e.maxByteLength)return e.resize(t),e;let r=new ArrayBuffer(t,{maxByteLength:ya(t)});return new Uint8Array(r).set(new Uint8Array(e),0),r},ya=e=>ga**Math.ceil(Math.log(e)/Math.log(ga)),ga=2,Yp=({contents:e,length:t})=>Sa()?e:e.slice(0,t),Sa=()=>"resize"in ArrayBuffer.prototype,qp={init:_p,convertChunk:{string:jp,buffer:ma,arrayBuffer:ma,dataView:ha,typedArray:ha,others:$t},getSize:vt,truncateChunk:Gp,addChunk:zp,getFinalChunk:je,finalize:Yp};async function jt(e,t){return Ee(e,Zp,t)}var Hp=()=>({contents:"",textDecoder:new TextDecoder}),_t=(e,{textDecoder:t})=>t.decode(e,{stream:!0}),Kp=(e,{contents:t})=>t+e,Xp=(e,t)=>e.slice(0,t),Jp=({textDecoder:e})=>{let t=e.decode();return t===""?void 0:t},Zp={init:Hp,convertChunk:{string:Y,buffer:_t,arrayBuffer:_t,dataView:_t,typedArray:_t,others:$t},getSize:vt,truncateChunk:Xp,addChunk:Kp,getFinalChunk:Jp,finalize:Ft};Object.assign(_e,{on:ba.on,finished:wa.finished});var xa=({error:e,stream:t,readableObjectMode:r,lines:n,encoding:o,fdNumber:i})=>{if(!(e instanceof _))throw e;if(i==="all")return e;let s=Qp(r,n,o);throw e.maxBufferInfo={fdNumber:i,unit:s},t.destroy(),e},Qp=(e,t,r)=>e?"objects":t?"lines":r==="buffer"?"bytes":"characters",Ea=(e,t,r)=>{if(t.length!==r)return;let n=new _;throw n.maxBufferInfo={fdNumber:"ipc"},n},Ta=(e,t)=>{let{streamName:r,threshold:n,unit:o}=eu(e,t);return`Command's ${r} was larger than ${n} ${o}`},eu=(e,t)=>{if(e?.maxBufferInfo===void 0)return{streamName:"output",threshold:t[1],unit:"bytes"};let{maxBufferInfo:{fdNumber:r,unit:n}}=e;delete e.maxBufferInfo;let o=V(t,r);return r==="ipc"?{streamName:"IPC output",threshold:o,unit:"messages"}:{streamName:ot(r),threshold:o,unit:n}},Aa=(e,t,r)=>e?.code==="ENOBUFS"&&t!==null&&t.some(n=>n!==null&&n.length>kt(r)),Oa=(e,t,r)=>{if(!t)return e;let n=kt(r);return e.length>n?e.slice(0,n):e},kt=([,e])=>e;var Ra=({stdio:e,all:t,ipcOutput:r,originalError:n,signal:o,signalDescription:i,exitCode:s,escapedCommand:a,timedOut:c,isCanceled:f,isGracefullyCanceled:l,isMaxBuffer:d,isForcefullyTerminated:p,forceKillAfterDelay:u,killSignal:m,maxBuffer:S,timeout:E,cwd:b})=>{let O=n?.code,I=tu({originalError:n,timedOut:c,timeout:E,isMaxBuffer:d,maxBuffer:S,errorCode:O,signal:o,signalDescription:i,exitCode:s,isCanceled:f,isGracefullyCanceled:l,isForcefullyTerminated:p,forceKillAfterDelay:u,killSignal:m}),C=nu(n,b),G=C===void 0?"":`
${C}`,H=`${I}: ${a}${G}`,ne=t===void 0?[e[2],e[1]]:[t],de=[H,...ne,...e.slice(3),r.map(K=>ou(K)).join(`
`)].map(K=>Le(xe(iu(K)))).filter(Boolean).join(`

`);return{originalMessage:C,shortMessage:H,message:de}},tu=({originalError:e,timedOut:t,timeout:r,isMaxBuffer:n,maxBuffer:o,errorCode:i,signal:s,signalDescription:a,exitCode:c,isCanceled:f,isGracefullyCanceled:l,isForcefullyTerminated:d,forceKillAfterDelay:p,killSignal:u})=>{let m=ru(d,p);return t?`Command timed out after ${r} milliseconds${m}`:l?s===void 0?`Command was gracefully canceled with exit code ${c}`:d?`Command was gracefully canceled${m}`:`Command was gracefully canceled with ${s} (${a})`:f?`Command was canceled${m}`:n?`${Ta(e,o)}${m}`:i!==void 0?`Command failed with ${i}${m}`:d?`Command was killed with ${u} (${yt(u)})${m}`:s!==void 0?`Command was killed with ${s} (${a})`:c!==void 0?`Command failed with exit code ${c}`:"Command failed"},ru=(e,t)=>e?` and was forcefully terminated after ${t} milliseconds`:"",nu=(e,t)=>{if(e instanceof L)return;let r=_i(e)?e.originalMessage:String(e?.message??e),n=Le(na(r,t));return n===""?void 0:n},ou=e=>typeof e=="string"?e:(0,Da.inspect)(e),iu=e=>Array.isArray(e)?e.map(t=>xe(Ia(t))).filter(Boolean).join(`
`):Ia(e),Ia=e=>typeof e=="string"?e:A(e)?tt(e):"";var Gt=({command:e,escapedCommand:t,stdio:r,all:n,ipcOutput:o,options:{cwd:i},startTime:s})=>Ca({command:e,escapedCommand:t,cwd:i,durationMs:Pr(s),failed:!1,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isTerminated:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,exitCode:0,stdout:r[1],stderr:r[2],all:n,stdio:r,ipcOutput:o,pipedFrom:[]}),Te=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:s})=>ke({error:e,command:t,escapedCommand:r,startTime:i,timedOut:!1,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:!1,isForcefullyTerminated:!1,stdio:Array.from({length:n.length}),ipcOutput:[],options:o,isSync:s}),ke=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,stdio:d,all:p,ipcOutput:u,options:{timeoutDuration:m,timeout:S=m,forceKillAfterDelay:E,killSignal:b,cwd:O,maxBuffer:I},isSync:C})=>{let{exitCode:G,signal:H,signalDescription:ne}=au(f,l),{originalMessage:de,shortMessage:K,message:pr}=Ra({stdio:d,all:p,ipcOutput:u,originalError:e,signal:H,signalDescription:ne,exitCode:G,escapedCommand:r,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,forceKillAfterDelay:E,killSignal:b,maxBuffer:I,timeout:S,cwd:O}),Me=Ui(e,pr,C);return Object.assign(Me,su({error:Me,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:G,signal:H,signalDescription:ne,stdio:d,all:p,ipcOutput:u,cwd:O,originalMessage:de,shortMessage:K})),Me},su=({error:e,command:t,escapedCommand:r,startTime:n,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,signalDescription:d,stdio:p,all:u,ipcOutput:m,cwd:S,originalMessage:E,shortMessage:b})=>Ca({shortMessage:b,originalMessage:E,command:t,escapedCommand:r,cwd:S,durationMs:Pr(n),failed:!0,timedOut:o,isCanceled:i,isGracefullyCanceled:s,isTerminated:l!==void 0,isMaxBuffer:a,isForcefullyTerminated:c,exitCode:f,signal:l,signalDescription:d,code:e.cause?.code,stdout:p[1],stderr:p[2],all:u,stdio:p,ipcOutput:m,pipedFrom:[]}),Ca=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0)),au=(e,t)=>{let r=e===null?void 0:e,n=t===null?void 0:t,o=n===void 0?void 0:yt(t);return{exitCode:r,signal:n,signalDescription:o}};var Ma=e=>Number.isFinite(e)?e:0;function cu(e){return{days:Math.trunc(e/864e5),hours:Math.trunc(e/36e5%24),minutes:Math.trunc(e/6e4%60),seconds:Math.trunc(e/1e3%60),milliseconds:Math.trunc(e%1e3),microseconds:Math.trunc(Ma(e*1e3)%1e3),nanoseconds:Math.trunc(Ma(e*1e6)%1e3)}}function lu(e){return{days:e/86400000n,hours:e/3600000n%24n,minutes:e/60000n%60n,seconds:e/1000n%60n,milliseconds:e%1000n,microseconds:0n,nanoseconds:0n}}function cn(e){switch(typeof e){case"number":{if(Number.isFinite(e))return cu(e);break}case"bigint":return lu(e)}throw new TypeError("Expected a finite number or bigint")}var fu=e=>e===0||e===0n,du=(e,t)=>t===1||t===1n?e:`${e}s`,pu=1e-7,uu=24n*60n*60n*1000n;function ln(e,t){let r=typeof e=="bigint";if(!r&&!Number.isFinite(e))throw new TypeError("Expected a finite number or bigint");t={...t};let n=e<0?"-":"";e=e<0?-e:e,t.colonNotation&&(t.compact=!1,t.formatSubMilliseconds=!1,t.separateMilliseconds=!1,t.verbose=!1),t.compact&&(t.unitCount=1,t.secondsDecimalDigits=0,t.millisecondsDecimalDigits=0);let o=[],i=(l,d)=>{let p=Math.floor(l*10**d+pu);return(Math.round(p)/10**d).toFixed(d)},s=(l,d,p,u)=>{if(!((o.length===0||!t.colonNotation)&&fu(l)&&!(t.colonNotation&&p==="m"))){if(u??=String(l),t.colonNotation){let m=u.includes(".")?u.split(".")[0].length:u.length,S=o.length>0?2:1;u="0".repeat(Math.max(0,S-m))+u}else u+=t.verbose?" "+du(d,l):p;o.push(u)}},a=cn(e),c=BigInt(a.days);if(s(c/365n,"year","y"),s(c%365n,"day","d"),s(Number(a.hours),"hour","h"),s(Number(a.minutes),"minute","m"),t.separateMilliseconds||t.formatSubMilliseconds||!t.colonNotation&&e<1e3){let l=Number(a.seconds),d=Number(a.milliseconds),p=Number(a.microseconds),u=Number(a.nanoseconds);if(s(l,"second","s"),t.formatSubMilliseconds)s(d,"millisecond","ms"),s(p,"microsecond","\xB5s"),s(u,"nanosecond","ns");else{let m=d+p/1e3+u/1e6,S=typeof t.millisecondsDecimalDigits=="number"?t.millisecondsDecimalDigits:0,E=m>=1?Math.round(m):Math.ceil(m),b=S?m.toFixed(S):E;s(Number.parseFloat(b),"millisecond","ms",b)}}else{let l=(r?Number(e%uu):e)/1e3%60,d=typeof t.secondsDecimalDigits=="number"?t.secondsDecimalDigits:1,p=i(l,d),u=t.keepDecimalsOnWholeSeconds?p:p.replace(/\.0+$/,"");s(Number.parseFloat(u),"second","s",u)}if(o.length===0)return n+"0"+(t.verbose?" milliseconds":"ms");let f=t.colonNotation?":":" ";return typeof t.unitCount=="number"&&(o=o.slice(0,Math.max(t.unitCount,1))),n+o.join(f)}var Pa=(e,t)=>{e.failed&&v({type:"error",verboseMessage:e.shortMessage,verboseInfo:t,result:e})};var Ba=(e,t)=>{ue(t)&&(Pa(e,t),mu(e,t))},mu=(e,t)=>{let r=`(done in ${ln(e.durationMs)})`;v({type:"duration",verboseMessage:r,verboseInfo:t,result:e})};var Ae=(e,t,{reject:r})=>{if(Ba(e,t),e.failed&&r)throw e;return e};var yn=require("node:fs");var $a=(e,t)=>ce(e)?"asyncGenerator":Na(e)?"generator":zt(e)?"fileUrl":bu(e)?"filePath":Eu(e)?"webStream":$(e,{checkOpen:!1})?"native":A(e)?"uint8Array":Tu(e)?"asyncIterable":Au(e)?"iterable":pn(e)?va({transform:e},t):Su(e)?hu(e,t):"native",hu=(e,t)=>nn(e.transform,{checkOpen:!1})?gu(e,t):pn(e.transform)?va(e,t):yu(e,t),gu=(e,t)=>(Ua(e,t,"Duplex stream"),"duplex"),va=(e,t)=>(Ua(e,t,"web TransformStream"),"webTransform"),Ua=({final:e,binary:t,objectMode:r},n,o)=>{La(e,`${n}.final`,o),La(t,`${n}.binary`,o),fn(r,`${n}.objectMode`)},La=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${t}\` option can only be defined when using a generator, not a ${r}.`)},yu=({transform:e,final:t,binary:r,objectMode:n},o)=>{if(e!==void 0&&!Fa(e))throw new TypeError(`The \`${o}.transform\` option must be a generator, a Duplex stream or a web TransformStream.`);if(nn(t,{checkOpen:!1}))throw new TypeError(`The \`${o}.final\` option must not be a Duplex stream.`);if(pn(t))throw new TypeError(`The \`${o}.final\` option must not be a web TransformStream.`);if(t!==void 0&&!Fa(t))throw new TypeError(`The \`${o}.final\` option must be a generator.`);return fn(r,`${o}.binary`),fn(n,`${o}.objectMode`),ce(e)||ce(t)?"asyncGenerator":"generator"},fn=(e,t)=>{if(e!==void 0&&typeof e!="boolean")throw new TypeError(`The \`${t}\` option must use a boolean.`)},Fa=e=>ce(e)||Na(e),ce=e=>Object.prototype.toString.call(e)==="[object AsyncGeneratorFunction]",Na=e=>Object.prototype.toString.call(e)==="[object GeneratorFunction]",Su=e=>x(e)&&(e.transform!==void 0||e.final!==void 0),zt=e=>Object.prototype.toString.call(e)==="[object URL]",_a=e=>zt(e)&&e.protocol!=="file:",bu=e=>x(e)&&Object.keys(e).length>0&&Object.keys(e).every(t=>wu.has(t))&&dn(e.file),wu=new Set(["file","append"]),dn=e=>typeof e=="string",ja=(e,t)=>e==="native"&&typeof t=="string"&&!xu.has(t),xu=new Set(["ipc","ignore","inherit","overlapped","pipe"]),ka=e=>Object.prototype.toString.call(e)==="[object ReadableStream]",Wt=e=>Object.prototype.toString.call(e)==="[object WritableStream]",Eu=e=>ka(e)||Wt(e),pn=e=>ka(e?.readable)&&Wt(e?.writable),Tu=e=>Ga(e)&&typeof e[Symbol.asyncIterator]=="function",Au=e=>Ga(e)&&typeof e[Symbol.iterator]=="function",Ga=e=>typeof e=="object"&&e!==null,P=new Set(["generator","asyncGenerator","duplex","webTransform"]),Vt=new Set(["fileUrl","filePath","fileNumber"]),un=new Set(["fileUrl","filePath"]),za=new Set([...un,"webStream","nodeStream"]),Wa=new Set(["webTransform","duplex"]),ee={generator:"a generator",asyncGenerator:"an async generator",fileUrl:"a file URL",filePath:"a file path string",fileNumber:"a file descriptor number",webStream:"a web stream",nodeStream:"a Node.js stream",webTransform:"a web TransformStream",duplex:"a Duplex stream",native:"any value",iterable:"an iterable",asyncIterable:"an async iterable",string:"a string",uint8Array:"a Uint8Array"};var mn=(e,t,r,n)=>n==="output"?Ou(e,t,r):Iu(e,t,r),Ou=(e,t,r)=>{let n=t!==0&&r[t-1].value.readableObjectMode;return{writableObjectMode:n,readableObjectMode:e??n}},Iu=(e,t,r)=>{let n=t===0?e===!0:r[t-1].value.readableObjectMode,o=t!==r.length-1&&(e??n);return{writableObjectMode:n,readableObjectMode:o}},Va=(e,t)=>{let r=e.findLast(({type:n})=>P.has(n));return r===void 0?!1:t==="input"?r.value.writableObjectMode:r.value.readableObjectMode};var Ya=(e,t,r,n)=>[...e.filter(({type:o})=>!P.has(o)),...Du(e,t,r,n)],Du=(e,t,r,{encoding:n})=>{let o=e.filter(({type:s})=>P.has(s)),i=Array.from({length:o.length});for(let[s,a]of Object.entries(o))i[s]=Ru({stdioItem:a,index:Number(s),newTransforms:i,optionName:t,direction:r,encoding:n});return Bu(i,r)},Ru=({stdioItem:e,stdioItem:{type:t},index:r,newTransforms:n,optionName:o,direction:i,encoding:s})=>t==="duplex"?Cu({stdioItem:e,optionName:o}):t==="webTransform"?Mu({stdioItem:e,index:r,newTransforms:n,direction:i}):Pu({stdioItem:e,index:r,newTransforms:n,direction:i,encoding:s}),Cu=({stdioItem:e,stdioItem:{value:{transform:t,transform:{writableObjectMode:r,readableObjectMode:n},objectMode:o=n}},optionName:i})=>{if(o&&!n)throw new TypeError(`The \`${i}.objectMode\` option can only be \`true\` if \`new Duplex({objectMode: true})\` is used.`);if(!o&&n)throw new TypeError(`The \`${i}.objectMode\` option cannot be \`false\` if \`new Duplex({objectMode: true})\` is used.`);return{...e,value:{transform:t,writableObjectMode:r,readableObjectMode:n}}},Mu=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o})=>{let{transform:i,objectMode:s}=x(t)?t:{transform:t},{writableObjectMode:a,readableObjectMode:c}=mn(s,r,n,o);return{...e,value:{transform:i,writableObjectMode:a,readableObjectMode:c}}},Pu=({stdioItem:e,stdioItem:{value:t},index:r,newTransforms:n,direction:o,encoding:i})=>{let{transform:s,final:a,binary:c=!1,preserveNewlines:f=!1,objectMode:l}=x(t)?t:{transform:t},d=c||R.has(i),{writableObjectMode:p,readableObjectMode:u}=mn(l,r,n,o);return{...e,value:{transform:s,final:a,binary:d,preserveNewlines:f,writableObjectMode:p,readableObjectMode:u}}},Bu=(e,t)=>t==="input"?e.reverse():e;var Yt=T(require("node:process"),1);var qa=(e,t,r)=>{let n=e.map(o=>Lu(o,t));if(n.includes("input")&&n.includes("output"))throw new TypeError(`The \`${r}\` option must not be an array of both readable and writable values.`);return n.find(Boolean)??vu},Lu=({type:e,value:t},r)=>Fu[r]??Ha[e](t),Fu=["input","output","output"],Oe=()=>{},hn=()=>"input",Ha={generator:Oe,asyncGenerator:Oe,fileUrl:Oe,filePath:Oe,iterable:hn,asyncIterable:hn,uint8Array:hn,webStream:e=>Wt(e)?"output":"input",nodeStream(e){return ae(e,{checkOpen:!1})?rn(e,{checkOpen:!1})?void 0:"input":"output"},webTransform:Oe,duplex:Oe,native(e){let t=$u(e);if(t!==void 0)return t;if($(e,{checkOpen:!1}))return Ha.nodeStream(e)}},$u=e=>{if([0,Yt.default.stdin].includes(e))return"input";if([1,2,Yt.default.stdout,Yt.default.stderr].includes(e))return"output"},vu="output";var Ka=(e,t)=>t&&!e.includes("ipc")?[...e,"ipc"]:e;var Xa=({stdio:e,ipc:t,buffer:r,...n},o,i)=>{let s=Uu(e,n).map((a,c)=>Ja(a,c));return i?_u(s,r,o):Ka(s,t)},Uu=(e,t)=>{if(e===void 0)return M.map(n=>t[n]);if(Nu(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${M.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return[e,e,e];if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,M.length);return Array.from({length:r},(n,o)=>e[o])},Nu=e=>M.some(t=>e[t]!==void 0),Ja=(e,t)=>Array.isArray(e)?e.map(r=>Ja(r,t)):e??(t>=M.length?"ignore":"pipe"),_u=(e,t,r)=>e.map((n,o)=>!t[o]&&o!==0&&!me(r,o)&&ju(n)?"ignore":n),ju=e=>e==="pipe"||Array.isArray(e)&&e.every(t=>t==="pipe");var Qa=require("node:fs"),ec=T(require("node:tty"),1);var tc=({stdioItem:e,stdioItem:{type:t},isStdioArray:r,fdNumber:n,direction:o,isSync:i})=>!r||t!=="native"?e:i?ku({stdioItem:e,fdNumber:n,direction:o}):Wu({stdioItem:e,fdNumber:n}),ku=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n,direction:o})=>{let i=Gu({value:t,optionName:r,fdNumber:n,direction:o});if(i!==void 0)return i;if($(t,{checkOpen:!1}))throw new TypeError(`The \`${r}: Stream\` option cannot both be an array and include a stream with synchronous methods.`);return e},Gu=({value:e,optionName:t,fdNumber:r,direction:n})=>{let o=zu(e,r);if(o!==void 0){if(n==="output")return{type:"fileNumber",value:o,optionName:t};if(ec.default.isatty(o))throw new TypeError(`The \`${t}: ${wt(e)}\` option is invalid: it cannot be a TTY with synchronous methods.`);return{type:"uint8Array",value:W((0,Qa.readFileSync)(o)),optionName:t}}},zu=(e,t)=>{if(e==="inherit")return t;if(typeof e=="number")return e;let r=nt.indexOf(e);if(r!==-1)return r},Wu=({stdioItem:e,stdioItem:{value:t,optionName:r},fdNumber:n})=>t==="inherit"?{type:"nodeStream",value:Za(n,t,r),optionName:r}:typeof t=="number"?{type:"nodeStream",value:Za(t,t,r),optionName:r}:$(t,{checkOpen:!1})?{type:"nodeStream",value:t,optionName:r}:e,Za=(e,t,r)=>{let n=nt[e];if(n===void 0)throw new TypeError(`The \`${r}: ${t}\` option is invalid: no such standard stream.`);return n};var rc=({input:e,inputFile:t},r)=>r===0?[...Vu(e),...qu(t)]:[],Vu=e=>e===void 0?[]:[{type:Yu(e),value:e,optionName:"input"}],Yu=e=>{if(ae(e,{checkOpen:!1}))return"nodeStream";if(typeof e=="string")return"string";if(A(e))return"uint8Array";throw new Error("The `input` option must be a string, a Uint8Array or a Node.js Readable stream.")},qu=e=>e===void 0?[]:[{...Hu(e),optionName:"inputFile"}],Hu=e=>{if(zt(e))return{type:"fileUrl",value:e};if(dn(e))return{type:"filePath",value:{file:e}};throw new Error("The `inputFile` option must be a file path string or a file URL.")};var nc=e=>e.filter((t,r)=>e.every((n,o)=>t.value!==n.value||r>=o||t.type==="generator"||t.type==="asyncGenerator")),oc=({stdioItem:{type:e,value:t,optionName:r},direction:n,fileDescriptors:o,isSync:i})=>{let s=Ku(o,e);if(s.length!==0){if(i){Xu({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});return}if(za.has(e))return ic({otherStdioItems:s,type:e,value:t,optionName:r,direction:n});Wa.has(e)&&Zu({otherStdioItems:s,type:e,value:t,optionName:r})}},Ku=(e,t)=>e.flatMap(({direction:r,stdioItems:n})=>n.filter(o=>o.type===t).map(o=>({...o,direction:r}))),Xu=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{un.has(t)&&ic({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})},ic=({otherStdioItems:e,type:t,value:r,optionName:n,direction:o})=>{let i=e.filter(a=>Ju(a,r));if(i.length===0)return;let s=i.find(a=>a.direction!==o);return sc(s,n,t),o==="output"?i[0].stream:void 0},Ju=({type:e,value:t},r)=>e==="filePath"?t.file===r.file:e==="fileUrl"?t.href===r.href:t===r,Zu=({otherStdioItems:e,type:t,value:r,optionName:n})=>{let o=e.find(({value:{transform:i}})=>i===r.transform);sc(o,n,t)},sc=(e,t,r)=>{if(e!==void 0)throw new TypeError(`The \`${e.optionName}\` and \`${t}\` options must not target ${ee[r]} that is the same.`)};var qt=(e,t,r,n)=>{let i=Xa(t,r,n).map((a,c)=>Qu({stdioOption:a,fdNumber:c,options:t,isSync:n})),s=am({initialFileDescriptors:i,addProperties:e,options:t,isSync:n});return t.stdio=s.map(({stdioItems:a})=>fm(a)),s},Qu=({stdioOption:e,fdNumber:t,options:r,isSync:n})=>{let o=ot(t),{stdioItems:i,isStdioArray:s}=em({stdioOption:e,fdNumber:t,options:r,optionName:o}),a=qa(i,t,o),c=i.map(d=>tc({stdioItem:d,isStdioArray:s,fdNumber:t,direction:a,isSync:n})),f=Ya(c,o,a,r),l=Va(f,a);return sm(f,l),{direction:a,objectMode:l,stdioItems:f}},em=({stdioOption:e,fdNumber:t,options:r,optionName:n})=>{let i=[...(Array.isArray(e)?e:[e]).map(c=>tm(c,n)),...rc(r,t)],s=nc(i),a=s.length>1;return rm(s,a,n),om(s),{stdioItems:s,isStdioArray:a}},tm=(e,t)=>({type:$a(e,t),value:e,optionName:t}),rm=(e,t,r)=>{if(e.length===0)throw new TypeError(`The \`${r}\` option must not be an empty array.`);if(t){for(let{value:n,optionName:o}of e)if(nm.has(n))throw new Error(`The \`${o}\` option must not include \`${n}\`.`)}},nm=new Set(["ignore","ipc"]),om=e=>{for(let t of e)im(t)},im=({type:e,value:t,optionName:r})=>{if(_a(t))throw new TypeError(`The \`${r}: URL\` option must use the \`file:\` scheme.
For example, you can use the \`pathToFileURL()\` method of the \`url\` core module.`);if(ja(e,t))throw new TypeError(`The \`${r}: { file: '...' }\` option must be used instead of \`${r}: '...'\`.`)},sm=(e,t)=>{if(!t)return;let r=e.find(({type:n})=>Vt.has(n));if(r!==void 0)throw new TypeError(`The \`${r.optionName}\` option cannot use both files and transforms in objectMode.`)},am=({initialFileDescriptors:e,addProperties:t,options:r,isSync:n})=>{let o=[];try{for(let i of e)o.push(cm({fileDescriptor:i,fileDescriptors:o,addProperties:t,options:r,isSync:n}));return o}catch(i){throw gn(o),i}},cm=({fileDescriptor:{direction:e,objectMode:t,stdioItems:r},fileDescriptors:n,addProperties:o,options:i,isSync:s})=>{let a=r.map(c=>lm({stdioItem:c,addProperties:o,direction:e,options:i,fileDescriptors:n,isSync:s}));return{direction:e,objectMode:t,stdioItems:a}},lm=({stdioItem:e,addProperties:t,direction:r,options:n,fileDescriptors:o,isSync:i})=>{let s=oc({stdioItem:e,direction:r,fileDescriptors:o,isSync:i});return s!==void 0?{...e,stream:s}:{...e,...t[r][e.type](e,n)}},gn=e=>{for(let{stdioItems:t}of e)for(let{stream:r}of t)r!==void 0&&!B(r)&&r.destroy()},fm=e=>{if(e.length>1)return e.some(({value:n})=>n==="overlapped")?"overlapped":"pipe";let[{type:t,value:r}]=e;return t==="native"?r:"pipe"};var cc=(e,t)=>qt(pm,e,t,!0),j=({type:e,optionName:t})=>{lc(t,ee[e])},dm=({optionName:e,value:t})=>((t==="ipc"||t==="overlapped")&&lc(e,`"${t}"`),{}),lc=(e,t)=>{throw new TypeError(`The \`${e}\` option cannot be ${t} with synchronous methods.`)},ac={generator(){},asyncGenerator:j,webStream:j,nodeStream:j,webTransform:j,duplex:j,asyncIterable:j,native:dm},pm={input:{...ac,fileUrl:({value:e})=>({contents:[W((0,yn.readFileSync)(e))]}),filePath:({value:{file:e}})=>({contents:[W((0,yn.readFileSync)(e))]}),fileNumber:j,iterable:({value:e})=>({contents:[...e]}),string:({value:e})=>({contents:[e]}),uint8Array:({value:e})=>({contents:[e]})},output:{...ac,fileUrl:({value:e})=>({path:e}),filePath:({value:{file:e,append:t}})=>({path:e,append:t}),fileNumber:({value:e})=>({path:e}),iterable:j,string:j,uint8Array:j}};var q=(e,{stripFinalNewline:t},r)=>Sn(t,r)&&e!==void 0&&!Array.isArray(e)?xe(e):e,Sn=(e,t)=>t==="all"?e[1]||e[2]:e[t];var ze=require("node:stream");var Ht=(e,t,r,n)=>e||r?void 0:dc(t,n),wn=(e,t,r)=>r?e.flatMap(n=>fc(n,t)):fc(e,t),fc=(e,t)=>{let{transform:r,final:n}=dc(t,{});return[...r(e),...n()]},dc=(e,t)=>(t.previousChunks="",{transform:um.bind(void 0,t,e),final:hm.bind(void 0,t)}),um=function*(e,t,r){if(typeof r!="string"){yield r;return}let{previousChunks:n}=e,o=-1;for(let i=0;i<r.length;i+=1)if(r[i]===`
`){let s=mm(r,i,t,e),a=r.slice(o+1,i+1-s);n.length>0&&(a=bn(n,a),n=""),yield a,o=i}o!==r.length-1&&(n=bn(n,r.slice(o+1))),e.previousChunks=n},mm=(e,t,r,n)=>r?0:(n.isWindowsNewline=t!==0&&e[t-1]==="\r",n.isWindowsNewline?2:1),hm=function*({previousChunks:e}){e.length>0&&(yield e)},pc=({binary:e,preserveNewlines:t,readableObjectMode:r,state:n})=>e||t||r?void 0:{transform:gm.bind(void 0,n)},gm=function*({isWindowsNewline:e=!1},t){let{unixNewline:r,windowsNewline:n,LF:o,concatBytes:i}=typeof t=="string"?ym:bm;if(t.at(-1)===o){yield t;return}yield i(t,e?n:r)},bn=(e,t)=>`${e}${t}`,ym={windowsNewline:`\r
`,unixNewline:`
`,LF:`
`,concatBytes:bn},Sm=(e,t)=>{let r=new Uint8Array(e.length+t.length);return r.set(e,0),r.set(t,e.length),r},bm={windowsNewline:new Uint8Array([13,10]),unixNewline:new Uint8Array([10]),LF:10,concatBytes:Sm};var uc=require("node:buffer");var mc=(e,t)=>e?void 0:wm.bind(void 0,t),wm=function*(e,t){if(typeof t!="string"&&!A(t)&&!uc.Buffer.isBuffer(t))throw new TypeError(`The \`${e}\` option's transform must use "objectMode: true" to receive as input: ${typeof t}.`);yield t},hc=(e,t)=>e?xm.bind(void 0,t):Em.bind(void 0,t),xm=function*(e,t){gc(e,t),yield t},Em=function*(e,t){if(gc(e,t),typeof t!="string"&&!A(t))throw new TypeError(`The \`${e}\` option's function must yield a string or an Uint8Array, not ${typeof t}.`);yield t},gc=(e,t)=>{if(t==null)throw new TypeError(`The \`${e}\` option's function must not call \`yield ${t}\`.
Instead, \`yield\` should either be called with a value, or not be called at all. For example:
  if (condition) { yield value; }`)};var yc=require("node:buffer"),Sc=require("node:string_decoder");var Kt=(e,t,r)=>{if(r)return;if(e)return{transform:Tm.bind(void 0,new TextEncoder)};let n=new Sc.StringDecoder(t);return{transform:Am.bind(void 0,n),final:Om.bind(void 0,n)}},Tm=function*(e,t){yc.Buffer.isBuffer(t)?yield W(t):typeof t=="string"?yield e.encode(t):yield t},Am=function*(e,t){yield A(t)?e.write(t):t},Om=function*(e){let t=e.end();t!==""&&(yield t)};var xn=require("node:util"),En=(0,xn.callbackify)(async(e,t,r,n)=>{t.currentIterable=e(...r);try{for await(let o of t.currentIterable)n.push(o)}finally{delete t.currentIterable}}),Xt=async function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Dm}=t[r];for await(let o of n(e))yield*Xt(o,t,r+1)},bc=async function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Im(r,Number(t),e)},Im=async function*(e,t,r){if(e!==void 0)for await(let n of e())yield*Xt(n,r,t+1)},wc=(0,xn.callbackify)(async({currentIterable:e},t)=>{if(e!==void 0){await(t?e.throw(t):e.return());return}if(t)throw t}),Dm=function*(e){yield e};var Tn=(e,t,r,n)=>{try{for(let o of e(...t))r.push(o);n()}catch(o){n(o)}},xc=(e,t)=>[...t.flatMap(r=>[...le(r,e,0)]),...Ge(e)],le=function*(e,t,r){if(r===t.length){yield e;return}let{transform:n=Cm}=t[r];for(let o of n(e))yield*le(o,t,r+1)},Ge=function*(e){for(let[t,{final:r}]of Object.entries(e))yield*Rm(r,Number(t),e)},Rm=function*(e,t,r){if(e!==void 0)for(let n of e())yield*le(n,r,t+1)},Cm=function*(e){yield e};var An=({value:e,value:{transform:t,final:r,writableObjectMode:n,readableObjectMode:o},optionName:i},{encoding:s})=>{let a={},c=Ec(e,s,i),f=ce(t),l=ce(r),d=f?En.bind(void 0,Xt,a):Tn.bind(void 0,le),p=f||l?En.bind(void 0,bc,a):Tn.bind(void 0,Ge),u=f||l?wc.bind(void 0,a):void 0;return{stream:new ze.Transform({writableObjectMode:n,writableHighWaterMark:(0,ze.getDefaultHighWaterMark)(n),readableObjectMode:o,readableHighWaterMark:(0,ze.getDefaultHighWaterMark)(o),transform(S,E,b){d([S,c,0],this,b)},flush(S){p([c],this,S)},destroy:u})}},Jt=(e,t,r,n)=>{let o=t.filter(({type:s})=>s==="generator"),i=n?o.reverse():o;for(let{value:s,optionName:a}of i){let c=Ec(s,r,a);e=xc(c,e)}return e},Ec=({transform:e,final:t,binary:r,writableObjectMode:n,readableObjectMode:o,preserveNewlines:i},s,a)=>{let c={};return[{transform:mc(n,a)},Kt(r,s,n),Ht(r,i,n,c),{transform:e,final:t},{transform:hc(o,a)},pc({binary:r,preserveNewlines:i,readableObjectMode:o,state:c})].filter(Boolean)};var Tc=(e,t)=>{for(let r of Mm(e))Pm(e,r,t)},Mm=e=>new Set(Object.entries(e).filter(([,{direction:t}])=>t==="input").map(([t])=>Number(t))),Pm=(e,t,r)=>{let{stdioItems:n}=e[t],o=n.filter(({contents:a})=>a!==void 0);if(o.length===0)return;if(t!==0){let[{type:a,optionName:c}]=o;throw new TypeError(`Only the \`stdin\` option, not \`${c}\`, can be ${ee[a]} with synchronous methods.`)}let s=o.map(({contents:a})=>a).map(a=>Bm(a,n));r.input=Be(s)},Bm=(e,t)=>{let r=Jt(e,t,"utf8",!0);return Lm(r),Be(r)},Lm=e=>{let t=e.find(r=>typeof r!="string"&&!A(r));if(t!==void 0)throw new TypeError(`The \`stdin\` option is invalid: when passing objects as input, a transform must be used to serialize them to strings or Uint8Arrays: ${t}.`)};var Qt=require("node:fs");var Zt=({stdioItems:e,encoding:t,verboseInfo:r,fdNumber:n})=>n!=="all"&&me(r,n)&&!R.has(t)&&Fm(n)&&(e.some(({type:o,value:i})=>o==="native"&&$m.has(i))||e.every(({type:o})=>P.has(o))),Fm=e=>e===1||e===2,$m=new Set(["pipe","overlapped"]),Ac=async(e,t,r,n)=>{for await(let o of e)vm(t)||Ic(o,r,n)},Oc=(e,t,r)=>{for(let n of e)Ic(n,t,r)},vm=e=>e._readableState.pipes.length>0,Ic=(e,t,r)=>{let n=ft(e);v({type:"output",verboseMessage:n,fdNumber:t,verboseInfo:r})};var Dc=({fileDescriptors:e,syncResult:{output:t},options:r,isMaxBuffer:n,verboseInfo:o})=>{if(t===null)return{output:Array.from({length:3})};let i={},s=new Set([]);return{output:t.map((c,f)=>Um({result:c,fileDescriptors:e,fdNumber:f,state:i,outputFiles:s,isMaxBuffer:n,verboseInfo:o},r)),...i}},Um=({result:e,fileDescriptors:t,fdNumber:r,state:n,outputFiles:o,isMaxBuffer:i,verboseInfo:s},{buffer:a,encoding:c,lines:f,stripFinalNewline:l,maxBuffer:d})=>{if(e===null)return;let p=Oa(e,i,d),u=W(p),{stdioItems:m,objectMode:S}=t[r],E=Nm([u],m,c,n),{serializedResult:b,finalResult:O=b}=_m({chunks:E,objectMode:S,encoding:c,lines:f,stripFinalNewline:l,fdNumber:r});jm({serializedResult:b,fdNumber:r,state:n,verboseInfo:s,encoding:c,stdioItems:m,objectMode:S});let I=a[r]?O:void 0;try{return n.error===void 0&&km(b,m,o),I}catch(C){return n.error=C,I}},Nm=(e,t,r,n)=>{try{return Jt(e,t,r,!1)}catch(o){return n.error=o,e}},_m=({chunks:e,objectMode:t,encoding:r,lines:n,stripFinalNewline:o,fdNumber:i})=>{if(t)return{serializedResult:e};if(r==="buffer")return{serializedResult:Be(e)};let s=yo(e,r);return n[i]?{serializedResult:s,finalResult:wn(s,!o[i],t)}:{serializedResult:s}},jm=({serializedResult:e,fdNumber:t,state:r,verboseInfo:n,encoding:o,stdioItems:i,objectMode:s})=>{if(!Zt({stdioItems:i,encoding:o,verboseInfo:n,fdNumber:t}))return;let a=wn(e,!1,s);try{Oc(a,t,n)}catch(c){r.error??=c}},km=(e,t,r)=>{for(let{path:n,append:o}of t.filter(({type:i})=>Vt.has(i))){let i=typeof n=="string"?n:n.toString();o||r.has(i)?(0,Qt.appendFileSync)(n,e):(r.add(i),(0,Qt.writeFileSync)(n,e))}};var Rc=([,e,t],r)=>{if(r.all)return e===void 0?t:t===void 0?e:Array.isArray(e)?Array.isArray(t)?[...e,...t]:[...e,q(t,r,"all")]:Array.isArray(t)?[q(e,r,"all"),...t]:A(e)&&A(t)?Or([e,t]):`${e}${t}`};var er=require("node:events");var Cc=async(e,t)=>{let[r,n]=await Gm(e);return t.isForcefullyTerminated??=!1,[r,n]},Gm=async e=>{let[t,r]=await Promise.allSettled([(0,er.once)(e,"spawn"),(0,er.once)(e,"exit")]);return t.status==="rejected"?[]:r.status==="rejected"?Mc(e):r.value},Mc=async e=>{try{return await(0,er.once)(e,"exit")}catch{return Mc(e)}},Pc=async e=>{let[t,r]=await e;if(!zm(t,r)&&On(t,r))throw new L;return[t,r]},zm=(e,t)=>e===void 0&&t===void 0,On=(e,t)=>e!==0||t!==null;var Bc=({error:e,status:t,signal:r,output:n},{maxBuffer:o})=>{let i=Wm(e,t,r),s=i?.code==="ETIMEDOUT",a=Aa(i,n,o);return{resultError:i,exitCode:t,signal:r,timedOut:s,isMaxBuffer:a}},Wm=(e,t,r)=>e!==void 0?e:On(t,r)?new L:void 0;var Fc=(e,t,r)=>{let{file:n,commandArguments:o,command:i,escapedCommand:s,startTime:a,verboseInfo:c,options:f,fileDescriptors:l}=Vm(e,t,r),d=Hm({file:n,commandArguments:o,options:f,command:i,escapedCommand:s,verboseInfo:c,fileDescriptors:l,startTime:a});return Ae(d,c,f)},Vm=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=pt(e,t,r),a=Ym(r),{file:c,commandArguments:f,options:l}=Lt(e,t,a);qm(l);let d=cc(l,s);return{file:c,commandArguments:f,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:d}},Ym=e=>e.node&&!e.ipc?{...e,ipc:!1}:e,qm=({ipc:e,ipcInput:t,detached:r,cancelSignal:n})=>{t&&tr("ipcInput"),e&&tr("ipc: true"),r&&tr("detached: true"),n&&tr("cancelSignal")},tr=e=>{throw new TypeError(`The "${e}" option cannot be used with synchronous methods.`)},Hm=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,verboseInfo:i,fileDescriptors:s,startTime:a})=>{let c=Km({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:s,startTime:a});if(c.failed)return c;let{resultError:f,exitCode:l,signal:d,timedOut:p,isMaxBuffer:u}=Bc(c,r),{output:m,error:S=f}=Dc({fileDescriptors:s,syncResult:c,options:r,isMaxBuffer:u,verboseInfo:i}),E=m.map((O,I)=>q(O,r,I)),b=q(Rc(m,r),r,"all");return Jm({error:S,exitCode:l,signal:d,timedOut:p,isMaxBuffer:u,stdio:E,all:b,options:r,command:n,escapedCommand:o,startTime:a})},Km=({file:e,commandArguments:t,options:r,command:n,escapedCommand:o,fileDescriptors:i,startTime:s})=>{try{Tc(i,r);let a=Xm(r);return(0,Lc.spawnSync)(e,t,a)}catch(a){return Te({error:a,command:n,escapedCommand:o,fileDescriptors:i,options:r,startTime:s,isSync:!0})}},Xm=({encoding:e,maxBuffer:t,...r})=>({...r,encoding:"buffer",maxBuffer:kt(t)}),Jm=({error:e,exitCode:t,signal:r,timedOut:n,isMaxBuffer:o,stdio:i,all:s,options:a,command:c,escapedCommand:f,startTime:l})=>e===void 0?Gt({command:c,escapedCommand:f,stdio:i,all:s,ipcOutput:[],options:a,startTime:l}):ke({error:e,command:c,escapedCommand:f,timedOut:n,isCanceled:!1,isGracefullyCanceled:!1,isMaxBuffer:o,isForcefullyTerminated:!1,exitCode:t,signal:r,stdio:i,all:s,ipcOutput:[],options:a,startTime:l,isSync:!0});var kl=require("node:events"),Gl=require("node:child_process");var Dn=T(require("node:process"),1);var Ie=require("node:events");var $c=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0,filter:i}={})=>(Se({methodName:"getOneMessage",isSubprocess:r,ipc:n,isConnected:It(e)}),Zm({anyProcess:e,channel:t,isSubprocess:r,filter:i,reference:o})),Zm=async({anyProcess:e,channel:t,isSubprocess:r,filter:n,reference:o})=>{Et(t,o);let i=Q(e,t,r),s=new AbortController;try{return await Promise.race([Qm(i,n,s),eh(i,r,s),th(i,r,s)])}catch(a){throw be(e),a}finally{s.abort(),Tt(t,o)}},Qm=async(e,t,{signal:r})=>{if(t===void 0){let[n]=await(0,Ie.once)(e,"message",{signal:r});return n}for await(let[n]of(0,Ie.on)(e,"message",{signal:r}))if(t(n))return n},eh=async(e,t,{signal:r})=>{await(0,Ie.once)(e,"disconnect",{signal:r}),os(t)},th=async(e,t,{signal:r})=>{let[n]=await(0,Ie.once)(e,"strict:error",{signal:r});throw bt(n,t)};var We=require("node:events");var Uc=({anyProcess:e,channel:t,isSubprocess:r,ipc:n},{reference:o=!0}={})=>In({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:!r,reference:o}),In=({anyProcess:e,channel:t,isSubprocess:r,ipc:n,shouldAwait:o,reference:i})=>{Se({methodName:"getEachMessage",isSubprocess:r,ipc:n,isConnected:It(e)}),Et(t,i);let s=Q(e,t,r),a=new AbortController,c={};return rh(e,s,a),nh({ipcEmitter:s,isSubprocess:r,controller:a,state:c}),oh({anyProcess:e,channel:t,ipcEmitter:s,isSubprocess:r,shouldAwait:o,controller:a,state:c,reference:i})},rh=async(e,t,r)=>{try{await(0,We.once)(t,"disconnect",{signal:r.signal}),r.abort()}catch{}},nh=async({ipcEmitter:e,isSubprocess:t,controller:r,state:n})=>{try{let[o]=await(0,We.once)(e,"strict:error",{signal:r.signal});n.error=bt(o,t),r.abort()}catch{}},oh=async function*({anyProcess:e,channel:t,ipcEmitter:r,isSubprocess:n,shouldAwait:o,controller:i,state:s,reference:a}){try{for await(let[c]of(0,We.on)(r,"message",{signal:i.signal}))vc(s),yield c}catch{vc(s)}finally{i.abort(),Tt(t,a),n||be(e),o&&await e}},vc=({error:e})=>{if(e)throw e};var Nc=(e,{ipc:t})=>{Object.assign(e,jc(e,!1,t))},_c=()=>{let e=Dn.default,t=!0,r=Dn.default.channel!==void 0;return{...jc(e,t,r),getCancelSignal:Ns.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})}},jc=(e,t,r)=>({sendMessage:Mt.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getOneMessage:$c.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r}),getEachMessage:Uc.bind(void 0,{anyProcess:e,channel:e.channel,isSubprocess:t,ipc:r})});var kc=require("node:child_process"),te=require("node:stream");var Gc=({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,verboseInfo:s})=>{gn(n);let a=new kc.ChildProcess;ih(a,n),Object.assign(a,{readable:sh,writable:ah,duplex:ch});let c=Te({error:e,command:t,escapedCommand:r,fileDescriptors:n,options:o,startTime:i,isSync:!1}),f=lh(c,s,o);return{subprocess:a,promise:f}},ih=(e,t)=>{let r=Ve(),n=Ve(),o=Ve(),i=Array.from({length:t.length-3},Ve),s=Ve(),a=[r,n,o,...i];Object.assign(e,{stdin:r,stdout:n,stderr:o,all:s,stdio:a})},Ve=()=>{let e=new te.PassThrough;return e.end(),e},sh=()=>new te.Readable({read(){}}),ah=()=>new te.Writable({write(){}}),ch=()=>new te.Duplex({read(){},write(){}}),lh=async(e,t,r)=>Ae(e,t,r);var De=require("node:fs"),Wc=require("node:buffer"),k=require("node:stream");var Vc=(e,t)=>qt(fh,e,t,!1),Ye=({type:e,optionName:t})=>{throw new TypeError(`The \`${t}\` option cannot be ${ee[e]}.`)},zc={fileNumber:Ye,generator:An,asyncGenerator:An,nodeStream:({value:e})=>({stream:e}),webTransform({value:{transform:e,writableObjectMode:t,readableObjectMode:r}}){let n=t||r;return{stream:k.Duplex.fromWeb(e,{objectMode:n})}},duplex:({value:{transform:e}})=>({stream:e}),native(){}},fh={input:{...zc,fileUrl:({value:e})=>({stream:(0,De.createReadStream)(e)}),filePath:({value:{file:e}})=>({stream:(0,De.createReadStream)(e)}),webStream:({value:e})=>({stream:k.Readable.fromWeb(e)}),iterable:({value:e})=>({stream:k.Readable.from(e)}),asyncIterable:({value:e})=>({stream:k.Readable.from(e)}),string:({value:e})=>({stream:k.Readable.from(e)}),uint8Array:({value:e})=>({stream:k.Readable.from(Wc.Buffer.from(e))})},output:{...zc,fileUrl:({value:e})=>({stream:(0,De.createWriteStream)(e)}),filePath:({value:{file:e,append:t}})=>({stream:(0,De.createWriteStream)(e,t?{flags:"a"}:{})}),webStream:({value:e})=>({stream:k.Writable.fromWeb(e)}),iterable:Ye,asyncIterable:Ye,string:Ye,uint8Array:Ye}};var qe=require("node:events"),nr=require("node:stream"),Mn=require("node:stream/promises");function fe(e){if(!Array.isArray(e))throw new TypeError(`Expected an array, got \`${typeof e}\`.`);for(let o of e)Cn(o);let t=e.some(({readableObjectMode:o})=>o),r=dh(e,t),n=new Rn({objectMode:t,writableHighWaterMark:r,readableHighWaterMark:r});for(let o of e)n.add(o);return n}var dh=(e,t)=>{if(e.length===0)return(0,nr.getDefaultHighWaterMark)(t);let r=e.filter(({readableObjectMode:n})=>n===t).map(({readableHighWaterMark:n})=>n);return Math.max(...r)},Rn=class extends nr.PassThrough{#t=new Set([]);#r=new Set([]);#e=new Set([]);#n;#i=Symbol("unpipe");#o=new WeakMap;add(t){if(Cn(t),this.#t.has(t))return;this.#t.add(t),this.#n??=ph(this,this.#t,this.#i);let r=hh({passThroughStream:this,stream:t,streams:this.#t,ended:this.#r,aborted:this.#e,onFinished:this.#n,unpipeEvent:this.#i});this.#o.set(t,r),t.pipe(this,{end:!1})}async remove(t){if(Cn(t),!this.#t.has(t))return!1;let r=this.#o.get(t);return r===void 0?!1:(this.#o.delete(t),t.unpipe(this),await r,!0)}},ph=async(e,t,r)=>{rr(e,Yc);let n=new AbortController;try{await Promise.race([uh(e,n),mh(e,t,r,n)])}finally{n.abort(),rr(e,-Yc)}},uh=async(e,{signal:t})=>{try{await(0,Mn.finished)(e,{signal:t,cleanup:!0})}catch(r){throw Hc(e,r),r}},mh=async(e,t,r,{signal:n})=>{for await(let[o]of(0,qe.on)(e,"unpipe",{signal:n}))t.has(o)&&o.emit(r)},Cn=e=>{if(typeof e?.pipe!="function")throw new TypeError(`Expected a readable stream, got: \`${typeof e}\`.`)},hh=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,onFinished:i,unpipeEvent:s})=>{rr(e,qc);let a=new AbortController;try{await Promise.race([gh(i,t,a),yh({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:a}),Sh({stream:t,streams:r,ended:n,aborted:o,unpipeEvent:s,controller:a})])}finally{a.abort(),rr(e,-qc)}r.size>0&&r.size===n.size+o.size&&(n.size===0&&o.size>0?Pn(e):bh(e))},gh=async(e,t,{signal:r})=>{try{await e,r.aborted||Pn(t)}catch(n){r.aborted||Hc(t,n)}},yh=async({passThroughStream:e,stream:t,streams:r,ended:n,aborted:o,controller:{signal:i}})=>{try{await(0,Mn.finished)(t,{signal:i,cleanup:!0,readable:!0,writable:!1}),r.has(t)&&n.add(t)}catch(s){if(i.aborted||!r.has(t))return;Kc(s)?o.add(t):Xc(e,s)}},Sh=async({stream:e,streams:t,ended:r,aborted:n,unpipeEvent:o,controller:{signal:i}})=>{if(await(0,qe.once)(e,o,{signal:i}),!e.readable)return(0,qe.once)(i,"abort",{signal:i});t.delete(e),r.delete(e),n.delete(e)},bh=e=>{e.writable&&e.end()},Hc=(e,t)=>{Kc(t)?Pn(e):Xc(e,t)},Kc=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",Pn=e=>{(e.readable||e.writable)&&e.destroy()},Xc=(e,t)=>{e.destroyed||(e.once("error",wh),e.destroy(t))},wh=()=>{},rr=(e,t)=>{let r=e.getMaxListeners();r!==0&&r!==Number.POSITIVE_INFINITY&&e.setMaxListeners(r+t)},Yc=2,qc=1;var Bn=require("node:stream/promises");var Re=(e,t)=>{e.pipe(t),xh(e,t),Eh(e,t)},xh=async(e,t)=>{if(!(B(e)||B(t))){try{await(0,Bn.finished)(e,{cleanup:!0,readable:!0,writable:!1})}catch{}Ln(t)}},Ln=e=>{e.writable&&e.end()},Eh=async(e,t)=>{if(!(B(e)||B(t))){try{await(0,Bn.finished)(t,{cleanup:!0,readable:!1,writable:!0})}catch{}Fn(e)}},Fn=e=>{e.readable&&e.destroy()};var Jc=(e,t,r)=>{let n=new Map;for(let[o,{stdioItems:i,direction:s}]of Object.entries(t)){for(let{stream:a}of i.filter(({type:c})=>P.has(c)))Th(e,a,s,o);for(let{stream:a}of i.filter(({type:c})=>!P.has(c)))Oh({subprocess:e,stream:a,direction:s,fdNumber:o,pipeGroups:n,controller:r})}for(let[o,i]of n.entries()){let s=i.length===1?i[0]:fe(i);Re(s,o)}},Th=(e,t,r,n)=>{r==="output"?Re(e.stdio[n],t):Re(t,e.stdio[n]);let o=Ah[n];o!==void 0&&(e[o]=t),e.stdio[n]=t},Ah=["stdin","stdout","stderr"],Oh=({subprocess:e,stream:t,direction:r,fdNumber:n,pipeGroups:o,controller:i})=>{if(t===void 0)return;Ih(t,i);let[s,a]=r==="output"?[t,e.stdio[n]]:[e.stdio[n],t],c=o.get(s)??[];o.set(s,[...c,a])},Ih=(e,{signal:t})=>{B(e)&&se(e,Dh,t)},Dh=2;var Zc=require("node:events");var Qc=(e,{cleanup:t,detached:r},{signal:n})=>{if(!t||r)return;let o=lo(()=>{e.kill()});(0,Zc.addAbortListener)(n,()=>{o()})};var tl=({source:e,sourcePromise:t,boundOptions:r,createNested:n},...o)=>{let i=dt(),{destination:s,destinationStream:a,destinationError:c,from:f,unpipeSignal:l}=Rh(r,n,o),{sourceStream:d,sourceError:p}=Mh(e,f),{options:u,fileDescriptors:m}=N.get(e);return{sourcePromise:t,sourceStream:d,sourceOptions:u,sourceError:p,destination:s,destinationStream:a,destinationError:c,unpipeSignal:l,fileDescriptors:m,startTime:i}},Rh=(e,t,r)=>{try{let{destination:n,pipeOptions:{from:o,to:i,unpipeSignal:s}={}}=Ch(e,t,...r),a=xt(n,i);return{destination:n,destinationStream:a,from:o,unpipeSignal:s}}catch(n){return{destinationError:n}}},Ch=(e,t,r,...n)=>{if(Array.isArray(r))return{destination:t(el,e)(r,...n),pipeOptions:e};if(typeof r=="string"||r instanceof URL||Tr(r)){if(Object.keys(e).length>0)throw new TypeError('Please use .pipe("file", ..., options) or .pipe(execa("file", ..., options)) instead of .pipe(options)("file", ...).');let[o,i,s]=et(r,...n);return{destination:t(el)(o,i,s),pipeOptions:s}}if(N.has(r)){if(Object.keys(e).length>0)throw new TypeError("Please use .pipe(options)`command` or .pipe($(options)`command`) instead of .pipe(options)($`command`).");return{destination:r,pipeOptions:n[0]}}throw new TypeError(`The first argument must be a template string, an options object, or an Execa subprocess: ${r}`)},el=({options:e})=>({options:{...e,stdin:"pipe",piped:!0}}),Mh=(e,t)=>{try{return{sourceStream:we(e,t)}}catch(r){return{sourceError:r}}};var nl=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n,fileDescriptors:o,sourceOptions:i,startTime:s})=>{let a=Ph({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n});if(a!==void 0)throw $n({error:a,fileDescriptors:o,sourceOptions:i,startTime:s})},Ph=({sourceStream:e,sourceError:t,destinationStream:r,destinationError:n})=>{if(t!==void 0&&n!==void 0)return n;if(n!==void 0)return Fn(e),n;if(t!==void 0)return Ln(r),t},$n=({error:e,fileDescriptors:t,sourceOptions:r,startTime:n})=>Te({error:e,command:rl,escapedCommand:rl,fileDescriptors:t,options:r,startTime:n,isSync:!1}),rl="source.pipe(destination)";var ol=async e=>{let[{status:t,reason:r,value:n=r},{status:o,reason:i,value:s=i}]=await e;if(s.pipedFrom.includes(n)||s.pipedFrom.push(n),o==="rejected")throw s;if(t==="rejected")throw n;return s};var il=require("node:stream/promises");var sl=(e,t,r)=>{let n=or.has(t)?Lh(e,t):Bh(e,t);return se(e,$h,r.signal),se(t,vh,r.signal),Fh(t),n},Bh=(e,t)=>{let r=fe([e]);return Re(r,t),or.set(t,r),r},Lh=(e,t)=>{let r=or.get(t);return r.add(e),r},Fh=async e=>{try{await(0,il.finished)(e,{cleanup:!0,readable:!1,writable:!0})}catch{}or.delete(e)},or=new WeakMap,$h=2,vh=1;var al=require("node:util");var cl=(e,t)=>e===void 0?[]:[Uh(e,t)],Uh=async(e,{sourceStream:t,mergedStream:r,fileDescriptors:n,sourceOptions:o,startTime:i})=>{await(0,al.aborted)(e,t),await r.remove(t);let s=new Error("Pipe canceled by `unpipeSignal` option.");throw $n({error:s,fileDescriptors:n,sourceOptions:o,startTime:i})};var ir=(e,...t)=>{if(x(t[0]))return ir.bind(void 0,{...e,boundOptions:{...e.boundOptions,...t[0]}});let{destination:r,...n}=tl(e,...t),o=Nh({...n,destination:r});return o.pipe=ir.bind(void 0,{...e,source:r,sourcePromise:o,boundOptions:{}}),o},Nh=async({sourcePromise:e,sourceStream:t,sourceOptions:r,sourceError:n,destination:o,destinationStream:i,destinationError:s,unpipeSignal:a,fileDescriptors:c,startTime:f})=>{let l=_h(e,o);nl({sourceStream:t,sourceError:n,destinationStream:i,destinationError:s,fileDescriptors:c,sourceOptions:r,startTime:f});let d=new AbortController;try{let p=sl(t,i,d);return await Promise.race([ol(l),...cl(a,{sourceStream:t,mergedStream:p,sourceOptions:r,fileDescriptors:c,startTime:f})])}finally{d.abort()}},_h=(e,t)=>Promise.allSettled([e,t]);var ul=require("node:timers/promises");var fl=require("node:events"),dl=require("node:stream");var sr=({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:n,encoding:o,preserveNewlines:i})=>{let s=new AbortController;return jh(t,s),pl({stream:e,controller:s,binary:r,shouldEncode:!e.readableObjectMode&&n,encoding:o,shouldSplit:!e.readableObjectMode,preserveNewlines:i})},jh=async(e,t)=>{try{await e}catch{}finally{t.abort()}},vn=({stream:e,onStreamEnd:t,lines:r,encoding:n,stripFinalNewline:o,allMixed:i})=>{let s=new AbortController;kh(t,s,e);let a=e.readableObjectMode&&!i;return pl({stream:e,controller:s,binary:n==="buffer",shouldEncode:!a,encoding:n,shouldSplit:!a&&r,preserveNewlines:!o})},kh=async(e,t,r)=>{try{await e}catch{r.destroy()}finally{t.abort()}},pl=({stream:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})=>{let a=(0,fl.on)(e,"data",{signal:t.signal,highWaterMark:ll,highWatermark:ll});return Gh({onStdoutChunk:a,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s})},Un=(0,dl.getDefaultHighWaterMark)(!0),ll=Un,Gh=async function*({onStdoutChunk:e,controller:t,binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s}){let a=zh({binary:r,shouldEncode:n,encoding:o,shouldSplit:i,preserveNewlines:s});try{for await(let[c]of e)yield*le(c,a,0)}catch(c){if(!t.signal.aborted)throw c}finally{yield*Ge(a)}},zh=({binary:e,shouldEncode:t,encoding:r,shouldSplit:n,preserveNewlines:o})=>[Kt(e,r,!t),Ht(e,o,!n,{})].filter(Boolean);var ml=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,buffer:o,maxBuffer:i,lines:s,allMixed:a,stripFinalNewline:c,verboseInfo:f,streamInfo:l})=>{let d=Wh({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:a,verboseInfo:f,streamInfo:l});if(!o){await Promise.all([Vh(e),d]);return}let p=Sn(c,r),u=vn({stream:e,onStreamEnd:t,lines:s,encoding:n,stripFinalNewline:p,allMixed:a}),[m]=await Promise.all([Yh({stream:e,iterable:u,fdNumber:r,encoding:n,maxBuffer:i,lines:s}),d]);return m},Wh=async({stream:e,onStreamEnd:t,fdNumber:r,encoding:n,allMixed:o,verboseInfo:i,streamInfo:{fileDescriptors:s}})=>{if(!Zt({stdioItems:s[r]?.stdioItems,encoding:n,verboseInfo:i,fdNumber:r}))return;let a=vn({stream:e,onStreamEnd:t,lines:!0,encoding:n,stripFinalNewline:!0,allMixed:o});await Ac(a,e,r,i)},Vh=async e=>{await(0,ul.setImmediate)(),e.readableFlowing===null&&e.resume()},Yh=async({stream:e,stream:{readableObjectMode:t},iterable:r,fdNumber:n,encoding:o,maxBuffer:i,lines:s})=>{try{return t||s?await Ut(r,{maxBuffer:i}):o==="buffer"?new Uint8Array(await Nt(r,{maxBuffer:i})):await jt(r,{maxBuffer:i})}catch(a){return hl(xa({error:a,stream:e,readableObjectMode:t,lines:s,encoding:o,fdNumber:n}))}},Nn=async e=>{try{return await e}catch(t){return hl(t)}},hl=({bufferedData:e})=>ho(e)?new Uint8Array(e):e;var yl=require("node:stream/promises"),He=async(e,t,r,{isSameDirection:n,stopOnExit:o=!1}={})=>{let i=qh(e,r),s=new AbortController;try{await Promise.race([...o?[r.exitPromise]:[],(0,yl.finished)(e,{cleanup:!0,signal:s.signal})])}catch(a){i.stdinCleanedUp||Xh(a,t,r,n)}finally{s.abort()}},qh=(e,{originalStreams:[t],subprocess:r})=>{let n={stdinCleanedUp:!1};return e===t&&Hh(e,r,n),n},Hh=(e,t,r)=>{let{_destroy:n}=e;e._destroy=(...o)=>{Kh(t,r),n.call(e,...o)}},Kh=({exitCode:e,signalCode:t},r)=>{(e!==null||t!==null)&&(r.stdinCleanedUp=!0)},Xh=(e,t,r,n)=>{if(!Jh(e,t,r,n))throw e},Jh=(e,t,r,n=!0)=>r.propagating?gl(e)||ar(e):(r.propagating=!0,_n(r,t)===n?gl(e):ar(e)),_n=({fileDescriptors:e},t)=>t!=="all"&&e[t].direction==="input",ar=e=>e?.code==="ERR_STREAM_PREMATURE_CLOSE",gl=e=>e?.code==="EPIPE";var Sl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>e.stdio.map((c,f)=>jn({stream:c,fdNumber:f,encoding:t,buffer:r[f],maxBuffer:n[f],lines:o[f],allMixed:!1,stripFinalNewline:i,verboseInfo:s,streamInfo:a})),jn=async({stream:e,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:c,streamInfo:f})=>{if(!e)return;let l=He(e,t,f);if(_n(f,t)){await l;return}let[d]=await Promise.all([ml({stream:e,onStreamEnd:l,fdNumber:t,encoding:r,buffer:n,maxBuffer:o,lines:i,allMixed:s,stripFinalNewline:a,verboseInfo:c,streamInfo:f}),l]);return d};var bl=({stdout:e,stderr:t},{all:r})=>r&&(e||t)?fe([e,t].filter(Boolean)):void 0,wl=({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:i,verboseInfo:s,streamInfo:a})=>jn({...Zh(e,r),fdNumber:"all",encoding:t,maxBuffer:n[1]+n[2],lines:o[1]||o[2],allMixed:Qh(e),stripFinalNewline:i,verboseInfo:s,streamInfo:a}),Zh=({stdout:e,stderr:t,all:r},[,n,o])=>{let i=n||o;return i?n?o?{stream:r,buffer:i}:{stream:e,buffer:i}:{stream:t,buffer:i}:{stream:r,buffer:i}},Qh=({all:e,stdout:t,stderr:r})=>e&&t&&r&&t.readableObjectMode!==r.readableObjectMode;var Ol=require("node:events");var xl=e=>me(e,"ipc"),El=(e,t)=>{let r=ft(e);v({type:"ipc",verboseMessage:r,fdNumber:"ipc",verboseInfo:t})};var Tl=async({subprocess:e,buffer:t,maxBuffer:r,ipc:n,ipcOutput:o,verboseInfo:i})=>{if(!n)return o;let s=xl(i),a=V(t,"ipc"),c=V(r,"ipc");for await(let f of In({anyProcess:e,channel:e.channel,isSubprocess:!1,ipc:n,shouldAwait:!1,reference:!0}))a&&(Ea(e,o,c),o.push(f)),s&&El(f,i);return o},Al=async(e,t)=>(await Promise.allSettled([e]),t);var Il=async({subprocess:e,options:{encoding:t,buffer:r,maxBuffer:n,lines:o,timeoutDuration:i,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:c,stripFinalNewline:f,ipc:l,ipcInput:d},context:p,verboseInfo:u,fileDescriptors:m,originalStreams:S,onInternalError:E,controller:b})=>{let O=Cc(e,p),I={originalStreams:S,fileDescriptors:m,subprocess:e,exitPromise:O,propagating:!1},C=Sl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:f,verboseInfo:u,streamInfo:I}),G=wl({subprocess:e,encoding:t,buffer:r,maxBuffer:n,lines:o,stripFinalNewline:f,verboseInfo:u,streamInfo:I}),H=[],ne=Tl({subprocess:e,buffer:r,maxBuffer:n,ipc:l,ipcOutput:H,verboseInfo:u}),de=eg(S,e,I),K=tg(m,I);try{return await Promise.race([Promise.all([{},Pc(O),Promise.all(C),G,ne,Ks(e,d),...de,...K]),E,rg(e,b),...Ws(e,i,p,b),...ns({subprocess:e,cancelSignal:s,gracefulCancel:a,context:p,controller:b}),...ks({subprocess:e,cancelSignal:s,gracefulCancel:a,forceKillAfterDelay:c,context:p,controller:b})])}catch(pr){return p.terminationReason??="other",Promise.all([{error:pr},O,Promise.all(C.map(Me=>Nn(Me))),Nn(G),Al(ne,H),Promise.allSettled(de),Promise.allSettled(K)])}},eg=(e,t,r)=>e.map((n,o)=>n===t.stdio[o]?void 0:He(n,o,r)),tg=(e,t)=>e.flatMap(({stdioItems:r},n)=>r.filter(({value:o,stream:i=o})=>$(i,{checkOpen:!1})&&!B(i)).map(({type:o,value:i,stream:s=i})=>He(s,n,t,{isSameDirection:P.has(o),stopOnExit:o==="native"}))),rg=async(e,{signal:t})=>{let[r]=await(0,Ol.once)(e,"error",{signal:t});throw r};var Dl=()=>({readableDestroy:new WeakMap,writableFinal:new WeakMap,writableDestroy:new WeakMap}),Ke=(e,t,r)=>{let n=e[r];n.has(t)||n.set(t,[]);let o=n.get(t),i=U();return o.push(i),{resolve:i.resolve.bind(i),promises:o}},Ce=async({resolve:e,promises:t},r)=>{e();let[n]=await Promise.race([Promise.allSettled([!0,r]),Promise.all([!1,...t])]);return!n};var Cl=require("node:stream"),Ml=require("node:util");var kn=require("node:stream/promises");var Gn=async e=>{if(e!==void 0)try{await zn(e)}catch{}},Rl=async e=>{if(e!==void 0)try{await Wn(e)}catch{}},zn=async e=>{await(0,kn.finished)(e,{cleanup:!0,readable:!1,writable:!0})},Wn=async e=>{await(0,kn.finished)(e,{cleanup:!0,readable:!0,writable:!1})},cr=async(e,t)=>{if(await e,t)throw t},lr=(e,t,r)=>{r&&!ar(r)?e.destroy(r):t&&e.destroy()};var Pl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,binary:o=!0,preserveNewlines:i=!0}={})=>{let s=o||R.has(r),{subprocessStdout:a,waitReadableDestroy:c}=Vn(e,n,t),{readableEncoding:f,readableObjectMode:l,readableHighWaterMark:d}=Yn(a,s),{read:p,onStdoutDataDone:u}=qn({subprocessStdout:a,subprocess:e,binary:s,encoding:r,preserveNewlines:i}),m=new Cl.Readable({read:p,destroy:(0,Ml.callbackify)(Kn.bind(void 0,{subprocessStdout:a,subprocess:e,waitReadableDestroy:c})),highWaterMark:d,objectMode:l,encoding:f});return Hn({subprocessStdout:a,onStdoutDataDone:u,readable:m,subprocess:e}),m},Vn=(e,t,r)=>{let n=we(e,t),o=Ke(r,n,"readableDestroy");return{subprocessStdout:n,waitReadableDestroy:o}},Yn=({readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r},n)=>n?{readableEncoding:e,readableObjectMode:t,readableHighWaterMark:r}:{readableEncoding:e,readableObjectMode:!0,readableHighWaterMark:Un},qn=({subprocessStdout:e,subprocess:t,binary:r,encoding:n,preserveNewlines:o})=>{let i=U(),s=sr({subprocessStdout:e,subprocess:t,binary:r,shouldEncode:!r,encoding:n,preserveNewlines:o});return{read(){ng(this,s,i)},onStdoutDataDone:i}},ng=async(e,t,r)=>{try{let{value:n,done:o}=await t.next();o?r.resolve():e.push(n)}catch{}},Hn=async({subprocessStdout:e,onStdoutDataDone:t,readable:r,subprocess:n,subprocessStdin:o})=>{try{await Wn(e),await n,await Gn(o),await t,r.readable&&r.push(null)}catch(i){await Gn(o),Bl(r,i)}},Kn=async({subprocessStdout:e,subprocess:t,waitReadableDestroy:r},n)=>{await Ce(r,t)&&(Bl(e,n),await cr(t,n))},Bl=(e,t)=>{lr(e,e.readable,t)};var Ll=require("node:stream"),Xn=require("node:util");var Fl=({subprocess:e,concurrentStreams:t},{to:r}={})=>{let{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}=Jn(e,r,t),s=new Ll.Writable({...Zn(n,e,o),destroy:(0,Xn.callbackify)(eo.bind(void 0,{subprocessStdin:n,subprocess:e,waitWritableFinal:o,waitWritableDestroy:i})),highWaterMark:n.writableHighWaterMark,objectMode:n.writableObjectMode});return Qn(n,s),s},Jn=(e,t,r)=>{let n=xt(e,t),o=Ke(r,n,"writableFinal"),i=Ke(r,n,"writableDestroy");return{subprocessStdin:n,waitWritableFinal:o,waitWritableDestroy:i}},Zn=(e,t,r)=>({write:og.bind(void 0,e),final:(0,Xn.callbackify)(ig.bind(void 0,e,t,r))}),og=(e,t,r,n)=>{e.write(t,r)?n():e.once("drain",n)},ig=async(e,t,r)=>{await Ce(r,t)&&(e.writable&&e.end(),await t)},Qn=async(e,t,r)=>{try{await zn(e),t.writable&&t.end()}catch(n){await Rl(r),$l(t,n)}},eo=async({subprocessStdin:e,subprocess:t,waitWritableFinal:r,waitWritableDestroy:n},o)=>{await Ce(r,t),await Ce(n,t)&&($l(e,o),await cr(t,o))},$l=(e,t)=>{lr(e,e.writable,t)};var vl=require("node:stream"),Ul=require("node:util");var Nl=({subprocess:e,concurrentStreams:t,encoding:r},{from:n,to:o,binary:i=!0,preserveNewlines:s=!0}={})=>{let a=i||R.has(r),{subprocessStdout:c,waitReadableDestroy:f}=Vn(e,n,t),{subprocessStdin:l,waitWritableFinal:d,waitWritableDestroy:p}=Jn(e,o,t),{readableEncoding:u,readableObjectMode:m,readableHighWaterMark:S}=Yn(c,a),{read:E,onStdoutDataDone:b}=qn({subprocessStdout:c,subprocess:e,binary:a,encoding:r,preserveNewlines:s}),O=new vl.Duplex({read:E,...Zn(l,e,d),destroy:(0,Ul.callbackify)(sg.bind(void 0,{subprocessStdout:c,subprocessStdin:l,subprocess:e,waitReadableDestroy:f,waitWritableFinal:d,waitWritableDestroy:p})),readableHighWaterMark:S,writableHighWaterMark:l.writableHighWaterMark,readableObjectMode:m,writableObjectMode:l.writableObjectMode,encoding:u});return Hn({subprocessStdout:c,onStdoutDataDone:b,readable:O,subprocess:e,subprocessStdin:l}),Qn(l,O,c),O},sg=async({subprocessStdout:e,subprocessStdin:t,subprocess:r,waitReadableDestroy:n,waitWritableFinal:o,waitWritableDestroy:i},s)=>{await Promise.all([Kn({subprocessStdout:e,subprocess:r,waitReadableDestroy:n},s),eo({subprocessStdin:t,subprocess:r,waitWritableFinal:o,waitWritableDestroy:i},s)])};var to=(e,t,{from:r,binary:n=!1,preserveNewlines:o=!1}={})=>{let i=n||R.has(t),s=we(e,r),a=sr({subprocessStdout:s,subprocess:e,binary:i,shouldEncode:!0,encoding:t,preserveNewlines:o});return ag(a,s,e)},ag=async function*(e,t,r){try{yield*e}finally{t.readable&&t.destroy(),await r}};var _l=(e,{encoding:t})=>{let r=Dl();e.readable=Pl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.writable=Fl.bind(void 0,{subprocess:e,concurrentStreams:r}),e.duplex=Nl.bind(void 0,{subprocess:e,concurrentStreams:r,encoding:t}),e.iterable=to.bind(void 0,e,t),e[Symbol.asyncIterator]=to.bind(void 0,e,t,{})};var jl=(e,t)=>{for(let[r,n]of lg){let o=n.value.bind(t);Reflect.defineProperty(e,r,{...n,value:o})}},cg=(async()=>{})().constructor.prototype,lg=["then","catch","finally"].map(e=>[e,Reflect.getOwnPropertyDescriptor(cg,e)]);var zl=(e,t,r,n)=>{let{file:o,commandArguments:i,command:s,escapedCommand:a,startTime:c,verboseInfo:f,options:l,fileDescriptors:d}=fg(e,t,r),{subprocess:p,promise:u}=pg({file:o,commandArguments:i,options:l,startTime:c,verboseInfo:f,command:s,escapedCommand:a,fileDescriptors:d});return p.pipe=ir.bind(void 0,{source:p,sourcePromise:u,boundOptions:{},createNested:n}),jl(p,u),N.set(p,{options:l,fileDescriptors:d}),p},fg=(e,t,r)=>{let{command:n,escapedCommand:o,startTime:i,verboseInfo:s}=pt(e,t,r),{file:a,commandArguments:c,options:f}=Lt(e,t,r),l=dg(f),d=Vc(l,s);return{file:a,commandArguments:c,command:n,escapedCommand:o,startTime:i,verboseInfo:s,options:l,fileDescriptors:d}},dg=({timeout:e,signal:t,...r})=>{if(t!==void 0)throw new TypeError('The "signal" option has been renamed to "cancelSignal" instead.');return{...r,timeoutDuration:e}},pg=({file:e,commandArguments:t,options:r,startTime:n,verboseInfo:o,command:i,escapedCommand:s,fileDescriptors:a})=>{let c;try{c=(0,Gl.spawn)(e,t,r)}catch(m){return Gc({error:m,command:i,escapedCommand:s,fileDescriptors:a,options:r,startTime:n,verboseInfo:o})}let f=new AbortController;(0,kl.setMaxListeners)(Number.POSITIVE_INFINITY,f.signal);let l=[...c.stdio];Jc(c,a,f),Qc(c,r,f);let d={},p=U();c.kill=es.bind(void 0,{kill:c.kill.bind(c),options:r,onInternalError:p,context:d,controller:f}),c.all=bl(c,r),_l(c,r),Nc(c,r);let u=ug({subprocess:c,options:r,startTime:n,verboseInfo:o,fileDescriptors:a,originalStreams:l,command:i,escapedCommand:s,context:d,onInternalError:p,controller:f});return{subprocess:c,promise:u}},ug=async({subprocess:e,options:t,startTime:r,verboseInfo:n,fileDescriptors:o,originalStreams:i,command:s,escapedCommand:a,context:c,onInternalError:f,controller:l})=>{let[d,[p,u],m,S,E]=await Il({subprocess:e,options:t,context:c,verboseInfo:n,fileDescriptors:o,originalStreams:i,onInternalError:f,controller:l});l.abort(),f.resolve();let b=m.map((C,G)=>q(C,t,G)),O=q(S,t,"all"),I=mg({errorInfo:d,exitCode:p,signal:u,stdio:b,all:O,ipcOutput:E,context:c,options:t,command:s,escapedCommand:a,startTime:r});return Ae(I,n,t)},mg=({errorInfo:e,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,context:s,options:a,command:c,escapedCommand:f,startTime:l})=>"error"in e?ke({error:e.error,command:c,escapedCommand:f,timedOut:s.terminationReason==="timeout",isCanceled:s.terminationReason==="cancel"||s.terminationReason==="gracefulCancel",isGracefullyCanceled:s.terminationReason==="gracefulCancel",isMaxBuffer:e.error instanceof _,isForcefullyTerminated:s.isForcefullyTerminated,exitCode:t,signal:r,stdio:n,all:o,ipcOutput:i,options:a,startTime:l,isSync:!1}):Gt({command:c,escapedCommand:f,stdio:n,all:o,ipcOutput:i,options:a,startTime:l});var fr=(e,t)=>{let r=Object.fromEntries(Object.entries(t).map(([n,o])=>[n,hg(n,e[n],o)]));return{...e,...r}},hg=(e,t,r)=>gg.has(e)&&x(t)&&x(r)?{...t,...r}:r,gg=new Set(["env",...Rr]);var re=(e,t,r,n)=>{let o=(s,a,c)=>re(s,a,r,c),i=(...s)=>yg({mapArguments:e,deepOptions:r,boundOptions:t,setBoundExeca:n,createNested:o},...s);return n!==void 0&&n(i,o,t),i},yg=({mapArguments:e,deepOptions:t={},boundOptions:r={},setBoundExeca:n,createNested:o},i,...s)=>{if(x(i))return o(e,fr(r,i),n);let{file:a,commandArguments:c,options:f,isSync:l}=Sg({mapArguments:e,firstArgument:i,nextArguments:s,deepOptions:t,boundOptions:r});return l?Fc(a,c,f):zl(a,c,f,o)},Sg=({mapArguments:e,firstArgument:t,nextArguments:r,deepOptions:n,boundOptions:o})=>{let i=Eo(t)?To(t,r):[t,...r],[s,a,c]=et(...i),f=fr(fr(n,o),c),{file:l=s,commandArguments:d=a,options:p=f,isSync:u=!1}=e({file:s,commandArguments:a,options:f});return{file:l,commandArguments:d,options:p,isSync:u}};var Wl=({file:e,commandArguments:t})=>Yl(e,t),Vl=({file:e,commandArguments:t})=>({...Yl(e,t),isSync:!0}),Yl=(e,t)=>{if(t.length>0)throw new TypeError(`The command and its arguments must be passed as a single string: ${e} ${t}.`);let[r,...n]=bg(e);return{file:r,commandArguments:n}},bg=e=>{if(typeof e!="string")throw new TypeError(`The command must be a string: ${String(e)}.`);let t=e.trim();if(t==="")return[];let r=[];for(let n of t.split(wg)){let o=r.at(-1);o&&o.endsWith("\\")?r[r.length-1]=`${o.slice(0,-1)} ${n}`:r.push(n)}return r},wg=/ +/g;var ql=(e,t,r)=>{e.sync=t(xg,r),e.s=e.sync},Hl=({options:e})=>Kl(e),xg=({options:e})=>({...Kl(e),isSync:!0}),Kl=e=>({options:{...Eg(e),...e}}),Eg=({input:e,inputFile:t,stdio:r})=>e===void 0&&t===void 0&&r===void 0?{stdin:"inherit"}:{},Xl={preferLocal:!0};var Jl=re(()=>({})),JO=re(()=>({isSync:!0})),ZO=re(Wl),QO=re(Vl),eI=re(Vs),tI=re(Hl,{},Xl,ql),{sendMessage:rI,getOneMessage:nI,getEachMessage:oI,getCancelSignal:iI}=_c();var dr=class e extends Qe{static summary="Migrate the extension to a newer version of the Raycast API";static description=`Migrate the extension to a newer version of the Raycast API
Internally, the command makes use of https://www.npmjs.com/package/@raycast/migration`;static flags={path:Zl.Flags.string({char:"p",description:"Path to extension root",default:"."})};async run(){let{flags:t}=await this.parse(e);await Jl("@raycast/migration@latest",[t.path],{stderr:"inherit",stdout:"inherit",stdin:"inherit"})}};
