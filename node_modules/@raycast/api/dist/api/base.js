"use strict";var ts=Object.create;var er=Object.defineProperty;var rs=Object.getOwnPropertyDescriptor;var ns=Object.getOwnPropertyNames;var os=Object.getPrototypeOf,as=Object.prototype.hasOwnProperty;var dt=(r,o)=>()=>(o||r((o={exports:{}}).exports,o),o.exports),is=(r,o)=>{for(var a in o)er(r,a,{get:o[a],enumerable:!0})},Do=(r,o,a,s)=>{if(o&&typeof o=="object"||typeof o=="function")for(let l of ns(o))!as.call(r,l)&&l!==a&&er(r,l,{get:()=>o[l],enumerable:!(s=rs(o,l))||s.enumerable});return r};var Fo=(r,o,a)=>(a=r!=null?ts(os(r)):{},Do(o||!r||!r.__esModule?er(a,"default",{value:r,enumerable:!0}):a,r)),ss=r=>Do(er({},"__esModule",{value:!0}),r);var da=dt(ca=>{var ls=Object.create,ur=Object.defineProperty,us=Object.getOwnPropertyDescriptor,fs=Object.getOwnPropertyNames,cs=Object.getPrototypeOf,ds=Object.prototype.hasOwnProperty,Xo=r=>ur(r,"__esModule",{value:!0}),gt=(r,o)=>function(){return r&&(o=(0,r[Object.keys(r)[0]])(r=0)),o},sn=(r,o)=>function(){return o||(0,r[Object.keys(r)[0]])((o={exports:{}}).exports,o),o.exports},Zo=(r,o)=>{Xo(r);for(var a in o)ur(r,a,{get:o[a],enumerable:!0})},hs=(r,o,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let s of fs(o))!ds.call(r,s)&&s!=="default"&&ur(r,s,{get:()=>o[s],enumerable:!(a=us(o,s))||a.enumerable});return r},M=r=>hs(Xo(ur(r!=null?ls(cs(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),ms=sn({"node_modules/web-streams-polyfill/dist/ponyfill.es2018.js"(r,o){(function(a,s){typeof r=="object"&&typeof o<"u"?s(r):typeof define=="function"&&define.amd?define(["exports"],s):(a=typeof globalThis<"u"?globalThis:a||self,s(a.WebStreamsPolyfill={}))})(r,function(a){"use strict";let s=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:e=>`Symbol(${e})`;function l(){}function c(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}let b=c();function h(e){return typeof e=="object"&&e!==null||typeof e=="function"}let g=l,y=Promise,q=Promise.prototype.then,I=Promise.resolve.bind(y),D=Promise.reject.bind(y);function S(e){return new y(e)}function m(e){return I(e)}function p(e){return D(e)}function C(e,t,n){return q.call(e,t,n)}function v(e,t,n){C(C(e,t,n),void 0,g)}function $(e,t){v(e,t)}function j(e,t){v(e,void 0,t)}function B(e,t,n){return C(e,t,n)}function W(e){C(e,void 0,g)}let F=(()=>{let e=b&&b.queueMicrotask;if(typeof e=="function")return e;let t=m(void 0);return n=>C(t,n)})();function Te(e,t,n){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,n)}function ie(e,t,n){try{return m(Te(e,t,n))}catch(i){return p(i)}}let yn=16384;class G{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){let n=this._back,i=n;n._elements.length===yn-1&&(i={_elements:[],_next:void 0}),n._elements.push(t),i!==n&&(this._back=i,n._next=i),++this._size}shift(){let t=this._front,n=t,i=this._cursor,u=i+1,f=t._elements,d=f[i];return u===yn&&(n=t._next,u=0),--this._size,this._cursor=u,t!==n&&(this._front=n),f[i]=void 0,d}forEach(t){let n=this._cursor,i=this._front,u=i._elements;for(;(n!==u.length||i._next!==void 0)&&!(n===u.length&&(i=i._next,u=i._elements,n=0,u.length===0));)t(u[n]),++n}peek(){let t=this._front,n=this._cursor;return t._elements[n]}}function _n(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?yr(e):t._state==="closed"?ka(e):gn(e,t._storedError)}function pr(e,t){let n=e._ownerReadableStream;return K(n,t)}function se(e){e._ownerReadableStream._state==="readable"?_r(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):Oa(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function Oe(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function yr(e){e._closedPromise=S((t,n)=>{e._closedPromise_resolve=t,e._closedPromise_reject=n})}function gn(e,t){yr(e),_r(e,t)}function ka(e){yr(e),Sn(e)}function _r(e,t){e._closedPromise_reject!==void 0&&(W(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function Oa(e,t){gn(e,t)}function Sn(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let wn=s("[[AbortSteps]]"),vn=s("[[ErrorSteps]]"),gr=s("[[CancelSteps]]"),Sr=s("[[PullSteps]]"),Rn=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},Wa=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function qa(e){return typeof e=="object"||typeof e=="function"}function le(e,t){if(e!==void 0&&!qa(e))throw new TypeError(`${t} is not an object.`)}function X(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}function Da(e){return typeof e=="object"&&e!==null||typeof e=="function"}function Tn(e,t){if(!Da(e))throw new TypeError(`${t} is not an object.`)}function ue(e,t,n){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${n}'.`)}function wr(e,t,n){if(e===void 0)throw new TypeError(`${t} is required in '${n}'.`)}function vr(e){return Number(e)}function En(e){return e===0?0:e}function Fa(e){return En(Wa(e))}function Cn(e,t){let i=Number.MAX_SAFE_INTEGER,u=Number(e);if(u=En(u),!Rn(u))throw new TypeError(`${t} is not a finite number`);if(u=Fa(u),u<0||u>i)throw new TypeError(`${t} is outside the accepted range of 0 to ${i}, inclusive`);return!Rn(u)||u===0?0:u}function Rr(e,t){if(!ge(e))throw new TypeError(`${t} is not a ReadableStream.`)}function We(e){return new Ze(e)}function Pn(e,t){e._reader._readRequests.push(t)}function Tr(e,t,n){let u=e._reader._readRequests.shift();n?u._closeSteps():u._chunkSteps(t)}function vt(e){return e._reader._readRequests.length}function An(e){let t=e._reader;return!(t===void 0||!be(t))}class Ze{constructor(t){if(ue(t,1,"ReadableStreamDefaultReader"),Rr(t,"First parameter"),Se(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");_n(this,t),this._readRequests=new G}get closed(){return be(this)?this._closedPromise:p(Rt("closed"))}cancel(t=void 0){return be(this)?this._ownerReadableStream===void 0?p(Oe("cancel")):pr(this,t):p(Rt("cancel"))}read(){if(!be(this))return p(Rt("read"));if(this._ownerReadableStream===void 0)return p(Oe("read from"));let t,n,i=S((f,d)=>{t=f,n=d});return Ke(this,{_chunkSteps:f=>t({value:f,done:!1}),_closeSteps:()=>t({value:void 0,done:!0}),_errorSteps:f=>n(f)}),i}releaseLock(){if(!be(this))throw Rt("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");se(this)}}}Object.defineProperties(Ze.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(Ze.prototype,s.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function be(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof Ze}function Ke(e,t){let n=e._ownerReadableStream;n._disturbed=!0,n._state==="closed"?t._closeSteps():n._state==="errored"?t._errorSteps(n._storedError):n._readableStreamController[Sr](t)}function Rt(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}let Bn=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class kn{constructor(t,n){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=n}next(){let t=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?B(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){let n=()=>this._returnSteps(t);return this._ongoingPromise?B(this._ongoingPromise,n,n):n()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let t=this._reader;if(t._ownerReadableStream===void 0)return p(Oe("iterate"));let n,i,u=S((d,_)=>{n=d,i=_});return Ke(t,{_chunkSteps:d=>{this._ongoingPromise=void 0,F(()=>n({value:d,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,se(t),n({value:void 0,done:!0})},_errorSteps:d=>{this._ongoingPromise=void 0,this._isFinished=!0,se(t),i(d)}}),u}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;let n=this._reader;if(n._ownerReadableStream===void 0)return p(Oe("finish iterating"));if(!this._preventCancel){let i=pr(n,t);return se(n),B(i,()=>({value:t,done:!0}))}return se(n),m({value:t,done:!0})}}let On={next(){return Wn(this)?this._asyncIteratorImpl.next():p(qn("next"))},return(e){return Wn(this)?this._asyncIteratorImpl.return(e):p(qn("return"))}};Bn!==void 0&&Object.setPrototypeOf(On,Bn);function La(e,t){let n=We(e),i=new kn(n,t),u=Object.create(On);return u._asyncIteratorImpl=i,u}function Wn(e){if(!h(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof kn}catch{return!1}}function qn(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}let Dn=Number.isNaN||function(e){return e!==e};function Je(e){return e.slice()}function Fn(e,t,n,i,u){new Uint8Array(e).set(new Uint8Array(n,i,u),t)}function ll(e){return e}function Tt(e){return!1}function Ln(e,t,n){if(e.slice)return e.slice(t,n);let i=n-t,u=new ArrayBuffer(i);return Fn(u,0,e,t,i),u}function za(e){return!(typeof e!="number"||Dn(e)||e<0)}function zn(e){let t=Ln(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function Er(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Cr(e,t,n){if(!za(n)||n===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:n}),e._queueTotalSize+=n}function Ia(e){return e._queue.peek().value}function pe(e){e._queue=new G,e._queueTotalSize=0}class et{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Pr(this))throw Or("view");return this._view}respond(t){if(!Pr(this))throw Or("respond");if(ue(t,1,"respond"),t=Cn(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Tt(this._view.buffer),Bt(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!Pr(this))throw Or("respondWithNewView");if(ue(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");Tt(t.buffer),kt(this._associatedReadableByteStreamController,t)}}Object.defineProperties(et.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(et.prototype,s.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class qe{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Ee(this))throw rt("byobRequest");return kr(this)}get desiredSize(){if(!Ee(this))throw rt("desiredSize");return Hn(this)}close(){if(!Ee(this))throw rt("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);tt(this)}enqueue(t){if(!Ee(this))throw rt("enqueue");if(ue(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let n=this._controlledReadableByteStream._state;if(n!=="readable")throw new TypeError(`The stream (in ${n} state) is not in the readable state and cannot be enqueued to`);At(this,t)}error(t=void 0){if(!Ee(this))throw rt("error");Z(this,t)}[gr](t){In(this),pe(this);let n=this._cancelAlgorithm(t);return Pt(this),n}[Sr](t){let n=this._controlledReadableByteStream;if(this._queueTotalSize>0){let u=this._queue.shift();this._queueTotalSize-=u.byteLength,Un(this);let f=new Uint8Array(u.buffer,u.byteOffset,u.byteLength);t._chunkSteps(f);return}let i=this._autoAllocateChunkSize;if(i!==void 0){let u;try{u=new ArrayBuffer(i)}catch(d){t._errorSteps(d);return}let f={buffer:u,bufferByteLength:i,byteOffset:0,byteLength:i,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(f)}Pn(n,t),Ce(this)}}Object.defineProperties(qe.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(qe.prototype,s.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function Ee(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof qe}function Pr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof et}function Ce(e){if(!Ua(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let n=e._pullAlgorithm();v(n,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Ce(e))},i=>{Z(e,i)})}function In(e){Br(e),e._pendingPullIntos=new G}function Ar(e,t){let n=!1;e._state==="closed"&&(n=!0);let i=$n(t);t.readerType==="default"?Tr(e,i,n):Ha(e,i,n)}function $n(e){let t=e.bytesFilled,n=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/n)}function Et(e,t,n,i){e._queue.push({buffer:t,byteOffset:n,byteLength:i}),e._queueTotalSize+=i}function jn(e,t){let n=t.elementSize,i=t.bytesFilled-t.bytesFilled%n,u=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),f=t.bytesFilled+u,d=f-f%n,_=u,R=!1;d>i&&(_=d-t.bytesFilled,R=!0);let E=e._queue;for(;_>0;){let P=E.peek(),A=Math.min(_,P.byteLength),L=t.byteOffset+t.bytesFilled;Fn(t.buffer,L,P.buffer,P.byteOffset,A),P.byteLength===A?E.shift():(P.byteOffset+=A,P.byteLength-=A),e._queueTotalSize-=A,Mn(e,A,t),_-=A}return R}function Mn(e,t,n){n.bytesFilled+=t}function Un(e){e._queueTotalSize===0&&e._closeRequested?(Pt(e),ft(e._controlledReadableByteStream)):Ce(e)}function Br(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function xn(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;let t=e._pendingPullIntos.peek();jn(e,t)&&(Ct(e),Ar(e._controlledReadableByteStream,t))}}function $a(e,t,n){let i=e._controlledReadableByteStream,u=1;t.constructor!==DataView&&(u=t.constructor.BYTES_PER_ELEMENT);let f=t.constructor,d=t.buffer,_={buffer:d,bufferByteLength:d.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:u,viewConstructor:f,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(_),Qn(i,n);return}if(i._state==="closed"){let R=new f(_.buffer,_.byteOffset,0);n._closeSteps(R);return}if(e._queueTotalSize>0){if(jn(e,_)){let R=$n(_);Un(e),n._chunkSteps(R);return}if(e._closeRequested){let R=new TypeError("Insufficient bytes to fill elements in the given buffer");Z(e,R),n._errorSteps(R);return}}e._pendingPullIntos.push(_),Qn(i,n),Ce(e)}function ja(e,t){let n=e._controlledReadableByteStream;if(Wr(n))for(;Gn(n)>0;){let i=Ct(e);Ar(n,i)}}function Ma(e,t,n){if(Mn(e,t,n),n.bytesFilled<n.elementSize)return;Ct(e);let i=n.bytesFilled%n.elementSize;if(i>0){let u=n.byteOffset+n.bytesFilled,f=Ln(n.buffer,u-i,u);Et(e,f,0,f.byteLength)}n.bytesFilled-=i,Ar(e._controlledReadableByteStream,n),xn(e)}function Nn(e,t){let n=e._pendingPullIntos.peek();Br(e),e._controlledReadableByteStream._state==="closed"?ja(e):Ma(e,t,n),Ce(e)}function Ct(e){return e._pendingPullIntos.shift()}function Ua(e){let t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(An(t)&&vt(t)>0||Wr(t)&&Gn(t)>0||Hn(e)>0)}function Pt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function tt(e){let t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let i=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Z(e,i),i}Pt(e),ft(t)}}function At(e,t){let n=e._controlledReadableByteStream;if(e._closeRequested||n._state!=="readable")return;let i=t.buffer,u=t.byteOffset,f=t.byteLength,d=i;if(e._pendingPullIntos.length>0){let _=e._pendingPullIntos.peek();Tt(_.buffer),_.buffer=_.buffer}if(Br(e),An(n))if(vt(n)===0)Et(e,d,u,f);else{e._pendingPullIntos.length>0&&Ct(e);let _=new Uint8Array(d,u,f);Tr(n,_,!1)}else Wr(n)?(Et(e,d,u,f),xn(e)):Et(e,d,u,f);Ce(e)}function Z(e,t){let n=e._controlledReadableByteStream;n._state==="readable"&&(In(e),pe(e),Pt(e),So(n,t))}function kr(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),n=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),i=Object.create(et.prototype);Na(i,e,n),e._byobRequest=i}return e._byobRequest}function Hn(e){let t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function Bt(e,t){let n=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(n.bytesFilled+t>n.byteLength)throw new RangeError("bytesWritten out of range")}n.buffer=n.buffer,Nn(e,t)}function kt(e,t){let n=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(n.byteOffset+n.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(n.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(n.bytesFilled+t.byteLength>n.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let u=t.byteLength;n.buffer=t.buffer,Nn(e,u)}function Vn(e,t,n,i,u,f,d){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,pe(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=f,t._pullAlgorithm=i,t._cancelAlgorithm=u,t._autoAllocateChunkSize=d,t._pendingPullIntos=new G,e._readableStreamController=t;let _=n();v(m(_),()=>{t._started=!0,Ce(t)},R=>{Z(t,R)})}function xa(e,t,n){let i=Object.create(qe.prototype),u=()=>{},f=()=>m(void 0),d=()=>m(void 0);t.start!==void 0&&(u=()=>t.start(i)),t.pull!==void 0&&(f=()=>t.pull(i)),t.cancel!==void 0&&(d=R=>t.cancel(R));let _=t.autoAllocateChunkSize;if(_===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");Vn(e,i,u,f,d,n,_)}function Na(e,t,n){e._associatedReadableByteStreamController=t,e._view=n}function Or(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function rt(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function Yn(e){return new nt(e)}function Qn(e,t){e._reader._readIntoRequests.push(t)}function Ha(e,t,n){let u=e._reader._readIntoRequests.shift();n?u._closeSteps(t):u._chunkSteps(t)}function Gn(e){return e._reader._readIntoRequests.length}function Wr(e){let t=e._reader;return!(t===void 0||!Pe(t))}class nt{constructor(t){if(ue(t,1,"ReadableStreamBYOBReader"),Rr(t,"First parameter"),Se(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Ee(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");_n(this,t),this._readIntoRequests=new G}get closed(){return Pe(this)?this._closedPromise:p(Ot("closed"))}cancel(t=void 0){return Pe(this)?this._ownerReadableStream===void 0?p(Oe("cancel")):pr(this,t):p(Ot("cancel"))}read(t){if(!Pe(this))return p(Ot("read"));if(!ArrayBuffer.isView(t))return p(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return p(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return p(new TypeError("view's buffer must have non-zero byteLength"));if(Tt(t.buffer),this._ownerReadableStream===void 0)return p(Oe("read from"));let n,i,u=S((d,_)=>{n=d,i=_});return Xn(this,t,{_chunkSteps:d=>n({value:d,done:!1}),_closeSteps:d=>n({value:d,done:!0}),_errorSteps:d=>i(d)}),u}releaseLock(){if(!Pe(this))throw Ot("releaseLock");if(this._ownerReadableStream!==void 0){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");se(this)}}}Object.defineProperties(nt.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(nt.prototype,s.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function Pe(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof nt}function Xn(e,t,n){let i=e._ownerReadableStream;i._disturbed=!0,i._state==="errored"?n._errorSteps(i._storedError):$a(i._readableStreamController,t,n)}function Ot(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function ot(e,t){let{highWaterMark:n}=e;if(n===void 0)return t;if(Dn(n)||n<0)throw new RangeError("Invalid highWaterMark");return n}function Wt(e){let{size:t}=e;return t||(()=>1)}function qt(e,t){le(e,t);let n=e?.highWaterMark,i=e?.size;return{highWaterMark:n===void 0?void 0:vr(n),size:i===void 0?void 0:Va(i,`${t} has member 'size' that`)}}function Va(e,t){return X(e,t),n=>vr(e(n))}function Ya(e,t){le(e,t);let n=e?.abort,i=e?.close,u=e?.start,f=e?.type,d=e?.write;return{abort:n===void 0?void 0:Qa(n,e,`${t} has member 'abort' that`),close:i===void 0?void 0:Ga(i,e,`${t} has member 'close' that`),start:u===void 0?void 0:Xa(u,e,`${t} has member 'start' that`),write:d===void 0?void 0:Za(d,e,`${t} has member 'write' that`),type:f}}function Qa(e,t,n){return X(e,n),i=>ie(e,t,[i])}function Ga(e,t,n){return X(e,n),()=>ie(e,t,[])}function Xa(e,t,n){return X(e,n),i=>Te(e,t,[i])}function Za(e,t,n){return X(e,n),(i,u)=>ie(e,t,[i,u])}function Zn(e,t){if(!De(e))throw new TypeError(`${t} is not a WritableStream.`)}function Ka(e){if(typeof e!="object"||e===null)return!1;try{return typeof e.aborted=="boolean"}catch{return!1}}let Ja=typeof AbortController=="function";function ei(){if(Ja)return new AbortController}class at{constructor(t={},n={}){t===void 0?t=null:Tn(t,"First parameter");let i=qt(n,"Second parameter"),u=Ya(t,"First parameter");if(Jn(this),u.type!==void 0)throw new RangeError("Invalid type is specified");let d=Wt(i),_=ot(i,1);mi(this,u,_,d)}get locked(){if(!De(this))throw It("locked");return Fe(this)}abort(t=void 0){return De(this)?Fe(this)?p(new TypeError("Cannot abort a stream that already has a writer")):Dt(this,t):p(It("abort"))}close(){return De(this)?Fe(this)?p(new TypeError("Cannot close a stream that already has a writer")):ne(this)?p(new TypeError("Cannot close an already-closing stream")):eo(this):p(It("close"))}getWriter(){if(!De(this))throw It("getWriter");return Kn(this)}}Object.defineProperties(at.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(at.prototype,s.toStringTag,{value:"WritableStream",configurable:!0});function Kn(e){return new it(e)}function ti(e,t,n,i,u=1,f=()=>1){let d=Object.create(at.prototype);Jn(d);let _=Object.create(Le.prototype);return io(d,_,e,t,n,i,u,f),d}function Jn(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new G,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function De(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof at}function Fe(e){return e._writer!==void 0}function Dt(e,t){var n;if(e._state==="closed"||e._state==="errored")return m(void 0);e._writableStreamController._abortReason=t,(n=e._writableStreamController._abortController)===null||n===void 0||n.abort();let i=e._state;if(i==="closed"||i==="errored")return m(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let u=!1;i==="erroring"&&(u=!0,t=void 0);let f=S((d,_)=>{e._pendingAbortRequest={_promise:void 0,_resolve:d,_reject:_,_reason:t,_wasAlreadyErroring:u}});return e._pendingAbortRequest._promise=f,u||Dr(e,t),f}function eo(e){let t=e._state;if(t==="closed"||t==="errored")return p(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));let n=S((u,f)=>{let d={_resolve:u,_reject:f};e._closeRequest=d}),i=e._writer;return i!==void 0&&e._backpressure&&t==="writable"&&xr(i),bi(e._writableStreamController),n}function ri(e){return S((n,i)=>{let u={_resolve:n,_reject:i};e._writeRequests.push(u)})}function qr(e,t){if(e._state==="writable"){Dr(e,t);return}Fr(e)}function Dr(e,t){let n=e._writableStreamController;e._state="erroring",e._storedError=t;let i=e._writer;i!==void 0&&ro(i,t),!si(e)&&n._started&&Fr(e)}function Fr(e){e._state="errored",e._writableStreamController[vn]();let t=e._storedError;if(e._writeRequests.forEach(u=>{u._reject(t)}),e._writeRequests=new G,e._pendingAbortRequest===void 0){Ft(e);return}let n=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,n._wasAlreadyErroring){n._reject(t),Ft(e);return}let i=e._writableStreamController[wn](n._reason);v(i,()=>{n._resolve(),Ft(e)},u=>{n._reject(u),Ft(e)})}function ni(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function oi(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,qr(e,t)}function ai(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let n=e._writer;n!==void 0&&fo(n)}function ii(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),qr(e,t)}function ne(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}function si(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}function li(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function ui(e){e._inFlightWriteRequest=e._writeRequests.shift()}function Ft(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;t!==void 0&&Mr(t,e._storedError)}function Lr(e,t){let n=e._writer;n!==void 0&&t!==e._backpressure&&(t?vi(n):xr(n)),e._backpressure=t}class it{constructor(t){if(ue(t,1,"WritableStreamDefaultWriter"),Zn(t,"First parameter"),Fe(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;let n=t._state;if(n==="writable")!ne(t)&&t._backpressure?jt(this):co(this),$t(this);else if(n==="erroring")Ur(this,t._storedError),$t(this);else if(n==="closed")co(this),Si(this);else{let i=t._storedError;Ur(this,i),uo(this,i)}}get closed(){return Ae(this)?this._closedPromise:p(Be("closed"))}get desiredSize(){if(!Ae(this))throw Be("desiredSize");if(this._ownerWritableStream===void 0)throw st("desiredSize");return hi(this)}get ready(){return Ae(this)?this._readyPromise:p(Be("ready"))}abort(t=void 0){return Ae(this)?this._ownerWritableStream===void 0?p(st("abort")):fi(this,t):p(Be("abort"))}close(){if(!Ae(this))return p(Be("close"));let t=this._ownerWritableStream;return t===void 0?p(st("close")):ne(t)?p(new TypeError("Cannot close an already-closing stream")):to(this)}releaseLock(){if(!Ae(this))throw Be("releaseLock");this._ownerWritableStream!==void 0&&no(this)}write(t=void 0){return Ae(this)?this._ownerWritableStream===void 0?p(st("write to")):oo(this,t):p(Be("write"))}}Object.defineProperties(it.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(it.prototype,s.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function Ae(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof it}function fi(e,t){let n=e._ownerWritableStream;return Dt(n,t)}function to(e){let t=e._ownerWritableStream;return eo(t)}function ci(e){let t=e._ownerWritableStream,n=t._state;return ne(t)||n==="closed"?m(void 0):n==="errored"?p(t._storedError):to(e)}function di(e,t){e._closedPromiseState==="pending"?Mr(e,t):wi(e,t)}function ro(e,t){e._readyPromiseState==="pending"?ho(e,t):Ri(e,t)}function hi(e){let t=e._ownerWritableStream,n=t._state;return n==="errored"||n==="erroring"?null:n==="closed"?0:so(t._writableStreamController)}function no(e){let t=e._ownerWritableStream,n=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");ro(e,n),di(e,n),t._writer=void 0,e._ownerWritableStream=void 0}function oo(e,t){let n=e._ownerWritableStream,i=n._writableStreamController,u=pi(i,t);if(n!==e._ownerWritableStream)return p(st("write to"));let f=n._state;if(f==="errored")return p(n._storedError);if(ne(n)||f==="closed")return p(new TypeError("The stream is closing or closed and cannot be written to"));if(f==="erroring")return p(n._storedError);let d=ri(n);return yi(i,t,u),d}let ao={};class Le{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!zr(this))throw jr("abortReason");return this._abortReason}get signal(){if(!zr(this))throw jr("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!zr(this))throw jr("error");this._controlledWritableStream._state==="writable"&&lo(this,t)}[wn](t){let n=this._abortAlgorithm(t);return Lt(this),n}[vn](){pe(this)}}Object.defineProperties(Le.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(Le.prototype,s.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function zr(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof Le}function io(e,t,n,i,u,f,d,_){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,pe(t),t._abortReason=void 0,t._abortController=ei(),t._started=!1,t._strategySizeAlgorithm=_,t._strategyHWM=d,t._writeAlgorithm=i,t._closeAlgorithm=u,t._abortAlgorithm=f;let R=$r(t);Lr(e,R);let E=n(),P=m(E);v(P,()=>{t._started=!0,zt(t)},A=>{t._started=!0,qr(e,A)})}function mi(e,t,n,i){let u=Object.create(Le.prototype),f=()=>{},d=()=>m(void 0),_=()=>m(void 0),R=()=>m(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.write!==void 0&&(d=E=>t.write(E,u)),t.close!==void 0&&(_=()=>t.close()),t.abort!==void 0&&(R=E=>t.abort(E)),io(e,u,f,d,_,R,n,i)}function Lt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function bi(e){Cr(e,ao,0),zt(e)}function pi(e,t){try{return e._strategySizeAlgorithm(t)}catch(n){return Ir(e,n),1}}function so(e){return e._strategyHWM-e._queueTotalSize}function yi(e,t,n){try{Cr(e,t,n)}catch(u){Ir(e,u);return}let i=e._controlledWritableStream;if(!ne(i)&&i._state==="writable"){let u=$r(e);Lr(i,u)}zt(e)}function zt(e){let t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){Fr(t);return}if(e._queue.length===0)return;let i=Ia(e);i===ao?_i(e):gi(e,i)}function Ir(e,t){e._controlledWritableStream._state==="writable"&&lo(e,t)}function _i(e){let t=e._controlledWritableStream;li(t),Er(e);let n=e._closeAlgorithm();Lt(e),v(n,()=>{ai(t)},i=>{ii(t,i)})}function gi(e,t){let n=e._controlledWritableStream;ui(n);let i=e._writeAlgorithm(t);v(i,()=>{ni(n);let u=n._state;if(Er(e),!ne(n)&&u==="writable"){let f=$r(e);Lr(n,f)}zt(e)},u=>{n._state==="writable"&&Lt(e),oi(n,u)})}function $r(e){return so(e)<=0}function lo(e,t){let n=e._controlledWritableStream;Lt(e),Dr(n,t)}function It(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function jr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Be(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function st(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function $t(e){e._closedPromise=S((t,n)=>{e._closedPromise_resolve=t,e._closedPromise_reject=n,e._closedPromiseState="pending"})}function uo(e,t){$t(e),Mr(e,t)}function Si(e){$t(e),fo(e)}function Mr(e,t){e._closedPromise_reject!==void 0&&(W(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function wi(e,t){uo(e,t)}function fo(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function jt(e){e._readyPromise=S((t,n)=>{e._readyPromise_resolve=t,e._readyPromise_reject=n}),e._readyPromiseState="pending"}function Ur(e,t){jt(e),ho(e,t)}function co(e){jt(e),xr(e)}function ho(e,t){e._readyPromise_reject!==void 0&&(W(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function vi(e){jt(e)}function Ri(e,t){Ur(e,t)}function xr(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}let mo=typeof DOMException<"u"?DOMException:void 0;function Ti(e){if(!(typeof e=="function"||typeof e=="object"))return!1;try{return new e,!0}catch{return!1}}function Ei(){let e=function(n,i){this.message=n||"",this.name=i||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}let Ci=Ti(mo)?mo:Ei();function bo(e,t,n,i,u,f){let d=We(e),_=Kn(t);e._disturbed=!0;let R=!1,E=m(void 0);return S((P,A)=>{let L;if(f!==void 0){if(L=()=>{let w=new Ci("Aborted","AbortError"),T=[];i||T.push(()=>t._state==="writable"?Dt(t,w):m(void 0)),u||T.push(()=>e._state==="readable"?K(e,w):m(void 0)),x(()=>Promise.all(T.map(O=>O())),!0,w)},f.aborted){L();return}f.addEventListener("abort",L)}function J(){return S((w,T)=>{function O(H){H?w():C($e(),O,T)}O(!1)})}function $e(){return R?m(!0):C(_._readyPromise,()=>S((w,T)=>{Ke(d,{_chunkSteps:O=>{E=C(oo(_,O),void 0,l),w(!1)},_closeSteps:()=>w(!0),_errorSteps:T})}))}if(fe(e,d._closedPromise,w=>{i?Y(!0,w):x(()=>Dt(t,w),!0,w)}),fe(t,_._closedPromise,w=>{u?Y(!0,w):x(()=>K(e,w),!0,w)}),U(e,d._closedPromise,()=>{n?Y():x(()=>ci(_))}),ne(t)||t._state==="closed"){let w=new TypeError("the destination writable stream closed before all data could be piped to it");u?Y(!0,w):x(()=>K(e,w),!0,w)}W(J());function we(){let w=E;return C(E,()=>w!==E?we():void 0)}function fe(w,T,O){w._state==="errored"?O(w._storedError):j(T,O)}function U(w,T,O){w._state==="closed"?O():$(T,O)}function x(w,T,O){if(R)return;R=!0,t._state==="writable"&&!ne(t)?$(we(),H):H();function H(){v(w(),()=>ce(T,O),je=>ce(!0,je))}}function Y(w,T){R||(R=!0,t._state==="writable"&&!ne(t)?$(we(),()=>ce(w,T)):ce(w,T))}function ce(w,T){no(_),se(d),f!==void 0&&f.removeEventListener("abort",L),w?A(T):P(void 0)}})}class ze{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Mt(this))throw Nt("desiredSize");return Nr(this)}close(){if(!Mt(this))throw Nt("close");if(!Ie(this))throw new TypeError("The stream is not in a state that permits close");ut(this)}enqueue(t=void 0){if(!Mt(this))throw Nt("enqueue");if(!Ie(this))throw new TypeError("The stream is not in a state that permits enqueue");return xt(this,t)}error(t=void 0){if(!Mt(this))throw Nt("error");ye(this,t)}[gr](t){pe(this);let n=this._cancelAlgorithm(t);return Ut(this),n}[Sr](t){let n=this._controlledReadableStream;if(this._queue.length>0){let i=Er(this);this._closeRequested&&this._queue.length===0?(Ut(this),ft(n)):lt(this),t._chunkSteps(i)}else Pn(n,t),lt(this)}}Object.defineProperties(ze.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(ze.prototype,s.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function Mt(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof ze}function lt(e){if(!po(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let n=e._pullAlgorithm();v(n,()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,lt(e))},i=>{ye(e,i)})}function po(e){let t=e._controlledReadableStream;return!Ie(e)||!e._started?!1:!!(Se(t)&&vt(t)>0||Nr(e)>0)}function Ut(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function ut(e){if(!Ie(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(Ut(e),ft(t))}function xt(e,t){if(!Ie(e))return;let n=e._controlledReadableStream;if(Se(n)&&vt(n)>0)Tr(n,t,!1);else{let i;try{i=e._strategySizeAlgorithm(t)}catch(u){throw ye(e,u),u}try{Cr(e,t,i)}catch(u){throw ye(e,u),u}}lt(e)}function ye(e,t){let n=e._controlledReadableStream;n._state==="readable"&&(pe(e),Ut(e),So(n,t))}function Nr(e){let t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function Pi(e){return!po(e)}function Ie(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&t==="readable"}function yo(e,t,n,i,u,f,d){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,pe(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=d,t._strategyHWM=f,t._pullAlgorithm=i,t._cancelAlgorithm=u,e._readableStreamController=t;let _=n();v(m(_),()=>{t._started=!0,lt(t)},R=>{ye(t,R)})}function Ai(e,t,n,i){let u=Object.create(ze.prototype),f=()=>{},d=()=>m(void 0),_=()=>m(void 0);t.start!==void 0&&(f=()=>t.start(u)),t.pull!==void 0&&(d=()=>t.pull(u)),t.cancel!==void 0&&(_=R=>t.cancel(R)),yo(e,u,f,d,_,n,i)}function Nt(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function Bi(e,t){return Ee(e._readableStreamController)?Oi(e):ki(e)}function ki(e,t){let n=We(e),i=!1,u=!1,f=!1,d=!1,_,R,E,P,A,L=S(U=>{A=U});function J(){return i?(u=!0,m(void 0)):(i=!0,Ke(n,{_chunkSteps:x=>{F(()=>{u=!1;let Y=x,ce=x;f||xt(E._readableStreamController,Y),d||xt(P._readableStreamController,ce),i=!1,u&&J()})},_closeSteps:()=>{i=!1,f||ut(E._readableStreamController),d||ut(P._readableStreamController),(!f||!d)&&A(void 0)},_errorSteps:()=>{i=!1}}),m(void 0))}function $e(U){if(f=!0,_=U,d){let x=Je([_,R]),Y=K(e,x);A(Y)}return L}function we(U){if(d=!0,R=U,f){let x=Je([_,R]),Y=K(e,x);A(Y)}return L}function fe(){}return E=Hr(fe,J,$e),P=Hr(fe,J,we),j(n._closedPromise,U=>{ye(E._readableStreamController,U),ye(P._readableStreamController,U),(!f||!d)&&A(void 0)}),[E,P]}function Oi(e){let t=We(e),n=!1,i=!1,u=!1,f=!1,d=!1,_,R,E,P,A,L=S(w=>{A=w});function J(w){j(w._closedPromise,T=>{w===t&&(Z(E._readableStreamController,T),Z(P._readableStreamController,T),(!f||!d)&&A(void 0))})}function $e(){Pe(t)&&(se(t),t=We(e),J(t)),Ke(t,{_chunkSteps:T=>{F(()=>{i=!1,u=!1;let O=T,H=T;if(!f&&!d)try{H=zn(T)}catch(je){Z(E._readableStreamController,je),Z(P._readableStreamController,je),A(K(e,je));return}f||At(E._readableStreamController,O),d||At(P._readableStreamController,H),n=!1,i?fe():u&&U()})},_closeSteps:()=>{n=!1,f||tt(E._readableStreamController),d||tt(P._readableStreamController),E._readableStreamController._pendingPullIntos.length>0&&Bt(E._readableStreamController,0),P._readableStreamController._pendingPullIntos.length>0&&Bt(P._readableStreamController,0),(!f||!d)&&A(void 0)},_errorSteps:()=>{n=!1}})}function we(w,T){be(t)&&(se(t),t=Yn(e),J(t));let O=T?P:E,H=T?E:P;Xn(t,w,{_chunkSteps:Me=>{F(()=>{i=!1,u=!1;let Ue=T?d:f;if(T?f:d)Ue||kt(O._readableStreamController,Me);else{let qo;try{qo=zn(Me)}catch(Yr){Z(O._readableStreamController,Yr),Z(H._readableStreamController,Yr),A(K(e,Yr));return}Ue||kt(O._readableStreamController,Me),At(H._readableStreamController,qo)}n=!1,i?fe():u&&U()})},_closeSteps:Me=>{n=!1;let Ue=T?d:f,Jt=T?f:d;Ue||tt(O._readableStreamController),Jt||tt(H._readableStreamController),Me!==void 0&&(Ue||kt(O._readableStreamController,Me),!Jt&&H._readableStreamController._pendingPullIntos.length>0&&Bt(H._readableStreamController,0)),(!Ue||!Jt)&&A(void 0)},_errorSteps:()=>{n=!1}})}function fe(){if(n)return i=!0,m(void 0);n=!0;let w=kr(E._readableStreamController);return w===null?$e():we(w._view,!1),m(void 0)}function U(){if(n)return u=!0,m(void 0);n=!0;let w=kr(P._readableStreamController);return w===null?$e():we(w._view,!0),m(void 0)}function x(w){if(f=!0,_=w,d){let T=Je([_,R]),O=K(e,T);A(O)}return L}function Y(w){if(d=!0,R=w,f){let T=Je([_,R]),O=K(e,T);A(O)}return L}function ce(){}return E=go(ce,fe,x),P=go(ce,U,Y),J(t),[E,P]}function Wi(e,t){le(e,t);let n=e,i=n?.autoAllocateChunkSize,u=n?.cancel,f=n?.pull,d=n?.start,_=n?.type;return{autoAllocateChunkSize:i===void 0?void 0:Cn(i,`${t} has member 'autoAllocateChunkSize' that`),cancel:u===void 0?void 0:qi(u,n,`${t} has member 'cancel' that`),pull:f===void 0?void 0:Di(f,n,`${t} has member 'pull' that`),start:d===void 0?void 0:Fi(d,n,`${t} has member 'start' that`),type:_===void 0?void 0:Li(_,`${t} has member 'type' that`)}}function qi(e,t,n){return X(e,n),i=>ie(e,t,[i])}function Di(e,t,n){return X(e,n),i=>ie(e,t,[i])}function Fi(e,t,n){return X(e,n),i=>Te(e,t,[i])}function Li(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function zi(e,t){le(e,t);let n=e?.mode;return{mode:n===void 0?void 0:Ii(n,`${t} has member 'mode' that`)}}function Ii(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function $i(e,t){return le(e,t),{preventCancel:!!e?.preventCancel}}function _o(e,t){le(e,t);let n=e?.preventAbort,i=e?.preventCancel,u=e?.preventClose,f=e?.signal;return f!==void 0&&ji(f,`${t} has member 'signal' that`),{preventAbort:!!n,preventCancel:!!i,preventClose:!!u,signal:f}}function ji(e,t){if(!Ka(e))throw new TypeError(`${t} is not an AbortSignal.`)}function Mi(e,t){le(e,t);let n=e?.readable;wr(n,"readable","ReadableWritablePair"),Rr(n,`${t} has member 'readable' that`);let i=e?.writable;return wr(i,"writable","ReadableWritablePair"),Zn(i,`${t} has member 'writable' that`),{readable:n,writable:i}}class _e{constructor(t={},n={}){t===void 0?t=null:Tn(t,"First parameter");let i=qt(n,"Second parameter"),u=Wi(t,"First parameter");if(Vr(this),u.type==="bytes"){if(i.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let f=ot(i,0);xa(this,u,f)}else{let f=Wt(i),d=ot(i,1);Ai(this,u,d,f)}}get locked(){if(!ge(this))throw ke("locked");return Se(this)}cancel(t=void 0){return ge(this)?Se(this)?p(new TypeError("Cannot cancel a stream that already has a reader")):K(this,t):p(ke("cancel"))}getReader(t=void 0){if(!ge(this))throw ke("getReader");return zi(t,"First parameter").mode===void 0?We(this):Yn(this)}pipeThrough(t,n={}){if(!ge(this))throw ke("pipeThrough");ue(t,1,"pipeThrough");let i=Mi(t,"First parameter"),u=_o(n,"Second parameter");if(Se(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Fe(i.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let f=bo(this,i.writable,u.preventClose,u.preventAbort,u.preventCancel,u.signal);return W(f),i.readable}pipeTo(t,n={}){if(!ge(this))return p(ke("pipeTo"));if(t===void 0)return p("Parameter 1 is required in 'pipeTo'.");if(!De(t))return p(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let i;try{i=_o(n,"Second parameter")}catch(u){return p(u)}return Se(this)?p(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Fe(t)?p(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):bo(this,t,i.preventClose,i.preventAbort,i.preventCancel,i.signal)}tee(){if(!ge(this))throw ke("tee");let t=Bi(this);return Je(t)}values(t=void 0){if(!ge(this))throw ke("values");let n=$i(t,"First parameter");return La(this,n.preventCancel)}}Object.defineProperties(_e.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(_e.prototype,s.toStringTag,{value:"ReadableStream",configurable:!0}),typeof s.asyncIterator=="symbol"&&Object.defineProperty(_e.prototype,s.asyncIterator,{value:_e.prototype.values,writable:!0,configurable:!0});function Hr(e,t,n,i=1,u=()=>1){let f=Object.create(_e.prototype);Vr(f);let d=Object.create(ze.prototype);return yo(f,d,e,t,n,i,u),f}function go(e,t,n){let i=Object.create(_e.prototype);Vr(i);let u=Object.create(qe.prototype);return Vn(i,u,e,t,n,0,void 0),i}function Vr(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function ge(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof _e}function Se(e){return e._reader!==void 0}function K(e,t){if(e._disturbed=!0,e._state==="closed")return m(void 0);if(e._state==="errored")return p(e._storedError);ft(e);let n=e._reader;n!==void 0&&Pe(n)&&(n._readIntoRequests.forEach(u=>{u._closeSteps(void 0)}),n._readIntoRequests=new G);let i=e._readableStreamController[gr](t);return B(i,l)}function ft(e){e._state="closed";let t=e._reader;t!==void 0&&(Sn(t),be(t)&&(t._readRequests.forEach(n=>{n._closeSteps()}),t._readRequests=new G))}function So(e,t){e._state="errored",e._storedError=t;let n=e._reader;n!==void 0&&(_r(n,t),be(n)?(n._readRequests.forEach(i=>{i._errorSteps(t)}),n._readRequests=new G):(n._readIntoRequests.forEach(i=>{i._errorSteps(t)}),n._readIntoRequests=new G))}function ke(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function wo(e,t){le(e,t);let n=e?.highWaterMark;return wr(n,"highWaterMark","QueuingStrategyInit"),{highWaterMark:vr(n)}}let vo=e=>e.byteLength;try{Object.defineProperty(vo,"name",{value:"size",configurable:!0})}catch{}class Ht{constructor(t){ue(t,1,"ByteLengthQueuingStrategy"),t=wo(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!To(this))throw Ro("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!To(this))throw Ro("size");return vo}}Object.defineProperties(Ht.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(Ht.prototype,s.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Ro(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function To(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof Ht}let Eo=()=>1;try{Object.defineProperty(Eo,"name",{value:"size",configurable:!0})}catch{}class Vt{constructor(t){ue(t,1,"CountQueuingStrategy"),t=wo(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!Po(this))throw Co("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Po(this))throw Co("size");return Eo}}Object.defineProperties(Vt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(Vt.prototype,s.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function Co(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Po(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof Vt}function Ui(e,t){le(e,t);let n=e?.flush,i=e?.readableType,u=e?.start,f=e?.transform,d=e?.writableType;return{flush:n===void 0?void 0:xi(n,e,`${t} has member 'flush' that`),readableType:i,start:u===void 0?void 0:Ni(u,e,`${t} has member 'start' that`),transform:f===void 0?void 0:Hi(f,e,`${t} has member 'transform' that`),writableType:d}}function xi(e,t,n){return X(e,n),i=>ie(e,t,[i])}function Ni(e,t,n){return X(e,n),i=>Te(e,t,[i])}function Hi(e,t,n){return X(e,n),(i,u)=>ie(e,t,[i,u])}class Yt{constructor(t={},n={},i={}){t===void 0&&(t=null);let u=qt(n,"Second parameter"),f=qt(i,"Third parameter"),d=Ui(t,"First parameter");if(d.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(d.writableType!==void 0)throw new RangeError("Invalid writableType specified");let _=ot(f,0),R=Wt(f),E=ot(u,1),P=Wt(u),A,L=S(J=>{A=J});Vi(this,L,E,P,_,R),Qi(this,d),d.start!==void 0?A(d.start(this._transformStreamController)):A(void 0)}get readable(){if(!Ao(this))throw Wo("readable");return this._readable}get writable(){if(!Ao(this))throw Wo("writable");return this._writable}}Object.defineProperties(Yt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(Yt.prototype,s.toStringTag,{value:"TransformStream",configurable:!0});function Vi(e,t,n,i,u,f){function d(){return t}function _(L){return Zi(e,L)}function R(L){return Ki(e,L)}function E(){return Ji(e)}e._writable=ti(d,_,E,R,n,i);function P(){return es(e)}function A(L){return Gt(e,L),m(void 0)}e._readable=Hr(d,P,A,u,f),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Xt(e,!0),e._transformStreamController=void 0}function Ao(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof Yt}function Qt(e,t){ye(e._readable._readableStreamController,t),Gt(e,t)}function Gt(e,t){Bo(e._transformStreamController),Ir(e._writable._writableStreamController,t),e._backpressure&&Xt(e,!1)}function Xt(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=S(n=>{e._backpressureChangePromise_resolve=n}),e._backpressure=t}class ct{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Zt(this))throw Kt("desiredSize");let t=this._controlledTransformStream._readable._readableStreamController;return Nr(t)}enqueue(t=void 0){if(!Zt(this))throw Kt("enqueue");ko(this,t)}error(t=void 0){if(!Zt(this))throw Kt("error");Gi(this,t)}terminate(){if(!Zt(this))throw Kt("terminate");Xi(this)}}Object.defineProperties(ct.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),typeof s.toStringTag=="symbol"&&Object.defineProperty(ct.prototype,s.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function Zt(e){return!h(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof ct}function Yi(e,t,n,i){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=n,t._flushAlgorithm=i}function Qi(e,t){let n=Object.create(ct.prototype),i=f=>{try{return ko(n,f),m(void 0)}catch(d){return p(d)}},u=()=>m(void 0);t.transform!==void 0&&(i=f=>t.transform(f,n)),t.flush!==void 0&&(u=()=>t.flush(n)),Yi(e,n,i,u)}function Bo(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function ko(e,t){let n=e._controlledTransformStream,i=n._readable._readableStreamController;if(!Ie(i))throw new TypeError("Readable side is not in a state that permits enqueue");try{xt(i,t)}catch(f){throw Gt(n,f),n._readable._storedError}Pi(i)!==n._backpressure&&Xt(n,!0)}function Gi(e,t){Qt(e._controlledTransformStream,t)}function Oo(e,t){let n=e._transformAlgorithm(t);return B(n,void 0,i=>{throw Qt(e._controlledTransformStream,i),i})}function Xi(e){let t=e._controlledTransformStream,n=t._readable._readableStreamController;ut(n);let i=new TypeError("TransformStream terminated");Gt(t,i)}function Zi(e,t){let n=e._transformStreamController;if(e._backpressure){let i=e._backpressureChangePromise;return B(i,()=>{let u=e._writable;if(u._state==="erroring")throw u._storedError;return Oo(n,t)})}return Oo(n,t)}function Ki(e,t){return Qt(e,t),m(void 0)}function Ji(e){let t=e._readable,n=e._transformStreamController,i=n._flushAlgorithm();return Bo(n),B(i,()=>{if(t._state==="errored")throw t._storedError;ut(t._readableStreamController)},u=>{throw Qt(e,u),t._storedError})}function es(e){return Xt(e,!1),e._backpressureChangePromise}function Kt(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Wo(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}a.ByteLengthQueuingStrategy=Ht,a.CountQueuingStrategy=Vt,a.ReadableByteStreamController=qe,a.ReadableStream=_e,a.ReadableStreamBYOBReader=nt,a.ReadableStreamBYOBRequest=et,a.ReadableStreamDefaultController=ze,a.ReadableStreamDefaultReader=Ze,a.TransformStream=Yt,a.TransformStreamDefaultController=ct,a.WritableStream=at,a.WritableStreamDefaultController=Le,a.WritableStreamDefaultWriter=it,Object.defineProperty(a,"__esModule",{value:!0})})}}),bs=sn({"node_modules/fetch-blob/streams.cjs"(){var r=65536;if(!globalThis.ReadableStream)try{let o=require("process"),{emitWarning:a}=o;try{o.emitWarning=()=>{},Object.assign(globalThis,require("stream/web")),o.emitWarning=a}catch(s){throw o.emitWarning=a,s}}catch{Object.assign(globalThis,ms())}try{let{Blob:o}=require("buffer");o&&!o.prototype.stream&&(o.prototype.stream=function(s){let l=0,c=this;return new ReadableStream({type:"bytes",async pull(b){let g=await c.slice(l,Math.min(c.size,l+r)).arrayBuffer();l+=g.byteLength,b.enqueue(new Uint8Array(g)),l===c.size&&b.close()}})})}catch{}}});async function*Qr(r,o=!0){for(let a of r)if("stream"in a)yield*a.stream();else if(ArrayBuffer.isView(a))if(o){let s=a.byteOffset,l=a.byteOffset+a.byteLength;for(;s!==l;){let c=Math.min(l-s,tn),b=a.buffer.slice(s,s+c);s+=b.byteLength,yield new Uint8Array(b)}}else yield a;else{let s=0,l=a;for(;s!==l.size;){let b=await l.slice(s,Math.min(l.size,s+tn)).arrayBuffer();s+=b.byteLength,yield new Uint8Array(b)}}}var ps,tn,Gr,rn,Ye,St=gt({"node_modules/fetch-blob/index.js"(){ps=M(bs()),tn=65536,Gr=class nn{#e=[];#t="";#r=0;#n="transparent";constructor(o=[],a={}){if(typeof o!="object"||o===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof o[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof a!="object"&&typeof a!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");a===null&&(a={});let s=new TextEncoder;for(let c of o){let b;ArrayBuffer.isView(c)?b=new Uint8Array(c.buffer.slice(c.byteOffset,c.byteOffset+c.byteLength)):c instanceof ArrayBuffer?b=new Uint8Array(c.slice(0)):c instanceof nn?b=c:b=s.encode(`${c}`),this.#r+=ArrayBuffer.isView(b)?b.byteLength:b.size,this.#e.push(b)}this.#n=`${a.endings===void 0?"transparent":a.endings}`;let l=a.type===void 0?"":String(a.type);this.#t=/^[\x20-\x7E]*$/.test(l)?l:""}get size(){return this.#r}get type(){return this.#t}async text(){let o=new TextDecoder,a="";for await(let s of Qr(this.#e,!1))a+=o.decode(s,{stream:!0});return a+=o.decode(),a}async arrayBuffer(){let o=new Uint8Array(this.size),a=0;for await(let s of Qr(this.#e,!1))o.set(s,a),a+=s.length;return o.buffer}stream(){let o=Qr(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(a){let s=await o.next();s.done?a.close():a.enqueue(s.value)},async cancel(){await o.return()}})}slice(o=0,a=this.size,s=""){let{size:l}=this,c=o<0?Math.max(l+o,0):Math.min(o,l),b=a<0?Math.max(l+a,0):Math.min(a,l),h=Math.max(b-c,0),g=this.#e,y=[],q=0;for(let D of g){if(q>=h)break;let S=ArrayBuffer.isView(D)?D.byteLength:D.size;if(c&&S<=c)c-=S,b-=S;else{let m;ArrayBuffer.isView(D)?(m=D.subarray(c,Math.min(S,b)),q+=m.byteLength):(m=D.slice(c,Math.min(S,b)),q+=m.size),b-=S,y.push(m),c=0}}let I=new nn([],{type:String(s).toLowerCase()});return I.#r=h,I.#e=y,I}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](o){return o&&typeof o=="object"&&typeof o.constructor=="function"&&(typeof o.stream=="function"||typeof o.arrayBuffer=="function")&&/^(Blob|File)$/.test(o[Symbol.toStringTag])}},Object.defineProperties(Gr.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),rn=Gr,Ye=rn}}),Lo,zo,wt,Ko=gt({"node_modules/fetch-blob/file.js"(){St(),Lo=class extends Ye{#e=0;#t="";constructor(o,a,s={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(o,s),s===null&&(s={});let l=s.lastModified===void 0?Date.now():Number(s.lastModified);Number.isNaN(l)||(this.#e=l),this.#t=String(a)}get name(){return this.#t}get lastModified(){return this.#e}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](o){return!!o&&o instanceof Ye&&/^(File)$/.test(o[Symbol.toStringTag])}},zo=Lo,wt=zo}});function ys(r,o=Ye){var a=`${on()}${on()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),s=[],l=`--${a}\r
Content-Disposition: form-data; name="`;return r.forEach((c,b)=>typeof c=="string"?s.push(l+or(b)+`"\r
\r
${c.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):s.push(l+or(b)+`"; filename="${or(c.name,1)}"\r
Content-Type: ${c.type||"application/octet-stream"}\r
\r
`,c,`\r
`)),s.push(`--${a}--`),new o(s,{type:"multipart/form-data; boundary="+a})}var xe,Io,$o,on,jo,Xr,or,ve,Qe,fr=gt({"node_modules/formdata-polyfill/esm.min.js"(){St(),Ko(),{toStringTag:xe,iterator:Io,hasInstance:$o}=Symbol,on=Math.random,jo="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),Xr=(r,o,a)=>(r+="",/^(Blob|File)$/.test(o&&o[xe])?[(a=a!==void 0?a+"":o[xe]=="File"?o.name:"blob",r),o.name!==a||o[xe]=="blob"?new wt([o],a,o):o]:[r,o+""]),or=(r,o)=>(o?r:r.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),ve=(r,o,a)=>{if(o.length<a)throw new TypeError(`Failed to execute '${r}' on 'FormData': ${a} arguments required, but only ${o.length} present.`)},Qe=class{#e=[];constructor(...o){if(o.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[xe](){return"FormData"}[Io](){return this.entries()}static[$o](o){return o&&typeof o=="object"&&o[xe]==="FormData"&&!jo.some(a=>typeof o[a]!="function")}append(...o){ve("append",arguments,2),this.#e.push(Xr(...o))}delete(o){ve("delete",arguments,1),o+="",this.#e=this.#e.filter(([a])=>a!==o)}get(o){ve("get",arguments,1),o+="";for(var a=this.#e,s=a.length,l=0;l<s;l++)if(a[l][0]===o)return a[l][1];return null}getAll(o,a){return ve("getAll",arguments,1),a=[],o+="",this.#e.forEach(s=>s[0]===o&&a.push(s[1])),a}has(o){return ve("has",arguments,1),o+="",this.#e.some(a=>a[0]===o)}forEach(o,a){ve("forEach",arguments,1);for(var[s,l]of this)o.call(a,l,s,this)}set(...o){ve("set",arguments,2);var a=[],s=!0;o=Xr(...o),this.#e.forEach(l=>{l[0]===o[0]?s&&(s=!a.push(o)):a.push(l)}),s&&a.push(o),this.#e=a}*entries(){yield*this.#e}*keys(){for(var[o]of this)yield o}*values(){for(var[,o]of this)yield o}}}}),_s=sn({"node_modules/node-domexception/index.js"(r,o){if(!globalThis.DOMException)try{let{MessageChannel:a}=require("worker_threads"),s=new a().port1,l=new ArrayBuffer;s.postMessage(l,[l,l])}catch(a){a.constructor.name==="DOMException"&&(globalThis.DOMException=a.constructor)}o.exports=globalThis.DOMException}}),ht,Mo,Uo,tr,Jo,ea,ta,ra,Zr,Kr,rr,na=gt({"node_modules/fetch-blob/from.js"(){ht=M(require("fs")),Mo=M(require("path")),Uo=M(_s()),Ko(),St(),{stat:tr}=ht.promises,Jo=(r,o)=>Zr((0,ht.statSync)(r),r,o),ea=(r,o)=>tr(r).then(a=>Zr(a,r,o)),ta=(r,o)=>tr(r).then(a=>Kr(a,r,o)),ra=(r,o)=>Kr((0,ht.statSync)(r),r,o),Zr=(r,o,a="")=>new Ye([new rr({path:o,size:r.size,lastModified:r.mtimeMs,start:0})],{type:a}),Kr=(r,o,a="")=>new wt([new rr({path:o,size:r.size,lastModified:r.mtimeMs,start:0})],(0,Mo.basename)(o),{type:a,lastModified:r.mtimeMs}),rr=class{#e;#t;constructor(r){this.#e=r.path,this.#t=r.start,this.size=r.size,this.lastModified=r.lastModified}slice(r,o){return new rr({path:this.#e,lastModified:this.lastModified,size:o-r,start:this.#t+r})}async*stream(){let{mtimeMs:r}=await tr(this.#e);if(r>this.lastModified)throw new Uo.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,ht.createReadStream)(this.#e,{start:this.#t,end:this.#t+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}}}),oa={};Zo(oa,{toFormData:()=>Ss});function gs(r){let o=r.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!o)return;let a=o[2]||o[3]||"",s=a.slice(a.lastIndexOf("\\")+1);return s=s.replace(/%22/g,'"'),s=s.replace(/&#(\d{4});/g,(l,c)=>String.fromCharCode(c)),s}async function Ss(r,o){if(!/multipart/i.test(o))throw new TypeError("Failed to fetch");let a=o.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!a)throw new TypeError("no or bad content-type header, no multipart boundary");let s=new aa(a[1]||a[2]),l,c,b,h,g,y,q=[],I=new Qe,D=v=>{b+=C.decode(v,{stream:!0})},S=v=>{q.push(v)},m=()=>{let v=new wt(q,y,{type:g});I.append(h,v)},p=()=>{I.append(h,b)},C=new TextDecoder("utf-8");C.decode(),s.onPartBegin=function(){s.onPartData=D,s.onPartEnd=p,l="",c="",b="",h="",g="",y=null,q.length=0},s.onHeaderField=function(v){l+=C.decode(v,{stream:!0})},s.onHeaderValue=function(v){c+=C.decode(v,{stream:!0})},s.onHeaderEnd=function(){if(c+=C.decode(),l=l.toLowerCase(),l==="content-disposition"){let v=c.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);v&&(h=v[2]||v[3]||""),y=gs(c),y&&(s.onPartData=S,s.onPartEnd=m)}else l==="content-type"&&(g=c);c="",l=""};for await(let v of r)s.write(v);return s.end(),I}var ee,k,Jr,de,mt,bt,xo,Ne,No,Ho,Vo,Yo,Re,aa,ws=gt({"node_modules/node-fetch/src/utils/multipart-parser.js"(){na(),fr(),ee=0,k={START_BOUNDARY:ee++,HEADER_FIELD_START:ee++,HEADER_FIELD:ee++,HEADER_VALUE_START:ee++,HEADER_VALUE:ee++,HEADER_VALUE_ALMOST_DONE:ee++,HEADERS_ALMOST_DONE:ee++,PART_DATA_START:ee++,PART_DATA:ee++,END:ee++},Jr=1,de={PART_BOUNDARY:Jr,LAST_BOUNDARY:Jr*=2},mt=10,bt=13,xo=32,Ne=45,No=58,Ho=97,Vo=122,Yo=r=>r|32,Re=()=>{},aa=class{constructor(r){this.index=0,this.flags=0,this.onHeaderEnd=Re,this.onHeaderField=Re,this.onHeadersEnd=Re,this.onHeaderValue=Re,this.onPartBegin=Re,this.onPartData=Re,this.onPartEnd=Re,this.boundaryChars={},r=`\r
--`+r;let o=new Uint8Array(r.length);for(let a=0;a<r.length;a++)o[a]=r.charCodeAt(a),this.boundaryChars[o[a]]=!0;this.boundary=o,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=k.START_BOUNDARY}write(r){let o=0,a=r.length,s=this.index,{lookbehind:l,boundary:c,boundaryChars:b,index:h,state:g,flags:y}=this,q=this.boundary.length,I=q-1,D=r.length,S,m,p=j=>{this[j+"Mark"]=o},C=j=>{delete this[j+"Mark"]},v=(j,B,W,F)=>{(B===void 0||B!==W)&&this[j](F&&F.subarray(B,W))},$=(j,B)=>{let W=j+"Mark";W in this&&(B?(v(j,this[W],o,r),delete this[W]):(v(j,this[W],r.length,r),this[W]=0))};for(o=0;o<a;o++)switch(S=r[o],g){case k.START_BOUNDARY:if(h===c.length-2){if(S===Ne)y|=de.LAST_BOUNDARY;else if(S!==bt)return;h++;break}else if(h-1===c.length-2){if(y&de.LAST_BOUNDARY&&S===Ne)g=k.END,y=0;else if(!(y&de.LAST_BOUNDARY)&&S===mt)h=0,v("onPartBegin"),g=k.HEADER_FIELD_START;else return;break}S!==c[h+2]&&(h=-2),S===c[h+2]&&h++;break;case k.HEADER_FIELD_START:g=k.HEADER_FIELD,p("onHeaderField"),h=0;case k.HEADER_FIELD:if(S===bt){C("onHeaderField"),g=k.HEADERS_ALMOST_DONE;break}if(h++,S===Ne)break;if(S===No){if(h===1)return;$("onHeaderField",!0),g=k.HEADER_VALUE_START;break}if(m=Yo(S),m<Ho||m>Vo)return;break;case k.HEADER_VALUE_START:if(S===xo)break;p("onHeaderValue"),g=k.HEADER_VALUE;case k.HEADER_VALUE:S===bt&&($("onHeaderValue",!0),v("onHeaderEnd"),g=k.HEADER_VALUE_ALMOST_DONE);break;case k.HEADER_VALUE_ALMOST_DONE:if(S!==mt)return;g=k.HEADER_FIELD_START;break;case k.HEADERS_ALMOST_DONE:if(S!==mt)return;v("onHeadersEnd"),g=k.PART_DATA_START;break;case k.PART_DATA_START:g=k.PART_DATA,p("onPartData");case k.PART_DATA:if(s=h,h===0){for(o+=I;o<D&&!(r[o]in b);)o+=q;o-=I,S=r[o]}if(h<c.length)c[h]===S?(h===0&&$("onPartData",!0),h++):h=0;else if(h===c.length)h++,S===bt?y|=de.PART_BOUNDARY:S===Ne?y|=de.LAST_BOUNDARY:h=0;else if(h-1===c.length)if(y&de.PART_BOUNDARY){if(h=0,S===mt){y&=~de.PART_BOUNDARY,v("onPartEnd"),v("onPartBegin"),g=k.HEADER_FIELD_START;break}}else y&de.LAST_BOUNDARY&&S===Ne?(v("onPartEnd"),g=k.END,y=0):h=0;if(h>0)l[h-1]=S;else if(s>0){let j=new Uint8Array(l.buffer,l.byteOffset,l.byteLength);v("onPartData",0,s,j),s=0,p("onPartData"),o--}break;case k.END:break;default:throw new Error(`Unexpected state entered: ${g}`)}$("onHeaderField"),$("onHeaderValue"),$("onPartData"),this.index=h,this.state=g,this.flags=y}end(){if(this.state===k.HEADER_FIELD_START&&this.index===0||this.state===k.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==k.END)throw new Error("MultipartParser.end(): stream ended unexpectedly")}}}});Zo(ca,{AbortError:()=>ua,Blob:()=>rn,FetchError:()=>re,File:()=>wt,FormData:()=>Qe,Headers:()=>he,Request:()=>_t,Response:()=>V,blobFrom:()=>ea,blobFromSync:()=>Jo,default:()=>fa,fileFrom:()=>ta,fileFromSync:()=>ra,isRedirect:()=>un});var vs=M(require("http")),Rs=M(require("https")),He=M(require("zlib")),oe=M(require("stream")),nr=M(require("buffer"));function Ts(r){if(!/^data:/i.test(r))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');r=r.replace(/\r?\n/g,"");let o=r.indexOf(",");if(o===-1||o<=4)throw new TypeError("malformed data: URI");let a=r.substring(5,o).split(";"),s="",l=!1,c=a[0]||"text/plain",b=c;for(let q=1;q<a.length;q++)a[q]==="base64"?l=!0:a[q]&&(b+=`;${a[q]}`,a[q].indexOf("charset=")===0&&(s=a[q].substring(8)));!a[0]&&!s.length&&(b+=";charset=US-ASCII",s="US-ASCII");let h=l?"base64":"ascii",g=unescape(r.substring(o+1)),y=Buffer.from(g,h);return y.type=c,y.typeFull=b,y.charset=s,y}var Es=Ts,ae=M(require("stream")),Ge=M(require("util")),Q=M(require("buffer"));St();fr();var cr=class extends Error{constructor(r,o){super(r),Error.captureStackTrace(this,this.constructor),this.type=o}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},re=class extends cr{constructor(r,o,a){super(r,o),a&&(this.code=this.errno=a.code,this.erroredSysCall=a.syscall)}},ir=Symbol.toStringTag,ia=r=>typeof r=="object"&&typeof r.append=="function"&&typeof r.delete=="function"&&typeof r.get=="function"&&typeof r.getAll=="function"&&typeof r.has=="function"&&typeof r.set=="function"&&typeof r.sort=="function"&&r[ir]==="URLSearchParams",sr=r=>r&&typeof r=="object"&&typeof r.arrayBuffer=="function"&&typeof r.type=="string"&&typeof r.stream=="function"&&typeof r.constructor=="function"&&/^(Blob|File)$/.test(r[ir]),Cs=r=>typeof r=="object"&&(r[ir]==="AbortSignal"||r[ir]==="EventTarget"),Ps=(r,o)=>{let a=new URL(o).hostname,s=new URL(r).hostname;return a===s||a.endsWith(`.${s}`)},As=(r,o)=>{let a=new URL(o).protocol,s=new URL(r).protocol;return a===s},Bs=(0,Ge.promisify)(ae.default.pipeline),N=Symbol("Body internals"),yt=class{constructor(r,{size:o=0}={}){let a=null;r===null?r=null:ia(r)?r=Q.Buffer.from(r.toString()):sr(r)||Q.Buffer.isBuffer(r)||(Ge.types.isAnyArrayBuffer(r)?r=Q.Buffer.from(r):ArrayBuffer.isView(r)?r=Q.Buffer.from(r.buffer,r.byteOffset,r.byteLength):r instanceof ae.default||(r instanceof Qe?(r=ys(r),a=r.type.split("=")[1]):r=Q.Buffer.from(String(r))));let s=r;Q.Buffer.isBuffer(r)?s=ae.default.Readable.from(r):sr(r)&&(s=ae.default.Readable.from(r.stream())),this[N]={body:r,stream:s,boundary:a,disturbed:!1,error:null},this.size=o,r instanceof ae.default&&r.on("error",l=>{let c=l instanceof cr?l:new re(`Invalid response body while trying to fetch ${this.url}: ${l.message}`,"system",l);this[N].error=c})}get body(){return this[N].stream}get bodyUsed(){return this[N].disturbed}async arrayBuffer(){let{buffer:r,byteOffset:o,byteLength:a}=await en(this);return r.slice(o,o+a)}async formData(){let r=this.headers.get("content-type");if(r.startsWith("application/x-www-form-urlencoded")){let a=new Qe,s=new URLSearchParams(await this.text());for(let[l,c]of s)a.append(l,c);return a}let{toFormData:o}=await Promise.resolve().then(()=>(ws(),oa));return o(this.body,r)}async blob(){let r=this.headers&&this.headers.get("content-type")||this[N].body&&this[N].body.type||"",o=await this.arrayBuffer();return new Ye([o],{type:r})}async json(){let r=await this.text();return JSON.parse(r)}async text(){let r=await en(this);return new TextDecoder().decode(r)}buffer(){return en(this)}};yt.prototype.buffer=(0,Ge.deprecate)(yt.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer");Object.defineProperties(yt.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,Ge.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function en(r){if(r[N].disturbed)throw new TypeError(`body used already for: ${r.url}`);if(r[N].disturbed=!0,r[N].error)throw r[N].error;let{body:o}=r;if(o===null||!(o instanceof ae.default))return Q.Buffer.alloc(0);let a=[],s=0;try{for await(let l of o){if(r.size>0&&s+l.length>r.size){let c=new re(`content size at ${r.url} over limit: ${r.size}`,"max-size");throw o.destroy(c),c}s+=l.length,a.push(l)}}catch(l){throw l instanceof cr?l:new re(`Invalid response body while trying to fetch ${r.url}: ${l.message}`,"system",l)}if(o.readableEnded===!0||o._readableState.ended===!0)try{return a.every(l=>typeof l=="string")?Q.Buffer.from(a.join("")):Q.Buffer.concat(a,s)}catch(l){throw new re(`Could not create Buffer from response body for ${r.url}: ${l.message}`,"system",l)}else throw new re(`Premature close of server response while trying to fetch ${r.url}`)}var ln=(r,o)=>{let a,s,{body:l}=r[N];if(r.bodyUsed)throw new Error("cannot clone body after it is used");return l instanceof ae.default&&typeof l.getBoundary!="function"&&(a=new ae.PassThrough({highWaterMark:o}),s=new ae.PassThrough({highWaterMark:o}),l.pipe(a),l.pipe(s),r[N].stream=a,l=s),l},ks=(0,Ge.deprecate)(r=>r.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),sa=(r,o)=>r===null?null:typeof r=="string"?"text/plain;charset=UTF-8":ia(r)?"application/x-www-form-urlencoded;charset=UTF-8":sr(r)?r.type||null:Q.Buffer.isBuffer(r)||Ge.types.isAnyArrayBuffer(r)||ArrayBuffer.isView(r)?null:r instanceof Qe?`multipart/form-data; boundary=${o[N].boundary}`:r&&typeof r.getBoundary=="function"?`multipart/form-data;boundary=${ks(r)}`:r instanceof ae.default?null:"text/plain;charset=UTF-8",Os=r=>{let{body:o}=r[N];return o===null?0:sr(o)?o.size:Q.Buffer.isBuffer(o)?o.length:o&&typeof o.getLengthSync=="function"&&o.hasKnownLength&&o.hasKnownLength()?o.getLengthSync():null},Ws=async(r,{body:o})=>{o===null?r.end():await Bs(o,r)},Qo=M(require("util")),lr=M(require("http")),ar=typeof lr.default.validateHeaderName=="function"?lr.default.validateHeaderName:r=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(r)){let o=new TypeError(`Header name must be a valid HTTP token [${r}]`);throw Object.defineProperty(o,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),o}},an=typeof lr.default.validateHeaderValue=="function"?lr.default.validateHeaderValue:(r,o)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(o)){let a=new TypeError(`Invalid character in header content ["${r}"]`);throw Object.defineProperty(a,"code",{value:"ERR_INVALID_CHAR"}),a}},he=class extends URLSearchParams{constructor(r){let o=[];if(r instanceof he){let a=r.raw();for(let[s,l]of Object.entries(a))o.push(...l.map(c=>[s,c]))}else if(r!=null)if(typeof r=="object"&&!Qo.types.isBoxedPrimitive(r)){let a=r[Symbol.iterator];if(a==null)o.push(...Object.entries(r));else{if(typeof a!="function")throw new TypeError("Header pairs must be iterable");o=[...r].map(s=>{if(typeof s!="object"||Qo.types.isBoxedPrimitive(s))throw new TypeError("Each header pair must be an iterable object");return[...s]}).map(s=>{if(s.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...s]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return o=o.length>0?o.map(([a,s])=>(ar(a),an(a,String(s)),[String(a).toLowerCase(),String(s)])):void 0,super(o),new Proxy(this,{get(a,s,l){switch(s){case"append":case"set":return(c,b)=>(ar(c),an(c,String(b)),URLSearchParams.prototype[s].call(a,String(c).toLowerCase(),String(b)));case"delete":case"has":case"getAll":return c=>(ar(c),URLSearchParams.prototype[s].call(a,String(c).toLowerCase()));case"keys":return()=>(a.sort(),new Set(URLSearchParams.prototype.keys.call(a)).keys());default:return Reflect.get(a,s,l)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(r){let o=this.getAll(r);if(o.length===0)return null;let a=o.join(", ");return/^content-encoding$/i.test(r)&&(a=a.toLowerCase()),a}forEach(r,o=void 0){for(let a of this.keys())Reflect.apply(r,o,[this.get(a),a,this])}*values(){for(let r of this.keys())yield this.get(r)}*entries(){for(let r of this.keys())yield[r,this.get(r)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((r,o)=>(r[o]=this.getAll(o),r),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((r,o)=>{let a=this.getAll(o);return o==="host"?r[o]=a[0]:r[o]=a.length>1?a:a[0],r},{})}};Object.defineProperties(he.prototype,["get","entries","forEach","values"].reduce((r,o)=>(r[o]={enumerable:!0},r),{}));function qs(r=[]){return new he(r.reduce((o,a,s,l)=>(s%2===0&&o.push(l.slice(s,s+2)),o),[]).filter(([o,a])=>{try{return ar(o),an(o,String(a)),!0}catch{return!1}}))}var Ds=new Set([301,302,303,307,308]),un=r=>Ds.has(r),te=Symbol("Response internals"),V=class extends yt{constructor(r=null,o={}){super(r,o);let a=o.status!=null?o.status:200,s=new he(o.headers);if(r!==null&&!s.has("Content-Type")){let l=sa(r,this);l&&s.append("Content-Type",l)}this[te]={type:"default",url:o.url,status:a,statusText:o.statusText||"",headers:s,counter:o.counter,highWaterMark:o.highWaterMark}}get type(){return this[te].type}get url(){return this[te].url||""}get status(){return this[te].status}get ok(){return this[te].status>=200&&this[te].status<300}get redirected(){return this[te].counter>0}get statusText(){return this[te].statusText}get headers(){return this[te].headers}get highWaterMark(){return this[te].highWaterMark}clone(){return new V(ln(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(r,o=302){if(!un(o))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new V(null,{headers:{location:new URL(r).toString()},status:o})}static error(){let r=new V(null,{status:0,statusText:""});return r[te].type="error",r}static json(r=void 0,o={}){let a=JSON.stringify(r);if(a===void 0)throw new TypeError("data is not JSON serializable");let s=new he(o&&o.headers);return s.has("content-type")||s.set("content-type","application/json"),new V(a,{...o,headers:s})}get[Symbol.toStringTag](){return"Response"}};Object.defineProperties(V.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var Fs=M(require("url")),Ls=M(require("util")),zs=r=>{if(r.search)return r.search;let o=r.href.length-1,a=r.hash||(r.href[o]==="#"?"#":"");return r.href[o-a.length]==="?"?"?":""},Is=M(require("net"));function Go(r,o=!1){return r==null||(r=new URL(r),/^(about|blob|data):$/.test(r.protocol))?"no-referrer":(r.username="",r.password="",r.hash="",o&&(r.pathname="",r.search=""),r)}var la=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),$s="strict-origin-when-cross-origin";function js(r){if(!la.has(r))throw new TypeError(`Invalid referrerPolicy: ${r}`);return r}function Ms(r){if(/^(http|ws)s:$/.test(r.protocol))return!0;let o=r.host.replace(/(^\[)|(]$)/g,""),a=(0,Is.isIP)(o);return a===4&&/^127\./.test(o)||a===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(o)?!0:r.host==="localhost"||r.host.endsWith(".localhost")?!1:r.protocol==="file:"}function Ve(r){return/^about:(blank|srcdoc)$/.test(r)||r.protocol==="data:"||/^(blob|filesystem):$/.test(r.protocol)?!0:Ms(r)}function Us(r,{referrerURLCallback:o,referrerOriginCallback:a}={}){if(r.referrer==="no-referrer"||r.referrerPolicy==="")return null;let s=r.referrerPolicy;if(r.referrer==="about:client")return"no-referrer";let l=r.referrer,c=Go(l),b=Go(l,!0);c.toString().length>4096&&(c=b),o&&(c=o(c)),a&&(b=a(b));let h=new URL(r.url);switch(s){case"no-referrer":return"no-referrer";case"origin":return b;case"unsafe-url":return c;case"strict-origin":return Ve(c)&&!Ve(h)?"no-referrer":b.toString();case"strict-origin-when-cross-origin":return c.origin===h.origin?c:Ve(c)&&!Ve(h)?"no-referrer":b;case"same-origin":return c.origin===h.origin?c:"no-referrer";case"origin-when-cross-origin":return c.origin===h.origin?c:b;case"no-referrer-when-downgrade":return Ve(c)&&!Ve(h)?"no-referrer":c;default:throw new TypeError(`Invalid referrerPolicy: ${s}`)}}function xs(r){let o=(r.get("referrer-policy")||"").split(/[,\s]+/),a="";for(let s of o)s&&la.has(s)&&(a=s);return a}var z=Symbol("Request internals"),pt=r=>typeof r=="object"&&typeof r[z]=="object",Ns=(0,Ls.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),_t=class extends yt{constructor(r,o={}){let a;if(pt(r)?a=new URL(r.url):(a=new URL(r),r={}),a.username!==""||a.password!=="")throw new TypeError(`${a} is an url with embedded credentials.`);let s=o.method||r.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(s)&&(s=s.toUpperCase()),!pt(o)&&"data"in o&&Ns(),(o.body!=null||pt(r)&&r.body!==null)&&(s==="GET"||s==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let l=o.body?o.body:pt(r)&&r.body!==null?ln(r):null;super(l,{size:o.size||r.size||0});let c=new he(o.headers||r.headers||{});if(l!==null&&!c.has("Content-Type")){let g=sa(l,this);g&&c.set("Content-Type",g)}let b=pt(r)?r.signal:null;if("signal"in o&&(b=o.signal),b!=null&&!Cs(b))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let h=o.referrer==null?r.referrer:o.referrer;if(h==="")h="no-referrer";else if(h){let g=new URL(h);h=/^about:(\/\/)?client$/.test(g)?"client":g}else h=void 0;this[z]={method:s,redirect:o.redirect||r.redirect||"follow",headers:c,parsedURL:a,signal:b,referrer:h},this.follow=o.follow===void 0?r.follow===void 0?20:r.follow:o.follow,this.compress=o.compress===void 0?r.compress===void 0?!0:r.compress:o.compress,this.counter=o.counter||r.counter||0,this.agent=o.agent||r.agent,this.highWaterMark=o.highWaterMark||r.highWaterMark||16384,this.insecureHTTPParser=o.insecureHTTPParser||r.insecureHTTPParser||!1,this.referrerPolicy=o.referrerPolicy||r.referrerPolicy||""}get method(){return this[z].method}get url(){return(0,Fs.format)(this[z].parsedURL)}get headers(){return this[z].headers}get redirect(){return this[z].redirect}get signal(){return this[z].signal}get referrer(){if(this[z].referrer==="no-referrer")return"";if(this[z].referrer==="client")return"about:client";if(this[z].referrer)return this[z].referrer.toString()}get referrerPolicy(){return this[z].referrerPolicy}set referrerPolicy(r){this[z].referrerPolicy=js(r)}clone(){return new _t(this)}get[Symbol.toStringTag](){return"Request"}};Object.defineProperties(_t.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});var Hs=r=>{let{parsedURL:o}=r[z],a=new he(r[z].headers);a.has("Accept")||a.set("Accept","*/*");let s=null;if(r.body===null&&/^(post|put)$/i.test(r.method)&&(s="0"),r.body!==null){let h=Os(r);typeof h=="number"&&!Number.isNaN(h)&&(s=String(h))}s&&a.set("Content-Length",s),r.referrerPolicy===""&&(r.referrerPolicy=$s),r.referrer&&r.referrer!=="no-referrer"?r[z].referrer=Us(r):r[z].referrer="no-referrer",r[z].referrer instanceof URL&&a.set("Referer",r.referrer),a.has("User-Agent")||a.set("User-Agent","node-fetch"),r.compress&&!a.has("Accept-Encoding")&&a.set("Accept-Encoding","gzip, deflate, br");let{agent:l}=r;typeof l=="function"&&(l=l(o));let c=zs(o),b={path:o.pathname+c,method:r.method,headers:a[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:r.insecureHTTPParser,agent:l};return{parsedURL:o,options:b}},ua=class extends cr{constructor(r,o="aborted"){super(r,o)}};fr();na();var Vs=new Set(["data:","http:","https:"]);async function fa(r,o){return new Promise((a,s)=>{let l=new _t(r,o),{parsedURL:c,options:b}=Hs(l);if(!Vs.has(c.protocol))throw new TypeError(`node-fetch cannot load ${r}. URL scheme "${c.protocol.replace(/:$/,"")}" is not supported.`);if(c.protocol==="data:"){let m=Es(l.url),p=new V(m,{headers:{"Content-Type":m.typeFull}});a(p);return}let h=(c.protocol==="https:"?Rs.default:vs.default).request,{signal:g}=l,y=null,q=()=>{let m=new ua("The operation was aborted.");s(m),l.body&&l.body instanceof oe.default.Readable&&l.body.destroy(m),!(!y||!y.body)&&y.body.emit("error",m)};if(g&&g.aborted){q();return}let I=()=>{q(),S()},D=h(c.toString(),b);g&&g.addEventListener("abort",I);let S=()=>{D.abort(),g&&g.removeEventListener("abort",I)};D.on("error",m=>{s(new re(`request to ${l.url} failed, reason: ${m.message}`,"system",m)),S()}),Ys(D,m=>{y&&y.body&&y.body.destroy(m)}),process.version<"v14"&&D.on("socket",m=>{let p;m.prependListener("end",()=>{p=m._eventsCount}),m.prependListener("close",C=>{if(y&&p<m._eventsCount&&!C){let v=new Error("Premature close");v.code="ERR_STREAM_PREMATURE_CLOSE",y.body.emit("error",v)}})}),D.on("response",m=>{D.setTimeout(0);let p=qs(m.rawHeaders);if(un(m.statusCode)){let B=p.get("Location"),W=null;try{W=B===null?null:new URL(B,l.url)}catch{if(l.redirect!=="manual"){s(new re(`uri requested responds with an invalid redirect URL: ${B}`,"invalid-redirect")),S();return}}switch(l.redirect){case"error":s(new re(`uri requested responds with a redirect, redirect mode is set to error: ${l.url}`,"no-redirect")),S();return;case"manual":break;case"follow":{if(W===null)break;if(l.counter>=l.follow){s(new re(`maximum redirect reached at: ${l.url}`,"max-redirect")),S();return}let F={headers:new he(l.headers),follow:l.follow,counter:l.counter+1,agent:l.agent,compress:l.compress,method:l.method,body:ln(l),signal:l.signal,size:l.size,referrer:l.referrer,referrerPolicy:l.referrerPolicy};if(!Ps(l.url,W)||!As(l.url,W))for(let ie of["authorization","www-authenticate","cookie","cookie2"])F.headers.delete(ie);if(m.statusCode!==303&&l.body&&o.body instanceof oe.default.Readable){s(new re("Cannot follow redirect with body being a readable stream","unsupported-redirect")),S();return}(m.statusCode===303||(m.statusCode===301||m.statusCode===302)&&l.method==="POST")&&(F.method="GET",F.body=void 0,F.headers.delete("content-length"));let Te=xs(p);Te&&(F.referrerPolicy=Te),a(fa(new _t(W,F))),S();return}default:return s(new TypeError(`Redirect option '${l.redirect}' is not a valid value of RequestRedirect`))}}g&&m.once("end",()=>{g.removeEventListener("abort",I)});let C=(0,oe.pipeline)(m,new oe.PassThrough,B=>{B&&s(B)});process.version<"v12.10"&&m.on("aborted",I);let v={url:l.url,status:m.statusCode,statusText:m.statusMessage,headers:p,size:l.size,counter:l.counter,highWaterMark:l.highWaterMark},$=p.get("Content-Encoding");if(!l.compress||l.method==="HEAD"||$===null||m.statusCode===204||m.statusCode===304){y=new V(C,v),a(y);return}let j={flush:He.default.Z_SYNC_FLUSH,finishFlush:He.default.Z_SYNC_FLUSH};if($==="gzip"||$==="x-gzip"){C=(0,oe.pipeline)(C,He.default.createGunzip(j),B=>{B&&s(B)}),y=new V(C,v),a(y);return}if($==="deflate"||$==="x-deflate"){let B=(0,oe.pipeline)(m,new oe.PassThrough,W=>{W&&s(W)});B.once("data",W=>{(W[0]&15)===8?C=(0,oe.pipeline)(C,He.default.createInflate(),F=>{F&&s(F)}):C=(0,oe.pipeline)(C,He.default.createInflateRaw(),F=>{F&&s(F)}),y=new V(C,v),a(y)}),B.once("end",()=>{y||(y=new V(C,v),a(y))});return}if($==="br"){C=(0,oe.pipeline)(C,He.default.createBrotliDecompress(),B=>{B&&s(B)}),y=new V(C,v),a(y);return}y=new V(C,v),a(y)}),Ws(D,l).catch(s)})}function Ys(r,o){let a=nr.Buffer.from(`0\r
\r
`),s=!1,l=!1,c;r.on("response",b=>{let{headers:h}=b;s=h["transfer-encoding"]==="chunked"&&!h["content-length"]}),r.on("socket",b=>{let h=()=>{if(s&&!l){let y=new Error("Premature close");y.code="ERR_STREAM_PREMATURE_CLOSE",o(y)}},g=y=>{l=nr.Buffer.compare(y.slice(-5),a)===0,!l&&c&&(l=nr.Buffer.compare(c.slice(-3),a.slice(0,3))===0&&nr.Buffer.compare(y.slice(-2),a.slice(3))===0),c=y};b.prependListener("close",h),b.on("data",g),r.on("close",()=>{b.removeListener("close",h),b.removeListener("data",g)})})}St();fr();});var cn=dt((vl,ma)=>{"use strict";var ha=require("fs"),fn;function Qs(){try{return ha.statSync("/.dockerenv"),!0}catch{return!1}}function Gs(){try{return ha.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}ma.exports=()=>(fn===void 0&&(fn=Qs()||Gs()),fn)});var ya=dt((Rl,dn)=>{"use strict";var Xs=require("os"),Zs=require("fs"),ba=cn(),pa=()=>{if(process.platform!=="linux")return!1;if(Xs.release().toLowerCase().includes("microsoft"))return!ba();try{return Zs.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!ba():!1}catch{return!1}};process.env.__IS_WSL_TEST__?dn.exports=pa:dn.exports=pa()});var ga=dt((Tl,_a)=>{"use strict";_a.exports=(r,o,a)=>{let s=l=>Object.defineProperty(r,o,{value:l,enumerable:!0,writable:!0});return Object.defineProperty(r,o,{configurable:!0,enumerable:!0,get(){let l=a();return s(l),l},set(l){s(l)}}),r}});var Ca=dt((El,Ea)=>{var Ks=require("path"),Js=require("child_process"),{promises:hr,constants:Ta}=require("fs"),dr=ya(),el=cn(),mn=ga(),Sa=Ks.join(__dirname,"xdg-open"),{platform:Xe,arch:wa}=process,tl=()=>{try{return hr.statSync("/run/.containerenv"),!0}catch{return!1}},hn;function rl(){return hn===void 0&&(hn=tl()||el()),hn}var nl=(()=>{let r="/mnt/",o;return async function(){if(o)return o;let a="/etc/wsl.conf",s=!1;try{await hr.access(a,Ta.F_OK),s=!0}catch{}if(!s)return r;let l=await hr.readFile(a,{encoding:"utf8"}),c=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(l);return c?(o=c.groups.mountPoint.trim(),o=o.endsWith("/")?o:`${o}/`,o):r}})(),va=async(r,o)=>{let a;for(let s of r)try{return await o(s)}catch(l){a=l}throw a},mr=async r=>{if(r={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...r},Array.isArray(r.app))return va(r.app,h=>mr({...r,app:h}));let{name:o,arguments:a=[]}=r.app||{};if(a=[...a],Array.isArray(o))return va(o,h=>mr({...r,app:{name:h,arguments:a}}));let s,l=[],c={};if(Xe==="darwin")s="open",r.wait&&l.push("--wait-apps"),r.background&&l.push("--background"),r.newInstance&&l.push("--new"),o&&l.push("-a",o);else if(Xe==="win32"||dr&&!rl()&&!o){let h=await nl();s=dr?`${h}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,l.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),dr||(c.windowsVerbatimArguments=!0);let g=["Start"];r.wait&&g.push("-Wait"),o?(g.push(`"\`"${o}\`""`,"-ArgumentList"),r.target&&a.unshift(r.target)):r.target&&g.push(`"${r.target}"`),a.length>0&&(a=a.map(y=>`"\`"${y}\`""`),g.push(a.join(","))),r.target=Buffer.from(g.join(" "),"utf16le").toString("base64")}else{if(o)s=o;else{let h=!__dirname||__dirname==="/",g=!1;try{await hr.access(Sa,Ta.X_OK),g=!0}catch{}s=process.versions.electron||Xe==="android"||h||!g?"xdg-open":Sa}a.length>0&&l.push(...a),r.wait||(c.stdio="ignore",c.detached=!0)}r.target&&l.push(r.target),Xe==="darwin"&&a.length>0&&l.push("--args",...a);let b=Js.spawn(s,l,c);return r.wait?new Promise((h,g)=>{b.once("error",g),b.once("close",y=>{if(!r.allowNonzeroExitCode&&y>0){g(new Error(`Exited with code ${y}`));return}h(b)})}):(b.unref(),b)},bn=(r,o)=>{if(typeof r!="string")throw new TypeError("Expected a `target`");return mr({...o,target:r})},ol=(r,o)=>{if(typeof r!="string")throw new TypeError("Expected a `name`");let{arguments:a=[]}=o||{};if(a!=null&&!Array.isArray(a))throw new TypeError("Expected `appArguments` as Array type");return mr({...o,app:{name:r,arguments:a}})};function Ra(r){if(typeof r=="string"||Array.isArray(r))return r;let{[wa]:o}=r;if(!o)throw new Error(`${wa} is not supported`);return o}function pn({[Xe]:r},{wsl:o}){if(o&&dr)return Ra(o);if(!r)throw new Error(`${Xe} is not supported`);return Ra(r)}var br={};mn(br,"chrome",()=>pn({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));mn(br,"firefox",()=>pn({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));mn(br,"edge",()=>pn({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));bn.apps=br;bn.openApp=ol;Ea.exports=bn});var sl={};is(sl,{HTTPError:()=>me,openBrowser:()=>il,request:()=>al});module.exports=ss(sl);var Pa=require("@oclif/core"),Aa=Fo(da()),Ba=Fo(Ca());async function al(r,o){let a;try{a=await(0,Aa.default)(r,{method:o.method||"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...o.token?{Authorization:`Bearer ${o.token}`}:void 0},body:o.body})}catch(s){throw new Error(`HTTP request: ${s.message}`)}if(!a.ok){switch(a.status){case 401:throw new me(a,"not authorized - please log in first using `npx ray login`");case 403:throw new me(a,"forbidden - you don't have permissions to perform the request");case 402:throw new me(a,"the limit of free commands has been reached")}let s=await a.text(),l;try{l=JSON.parse(s)}catch{throw new me(a,`HTTP error: ${a.status} - ${s}`)}throw Array.isArray(l.errors)&&l.errors.length>0?new me(a,`error: ${l.errors[0].status} - ${l.errors[0].title}`):new me(a,`HTTP error: ${a.status} - ${s}`)}return await a.json()}var me=class extends Error{constructor(o,a){let s=o.headers.get("X-Request-Id");s?super(`${a} (${o.url} RequestID: ${s})`):super(a),this.name="HTTPError"}};function il(r){(0,Ba.default)(r).catch(o=>{Pa.ux.error(new Error(`failed opening browser to URL ${r}: ${o.message}`),{exit:1})})}0&&(module.exports={HTTPError,openBrowser,request});
/*! Bundled license information:

node-fetch-cjs/dist/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/
