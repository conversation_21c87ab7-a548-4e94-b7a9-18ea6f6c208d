"use strict";var l=Object.defineProperty;var h=Object.getOwnPropertyDescriptor;var A=Object.getOwnPropertyNames;var T=Object.prototype.hasOwnProperty;var b=(e,t)=>{for(var o in t)l(e,o,{get:t[o],enumerable:!0})},k=(e,t,o,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of A(t))!T.call(e,s)&&s!==o&&l(e,s,{get:()=>t[s],enumerable:!(i=h(t,s))||i.enumerable});return e};var v=e=>k(l({},"__esModule",{value:!0}),e);var R={};b(R,{runEvalsRemotely:()=>N});module.exports=v(R);var f={production:{url:"https://www.raycast.com",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},staging:{url:"https://www.rayca.st",clientID:"KqAdcGhNfBKiUpfjD3nU3VNgh1t0iMPoX2hYEkN9d4I"},development:{url:"http://localhost:3000",clientID:"rOCD_LhnXFObSLu9v7788qOGu25FZDqEzGjet4Q45ZY"}}[process.env.API_ENV||"production"],C="e69bae0ec90f5e838555",r={};function p(e){switch(e){case"raycastApiURL":return process.env.RAY_APIURL||r.APIURL||f.url;case"raycastAccessToken":return process.env.RAY_TOKEN||r.Token||r.AccessToken||"";case"raycastClientId":return process.env.RAY_CLIENT_ID||r.ClientID||f.clientID;case"githubClientId":return process.env.RAY_GithubClientID||r.GithubClientID||C;case"githubAccessToken":return process.env.GITHUB_ACCESS_TOKEN||r.GithubAccessToken||"";case"flavorName":return process.env.RAY_Target?process.env.RAY_Target:typeof r.Target<"u"?r.Target:m(process.platform==="win32"?"x":"release")}}function m(e){switch(e){case"debug":return"debug";case"internal":return"internal";case"release":return"";case"x":return"x";case"x-internal":return"x-internal";case"x-development":return"x-development";case void 0:return p("flavorName")}}async function N(e,{apiEndpoint:t,...o},i){let s=p("raycastAccessToken"),a=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({packageJson:e,options:o})});if(!a.ok||a.body===null)throw await a.text();let u=a.body.getReader(),y=new TextDecoder("utf-8"),{done:g,value:d}=await u.read(),n="";for(;!g;){for(n+=y.decode(d,{stream:!0});n.includes(`

`);){let c=n.indexOf(`

`)+2,x=n.substring(0,c);n=n.substring(c),i(JSON.parse(x))}({done:g,value:d}=await u.read())}n&&(i(JSON.parse(n)),n="")}0&&(module.exports={runEvalsRemotely});
