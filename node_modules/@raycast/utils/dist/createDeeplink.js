"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDeeplink = exports.createExtensionDeeplink = exports.createScriptCommandDeeplink = exports.DeeplinkType = void 0;
const api_1 = require("@raycast/api");
const node_fs_1 = __importDefault(require("node:fs"));
const node_path_1 = __importDefault(require("node:path"));
var DeeplinkType;
(function (DeeplinkType) {
    /** A script command */
    DeeplinkType["ScriptCommand"] = "script-command";
    /** An extension command */
    DeeplinkType["Extension"] = "extension";
})(DeeplinkType || (exports.DeeplinkType = DeeplinkType = {}));
function getProtocol() {
    return api_1.environment.raycastVersion.includes("alpha") ? "raycastinternal://" : "raycast://";
}
function getOwnerOrAuthorName() {
    const packageJSON = JSON.parse(node_fs_1.default.readFileSync(node_path_1.default.join(api_1.environment.assetsPath, "..", "package.json"), "utf8"));
    return packageJSON.owner || packageJSON.author;
}
function createScriptCommandDeeplink(options) {
    let url = `${getProtocol()}script-commands/${options.command}`;
    if (options.arguments) {
        let params = "";
        for (const arg of options.arguments) {
            params += "&arguments=" + encodeURIComponent(arg);
        }
        url += "?" + params.substring(1);
    }
    return url;
}
exports.createScriptCommandDeeplink = createScriptCommandDeeplink;
function createExtensionDeeplink(options) {
    let ownerOrAuthorName = getOwnerOrAuthorName();
    let extensionName = api_1.environment.extensionName;
    if ("ownerOrAuthorName" in options && "extensionName" in options) {
        ownerOrAuthorName = options.ownerOrAuthorName;
        extensionName = options.extensionName;
    }
    let url = `${getProtocol()}extensions/${ownerOrAuthorName}/${extensionName}/${options.command}`;
    let params = "";
    if (options.launchType) {
        params += "&launchType=" + encodeURIComponent(options.launchType);
    }
    if (options.arguments) {
        params += "&arguments=" + encodeURIComponent(JSON.stringify(options.arguments));
    }
    if (options.context) {
        params += "&context=" + encodeURIComponent(JSON.stringify(options.context));
    }
    if (options.fallbackText) {
        params += "&fallbackText=" + encodeURIComponent(options.fallbackText);
    }
    if (params) {
        url += "?" + params.substring(1);
    }
    return url;
}
exports.createExtensionDeeplink = createExtensionDeeplink;
/**
 * Creates a deeplink to a script command or extension.
 */
function createDeeplink(options) {
    if (options.type === DeeplinkType.ScriptCommand) {
        return createScriptCommandDeeplink(options);
    }
    else {
        return createExtensionDeeplink(options);
    }
}
exports.createDeeplink = createDeeplink;
