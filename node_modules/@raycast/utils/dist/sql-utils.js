"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.baseExecuteSQL = exports.isPermissionError = exports.PermissionError = void 0;
const node_fs_1 = require("node:fs");
const promises_1 = require("node:fs/promises");
const node_os_1 = __importDefault(require("node:os"));
const node_child_process_1 = __importDefault(require("node:child_process"));
const node_path_1 = __importDefault(require("node:path"));
const exec_utils_1 = require("./exec-utils");
const helpers_1 = require("./helpers");
class PermissionError extends Error {
    constructor(message) {
        super(message);
        this.name = "PermissionError";
    }
}
exports.PermissionError = PermissionError;
function isPermissionError(error) {
    return error instanceof Error && error.name === "PermissionError";
}
exports.isPermissionError = isPermissionError;
async function baseExecuteSQL(databasePath, query, options) {
    if (!(0, node_fs_1.existsSync)(databasePath)) {
        throw new Error("The database does not exist");
    }
    const abortSignal = options?.signal;
    let workaroundCopiedDb;
    let spawned = node_child_process_1.default.spawn("sqlite3", ["--json", "--readonly", databasePath, query], { signal: abortSignal });
    let spawnedPromise = (0, exec_utils_1.getSpawnedPromise)(spawned);
    let [{ error, exitCode, signal }, stdoutResult, stderrResult] = await (0, exec_utils_1.getSpawnedResult)(spawned, { encoding: "utf-8" }, spawnedPromise);
    checkAborted(abortSignal);
    if (stderrResult.match("(5)") || stderrResult.match("(14)")) {
        // That means that the DB is busy because of another app is locking it
        // This happens when Chrome or Arc is opened: they lock the History db.
        // As an ugly workaround, we duplicate the file and read that instead
        // (with vfs unix - none to just not care about locks)
        if (!workaroundCopiedDb) {
            const tempFolder = node_path_1.default.join(node_os_1.default.tmpdir(), "useSQL", (0, helpers_1.hash)(databasePath));
            await (0, promises_1.mkdir)(tempFolder, { recursive: true });
            checkAborted(abortSignal);
            workaroundCopiedDb = node_path_1.default.join(tempFolder, "db.db");
            await (0, promises_1.copyFile)(databasePath, workaroundCopiedDb);
            await (0, promises_1.writeFile)(workaroundCopiedDb + "-shm", "");
            await (0, promises_1.writeFile)(workaroundCopiedDb + "-wal", "");
            checkAborted(abortSignal);
        }
        spawned = node_child_process_1.default.spawn("sqlite3", ["--json", "--readonly", "--vfs", "unix-none", workaroundCopiedDb, query], {
            signal: abortSignal,
        });
        spawnedPromise = (0, exec_utils_1.getSpawnedPromise)(spawned);
        [{ error, exitCode, signal }, stdoutResult, stderrResult] = await (0, exec_utils_1.getSpawnedResult)(spawned, { encoding: "utf-8" }, spawnedPromise);
        checkAborted(abortSignal);
    }
    if (error || exitCode !== 0 || signal !== null) {
        if (stderrResult.includes("authorization denied")) {
            throw new PermissionError("You do not have permission to access the database.");
        }
        else {
            throw new Error(stderrResult || "Unknown error");
        }
    }
    return JSON.parse(stdoutResult.trim() || "[]");
}
exports.baseExecuteSQL = baseExecuteSQL;
function checkAborted(signal) {
    if (signal?.aborted) {
        const error = new Error("aborted");
        error.name = "AbortError";
        throw error;
    }
}
