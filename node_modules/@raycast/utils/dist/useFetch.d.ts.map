{"version": 3, "file": "useFetch.d.ts", "sourceRoot": "", "sources": ["../src/useFetch.ts"], "names": [], "mappings": "AACA,OAAO,EAAoB,oBAAoB,EAAE,MAAM,oBAAoB,CAAC;AAE5E,OAAO,EAA+D,0BAA0B,EAAE,MAAM,SAAS,CAAC;AAsBlH,KAAK,oBAAoB,GAAG,CAAC,UAAU,EAAE;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,QAAQ,CAAC,EAAE,GAAG,CAAC;IAAC,MAAM,CAAC,EAAE,GAAG,CAAA;CAAE,KAAK,WAAW,CAAC;AAExG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH,wBAAgB,QAAQ,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,SAAS,OAAO,EAAE,GAAG,OAAO,EAAE,EAClF,GAAG,EAAE,oBAAoB,EACzB,OAAO,EAAE,WAAW,GAAG;IACrB,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,OAAO,CAAC,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,GAAG,CAAA;KAAE,CAAC;IACvE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;CACpD,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,GACtG,0BAA0B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,wBAAgB,QAAQ,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,CAAC,EACxD,GAAG,EAAE,WAAW,EAChB,OAAO,CAAC,EAAE,WAAW,GAAG;IACtB,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,OAAO,CAAC,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,GAAG,CAAA;KAAE,CAAC;IACxE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;CACpD,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,GACtG,0BAA0B,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;IAAE,UAAU,EAAE,SAAS,CAAA;CAAE,CAAC"}