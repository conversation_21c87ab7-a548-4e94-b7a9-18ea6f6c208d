{"version": 3, "file": "usePromise.d.ts", "sourceRoot": "", "sources": ["../src/usePromise.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0B,gBAAgB,EAAoB,MAAM,OAAO,CAAC;AACnF,OAAO,EAA2B,KAAK,EAAE,MAAM,cAAc,CAAC;AAE9D,OAAO,EACL,wBAAwB,EAExB,oBAAoB,EAEpB,iCAAiC,EACjC,YAAY,EACZ,iBAAiB,EAClB,MAAM,SAAS,CAAC;AAIjB,MAAM,MAAM,cAAc,CAAC,CAAC,SAAS,wBAAwB,GAAG,iCAAiC,IAAI;IACnG;;OAEG;IACH,SAAS,CAAC,EAAE,gBAAgB,CAAC,eAAe,GAAG,IAAI,GAAG,SAAS,CAAC,CAAC;IACjE;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,GAAG,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC;IAC1F;;;OAGG;IACH,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACjD;;OAEG;IACH,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1G;;OAEG;IACH,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;CACrD,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCG;AACH,wBAAgB,UAAU,CAAC,CAAC,SAAS,iCAAiC,CAAC,EAAE,CAAC,EACxE,EAAE,EAAE,CAAC,GACJ,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,wBAAgB,UAAU,CAAC,CAAC,SAAS,iCAAiC,EACpE,EAAE,EAAE,CAAC,EACL,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EACnB,OAAO,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,GAC1B,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAEzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,wBAAgB,UAAU,CAAC,CAAC,SAAS,wBAAwB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AACjH,wBAAgB,UAAU,CAAC,CAAC,SAAS,wBAAwB,EAC3D,EAAE,EAAE,CAAC,EACL,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EACnB,OAAO,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,GAC1B,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC"}