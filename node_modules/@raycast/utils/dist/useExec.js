"use strict";
/*
 * Inspired by Execa
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useExec = void 0;
const node_child_process_1 = __importDefault(require("node:child_process"));
const react_1 = require("react");
const useCachedPromise_1 = require("./useCachedPromise");
const useLatest_1 = require("./useLatest");
const exec_utils_1 = require("./exec-utils");
const SPACES_REGEXP = / +/g;
function parseCommand(command, args) {
    if (args) {
        return [command, ...args];
    }
    const tokens = [];
    for (const token of command.trim().split(SPACES_REGEXP)) {
        // Allow spaces to be escaped by a backslash if not meant as a delimiter
        const previousToken = tokens[tokens.length - 1];
        if (previousToken && previousToken.endsWith("\\")) {
            // Merge previous token with current one
            tokens[tokens.length - 1] = `${previousToken.slice(0, -1)} ${token}`;
        }
        else {
            tokens.push(token);
        }
    }
    return tokens;
}
function useExec(command, optionsOrArgs, options) {
    const { parseOutput, input, onData, onWillExecute, initialData, execute, keepPreviousData, onError, failureToastOptions, ...execOptions } = Array.isArray(optionsOrArgs) ? options || {} : optionsOrArgs || {};
    const useCachedPromiseOptions = {
        initialData,
        execute,
        keepPreviousData,
        onError,
        onData,
        onWillExecute,
        failureToastOptions,
    };
    const abortable = (0, react_1.useRef)();
    const parseOutputRef = (0, useLatest_1.useLatest)(parseOutput || exec_utils_1.defaultParsing);
    const fn = (0, react_1.useCallback)(async (_command, _args, _options, input) => {
        const [file, ...args] = parseCommand(_command, _args);
        const command = [file, ...args].join(" ");
        const options = {
            stripFinalNewline: true,
            ..._options,
            timeout: _options?.timeout || 10000,
            signal: abortable.current?.signal,
            encoding: _options?.encoding === null ? "buffer" : _options?.encoding || "utf8",
            env: { PATH: "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin", ...process.env, ..._options?.env },
        };
        const spawned = node_child_process_1.default.spawn(file, args, options);
        const spawnedPromise = (0, exec_utils_1.getSpawnedPromise)(spawned, options);
        if (input) {
            spawned.stdin.end(input);
        }
        const [{ error, exitCode, signal, timedOut }, stdoutResult, stderrResult] = await (0, exec_utils_1.getSpawnedResult)(spawned, options, spawnedPromise);
        const stdout = (0, exec_utils_1.handleOutput)(options, stdoutResult);
        const stderr = (0, exec_utils_1.handleOutput)(options, stderrResult);
        return parseOutputRef.current({
            // @ts-expect-error too many generics, I give up
            stdout,
            // @ts-expect-error too many generics, I give up
            stderr,
            error,
            exitCode,
            signal,
            timedOut,
            command,
            options,
            parentError: new Error(),
        });
    }, [parseOutputRef]);
    // @ts-expect-error T can't be a Promise so it's actually the same
    return (0, useCachedPromise_1.useCachedPromise)(fn, [command, Array.isArray(optionsOrArgs) ? optionsOrArgs : [], execOptions, input], {
        ...useCachedPromiseOptions,
        abortable,
    });
}
exports.useExec = useExec;
