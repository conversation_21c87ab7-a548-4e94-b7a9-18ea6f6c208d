import {useRef as $hgUW1$useRef, useState as $hgUW1$useState, useCallback as $hgUW1$useCallback, useEffect as $hgUW1$useEffect, useMemo as $hgUW1$useMemo, useSyncExternalStore as $hgUW1$useSyncExternalStore} from "react";
import {environment as $hgUW1$environment, LaunchType as $hgUW1$LaunchType, showToast as $hgUW1$showToast, Toast as $hgUW1$Toast, Clipboard as $hgUW1$Clipboard, open as $hgUW1$open, Cache as $hgUW1$Cache, MenuBarExtra as $hgUW1$MenuBarExtra, Icon as $hgUW1$Icon, List as $hgUW1$List, ActionPanel as $hgUW1$ActionPanel, Action as $hgUW1$Action, AI as $hgUW1$AI, LocalStorage as $hgUW1$LocalStorage, Color as $hgUW1$Color, OAuth as $hgUW1$OAuth} from "@raycast/api";
import {dequal as $hgUW1$dequal} from "dequal/lite";
import {readFileSync as $hgUW1$readFileSync, createWriteStream as $hgUW1$createWriteStream, createReadStream as $hgUW1$createReadStream, mkdirSync as $hgUW1$mkdirSync, existsSync as $hgUW1$existsSync} from "node:fs";
import $hgUW1$nodepath, {join as $hgUW1$join, normalize as $hgUW1$normalize} from "node:path";
import {Buffer as $hgUW1$Buffer} from "buffer";
import $hgUW1$objecthash from "object-hash";
import $hgUW1$nodefetchcjs from "node-fetch-cjs";
import {env as $hgUW1$env} from "process";
import $hgUW1$nodechild_process from "node:child_process";
import {constants as $hgUW1$constants} from "node:buffer";
import $hgUW1$nodestream from "node:stream";
import {promisify as $hgUW1$promisify} from "node:util";
import * as $hgUW1$signalexit from "signal-exit";
import {stat as $hgUW1$stat, mkdir as $hgUW1$mkdir, copyFile as $hgUW1$copyFile, writeFile as $hgUW1$writeFile} from "node:fs/promises";
import {pipeline as $hgUW1$pipeline} from "node:stream/promises";
import $hgUW1$streamchain from "stream-chain";
import {parser as $hgUW1$parser} from "stream-json";
import $hgUW1$streamjsonfiltersPick from "stream-json/filters/Pick";
import $hgUW1$streamjsonstreamersStreamArray from "stream-json/streamers/StreamArray";
import {jsx as $hgUW1$jsx, jsxs as $hgUW1$jsxs} from "react/jsx-runtime";
import $hgUW1$nodeos from "node:os";
import {URL as $hgUW1$URL} from "node:url";





function $a57ed8effbd797c7$export$722debc0e56fea39(value) {
    const ref = (0, $hgUW1$useRef)(value);
    const signalRef = (0, $hgUW1$useRef)(0);
    if (!(0, $hgUW1$dequal)(value, ref.current)) {
        ref.current = value;
        signalRef.current += 1;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return (0, $hgUW1$useMemo)(()=>ref.current, [
        signalRef.current
    ]);
}



function $bfcf6ee368b3bd9f$export$d4b699e2c1148419(value) {
    const ref = (0, $hgUW1$useRef)(value);
    ref.current = value;
    return ref;
}





function $c718fd03aba6111c$export$80e5033e369189f3(error, options) {
    const message = error instanceof Error ? error.message : String(error);
    return (0, $hgUW1$showToast)({
        style: (0, $hgUW1$Toast).Style.Failure,
        title: options?.title ?? "Something went wrong",
        message: message,
        primaryAction: options?.primaryAction ?? $c718fd03aba6111c$var$handleErrorToastAction(error),
        secondaryAction: options?.primaryAction ? $c718fd03aba6111c$var$handleErrorToastAction(error) : undefined
    });
}
const $c718fd03aba6111c$var$handleErrorToastAction = (error)=>{
    let privateExtension = true;
    let title = "[Extension Name]...";
    let extensionURL = "";
    try {
        const packageJSON = JSON.parse($hgUW1$readFileSync($hgUW1$join((0, $hgUW1$environment).assetsPath, "..", "package.json"), "utf8"));
        title = `[${packageJSON.title}]...`;
        extensionURL = `https://raycast.com/${packageJSON.owner || packageJSON.author}/${packageJSON.name}`;
        if (!packageJSON.owner || packageJSON.access === "public") privateExtension = false;
    } catch (err) {
    // no-op
    }
    // if it's a private extension, we can't construct the URL to report the error
    // so we fallback to copying the error to the clipboard
    const fallback = (0, $hgUW1$environment).isDevelopment || privateExtension;
    const stack = error instanceof Error ? error?.stack || error?.message || "" : String(error);
    return {
        title: fallback ? "Copy Logs" : "Report Error",
        onAction (toast) {
            toast.hide();
            if (fallback) (0, $hgUW1$Clipboard).copy(stack);
            else (0, $hgUW1$open)(`https://github.com/raycast/extensions/issues/new?&labels=extension%2Cbug&template=extension_bug_report.yml&title=${encodeURIComponent(title)}&extension-url=${encodeURI(extensionURL)}&description=${encodeURIComponent(`#### Error:
\`\`\`
${stack}
\`\`\`
`)}`);
        }
    };
};


function $cefc05764ce5eacd$export$dd6b79aaabe7bc37(fn, args, options) {
    const lastCallId = (0, $hgUW1$useRef)(0);
    const [state, set] = (0, $hgUW1$useState)({
        isLoading: true
    });
    const fnRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(fn);
    const latestAbortable = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(options?.abortable);
    const latestArgs = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(args || []);
    const latestOnError = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(options?.onError);
    const latestOnData = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(options?.onData);
    const latestOnWillExecute = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(options?.onWillExecute);
    const latestValue = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(state.data);
    const latestCallback = (0, $hgUW1$useRef)();
    const paginationArgsRef = (0, $hgUW1$useRef)({
        page: 0
    });
    const usePaginationRef = (0, $hgUW1$useRef)(false);
    const hasMoreRef = (0, $hgUW1$useRef)(true);
    const pageSizeRef = (0, $hgUW1$useRef)(50);
    const callback = (0, $hgUW1$useCallback)((...args)=>{
        const callId = ++lastCallId.current;
        if (latestAbortable.current) {
            latestAbortable.current.current?.abort();
            latestAbortable.current.current = new AbortController();
        }
        latestOnWillExecute.current?.(args);
        set((prevState)=>({
                ...prevState,
                isLoading: true
            }));
        const promiseOrPaginatedPromise = $cefc05764ce5eacd$var$bindPromiseIfNeeded(fnRef.current)(...args);
        function handleError(error) {
            if (error.name == "AbortError") return error;
            if (callId === lastCallId.current) {
                // handle errors
                if (latestOnError.current) latestOnError.current(error);
                else if ((0, $hgUW1$environment).launchType !== (0, $hgUW1$LaunchType).Background) (0, $c718fd03aba6111c$export$80e5033e369189f3)(error, {
                    title: "Failed to fetch latest data",
                    primaryAction: {
                        title: "Retry",
                        onAction (toast) {
                            toast.hide();
                            latestCallback.current?.(...latestArgs.current || []);
                        }
                    }
                });
                set({
                    error: error,
                    isLoading: false
                });
            }
            return error;
        }
        if (typeof promiseOrPaginatedPromise === "function") {
            usePaginationRef.current = true;
            return promiseOrPaginatedPromise(paginationArgsRef.current).then(// @ts-expect-error too complicated for TS
            ({ data: data, hasMore: hasMore, cursor: cursor })=>{
                if (callId === lastCallId.current) {
                    if (paginationArgsRef.current) {
                        paginationArgsRef.current.cursor = cursor;
                        paginationArgsRef.current.lastItem = data?.[data.length - 1];
                    }
                    if (latestOnData.current) latestOnData.current(data, paginationArgsRef.current);
                    if (hasMore) pageSizeRef.current = data.length;
                    hasMoreRef.current = hasMore;
                    set((previousData)=>{
                        if (paginationArgsRef.current.page === 0) return {
                            data: data,
                            isLoading: false
                        };
                        // @ts-expect-error we know it's an array here
                        return {
                            data: (previousData.data || [])?.concat(data),
                            isLoading: false
                        };
                    });
                }
                return data;
            }, (error)=>{
                hasMoreRef.current = false;
                return handleError(error);
            });
        }
        usePaginationRef.current = false;
        return promiseOrPaginatedPromise.then((data)=>{
            if (callId === lastCallId.current) {
                if (latestOnData.current) latestOnData.current(data);
                set({
                    data: data,
                    isLoading: false
                });
            }
            return data;
        }, handleError);
    }, [
        latestAbortable,
        latestOnData,
        latestOnError,
        latestArgs,
        fnRef,
        set,
        latestCallback,
        latestOnWillExecute,
        paginationArgsRef
    ]);
    latestCallback.current = callback;
    const revalidate = (0, $hgUW1$useCallback)(()=>{
        // reset the pagination
        paginationArgsRef.current = {
            page: 0
        };
        const args = latestArgs.current || [];
        return callback(...args);
    }, [
        callback,
        latestArgs
    ]);
    const mutate = (0, $hgUW1$useCallback)(async (asyncUpdate, options)=>{
        let dataBeforeOptimisticUpdate;
        try {
            if (options?.optimisticUpdate) {
                if (typeof options?.rollbackOnError !== "function" && options?.rollbackOnError !== false) // keep track of the data before the optimistic update,
                // but only if we need it (eg. only when we want to automatically rollback after)
                dataBeforeOptimisticUpdate = structuredClone(latestValue.current?.value);
                const update = options.optimisticUpdate;
                set((prevState)=>({
                        ...prevState,
                        data: update(prevState.data)
                    }));
            }
            return await asyncUpdate;
        } catch (err) {
            if (typeof options?.rollbackOnError === "function") {
                const update = options.rollbackOnError;
                set((prevState)=>({
                        ...prevState,
                        data: update(prevState.data)
                    }));
            } else if (options?.optimisticUpdate && options?.rollbackOnError !== false) set((prevState)=>({
                    ...prevState,
                    data: dataBeforeOptimisticUpdate
                }));
            throw err;
        } finally{
            if (options?.shouldRevalidateAfter !== false) {
                if ((0, $hgUW1$environment).launchType === (0, $hgUW1$LaunchType).Background || (0, $hgUW1$environment).commandMode === "menu-bar") // when in the background or in a menu bar, we are going to await the revalidation
                // to make sure we get the right data at the end of the mutation
                await revalidate();
                else revalidate();
            }
        }
    }, [
        revalidate,
        latestValue,
        set
    ]);
    const onLoadMore = (0, $hgUW1$useCallback)(()=>{
        paginationArgsRef.current.page += 1;
        const args = latestArgs.current || [];
        callback(...args);
    }, [
        paginationArgsRef,
        latestValue,
        latestArgs,
        callback
    ]);
    // revalidate when the args change
    (0, $hgUW1$useEffect)(()=>{
        // reset the pagination
        paginationArgsRef.current = {
            page: 0
        };
        if (options?.execute !== false) callback(...args || []);
        else // cancel the previous request if we don't want to execute anymore
        if (latestAbortable.current) latestAbortable.current.current?.abort();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        (0, $a57ed8effbd797c7$export$722debc0e56fea39)([
            args,
            options?.execute,
            callback
        ]),
        latestAbortable,
        paginationArgsRef
    ]);
    // abort request when unmounting
    (0, $hgUW1$useEffect)(()=>{
        return ()=>{
            if (latestAbortable.current) // eslint-disable-next-line react-hooks/exhaustive-deps
            latestAbortable.current.current?.abort();
        };
    }, [
        latestAbortable
    ]);
    // we only want to show the loading indicator if the promise is executing
    const isLoading = options?.execute !== false ? state.isLoading : false;
    // @ts-expect-error loading is has some fixed value in the enum which
    const stateWithLoadingFixed = {
        ...state,
        isLoading: isLoading
    };
    const pagination = usePaginationRef.current ? {
        pageSize: pageSizeRef.current,
        hasMore: hasMoreRef.current,
        onLoadMore: onLoadMore
    } : undefined;
    return {
        ...stateWithLoadingFixed,
        revalidate: revalidate,
        mutate: mutate,
        pagination: pagination
    };
}
/** Bind the fn if it's a Promise method */ function $cefc05764ce5eacd$var$bindPromiseIfNeeded(fn) {
    if (fn === Promise.all) // @ts-expect-error this is fine
    return fn.bind(Promise);
    if (fn === Promise.race) // @ts-expect-error this is fine
    return fn.bind(Promise);
    if (fn === Promise.resolve) // @ts-expect-error this is fine
    return fn.bind(Promise);
    if (fn === Promise.reject) // @ts-expect-error this is fine
    return fn.bind(Promise);
    return fn;
}





// eslint-disable-next-line @typescript-eslint/no-explicit-any

var $e2e1ea6dd3b7d2e1$require$Buffer = $hgUW1$Buffer;
function $e2e1ea6dd3b7d2e1$export$b644b65666fe0c18(key, _value) {
    const value = this[key];
    if (value instanceof Date) return `__raycast_cached_date__${value.toString()}`;
    if ($e2e1ea6dd3b7d2e1$require$Buffer.isBuffer(value)) return `__raycast_cached_buffer__${value.toString("base64")}`;
    return _value;
}
function $e2e1ea6dd3b7d2e1$export$63698c10df99509c(_key, value) {
    if (typeof value === "string" && value.startsWith("__raycast_cached_date__")) return new Date(value.replace("__raycast_cached_date__", ""));
    if (typeof value === "string" && value.startsWith("__raycast_cached_buffer__")) return $e2e1ea6dd3b7d2e1$require$Buffer.from(value.replace("__raycast_cached_buffer__", ""), "base64");
    return value;
}


const $c40d7eded38ca69c$var$rootCache = /* #__PURE__ */ Symbol("cache without namespace");
const $c40d7eded38ca69c$var$cacheMap = /* #__PURE__ */ new Map();
function $c40d7eded38ca69c$export$14afb9e4c16377d3(key, initialState, config) {
    const cacheKey = config?.cacheNamespace || $c40d7eded38ca69c$var$rootCache;
    const cache = $c40d7eded38ca69c$var$cacheMap.get(cacheKey) || $c40d7eded38ca69c$var$cacheMap.set(cacheKey, new (0, $hgUW1$Cache)({
        namespace: config?.cacheNamespace
    })).get(cacheKey);
    if (!cache) throw new Error("Missing cache");
    const keyRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(key);
    const initialValueRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(initialState);
    const cachedState = (0, $hgUW1$useSyncExternalStore)(cache.subscribe, ()=>{
        try {
            return cache.get(keyRef.current);
        } catch (error) {
            console.error("Could not get Cache data:", error);
            return undefined;
        }
    });
    const state = (0, $hgUW1$useMemo)(()=>{
        if (typeof cachedState !== "undefined") {
            if (cachedState === "undefined") return undefined;
            try {
                return JSON.parse(cachedState, (0, $e2e1ea6dd3b7d2e1$export$63698c10df99509c));
            } catch (err) {
                // the data got corrupted somehow
                console.warn("The cached data is corrupted", err);
                return initialValueRef.current;
            }
        } else return initialValueRef.current;
    }, [
        cachedState,
        initialValueRef
    ]);
    const stateRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(state);
    const setStateAndCache = (0, $hgUW1$useCallback)((updater)=>{
        // @ts-expect-error TS struggles to infer the types as T could potentially be a function
        const newValue = typeof updater === "function" ? updater(stateRef.current) : updater;
        if (typeof newValue === "undefined") cache.set(keyRef.current, "undefined");
        else {
            const stringifiedValue = JSON.stringify(newValue, (0, $e2e1ea6dd3b7d2e1$export$b644b65666fe0c18));
            cache.set(keyRef.current, stringifiedValue);
        }
        return newValue;
    }, [
        cache,
        keyRef,
        stateRef
    ]);
    return [
        state,
        setStateAndCache
    ];
}







// Symbol to differentiate an empty cache from `undefined`
const $a7f3824c7be647eb$var$emptyCache = /* #__PURE__ */ Symbol();
function $a7f3824c7be647eb$export$b15740c74e256244(fn, args, options) {
    /**
   * The hook generates a cache key from the promise it receives & its arguments.
   * Sometimes that's not enough to guarantee uniqueness, so hooks that build on top of `useCachedPromise` can
   * use an `internal_cacheKeySuffix` to help it.
   *
   * @remark For internal use only.
   */ const { initialData: initialData, keepPreviousData: keepPreviousData, internal_cacheKeySuffix: internal_cacheKeySuffix, ...usePromiseOptions } = options || {};
    const lastUpdateFrom = (0, $hgUW1$useRef)();
    const [cachedData, mutateCache] = (0, $c40d7eded38ca69c$export$14afb9e4c16377d3)((0, $hgUW1$objecthash)(args || []) + (internal_cacheKeySuffix ?? ""), $a7f3824c7be647eb$var$emptyCache, {
        cacheNamespace: (0, $hgUW1$objecthash)(fn)
    });
    // Use a ref to store previous returned data. Use the inital data as its inital value from the cache.
    const laggyDataRef = (0, $hgUW1$useRef)(cachedData !== $a7f3824c7be647eb$var$emptyCache ? cachedData : initialData);
    const paginationArgsRef = (0, $hgUW1$useRef)(undefined);
    const { mutate: _mutate, revalidate: revalidate, ...state } = (0, $cefc05764ce5eacd$export$dd6b79aaabe7bc37)(fn, args || [], {
        ...usePromiseOptions,
        onData (data, pagination) {
            paginationArgsRef.current = pagination;
            if (usePromiseOptions.onData) usePromiseOptions.onData(data, pagination);
            if (pagination && pagination.page > 0) // don't cache beyond the first page
            return;
            lastUpdateFrom.current = "promise";
            laggyDataRef.current = data;
            mutateCache(data);
        }
    });
    let returnedData;
    const pagination = state.pagination;
    // when paginating, only the first page gets cached, so we return the data we get from `usePromise`, because
    // it will be accumulated.
    if (paginationArgsRef.current && paginationArgsRef.current.page > 0 && state.data) returnedData = state.data;
    else if (lastUpdateFrom.current === "promise") returnedData = laggyDataRef.current;
    else if (keepPreviousData && cachedData !== $a7f3824c7be647eb$var$emptyCache) {
        // if we want to keep the latest data, we pick the cache but only if it's not empty
        returnedData = cachedData;
        if (pagination) {
            pagination.hasMore = true;
            pagination.pageSize = cachedData.length;
        }
    } else if (keepPreviousData && cachedData === $a7f3824c7be647eb$var$emptyCache) // if the cache is empty, we will return the previous data
    returnedData = laggyDataRef.current;
    else if (cachedData !== $a7f3824c7be647eb$var$emptyCache) {
        returnedData = cachedData;
        if (pagination) {
            pagination.hasMore = true;
            pagination.pageSize = cachedData.length;
        }
    } else returnedData = initialData;
    const latestData = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(returnedData);
    // we rewrite the mutate function to update the cache instead
    const mutate = (0, $hgUW1$useCallback)(async (asyncUpdate, options)=>{
        let dataBeforeOptimisticUpdate;
        try {
            if (options?.optimisticUpdate) {
                if (typeof options?.rollbackOnError !== "function" && options?.rollbackOnError !== false) // keep track of the data before the optimistic update,
                // but only if we need it (eg. only when we want to automatically rollback after)
                dataBeforeOptimisticUpdate = structuredClone(latestData.current);
                const data = options.optimisticUpdate(latestData.current);
                lastUpdateFrom.current = "cache";
                laggyDataRef.current = data;
                mutateCache(data);
            }
            return await _mutate(asyncUpdate, {
                shouldRevalidateAfter: options?.shouldRevalidateAfter
            });
        } catch (err) {
            if (typeof options?.rollbackOnError === "function") {
                const data = options.rollbackOnError(latestData.current);
                lastUpdateFrom.current = "cache";
                laggyDataRef.current = data;
                mutateCache(data);
            } else if (options?.optimisticUpdate && options?.rollbackOnError !== false) {
                lastUpdateFrom.current = "cache";
                // @ts-expect-error when undefined, it's expected
                laggyDataRef.current = dataBeforeOptimisticUpdate;
                // @ts-expect-error when undefined, it's expected
                mutateCache(dataBeforeOptimisticUpdate);
            }
            throw err;
        }
    }, [
        mutateCache,
        _mutate,
        latestData,
        laggyDataRef,
        lastUpdateFrom
    ]);
    (0, $hgUW1$useEffect)(()=>{
        if (cachedData !== $a7f3824c7be647eb$var$emptyCache) {
            lastUpdateFrom.current = "cache";
            laggyDataRef.current = cachedData;
        }
    }, [
        cachedData
    ]);
    return {
        data: returnedData,
        isLoading: state.isLoading,
        error: state.error,
        mutate: paginationArgsRef.current && paginationArgsRef.current.page > 0 ? _mutate : mutate,
        pagination: pagination,
        revalidate: revalidate
    };
}







function $57d824a69312cff2$export$3427e80ee71d60ba(contentTypeHeader) {
    if (contentTypeHeader) {
        const mediaType = $57d824a69312cff2$var$parseContentType(contentTypeHeader);
        if (mediaType.subtype === "json") return true;
        if (mediaType.suffix === "json") return true;
        if (mediaType.suffix && /\bjson\b/i.test(mediaType.suffix)) return true;
        if (mediaType.subtype && /\bjson\b/i.test(mediaType.subtype)) return true;
    }
    return false;
}
/**
 * RegExp to match type in RFC 7231 sec 3.1.1.1
 *
 * media-type = type "/" subtype
 * type       = token
 * subtype    = token
 */ const $57d824a69312cff2$var$CT_TYPE_REGEXP = /^[!#$%&'*+.^_`|~0-9A-Za-z-]+\/[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;
/**
 * RegExp to match type in RFC 6838
 *
 * type-name = restricted-name
 * subtype-name = restricted-name
 * restricted-name = restricted-name-first *126restricted-name-chars
 * restricted-name-first  = ALPHA / DIGIT
 * restricted-name-chars  = ALPHA / DIGIT / "!" / "#" /
 *                          "$" / "&" / "-" / "^" / "_"
 * restricted-name-chars =/ "." ; Characters before first dot always
 *                              ; specify a facet name
 * restricted-name-chars =/ "+" ; Characters after last plus always
 *                              ; specify a structured syntax suffix
 * ALPHA =  %x41-5A / %x61-7A   ; A-Z / a-z
 * DIGIT =  %x30-39             ; 0-9
 */ const $57d824a69312cff2$var$MEDIA_TYPE_REGEXP = /^ *([A-Za-z0-9][A-Za-z0-9!#$&^_-]{0,126})\/([A-Za-z0-9][A-Za-z0-9!#$&^_.+-]{0,126}) *$/;
function $57d824a69312cff2$var$parseContentType(header) {
    const headerDelimitationindex = header.indexOf(";");
    const contentType = headerDelimitationindex !== -1 ? header.slice(0, headerDelimitationindex).trim() : header.trim();
    if (!$57d824a69312cff2$var$CT_TYPE_REGEXP.test(contentType)) throw new TypeError("invalid media type");
    const match = $57d824a69312cff2$var$MEDIA_TYPE_REGEXP.exec(contentType.toLowerCase().toLowerCase());
    if (!match) throw new TypeError("invalid media type");
    const type = match[1];
    let subtype = match[2];
    let suffix;
    // suffix after last +
    const index = subtype.lastIndexOf("+");
    if (index !== -1) {
        suffix = subtype.substring(index + 1);
        subtype = subtype.substring(0, index);
    }
    return {
        type: type,
        subtype: subtype,
        suffix: suffix
    };
}


async function $07de1abb43355e7b$var$defaultParsing(response) {
    if (!response.ok) throw new Error(response.statusText);
    const contentTypeHeader = response.headers.get("content-type");
    if (contentTypeHeader && (0, $57d824a69312cff2$export$3427e80ee71d60ba)(contentTypeHeader)) return await response.json();
    return await response.text();
}
function $07de1abb43355e7b$var$defaultMapping(result) {
    return {
        data: result,
        hasMore: false
    };
}
function $07de1abb43355e7b$export$d852f5f778460fa4(url, options) {
    const { parseResponse: parseResponse, mapResult: mapResult, initialData: initialData, execute: execute, keepPreviousData: keepPreviousData, onError: onError, onData: onData, onWillExecute: onWillExecute, ...fetchOptions } = options || {};
    const useCachedPromiseOptions = {
        initialData: initialData,
        execute: execute,
        keepPreviousData: keepPreviousData,
        onError: onError,
        onData: onData,
        onWillExecute: onWillExecute
    };
    const parseResponseRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(parseResponse || $07de1abb43355e7b$var$defaultParsing);
    const mapResultRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(mapResult || $07de1abb43355e7b$var$defaultMapping);
    const urlRef = (0, $hgUW1$useRef)();
    const firstPageUrlRef = (0, $hgUW1$useRef)();
    const firstPageUrl = typeof url === "function" ? url({
        page: 0
    }) : undefined;
    /**
   * When paginating, `url` is a `PaginatedRequestInfo`, so we only want to update the ref when the `firstPageUrl` changes.
   * When not paginating, `url` is a `RequestInfo`, so we want to update the ref whenever `url` changes.
   */ if (!urlRef.current || typeof firstPageUrlRef.current === "undefined" || firstPageUrlRef.current !== firstPageUrl) urlRef.current = url;
    firstPageUrlRef.current = firstPageUrl;
    const abortable = (0, $hgUW1$useRef)();
    const paginatedFn = (0, $hgUW1$useCallback)((url, options)=>async (pagination)=>{
            const res = await (0, $hgUW1$nodefetchcjs)(url(pagination), {
                signal: abortable.current?.signal,
                ...options
            });
            const parsed = await parseResponseRef.current(res);
            return mapResultRef.current?.(parsed);
        }, [
        parseResponseRef,
        mapResultRef
    ]);
    const fn = (0, $hgUW1$useCallback)(async (url, options)=>{
        const res = await (0, $hgUW1$nodefetchcjs)(url, {
            signal: abortable.current?.signal,
            ...options
        });
        const parsed = await parseResponseRef.current(res);
        const mapped = mapResultRef.current(parsed);
        return mapped?.data;
    }, [
        parseResponseRef,
        mapResultRef
    ]);
    const promise = (0, $hgUW1$useMemo)(()=>{
        if (firstPageUrlRef.current) return paginatedFn;
        return fn;
    }, [
        firstPageUrlRef,
        fn,
        paginatedFn
    ]);
    // @ts-expect-error lastItem can't be inferred properly
    return (0, $a7f3824c7be647eb$export$b15740c74e256244)(promise, [
        urlRef.current,
        fetchOptions
    ], {
        ...useCachedPromiseOptions,
        internal_cacheKeySuffix: firstPageUrlRef.current + (0, $hgUW1$objecthash)(mapResultRef.current) + (0, $hgUW1$objecthash)(parseResponseRef.current),
        abortable: abortable
    });
}


/*
 * Inspired by Execa
 */ 







var $c86e3701e284ece4$require$Buffer = $hgUW1$Buffer;

function $c86e3701e284ece4$export$6e3a9b5342d42997(spawned, { timeout: timeout } = {}) {
    const { onExit: onExit } = $hgUW1$signalexit;
    const spawnedPromise = new Promise((resolve, reject)=>{
        spawned.on("exit", (exitCode, signal)=>{
            resolve({
                exitCode: exitCode,
                signal: signal,
                timedOut: false
            });
        });
        spawned.on("error", (error)=>{
            reject(error);
        });
        if (spawned.stdin) spawned.stdin.on("error", (error)=>{
            reject(error);
        });
    });
    const removeExitHandler = onExit(()=>{
        spawned.kill();
    });
    if (timeout === 0 || timeout === undefined) return spawnedPromise.finally(()=>removeExitHandler());
    let timeoutId;
    const timeoutPromise = new Promise((_resolve, reject)=>{
        timeoutId = setTimeout(()=>{
            spawned.kill("SIGTERM");
            reject(Object.assign(new Error("Timed out"), {
                timedOut: true,
                signal: "SIGTERM"
            }));
        }, timeout);
    });
    const safeSpawnedPromise = spawnedPromise.finally(()=>{
        clearTimeout(timeoutId);
    });
    return Promise.race([
        timeoutPromise,
        safeSpawnedPromise
    ]).finally(()=>removeExitHandler());
}
class $c86e3701e284ece4$var$MaxBufferError extends Error {
    constructor(){
        super("The output is too big");
        this.name = "MaxBufferError";
    }
}
function $c86e3701e284ece4$var$bufferStream(options) {
    const { encoding: encoding } = options;
    const isBuffer = encoding === "buffer";
    // @ts-expect-error missing the methods we are adding below
    const stream = new (0, $hgUW1$nodestream).PassThrough({
        objectMode: false
    });
    if (encoding && encoding !== "buffer") stream.setEncoding(encoding);
    let length = 0;
    const chunks = [];
    stream.on("data", (chunk)=>{
        chunks.push(chunk);
        length += chunk.length;
    });
    stream.getBufferedValue = ()=>{
        return isBuffer ? $c86e3701e284ece4$require$Buffer.concat(chunks, length) : chunks.join("");
    };
    stream.getBufferedLength = ()=>length;
    return stream;
}
async function $c86e3701e284ece4$var$getStream(inputStream, options) {
    const stream = $c86e3701e284ece4$var$bufferStream(options);
    await new Promise((resolve, reject)=>{
        const rejectPromise = (error)=>{
            // Don't retrieve an oversized buffer.
            if (error && stream.getBufferedLength() <= (0, $hgUW1$constants).MAX_LENGTH) error.bufferedData = stream.getBufferedValue();
            reject(error);
        };
        (async ()=>{
            try {
                await (0, $hgUW1$promisify)((0, $hgUW1$nodestream).pipeline)(inputStream, stream);
                resolve();
            } catch (error) {
                rejectPromise(error);
            }
        })();
        stream.on("data", ()=>{
            // 80mb
            if (stream.getBufferedLength() > 80000000) rejectPromise(new $c86e3701e284ece4$var$MaxBufferError());
        });
    });
    return stream.getBufferedValue();
}
// On failure, `result.stdout|stderr` should contain the currently buffered stream
async function $c86e3701e284ece4$var$getBufferedData(stream, streamPromise) {
    stream.destroy();
    try {
        return await streamPromise;
    } catch (error) {
        return error.bufferedData;
    }
}
async function $c86e3701e284ece4$export$67b768ac9e1c70fa({ stdout: stdout, stderr: stderr }, { encoding: encoding }, processDone) {
    const stdoutPromise = $c86e3701e284ece4$var$getStream(stdout, {
        encoding: encoding
    });
    const stderrPromise = $c86e3701e284ece4$var$getStream(stderr, {
        encoding: encoding
    });
    try {
        return await Promise.all([
            processDone,
            stdoutPromise,
            stderrPromise
        ]);
    } catch (error) {
        return Promise.all([
            {
                error: error,
                exitCode: null,
                signal: error.signal,
                timedOut: error.timedOut || false
            },
            $c86e3701e284ece4$var$getBufferedData(stdout, stdoutPromise),
            $c86e3701e284ece4$var$getBufferedData(stderr, stderrPromise)
        ]);
    }
}
function $c86e3701e284ece4$var$stripFinalNewline(input) {
    const LF = typeof input === "string" ? "\n" : "\n".charCodeAt(0);
    const CR = typeof input === "string" ? "\r" : "\r".charCodeAt(0);
    if (input[input.length - 1] === LF) // @ts-expect-error we are doing some nasty stuff here
    input = input.slice(0, -1);
    if (input[input.length - 1] === CR) // @ts-expect-error we are doing some nasty stuff here
    input = input.slice(0, -1);
    return input;
}
function $c86e3701e284ece4$export$200978bbc0b73ca(options, value) {
    if (options.stripFinalNewline) return $c86e3701e284ece4$var$stripFinalNewline(value);
    return value;
}
function $c86e3701e284ece4$var$getErrorPrefix({ timedOut: timedOut, timeout: timeout, signal: signal, exitCode: exitCode }) {
    if (timedOut) return `timed out after ${timeout} milliseconds`;
    if (signal !== undefined && signal !== null) return `was killed with ${signal}`;
    if (exitCode !== undefined && exitCode !== null) return `failed with exit code ${exitCode}`;
    return "failed";
}
function $c86e3701e284ece4$var$makeError({ stdout: stdout, stderr: stderr, error: error, signal: signal, exitCode: exitCode, command: command, timedOut: timedOut, options: options, parentError: parentError }) {
    const prefix = $c86e3701e284ece4$var$getErrorPrefix({
        timedOut: timedOut,
        timeout: options?.timeout,
        signal: signal,
        exitCode: exitCode
    });
    const execaMessage = `Command ${prefix}: ${command}`;
    const shortMessage = error ? `${execaMessage}\n${error.message}` : execaMessage;
    const message = [
        shortMessage,
        stderr,
        stdout
    ].filter(Boolean).join("\n");
    if (error) // @ts-expect-error not on Error
    error.originalMessage = error.message;
    else error = parentError;
    error.message = message;
    // @ts-expect-error not on Error
    error.shortMessage = shortMessage;
    // @ts-expect-error not on Error
    error.command = command;
    // @ts-expect-error not on Error
    error.exitCode = exitCode;
    // @ts-expect-error not on Error
    error.signal = signal;
    // @ts-expect-error not on Error
    error.stdout = stdout;
    // @ts-expect-error not on Error
    error.stderr = stderr;
    if ("bufferedData" in error) delete error["bufferedData"];
    return error;
}
function $c86e3701e284ece4$export$a8f5efe603803b77({ stdout: stdout, stderr: stderr, error: error, exitCode: exitCode, signal: signal, timedOut: timedOut, command: command, options: options, parentError: parentError }) {
    if (error || exitCode !== 0 || signal !== null) {
        const returnedError = $c86e3701e284ece4$var$makeError({
            error: error,
            exitCode: exitCode,
            signal: signal,
            stdout: stdout,
            stderr: stderr,
            command: command,
            timedOut: timedOut,
            options: options,
            parentError: parentError
        });
        throw returnedError;
    }
    return stdout;
}



const $915f026f98ccae1a$var$SPACES_REGEXP = / +/g;
function $915f026f98ccae1a$var$parseCommand(command, args) {
    if (args) return [
        command,
        ...args
    ];
    const tokens = [];
    for (const token of command.trim().split($915f026f98ccae1a$var$SPACES_REGEXP)){
        // Allow spaces to be escaped by a backslash if not meant as a delimiter
        const previousToken = tokens[tokens.length - 1];
        if (previousToken && previousToken.endsWith("\\")) // Merge previous token with current one
        tokens[tokens.length - 1] = `${previousToken.slice(0, -1)} ${token}`;
        else tokens.push(token);
    }
    return tokens;
}
function $915f026f98ccae1a$export$3f4d948c82873887(command, optionsOrArgs, options) {
    const { parseOutput: parseOutput, input: input, onData: onData, onWillExecute: onWillExecute, initialData: initialData, execute: execute, keepPreviousData: keepPreviousData, onError: onError, ...execOptions } = Array.isArray(optionsOrArgs) ? options || {} : optionsOrArgs || {};
    const useCachedPromiseOptions = {
        initialData: initialData,
        execute: execute,
        keepPreviousData: keepPreviousData,
        onError: onError,
        onData: onData,
        onWillExecute: onWillExecute
    };
    const abortable = (0, $hgUW1$useRef)();
    const parseOutputRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(parseOutput || (0, $c86e3701e284ece4$export$a8f5efe603803b77));
    const fn = (0, $hgUW1$useCallback)(async (_command, _args, _options, input)=>{
        const [file, ...args] = $915f026f98ccae1a$var$parseCommand(_command, _args);
        const command = [
            file,
            ...args
        ].join(" ");
        const options = {
            stripFinalNewline: true,
            ..._options,
            timeout: _options?.timeout || 10000,
            signal: abortable.current?.signal,
            encoding: _options?.encoding === null ? "buffer" : _options?.encoding || "utf8",
            env: {
                PATH: "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin",
                ...$hgUW1$env,
                ..._options?.env
            }
        };
        const spawned = (0, $hgUW1$nodechild_process).spawn(file, args, options);
        const spawnedPromise = (0, $c86e3701e284ece4$export$6e3a9b5342d42997)(spawned, options);
        if (input) spawned.stdin.end(input);
        const [{ error: error, exitCode: exitCode, signal: signal, timedOut: timedOut }, stdoutResult, stderrResult] = await (0, $c86e3701e284ece4$export$67b768ac9e1c70fa)(spawned, options, spawnedPromise);
        const stdout = (0, $c86e3701e284ece4$export$200978bbc0b73ca)(options, stdoutResult);
        const stderr = (0, $c86e3701e284ece4$export$200978bbc0b73ca)(options, stderrResult);
        return parseOutputRef.current({
            stdout: // @ts-expect-error too many generics, I give up
            stdout,
            stderr: // @ts-expect-error too many generics, I give up
            stderr,
            error: error,
            exitCode: exitCode,
            signal: signal,
            timedOut: timedOut,
            command: command,
            options: options,
            parentError: new Error()
        });
    }, [
        parseOutputRef
    ]);
    // @ts-expect-error T can't be a Promise so it's actually the same
    return (0, $a7f3824c7be647eb$export$b15740c74e256244)(fn, [
        command,
        Array.isArray(optionsOrArgs) ? optionsOrArgs : [],
        execOptions,
        input
    ], {
        ...useCachedPromiseOptions,
        abortable: abortable
    });
}
















async function $ba5a1846053848df$var$cache(url, destination, fetchOptions) {
    if (typeof url === "object" || url.startsWith("http://") || url.startsWith("https://")) return await $ba5a1846053848df$var$cacheURL(url, destination, fetchOptions);
    else if (url.startsWith("file://")) return await $ba5a1846053848df$var$cacheFile((0, $hgUW1$normalize)(decodeURIComponent(new URL(url).pathname)), destination, fetchOptions?.signal ? fetchOptions.signal : undefined);
    else throw new Error("Only HTTP(S) or file URLs are supported");
}
async function $ba5a1846053848df$var$cacheURL(url, destination, fetchOptions) {
    const response = await (0, $hgUW1$nodefetchcjs)(url, fetchOptions);
    if (!response.ok) throw new Error("Failed to fetch URL");
    if (!(0, $57d824a69312cff2$export$3427e80ee71d60ba)(response.headers.get("content-type"))) throw new Error("URL does not return JSON");
    if (!response.body) throw new Error("Failed to retrieve expected JSON content: Response body is missing or inaccessible.");
    await (0, $hgUW1$pipeline)(response.body, (0, $hgUW1$createWriteStream)(destination), fetchOptions?.signal ? {
        signal: fetchOptions.signal
    } : undefined);
}
async function $ba5a1846053848df$var$cacheFile(source, destination, abortSignal) {
    await (0, $hgUW1$pipeline)((0, $hgUW1$createReadStream)(source), (0, $hgUW1$createWriteStream)(destination), abortSignal ? {
        signal: abortSignal
    } : undefined);
}
async function $ba5a1846053848df$var$cacheURLIfNecessary(url, folder, fileName, forceUpdate, fetchOptions) {
    const destination = (0, $hgUW1$join)(folder, fileName);
    try {
        await (0, $hgUW1$stat)(folder);
    } catch (e) {
        (0, $hgUW1$mkdirSync)(folder, {
            recursive: true
        });
        await $ba5a1846053848df$var$cache(url, destination, fetchOptions);
        return;
    }
    if (forceUpdate) {
        await $ba5a1846053848df$var$cache(url, destination, fetchOptions);
        return;
    }
    let stats = undefined;
    try {
        stats = await (0, $hgUW1$stat)(destination);
    } catch (e) {
        await $ba5a1846053848df$var$cache(url, destination, fetchOptions);
        return;
    }
    if (typeof url === "object" || url.startsWith("http://") || url.startsWith("https://")) {
        const headResponse = await (0, $hgUW1$nodefetchcjs)(url, {
            ...fetchOptions,
            method: "HEAD"
        });
        if (!headResponse.ok) throw new Error("Could not fetch URL");
        if (!(0, $57d824a69312cff2$export$3427e80ee71d60ba)(headResponse.headers.get("content-type"))) throw new Error("URL does not return JSON");
        const lastModified = Date.parse(headResponse.headers.get("last-modified") ?? "");
        if (stats.size === 0 || Number.isNaN(lastModified) || lastModified > stats.mtimeMs) {
            await $ba5a1846053848df$var$cache(url, destination, fetchOptions);
            return;
        }
    } else if (url.startsWith("file://")) try {
        const sourceStats = await (0, $hgUW1$stat)((0, $hgUW1$normalize)(decodeURIComponent(new URL(url).pathname)));
        if (sourceStats.mtimeMs > stats.mtimeMs) await $ba5a1846053848df$var$cache(url, destination, fetchOptions);
    } catch (e) {
        throw new Error("Source file could not be read");
    }
    else throw new Error("Only HTTP(S) or file URLs are supported");
}
async function* $ba5a1846053848df$var$streamJsonFile(filePath, pageSize, abortSignal, dataPath, filterFn, transformFn) {
    let page = [];
    const pipeline = new (0, $hgUW1$streamchain)([
        (0, $hgUW1$createReadStream)(filePath),
        dataPath ? (0, $hgUW1$streamjsonfiltersPick).withParser({
            filter: dataPath
        }) : (0, $hgUW1$parser)(),
        new (0, $hgUW1$streamjsonstreamersStreamArray)(),
        (data)=>transformFn?.(data.value) ?? data.value
    ]);
    abortSignal?.addEventListener("abort", ()=>{
        pipeline.destroy();
    });
    try {
        for await (const data of pipeline){
            if (abortSignal?.aborted) return [];
            if (!filterFn || filterFn(data)) page.push(data);
            if (page.length >= pageSize) {
                yield page;
                page = [];
            }
        }
    } catch (e) {
        pipeline.destroy();
        throw e;
    }
    if (page.length > 0) yield page;
    return [];
}
function $ba5a1846053848df$export$48c74caed1925dc8(url, options) {
    const { initialData: initialData, execute: execute, keepPreviousData: keepPreviousData, onError: onError, onData: onData, onWillExecute: onWillExecute, dataPath: dataPath, filter: filter, transform: transform, pageSize: pageSize = 20, ...fetchOptions } = options ?? {};
    const previousUrl = (0, $hgUW1$useRef)();
    const previousDestination = (0, $hgUW1$useRef)();
    const useCachedPromiseOptions = {
        initialData: initialData,
        execute: execute,
        keepPreviousData: keepPreviousData,
        onError: onError,
        onData: onData,
        onWillExecute: onWillExecute
    };
    const generatorRef = (0, $hgUW1$useRef)(null);
    const controllerRef = (0, $hgUW1$useRef)(null);
    const hasMoreRef = (0, $hgUW1$useRef)(false);
    return (0, $a7f3824c7be647eb$export$b15740c74e256244)((url, pageSize, fetchOptions, dataPath, filter, transform)=>async ({ page: page })=>{
            const fileName = (0, $hgUW1$objecthash)(url) + ".json";
            const folder = (0, $hgUW1$environment).supportPath;
            if (page === 0) {
                controllerRef.current?.abort();
                controllerRef.current = new AbortController();
                const destination = (0, $hgUW1$join)(folder, fileName);
                /**
           * Force update the cache when the URL changes but the cache destination does not.
           */ const forceCacheUpdate = Boolean(previousUrl.current && previousUrl.current !== url && previousDestination.current && previousDestination.current === destination);
                previousUrl.current = url;
                previousDestination.current = destination;
                await $ba5a1846053848df$var$cacheURLIfNecessary(url, folder, fileName, forceCacheUpdate, {
                    ...fetchOptions,
                    signal: controllerRef.current?.signal
                });
                generatorRef.current = $ba5a1846053848df$var$streamJsonFile(destination, pageSize, controllerRef.current?.signal, dataPath, filter, transform);
            }
            if (!generatorRef.current) return {
                hasMore: hasMoreRef.current,
                data: []
            };
            const { value: newData, done: done } = await generatorRef.current.next();
            hasMoreRef.current = !done;
            return {
                hasMore: hasMoreRef.current,
                data: newData ?? []
            };
        }, [
        url,
        pageSize,
        fetchOptions,
        dataPath,
        filter,
        transform
    ], useCachedPromiseOptions);
}















function $e88ea8aee5e4e2a5$export$d74ef0af94fb1db6(databasePath, query, options) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { permissionPriming: permissionPriming, ...usePromiseOptions } = options || {};
    const [permissionView, setPermissionView] = (0, $hgUW1$useState)();
    const latestOptions = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(options || {});
    const abortable = (0, $hgUW1$useRef)();
    const handleError = (0, $hgUW1$useCallback)((_error)=>{
        console.error(_error);
        const error = _error instanceof Error && _error.message.includes("authorization denied") ? new $e88ea8aee5e4e2a5$var$PermissionError("You do not have permission to access the database.") : _error;
        if ($e88ea8aee5e4e2a5$var$isPermissionError(error)) setPermissionView(/*#__PURE__*/ (0, $hgUW1$jsx)($e88ea8aee5e4e2a5$var$PermissionErrorScreen, {
            priming: latestOptions.current.permissionPriming
        }));
        else {
            if (latestOptions.current.onError) latestOptions.current.onError(error);
            else if ((0, $hgUW1$environment).launchType !== (0, $hgUW1$LaunchType).Background) (0, $c718fd03aba6111c$export$80e5033e369189f3)(error, {
                title: "Cannot query the data"
            });
        }
    }, [
        latestOptions
    ]);
    const fn = (0, $hgUW1$useMemo)(()=>{
        if (!(0, $hgUW1$existsSync)(databasePath)) throw new Error("The database does not exist");
        let workaroundCopiedDb = undefined;
        return async (databasePath, query)=>{
            const abortSignal = abortable.current?.signal;
            const spawned = (0, $hgUW1$nodechild_process).spawn("sqlite3", [
                "--json",
                "--readonly",
                databasePath,
                query
            ], {
                signal: abortSignal
            });
            const spawnedPromise = (0, $c86e3701e284ece4$export$6e3a9b5342d42997)(spawned);
            let [{ error: error, exitCode: exitCode, signal: signal }, stdoutResult, stderrResult] = await (0, $c86e3701e284ece4$export$67b768ac9e1c70fa)(spawned, {
                encoding: "utf-8"
            }, spawnedPromise);
            $e88ea8aee5e4e2a5$var$checkAborted(abortSignal);
            if (stderrResult.match("(5)") || stderrResult.match("(14)")) {
                // That means that the DB is busy because of another app is locking it
                // This happens when Chrome or Arc is opened: they lock the History db.
                // As an ugly workaround, we duplicate the file and read that instead
                // (with vfs unix - none to just not care about locks)
                if (!workaroundCopiedDb) {
                    const tempFolder = (0, $hgUW1$nodepath).join((0, $hgUW1$nodeos).tmpdir(), "useSQL", (0, $hgUW1$objecthash)(databasePath));
                    await (0, $hgUW1$mkdir)(tempFolder, {
                        recursive: true
                    });
                    $e88ea8aee5e4e2a5$var$checkAborted(abortSignal);
                    workaroundCopiedDb = (0, $hgUW1$nodepath).join(tempFolder, "db.db");
                    await (0, $hgUW1$copyFile)(databasePath, workaroundCopiedDb);
                    // needed for certain db
                    await (0, $hgUW1$writeFile)(workaroundCopiedDb + "-shm", "");
                    await (0, $hgUW1$writeFile)(workaroundCopiedDb + "-wal", "");
                    $e88ea8aee5e4e2a5$var$checkAborted(abortSignal);
                }
                const spawned = (0, $hgUW1$nodechild_process).spawn("sqlite3", [
                    "--json",
                    "--readonly",
                    "--vfs",
                    "unix-none",
                    workaroundCopiedDb,
                    query
                ], {
                    signal: abortSignal
                });
                const spawnedPromise = (0, $c86e3701e284ece4$export$6e3a9b5342d42997)(spawned);
                [{ error: error, exitCode: exitCode, signal: signal }, stdoutResult, stderrResult] = await (0, $c86e3701e284ece4$export$67b768ac9e1c70fa)(spawned, {
                    encoding: "utf-8"
                }, spawnedPromise);
                $e88ea8aee5e4e2a5$var$checkAborted(abortSignal);
            }
            if (error || exitCode !== 0 || signal !== null) throw new Error(stderrResult);
            return JSON.parse(stdoutResult.trim() || "[]");
        };
    }, [
        databasePath
    ]);
    return {
        ...(0, $cefc05764ce5eacd$export$dd6b79aaabe7bc37)(fn, [
            databasePath,
            query
        ], {
            ...usePromiseOptions,
            onError: handleError
        }),
        permissionView: permissionView
    };
}
class $e88ea8aee5e4e2a5$var$PermissionError extends Error {
    constructor(message){
        super(message);
        this.name = "PermissionError";
    }
}
function $e88ea8aee5e4e2a5$var$isPermissionError(error) {
    return error instanceof Error && error.name === "PermissionError";
}
function $e88ea8aee5e4e2a5$var$PermissionErrorScreen(props) {
    const macosVenturaAndLater = parseInt((0, $hgUW1$nodeos).release().split(".")[0]) >= 22;
    const preferencesString = macosVenturaAndLater ? "Settings" : "Preferences";
    const action = macosVenturaAndLater ? {
        title: "Open System Settings -> Privacy",
        target: "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"
    } : {
        title: "Open System Preferences -> Security",
        target: "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"
    };
    if ((0, $hgUW1$environment).commandMode === "menu-bar") return /*#__PURE__*/ (0, $hgUW1$jsxs)((0, $hgUW1$MenuBarExtra), {
        icon: (0, $hgUW1$Icon).Warning,
        title: (0, $hgUW1$environment).commandName,
        children: [
            /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$MenuBarExtra).Item, {
                title: "Raycast needs full disk access",
                tooltip: `You can revert this access in ${preferencesString} whenever you want`
            }),
            props.priming ? /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$MenuBarExtra).Item, {
                title: props.priming,
                tooltip: `You can revert this access in ${preferencesString} whenever you want`
            }) : null,
            /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$MenuBarExtra).Separator, {}),
            /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$MenuBarExtra).Item, {
                title: action.title,
                onAction: ()=>(0, $hgUW1$open)(action.target)
            })
        ]
    });
    return /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$List), {
        children: /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$List).EmptyView, {
            icon: {
                source: {
                    light: "https://raycast.com/uploads/extensions-utils-security-permissions-light.png",
                    dark: "https://raycast.com/uploads/extensions-utils-security-permissions-dark.png"
                }
            },
            title: "Raycast needs full disk access.",
            description: `${props.priming ? props.priming + "\n" : ""}You can revert this access in ${preferencesString} whenever you want.`,
            actions: /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$ActionPanel), {
                children: /*#__PURE__*/ (0, $hgUW1$jsx)((0, $hgUW1$Action).Open, {
                    ...action
                })
            })
        })
    });
}
function $e88ea8aee5e4e2a5$var$checkAborted(signal) {
    if (signal?.aborted) {
        const error = new Error("aborted");
        error.name = "AbortError";
        throw error;
    }
}




var $79498421851e7e84$export$cd58ffd7e3880e66;
(function(FormValidation) {
    /** Show an error when the value of the item is empty */ FormValidation["Required"] = "required";
})($79498421851e7e84$export$cd58ffd7e3880e66 || ($79498421851e7e84$export$cd58ffd7e3880e66 = {}));
function $79498421851e7e84$var$validationError(validation, value) {
    if (validation) {
        if (typeof validation === "function") return validation(value);
        else if (validation === "required") {
            let valueIsValid = typeof value !== "undefined" && value !== null;
            if (valueIsValid) switch(typeof value){
                case "string":
                    valueIsValid = value.length > 0;
                    break;
                case "object":
                    if (Array.isArray(value)) valueIsValid = value.length > 0;
                    else if (value instanceof Date) valueIsValid = value.getTime() > 0;
                    break;
                default:
                    break;
            }
            if (!valueIsValid) return "The item is required";
        }
    }
}
function $79498421851e7e84$export$87c0cf8eb5a167e0(props) {
    const { onSubmit: _onSubmit, validation: validation, initialValues: initialValues = {} } = props;
    // @ts-expect-error it's fine if we don't specify all the values
    const [values, setValues] = (0, $hgUW1$useState)(initialValues);
    const [errors, setErrors] = (0, $hgUW1$useState)({});
    const refs = (0, $hgUW1$useRef)({});
    const latestValidation = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(validation || {});
    const latestOnSubmit = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(_onSubmit);
    const focus = (0, $hgUW1$useCallback)((id)=>{
        refs.current[id]?.focus();
    }, [
        refs
    ]);
    const handleSubmit = (0, $hgUW1$useCallback)(async (values)=>{
        let validationErrors = false;
        for (const [id, validation] of Object.entries(latestValidation.current)){
            const error = $79498421851e7e84$var$validationError(validation, values[id]);
            if (error) {
                if (!validationErrors) {
                    validationErrors = {};
                    // we focus the first item that has an error
                    focus(id);
                }
                validationErrors[id] = error;
            }
        }
        if (validationErrors) {
            setErrors(validationErrors);
            return false;
        }
        const result = await latestOnSubmit.current(values);
        return typeof result === "boolean" ? result : true;
    }, [
        latestValidation,
        latestOnSubmit,
        focus
    ]);
    const setValidationError = (0, $hgUW1$useCallback)((id, error)=>{
        setErrors((errors)=>({
                ...errors,
                [id]: error
            }));
    }, [
        setErrors
    ]);
    const setValue = (0, $hgUW1$useCallback)(function(id, value) {
        // @ts-expect-error TS is always confused about SetStateAction, but it's fine here
        setValues((values)=>({
                ...values,
                [id]: typeof value === "function" ? value(values[id]) : value
            }));
    }, [
        setValues
    ]);
    const itemProps = (0, $hgUW1$useMemo)(()=>{
        // we have to use a proxy because we don't actually have any object to iterate through
        // so instead we dynamically create the props when required
        return new Proxy(// @ts-expect-error the whole point of a proxy...
        {}, {
            get (target, id) {
                const validation = latestValidation.current[id];
                const value = values[id];
                return {
                    onChange (value) {
                        if (errors[id]) {
                            const error = $79498421851e7e84$var$validationError(validation, value);
                            if (!error) setValidationError(id, undefined);
                        }
                        setValue(id, value);
                    },
                    onBlur (event) {
                        const error = $79498421851e7e84$var$validationError(validation, event.target.value);
                        if (error) setValidationError(id, error);
                    },
                    error: errors[id],
                    id: id,
                    // we shouldn't return `undefined` otherwise it will be an uncontrolled component
                    value: typeof value === "undefined" ? null : value,
                    ref: (instance)=>{
                        refs.current[id] = instance;
                    }
                };
            }
        });
    }, [
        errors,
        latestValidation,
        setValidationError,
        values,
        refs,
        setValue
    ]);
    const reset = (0, $hgUW1$useCallback)((values)=>{
        setErrors({});
        Object.entries(refs.current).forEach(([id, ref])=>{
            if (!values?.[id]) ref?.reset();
        });
        if (values) // @ts-expect-error it's fine if we don't specify all the values
        setValues(values);
    }, [
        setValues,
        setErrors,
        refs
    ]);
    return {
        handleSubmit: handleSubmit,
        setValidationError: setValidationError,
        setValue: setValue,
        values: values,
        itemProps: itemProps,
        focus: focus,
        reset: reset
    };
}





function $571cbdb5f3bef8a7$export$835fc3bd312a97c(prompt, options = {}) {
    const { creativity: creativity, stream: stream, model: model, ...usePromiseOptions } = options;
    const [data, setData] = (0, $hgUW1$useState)("");
    const abortable = (0, $hgUW1$useRef)();
    const { isLoading: isLoading, error: error, revalidate: revalidate } = (0, $cefc05764ce5eacd$export$dd6b79aaabe7bc37)(async (prompt, creativity, shouldStream)=>{
        setData("");
        const stream = (0, $hgUW1$AI).ask(prompt, {
            creativity: creativity,
            model: model,
            signal: abortable.current?.signal
        });
        if (shouldStream === false) setData(await stream);
        else {
            stream.on("data", (data)=>{
                setData((x)=>x + data);
            });
            await stream;
        }
    }, [
        prompt,
        creativity,
        stream
    ], {
        ...usePromiseOptions,
        abortable: abortable
    });
    return {
        isLoading: isLoading,
        data: data,
        error: error,
        revalidate: revalidate
    };
}





const $ae6377cf02b7ee4b$var$HALF_LIFE_DAYS = 10;
const $ae6377cf02b7ee4b$var$MS_PER_DAY = 86400000;
const $ae6377cf02b7ee4b$var$VISIT_TYPE_POINTS = {
    Default: 100,
    Embed: 0,
    Bookmark: 140
};
function $ae6377cf02b7ee4b$var$getNewFrecency(item) {
    const now = Date.now();
    const lastVisited = item ? item.lastVisited : 0;
    const frecency = item ? item.frecency : 0;
    const visitAgeInDays = (now - lastVisited) / $ae6377cf02b7ee4b$var$MS_PER_DAY;
    const DECAY_RATE_CONSTANT = Math.log(2) / ($ae6377cf02b7ee4b$var$HALF_LIFE_DAYS * $ae6377cf02b7ee4b$var$MS_PER_DAY);
    const currentVisitValue = $ae6377cf02b7ee4b$var$VISIT_TYPE_POINTS.Default * Math.exp(-DECAY_RATE_CONSTANT * visitAgeInDays);
    const totalVisitValue = frecency + currentVisitValue;
    return {
        lastVisited: now,
        frecency: totalVisitValue
    };
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const $ae6377cf02b7ee4b$var$defaultKey = (item)=>{
    return item.id;
};
function $ae6377cf02b7ee4b$export$5f452da9662a701d(data, options) {
    const keyRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(options?.key || $ae6377cf02b7ee4b$var$defaultKey);
    const sortUnvisitedRef = (0, $bfcf6ee368b3bd9f$export$d4b699e2c1148419)(options?.sortUnvisited);
    const [storedFrecencies, setStoredFrecencies] = (0, $c40d7eded38ca69c$export$14afb9e4c16377d3)(`raycast_frecency_${options?.namespace}`, {});
    const visitItem = (0, $hgUW1$useCallback)(async function updateFrecency(item) {
        const itemKey = keyRef.current(item);
        setStoredFrecencies((storedFrecencies)=>{
            const frecency = storedFrecencies[itemKey];
            const newFrecency = $ae6377cf02b7ee4b$var$getNewFrecency(frecency);
            return {
                ...storedFrecencies,
                [itemKey]: newFrecency
            };
        });
    }, [
        keyRef,
        setStoredFrecencies
    ]);
    const resetRanking = (0, $hgUW1$useCallback)(async function removeFrecency(item) {
        const itemKey = keyRef.current(item);
        setStoredFrecencies((storedFrecencies)=>{
            const newFrencencies = {
                ...storedFrecencies
            };
            delete newFrencencies[itemKey];
            return newFrencencies;
        });
    }, [
        keyRef,
        setStoredFrecencies
    ]);
    const sortedData = (0, $hgUW1$useMemo)(()=>{
        if (!data) return [];
        return data.sort((a, b)=>{
            const frecencyA = storedFrecencies[keyRef.current(a)];
            const frecencyB = storedFrecencies[keyRef.current(b)];
            // If a has a frecency, but b doesn't, a should come first
            if (frecencyA && !frecencyB) return -1;
            // If b has a frecency, but a doesn't, b should come first
            if (!frecencyA && frecencyB) return 1;
            // If both frecencies are defined,put the one with the higher frecency first
            if (frecencyA && frecencyB) return frecencyB.frecency - frecencyA.frecency;
            // If both frecencies are undefined, keep the original order
            return sortUnvisitedRef.current ? sortUnvisitedRef.current(a, b) : 0;
        });
    }, [
        storedFrecencies,
        data,
        keyRef,
        sortUnvisitedRef
    ]);
    return {
        data: sortedData,
        visitItem: visitItem,
        resetRanking: resetRanking
    };
}






function $a9c4f9ee71c485e4$export$86e2cef2561044ac(key, initialValue) {
    const { data: value, isLoading: isLoading, mutate: mutate } = (0, $cefc05764ce5eacd$export$dd6b79aaabe7bc37)(async (storageKey)=>{
        const item = await (0, $hgUW1$LocalStorage).getItem(storageKey);
        return typeof item !== "undefined" ? JSON.parse(item, (0, $e2e1ea6dd3b7d2e1$export$63698c10df99509c)) : initialValue;
    }, [
        key
    ]);
    async function setValue(value) {
        try {
            await mutate((0, $hgUW1$LocalStorage).setItem(key, JSON.stringify(value, (0, $e2e1ea6dd3b7d2e1$export$b644b65666fe0c18))), {
                optimisticUpdate (value) {
                    return value;
                }
            });
        } catch (error) {
            await (0, $c718fd03aba6111c$export$80e5033e369189f3)(error, {
                title: "Failed to set value in local storage"
            });
        }
    }
    async function removeValue() {
        try {
            await mutate((0, $hgUW1$LocalStorage).removeItem(key), {
                optimisticUpdate () {
                    return undefined;
                }
            });
        } catch (error) {
            await (0, $c718fd03aba6111c$export$80e5033e369189f3)(error, {
                title: "Failed to remove value from local storage"
            });
        }
    }
    return {
        value: value,
        setValue: setValue,
        removeValue: removeValue,
        isLoading: isLoading
    };
}


function $d974a5a2d3b7526a$var$hexToRGB(hex) {
    let r = 0;
    let g = 0;
    let b = 0;
    // 3 digits
    if (hex.length === 4) {
        r = parseInt(`${hex[1]}${hex[1]}`, 16);
        g = parseInt(`${hex[2]}${hex[2]}`, 16);
        b = parseInt(`${hex[3]}${hex[3]}`, 16);
    // 6 digits
    } else if (hex.length === 7) {
        r = parseInt(`${hex[1]}${hex[2]}`, 16);
        g = parseInt(`${hex[3]}${hex[4]}`, 16);
        b = parseInt(`${hex[5]}${hex[6]}`, 16);
    } else throw new Error(`Malformed hex color: ${hex}`);
    return {
        r: r,
        g: g,
        b: b
    };
}
function $d974a5a2d3b7526a$var$rgbToHex({ r: r, g: g, b: b }) {
    let rString = r.toString(16);
    let gString = g.toString(16);
    let bString = b.toString(16);
    if (rString.length === 1) rString = `0${rString}`;
    if (gString.length === 1) gString = `0${gString}`;
    if (bString.length === 1) bString = `0${bString}`;
    return `#${rString}${gString}${bString}`;
}
function $d974a5a2d3b7526a$var$rgbToHSL({ r: r, g: g, b: b }) {
    // Make r, g, and b fractions of 1
    r /= 255;
    g /= 255;
    b /= 255;
    // Find greatest and smallest channel values
    const cmin = Math.min(r, g, b);
    const cmax = Math.max(r, g, b);
    const delta = cmax - cmin;
    let h = 0;
    let s = 0;
    let l = 0;
    // Calculate hue
    // No difference
    if (delta === 0) h = 0;
    else if (cmax === r) h = (g - b) / delta % 6;
    else if (cmax === g) h = (b - r) / delta + 2;
    else h = (r - g) / delta + 4;
    h = Math.round(h * 60);
    // Make negative hues positive behind 360°
    if (h < 0) h += 360;
    // Calculate lightness
    l = (cmax + cmin) / 2;
    // Calculate saturation
    s = delta === 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));
    // Multiply l and s by 100
    s = +(s * 100).toFixed(1);
    l = +(l * 100).toFixed(1);
    return {
        h: h,
        s: s,
        l: l
    };
}
function $d974a5a2d3b7526a$var$hslToRGB({ h: h, s: s, l: l }) {
    // Must be fractions of 1
    s /= 100;
    l /= 100;
    const c = (1 - Math.abs(2 * l - 1)) * s;
    const x = c * (1 - Math.abs(h / 60 % 2 - 1));
    const m = l - c / 2;
    let r = 0;
    let g = 0;
    let b = 0;
    if (h >= 0 && h < 60) {
        r = c;
        g = x;
        b = 0;
    } else if (h >= 60 && h < 120) {
        r = x;
        g = c;
        b = 0;
    } else if (h >= 120 && h < 180) {
        r = 0;
        g = c;
        b = x;
    } else if (h >= 180 && h < 240) {
        r = 0;
        g = x;
        b = c;
    } else if (h >= 240 && h < 300) {
        r = x;
        g = 0;
        b = c;
    } else if (h >= 300 && h < 360) {
        r = c;
        g = 0;
        b = x;
    }
    r = Math.round((r + m) * 255);
    g = Math.round((g + m) * 255);
    b = Math.round((b + m) * 255);
    return {
        r: r,
        g: g,
        b: b
    };
}
function $d974a5a2d3b7526a$var$hexToHSL(hex) {
    return $d974a5a2d3b7526a$var$rgbToHSL($d974a5a2d3b7526a$var$hexToRGB(hex));
}
function $d974a5a2d3b7526a$var$hslToHex(hsl) {
    return $d974a5a2d3b7526a$var$rgbToHex($d974a5a2d3b7526a$var$hslToRGB(hsl));
}
function $d974a5a2d3b7526a$var$clamp(value, min, max) {
    return min < max ? value < min ? min : value > max ? max : value : value < max ? max : value > min ? min : value;
}
const $d974a5a2d3b7526a$var$offset = 12;
function $d974a5a2d3b7526a$export$98ff09ca20bf2fe1(hex) {
    const hsl = $d974a5a2d3b7526a$var$hexToHSL(hex);
    return $d974a5a2d3b7526a$var$hslToHex({
        h: hsl.h,
        s: hsl.s,
        l: $d974a5a2d3b7526a$var$clamp(hsl.l - $d974a5a2d3b7526a$var$offset, 0, 100)
    });
}
function $d974a5a2d3b7526a$export$31cf9e737f626ef1(hex) {
    const hsl = $d974a5a2d3b7526a$var$hexToHSL(hex);
    return $d974a5a2d3b7526a$var$hslToHex({
        h: hsl.h,
        s: hsl.s,
        l: $d974a5a2d3b7526a$var$clamp(hsl.l + $d974a5a2d3b7526a$var$offset, 0, 100)
    });
}


function $7d691a802b89543d$var$getWholeCharAndI(str, i) {
    const code = str.charCodeAt(i);
    if (Number.isNaN(code)) return [
        "",
        i
    ];
    if (code < 0xd800 || code > 0xdfff) return [
        str.charAt(i),
        i
    ]; // Normal character, keeping 'i' the same
    // High surrogate (could change last hex to 0xDB7F to treat high private
    // surrogates as single characters)
    if (0xd800 <= code && code <= 0xdbff) {
        if (str.length <= i + 1) throw new Error("High surrogate without following low surrogate");
        const next = str.charCodeAt(i + 1);
        if (0xdc00 > next || next > 0xdfff) throw new Error("High surrogate without following low surrogate");
        return [
            str.charAt(i) + str.charAt(i + 1),
            i + 1
        ];
    }
    // Low surrogate (0xDC00 <= code && code <= 0xDFFF)
    if (i === 0) throw new Error("Low surrogate without preceding high surrogate");
    const prev = str.charCodeAt(i - 1);
    // (could change last hex to 0xDB7F to treat high private surrogates
    // as single characters)
    if (0xd800 > prev || prev > 0xdbff) throw new Error("Low surrogate without preceding high surrogate");
    // Return the next character instead (and increment)
    return [
        str.charAt(i + 1),
        i + 1
    ];
}
const $7d691a802b89543d$var$avatarColorSet = [
    "#DC829A",
    "#D64854",
    "#D47600",
    "#D36CDD",
    "#52A9E4",
    "#7871E8",
    "#70920F",
    "#43B93A",
    "#EB6B3E",
    "#26B795",
    "#D85A9B",
    "#A067DC",
    "#BD9500",
    "#5385D9"
];
function $7d691a802b89543d$export$378f247c814d3cc9(name, options) {
    const words = name.trim().split(" ");
    let initials;
    if (words.length == 1 && $7d691a802b89543d$var$getWholeCharAndI(words[0], 0)[0]) initials = $7d691a802b89543d$var$getWholeCharAndI(words[0], 0)[0];
    else if (words.length > 1) {
        const firstWordFirstLetter = $7d691a802b89543d$var$getWholeCharAndI(words[0], 0)[0] || "";
        const lastWordFirstLetter = $7d691a802b89543d$var$getWholeCharAndI(words[words.length - 1], 0)[0] ?? "";
        initials = firstWordFirstLetter + lastWordFirstLetter;
    } else initials = "";
    let backgroundColor;
    if (options?.background) backgroundColor = options?.background;
    else {
        let initialsCharIndex = 0;
        let [char, i] = $7d691a802b89543d$var$getWholeCharAndI(initials, 0);
        while(char){
            initialsCharIndex += char.charCodeAt(0);
            [char, i] = $7d691a802b89543d$var$getWholeCharAndI(initials, i + 1);
        }
        const colorIndex = initialsCharIndex % $7d691a802b89543d$var$avatarColorSet.length;
        backgroundColor = $7d691a802b89543d$var$avatarColorSet[colorIndex];
    }
    const padding = 0;
    const radius = 50 - padding;
    const svg = `<svg width="100px" height="100px">
  ${options?.gradient !== false ? `<defs>
      <linearGradient id="Gradient" x1="0.25" x2="0.75" y1="0" y2="1">
        <stop offset="0%" stop-color="${(0, $d974a5a2d3b7526a$export$31cf9e737f626ef1)(backgroundColor)}"/>
        <stop offset="50%" stop-color="${backgroundColor}"/>
        <stop offset="100%" stop-color="${(0, $d974a5a2d3b7526a$export$98ff09ca20bf2fe1)(backgroundColor)}"/>
      </linearGradient>
  </defs>` : ""}
      <circle cx="50" cy="50" r="${radius}" fill="${options?.gradient !== false ? "url(#Gradient)" : backgroundColor}" />
      ${initials ? `<text x="50" y="80" font-size="${radius - 1}" font-family="Inter, sans-serif" text-anchor="middle" fill="white">${initials.toUpperCase()}</text>` : ""}
    </svg>
  `.replaceAll("\n", "");
    return `data:image/svg+xml,${svg}`;
}




function $d64c8f690cf3b07a$export$30c2a0095db95d06(url, options) {
    try {
        const urlObj = typeof url === "string" ? new (0, $hgUW1$URL)(url) : url;
        const hostname = urlObj.hostname;
        return {
            source: `https://www.google.com/s2/favicons?sz=${options?.size ?? 64}&domain=${hostname}`,
            fallback: options?.fallback ?? (0, $hgUW1$Icon).Link,
            mask: options?.mask
        };
    } catch (e) {
        console.error(e);
        return (0, $hgUW1$Icon).Link;
    }
}



function $a5607a6abce196a8$var$polarToCartesian(centerX, centerY, radius, angleInDegrees) {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
        x: centerX + radius * Math.cos(angleInRadians),
        y: centerY + radius * Math.sin(angleInRadians)
    };
}
function $a5607a6abce196a8$var$describeArc(x, y, radius, startAngle, endAngle) {
    const start = $a5607a6abce196a8$var$polarToCartesian(x, y, radius, endAngle);
    const end = $a5607a6abce196a8$var$polarToCartesian(x, y, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    const d = [
        "M",
        start.x,
        start.y,
        "A",
        radius,
        radius,
        0,
        largeArcFlag,
        0,
        end.x,
        end.y
    ].join(" ");
    return d;
}
function $a5607a6abce196a8$export$e7138cfc4fbdc77c(progress, color = (0, $hgUW1$Color).Red, options) {
    const background = options?.background || ((0, $hgUW1$environment).appearance === "light" ? "black" : "white");
    const backgroundOpacity = options?.backgroundOpacity || 0.1;
    const stroke = 10;
    const padding = 5;
    const radius = 50 - padding - stroke / 2;
    const svg = `<svg width="100px" height="100px">
      <circle cx="50" cy="50" r="${radius}" stroke-width="${stroke}" stroke="${progress < 1 ? background : color}" opacity="${progress < 1 ? backgroundOpacity : "1"}" fill="none" />
      ${progress > 0 && progress < 1 ? `<path d="${$a5607a6abce196a8$var$describeArc(50, 50, radius, 0, progress * 360)}" stroke="${color}" stroke-width="${stroke}" fill="none" />` : ""}
    </svg>
  `.replaceAll("\n", "");
    return `data:image/svg+xml,${svg}`;
}







const $6f4a1da9998d2612$var$PROVIDER_CLIENT_IDS = {
    asana: "1191201745684312",
    github: "7235fe8d42157f1f38c0",
    linear: "c8ff37b9225c3c9aefd7d66ea0e5b6f1",
    slack: "851756884692.5546927290212"
};
const $6f4a1da9998d2612$var$getIcon = (markup)=>`data:image/svg+xml,${markup}`;
const $6f4a1da9998d2612$var$PROVIDERS_ICONS = /* #__PURE__ */ {
    asana: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" width="251" height="232" fill="none"><path fill="#F06A6A" d="M179.383 54.373c0 30.017-24.337 54.374-54.354 54.374-30.035 0-54.373-24.338-54.373-54.374C70.656 24.338 94.993 0 125.029 0c30.017 0 54.354 24.338 54.354 54.373ZM54.393 122.33C24.376 122.33.02 146.668.02 176.685c0 30.017 24.337 54.373 54.373 54.373 30.035 0 54.373-24.338 54.373-54.373 0-30.017-24.338-54.355-54.373-54.355Zm141.253 0c-30.035 0-54.373 24.338-54.373 54.374 0 30.035 24.338 54.373 54.373 54.373 30.017 0 54.374-24.338 54.374-54.373 0-30.036-24.338-54.374-54.374-54.374Z"/></svg>`),
    github: {
        source: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>`),
        tintColor: (0, $hgUW1$Color).PrimaryText
    },
    google: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" style="display:block" viewBox="0 0 48 48"><path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"/><path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"/><path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"/><path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"/><path fill="none" d="M0 0h48v48H0z"/></svg>`),
    jira: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="2361" height="2500" viewBox="2.59 0 214.091 224"><linearGradient id="a" x1="102.4" x2="56.15" y1="218.63" y2="172.39" gradientTransform="matrix(1 0 0 -1 0 264)" gradientUnits="userSpaceOnUse"><stop offset=".18" stop-color="#0052cc"/><stop offset="1" stop-color="#2684ff"/></linearGradient><linearGradient xlink:href="#a" id="b" x1="114.65" x2="160.81" y1="85.77" y2="131.92"/><path fill="#2684ff" d="M214.06 105.73 117.67 9.34 108.33 0 35.77 72.56 2.59 105.73a8.89 8.89 0 0 0 0 12.54l66.29 66.29L108.33 224l72.55-72.56 1.13-1.12 32.05-32a8.87 8.87 0 0 0 0-12.59zm-105.73 39.39L75.21 112l33.12-33.12L141.44 112z"/><path fill="url(#a)" d="M108.33 78.88a55.75 55.75 0 0 1-.24-78.61L35.62 72.71l39.44 39.44z"/><path fill="url(#b)" d="m141.53 111.91-33.2 33.21a55.77 55.77 0 0 1 0 78.86L181 151.35z"/></svg>`),
    linear: {
        source: {
            light: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" fill="#222326" width="200" height="200" viewBox="0 0 100 100"><path d="M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z"/></svg>`),
            dark: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" fill="#fff" width="200" height="200" viewBox="0 0 100 100"><path d="M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z" /></svg>`)
        }
    },
    slack: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="73 73 124 124"><style>.st0{fill:#e01e5a}.st1{fill:#36c5f0}.st2{fill:#2eb67d}.st3{fill:#ecb22e}</style><path d="M99.4 151.2c0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9h12.9v12.9zM105.9 151.2c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v32.3c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9v-32.3z" class="st0"/><path d="M118.8 99.4c-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v12.9h-12.9zM118.8 105.9c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H86.5c-7.1 0-12.9-5.8-12.9-12.9s5.8-12.9 12.9-12.9h32.3z" class="st1"/><path d="M170.6 118.8c0-7.1 5.8-12.9 12.9-12.9 7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9h-12.9v-12.9zM164.1 118.8c0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9V86.5c0-7.1 5.8-12.9 12.9-12.9 7.1 0 12.9 5.8 12.9 12.9v32.3z" class="st2"/><path d="M151.2 170.6c7.1 0 12.9 5.8 12.9 12.9 0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9v-12.9h12.9zM151.2 164.1c-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9h32.3c7.1 0 12.9 5.8 12.9 12.9 0 7.1-5.8 12.9-12.9 12.9h-32.3z" class="st3"/></svg>`),
    zoom: /* #__PURE__ */ $6f4a1da9998d2612$var$getIcon(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 351.845 80"><path d="M73.786 78.835H10.88A10.842 10.842 0 0 1 .833 72.122a10.841 10.841 0 0 1 2.357-11.85L46.764 16.7h-31.23C6.954 16.699 0 9.744 0 1.165h58.014c4.414 0 8.357 2.634 10.046 6.712a10.843 10.843 0 0 1-2.356 11.85L22.13 63.302h36.122c8.58 0 15.534 6.955 15.534 15.534Zm278.059-48.544C351.845 13.588 338.256 0 321.553 0c-8.934 0-16.975 3.89-22.524 10.063C293.48 3.89 285.44 0 276.505 0c-16.703 0-30.291 13.588-30.291 30.291v48.544c8.579 0 15.534-6.955 15.534-15.534v-33.01c0-8.137 6.62-14.757 14.757-14.757s14.757 6.62 14.757 14.757v33.01c0 8.58 6.955 15.534 15.534 15.534V30.291c0-8.137 6.62-14.757 14.757-14.757s14.758 6.62 14.758 14.757v33.01c0 8.58 6.954 15.534 15.534 15.534V30.291ZM238.447 40c0 22.091-17.909 40-40 40s-40-17.909-40-40 17.908-40 40-40 40 17.909 40 40Zm-15.534 0c0-13.512-10.954-24.466-24.466-24.466S173.98 26.488 173.98 40s10.953 24.466 24.466 24.466S222.913 53.512 222.913 40Zm-70.68 0c0 22.091-17.909 40-40 40s-40-17.909-40-40 17.909-40 40-40 40 17.909 40 40Zm-15.534 0c0-13.512-10.954-24.466-24.466-24.466S87.767 26.488 87.767 40s10.954 24.466 24.466 24.466S136.699 53.512 136.699 40Z" style="fill:#0b5cff"/></svg>`)
};
const $6f4a1da9998d2612$export$780dd28051b4e9d4 = (options)=>new (0, $a613cd7627dc664f$export$905bf243392675e9)({
        client: new (0, $hgUW1$OAuth).PKCEClient({
            redirectMethod: (0, $hgUW1$OAuth).RedirectMethod.Web,
            providerName: "Asana",
            providerIcon: $6f4a1da9998d2612$var$PROVIDERS_ICONS.asana,
            providerId: "asana",
            description: "Connect your Asana account"
        }),
        clientId: options.clientId ?? $6f4a1da9998d2612$var$PROVIDER_CLIENT_IDS.asana,
        authorizeUrl: options.authorizeUrl ?? "https://asana.oauth.raycast.com/authorize",
        tokenUrl: options.tokenUrl ?? "https://asana.oauth.raycast.com/token",
        refreshTokenUrl: options.refreshTokenUrl ?? "https://asana.oauth.raycast.com/refresh-token",
        scope: options.scope,
        personalAccessToken: options.personalAccessToken,
        onAuthorize: options.onAuthorize,
        bodyEncoding: options.bodyEncoding,
        tokenRefreshResponseParser: options.tokenRefreshResponseParser,
        tokenResponseParser: options.tokenResponseParser
    });
const $6f4a1da9998d2612$export$dd4b0abe8c85d023 = (options)=>new (0, $a613cd7627dc664f$export$905bf243392675e9)({
        client: new (0, $hgUW1$OAuth).PKCEClient({
            redirectMethod: (0, $hgUW1$OAuth).RedirectMethod.Web,
            providerName: "GitHub",
            providerIcon: $6f4a1da9998d2612$var$PROVIDERS_ICONS.github,
            providerId: "github",
            description: "Connect your GitHub account"
        }),
        clientId: options.clientId ?? $6f4a1da9998d2612$var$PROVIDER_CLIENT_IDS.github,
        authorizeUrl: options.authorizeUrl ?? "https://github.oauth.raycast.com/authorize",
        tokenUrl: options.tokenUrl ?? "https://github.oauth.raycast.com/token",
        refreshTokenUrl: options.refreshTokenUrl ?? "https://github.oauth.raycast.com/refresh-token",
        scope: options.scope,
        personalAccessToken: options.personalAccessToken,
        onAuthorize: options.onAuthorize,
        bodyEncoding: options.bodyEncoding,
        tokenRefreshResponseParser: options.tokenRefreshResponseParser,
        tokenResponseParser: options.tokenResponseParser
    });
const $6f4a1da9998d2612$export$11f12f8783089c54 = (options)=>new (0, $a613cd7627dc664f$export$905bf243392675e9)({
        client: new (0, $hgUW1$OAuth).PKCEClient({
            redirectMethod: (0, $hgUW1$OAuth).RedirectMethod.AppURI,
            providerName: "Google",
            providerIcon: $6f4a1da9998d2612$var$PROVIDERS_ICONS.google,
            providerId: "google",
            description: "Connect your Google account"
        }),
        clientId: options.clientId,
        authorizeUrl: options.authorizeUrl ?? "https://accounts.google.com/o/oauth2/v2/auth",
        tokenUrl: options.tokenUrl ?? "https://oauth2.googleapis.com/token",
        refreshTokenUrl: options.tokenUrl,
        scope: options.scope,
        personalAccessToken: options.personalAccessToken,
        bodyEncoding: options.bodyEncoding ?? "url-encoded",
        onAuthorize: options.onAuthorize,
        tokenRefreshResponseParser: options.tokenRefreshResponseParser,
        tokenResponseParser: options.tokenResponseParser
    });
const $6f4a1da9998d2612$export$61dd380016c0b337 = (options)=>new (0, $a613cd7627dc664f$export$905bf243392675e9)({
        client: new (0, $hgUW1$OAuth).PKCEClient({
            redirectMethod: (0, $hgUW1$OAuth).RedirectMethod.Web,
            providerName: "Jira",
            providerIcon: $6f4a1da9998d2612$var$PROVIDERS_ICONS.jira,
            providerId: "jira",
            description: "Connect your Jira account"
        }),
        clientId: options.clientId,
        authorizeUrl: options.authorizeUrl ?? "https://auth.atlassian.com/authorize",
        tokenUrl: options.tokenUrl ?? "https://auth.atlassian.com/oauth/token",
        refreshTokenUrl: options.refreshTokenUrl,
        scope: options.scope,
        personalAccessToken: options.personalAccessToken,
        onAuthorize: options.onAuthorize,
        bodyEncoding: options.bodyEncoding,
        tokenRefreshResponseParser: options.tokenRefreshResponseParser,
        tokenResponseParser: options.tokenResponseParser
    });
const $6f4a1da9998d2612$export$64c7606493acd37f = (options)=>new (0, $a613cd7627dc664f$export$905bf243392675e9)({
        client: new (0, $hgUW1$OAuth).PKCEClient({
            redirectMethod: (0, $hgUW1$OAuth).RedirectMethod.Web,
            providerName: "Linear",
            providerIcon: $6f4a1da9998d2612$var$PROVIDERS_ICONS.linear,
            providerId: "linear",
            description: "Connect your Linear account"
        }),
        clientId: options.clientId ?? $6f4a1da9998d2612$var$PROVIDER_CLIENT_IDS.linear,
        authorizeUrl: options.authorizeUrl ?? "https://linear.oauth.raycast.com/authorize",
        tokenUrl: options.tokenUrl ?? "https://linear.oauth.raycast.com/token",
        refreshTokenUrl: options.refreshTokenUrl ?? "https://linear.oauth.raycast.com/refresh-token",
        scope: options.scope,
        extraParameters: {
            actor: "user"
        },
        onAuthorize: options.onAuthorize,
        bodyEncoding: options.bodyEncoding,
        tokenRefreshResponseParser: options.tokenRefreshResponseParser,
        tokenResponseParser: options.tokenResponseParser
    });
const $6f4a1da9998d2612$export$f0b919bdea166402 = (options)=>new (0, $a613cd7627dc664f$export$905bf243392675e9)({
        client: new (0, $hgUW1$OAuth).PKCEClient({
            redirectMethod: (0, $hgUW1$OAuth).RedirectMethod.Web,
            providerName: "Slack",
            providerIcon: $6f4a1da9998d2612$var$PROVIDERS_ICONS.slack,
            providerId: "slack",
            description: "Connect your Slack account"
        }),
        clientId: options.clientId ?? $6f4a1da9998d2612$var$PROVIDER_CLIENT_IDS.slack,
        authorizeUrl: options.authorizeUrl ?? "https://slack.oauth.raycast.com/authorize",
        tokenUrl: options.tokenUrl ?? "https://slack.oauth.raycast.com/token",
        refreshTokenUrl: options.tokenUrl ?? "https://slack.oauth.raycast.com/refresh-token",
        scope: "",
        extraParameters: {
            user_scope: options.scope
        },
        personalAccessToken: options.personalAccessToken,
        bodyEncoding: options.tokenUrl ? options.bodyEncoding ?? "url-encoded" : "json",
        onAuthorize: options.onAuthorize,
        tokenRefreshResponseParser: options.tokenRefreshResponseParser,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        tokenResponseParser: options.tokenResponseParser ?? ((response)=>{
            return {
                access_token: response.authed_user.access_token,
                scope: response.authed_user.scope
            };
        })
    });
const $6f4a1da9998d2612$export$d20f91c90486e947 = (options)=>new (0, $a613cd7627dc664f$export$905bf243392675e9)({
        client: new (0, $hgUW1$OAuth).PKCEClient({
            redirectMethod: (0, $hgUW1$OAuth).RedirectMethod.Web,
            providerName: "Zoom",
            providerIcon: $6f4a1da9998d2612$var$PROVIDERS_ICONS.zoom,
            providerId: "zoom",
            description: "Connect your Zoom account"
        }),
        clientId: options.clientId,
        authorizeUrl: options.authorizeUrl ?? "https://zoom.us/oauth/authorize",
        tokenUrl: options.tokenUrl ?? "https://zoom.us/oauth/token",
        refreshTokenUrl: options.refreshTokenUrl,
        scope: options.scope,
        personalAccessToken: options.personalAccessToken,
        bodyEncoding: options.bodyEncoding ?? "url-encoded",
        onAuthorize: options.onAuthorize,
        tokenRefreshResponseParser: options.tokenRefreshResponseParser,
        tokenResponseParser: options.tokenResponseParser
    });


class $a613cd7627dc664f$export$905bf243392675e9 {
    constructor(options){
        this.clientId = options.clientId;
        this.scope = Array.isArray(options.scope) ? options.scope.join(" ") : options.scope;
        this.personalAccessToken = options.personalAccessToken;
        this.bodyEncoding = options.bodyEncoding;
        this.client = options.client;
        this.extraParameters = options.extraParameters;
        this.authorizeUrl = options.authorizeUrl;
        this.tokenUrl = options.tokenUrl;
        this.refreshTokenUrl = options.refreshTokenUrl;
        this.onAuthorize = options.onAuthorize;
        this.tokenResponseParser = options.tokenResponseParser ?? ((x)=>x);
        this.tokenRefreshResponseParser = options.tokenRefreshResponseParser ?? ((x)=>x);
        this.authorize = this.authorize.bind(this);
    }
    static{
        /**
   * Asana OAuth service provided out of the box.
   *
   * @example
   * ```typescript
   * const asana = OAuthService.asana({ scope: 'default' })
   * ```
   */ this.asana = (0, $6f4a1da9998d2612$export$780dd28051b4e9d4);
    }
    static{
        /**
   * GitHub OAuth service provided out of the box.
   *
   * @example
   * ```typescript
   * const github = OAuthService.github({ scope: 'repo user' })
   * ```
   */ this.github = (0, $6f4a1da9998d2612$export$dd4b0abe8c85d023);
    }
    static{
        /**
   * Google OAuth service provided out of the box.
   *
   * @example
   * ```typescript
   * const google = OAuthService.google({
   *   clientId: 'custom-client-id',
   *   authorizeUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
   *   tokenUrl: 'https://oauth2.googleapis.com/token',
   *   scope: 'https://www.googleapis.com/auth/drive.readonly',
   * });
   * ```
   */ this.google = (0, $6f4a1da9998d2612$export$11f12f8783089c54);
    }
    static{
        /**
   * Jira OAuth service provided out of the box.
   *
   * @example
   * ```typescript
   * const jira = OAuthService.jira({
   *   clientId: 'custom-client-id',
   *   authorizeUrl: 'https://auth.atlassian.com/authorize',
   *   tokenUrl: 'https://api.atlassian.com/oauth/token',
   *   scope: 'read:jira-user read:jira-work offline_access'
   * });
   * ```
   */ this.jira = (0, $6f4a1da9998d2612$export$61dd380016c0b337);
    }
    static{
        /**
   * Linear OAuth service provided out of the box.
   *
   * @example
   * ```typescript
   * const linear = OAuthService.linear({ scope: 'read write' })
   * ```
   */ this.linear = (0, $6f4a1da9998d2612$export$64c7606493acd37f);
    }
    static{
        /**
   * Slack OAuth service provided out of the box.
   *
   * @example
   * ```typescript
   * const slack = OAuthService.slack({ scope: 'emoji:read' })
   * ```
   */ this.slack = (0, $6f4a1da9998d2612$export$f0b919bdea166402);
    }
    static{
        /**
   * Zoom OAuth service provided out of the box.
   *
   * @example
   * ```typescript
   * const zoom = OAuthService.zoom({
   *   clientId: 'custom-client-id',
   *   authorizeUrl: 'https://zoom.us/oauth/authorize',
   *   tokenUrl: 'https://zoom.us/oauth/token',
   *   scope: 'meeting:write',
   *   personalAccessToken: 'personal-access-token',
   * });
   * ```
   */ this.zoom = (0, $6f4a1da9998d2612$export$d20f91c90486e947);
    }
    /**
   * Initiates the OAuth authorization process or refreshes existing tokens if necessary.
   * If the current token set has a refresh token and it is expired, then the function will refresh the tokens.
   * If no tokens exist, it will initiate the OAuth authorization process and fetch the tokens.
   *
   * @returns {Promise<string>} A promise that resolves with the access token obtained from the authorization flow, or null if the token could not be obtained.
   */ async authorize() {
        const currentTokenSet = await this.client.getTokens();
        if (currentTokenSet?.accessToken) {
            if (currentTokenSet.refreshToken && currentTokenSet.isExpired()) {
                const tokens = await this.refreshTokens({
                    token: currentTokenSet.refreshToken,
                    clientId: this.clientId,
                    tokenUrl: this.refreshTokenUrl ?? this.tokenUrl
                });
                await this.client.setTokens(tokens);
                return tokens.access_token;
            }
            return currentTokenSet.accessToken;
        }
        const authRequest = await this.client.authorizationRequest({
            endpoint: this.authorizeUrl,
            clientId: this.clientId,
            scope: this.scope,
            extraParameters: this.extraParameters
        });
        const { authorizationCode: authorizationCode } = await this.client.authorize(authRequest);
        const tokens = await this.fetchTokens({
            authRequest: authRequest,
            authorizationCode: authorizationCode,
            clientId: this.clientId,
            tokenUrl: this.tokenUrl
        });
        await this.client.setTokens(tokens);
        return tokens.access_token;
    }
    async fetchTokens({ authRequest: authRequest, authorizationCode: authorizationCode, clientId: clientId, tokenUrl: tokenUrl, bodyEncoding: bodyEncoding }) {
        let options;
        if (bodyEncoding === "url-encoded") {
            const params = new URLSearchParams();
            params.append("client_id", clientId);
            params.append("code", authorizationCode);
            params.append("code_verifier", authRequest.codeVerifier);
            params.append("grant_type", "authorization_code");
            params.append("redirect_uri", authRequest.redirectURI);
            options = {
                body: params
            };
        } else options = {
            body: JSON.stringify({
                client_id: clientId,
                code: authorizationCode,
                code_verifier: authRequest.codeVerifier,
                grant_type: "authorization_code",
                redirect_uri: authRequest.redirectURI
            }),
            headers: {
                "Content-Type": "application/json"
            }
        };
        const response = await (0, $hgUW1$nodefetchcjs)(tokenUrl, {
            method: "POST",
            ...options
        });
        if (!response.ok) {
            const responseText = await response.text();
            console.error("fetch tokens error:", responseText);
            throw new Error(`Error while fetching tokens: ${response.status} (${response.statusText})\n${responseText}`);
        }
        const tokens = this.tokenResponseParser(await response.json());
        // Some clients such as Linear can return a scope array instead of a string
        return Array.isArray(tokens.scope) ? {
            ...tokens,
            scope: tokens.scope.join(" ")
        } : tokens;
    }
    async refreshTokens({ token: token, clientId: clientId, tokenUrl: tokenUrl, bodyEncoding: bodyEncoding }) {
        let options;
        if (bodyEncoding === "url-encoded") {
            const params = new URLSearchParams();
            params.append("client_id", clientId);
            params.append("refresh_token", token);
            params.append("grant_type", "refresh_token");
            options = {
                body: params
            };
        } else options = {
            body: JSON.stringify({
                client_id: clientId,
                refresh_token: token,
                grant_type: "refresh_token"
            }),
            headers: {
                "Content-Type": "application/json"
            }
        };
        const response = await (0, $hgUW1$nodefetchcjs)(tokenUrl, {
            method: "POST",
            ...options
        });
        if (!response.ok) {
            const responseText = await response.text();
            console.error("refresh tokens error:", responseText);
            throw new Error(`Error while refreshing tokens: ${response.status} (${response.statusText})\n${responseText}`);
        }
        const tokenResponse = this.tokenRefreshResponseParser(await response.json());
        tokenResponse.refresh_token = tokenResponse.refresh_token ?? token;
        return tokenResponse;
    }
}





let $610e71f8af383779$var$token = null;
let $610e71f8af383779$var$type = null;
let $610e71f8af383779$var$authorize = null;
let $610e71f8af383779$var$getIdToken = null;
let $610e71f8af383779$var$onAuthorize = null;
function $610e71f8af383779$export$d6bc6f4110e73ed6(options) {
    if ((0, $hgUW1$environment).commandMode === "no-view") return (fn)=>{
        const noViewFn = async (props)=>{
            if (!$610e71f8af383779$var$token) {
                $610e71f8af383779$var$token = options.personalAccessToken ?? await options.authorize();
                $610e71f8af383779$var$type = options.personalAccessToken ? "personal" : "oauth";
                const idToken = (await options.client?.getTokens())?.idToken;
                if (options.onAuthorize) await Promise.resolve(options.onAuthorize({
                    token: $610e71f8af383779$var$token,
                    type: $610e71f8af383779$var$type,
                    idToken: idToken
                }));
            }
            return fn(props);
        };
        return noViewFn;
    };
    return (Component)=>{
        const WrappedComponent = (props)=>{
            if (options.personalAccessToken) {
                $610e71f8af383779$var$token = options.personalAccessToken;
                $610e71f8af383779$var$type = "personal";
            } else {
                if (!$610e71f8af383779$var$authorize) $610e71f8af383779$var$authorize = $610e71f8af383779$var$wrapPromise(options.authorize());
                $610e71f8af383779$var$token = $610e71f8af383779$var$authorize.read();
                $610e71f8af383779$var$type = "oauth";
            }
            let idToken;
            if (options.client) {
                if (!$610e71f8af383779$var$getIdToken) $610e71f8af383779$var$getIdToken = $610e71f8af383779$var$wrapPromise(options.client.getTokens());
                idToken = $610e71f8af383779$var$getIdToken.read()?.idToken;
            }
            if (!$610e71f8af383779$var$onAuthorize && options.onAuthorize) $610e71f8af383779$var$onAuthorize = $610e71f8af383779$var$wrapPromise(Promise.resolve(options.onAuthorize({
                token: $610e71f8af383779$var$token,
                type: $610e71f8af383779$var$type,
                idToken: idToken
            })));
            $610e71f8af383779$var$onAuthorize?.read();
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore too complicated for TS
            return /*#__PURE__*/ (0, $hgUW1$jsx)(Component, {
                ...props
            });
        };
        WrappedComponent.displayName = `withAccessToken(${Component.displayName || Component.name})`;
        return WrappedComponent;
    };
}
function $610e71f8af383779$export$84773cc9bf7dbe87() {
    if (!$610e71f8af383779$var$token || !$610e71f8af383779$var$type) throw new Error("getAccessToken must be used when authenticated (eg. used inside `withAccessToken`)");
    return {
        token: $610e71f8af383779$var$token,
        type: $610e71f8af383779$var$type
    };
}
function $610e71f8af383779$var$wrapPromise(promise) {
    let status = "pending";
    let response;
    const suspender = promise.then((res)=>{
        status = "success";
        response = res;
    }, (err)=>{
        status = "error";
        response = err;
    });
    const read = ()=>{
        switch(status){
            case "pending":
                throw suspender;
            case "error":
                throw response;
            default:
                return response;
        }
    };
    return {
        read: read
    };
}






async function $179449bbef1d262b$export$8982d27dc997624f(script, optionsOrArgs, options) {
    const { humanReadableOutput: humanReadableOutput, language: language, timeout: timeout, ...execOptions } = Array.isArray(optionsOrArgs) ? options || {} : optionsOrArgs || {};
    const outputArguments = humanReadableOutput !== false ? [] : [
        "-ss"
    ];
    if (language === "JavaScript") outputArguments.push("-l", "JavaScript");
    if (Array.isArray(optionsOrArgs)) outputArguments.push("-", ...optionsOrArgs);
    const spawned = (0, $hgUW1$nodechild_process).spawn("osascript", outputArguments, {
        ...execOptions,
        env: {
            PATH: "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
        }
    });
    const spawnedPromise = (0, $c86e3701e284ece4$export$6e3a9b5342d42997)(spawned, {
        timeout: timeout || 10000
    });
    spawned.stdin.end(script);
    const [{ error: error, exitCode: exitCode, signal: signal, timedOut: timedOut }, stdoutResult, stderrResult] = await (0, $c86e3701e284ece4$export$67b768ac9e1c70fa)(spawned, {
        encoding: "utf8"
    }, spawnedPromise);
    const stdout = (0, $c86e3701e284ece4$export$200978bbc0b73ca)({
        stripFinalNewline: true
    }, stdoutResult);
    const stderr = (0, $c86e3701e284ece4$export$200978bbc0b73ca)({
        stripFinalNewline: true
    }, stderrResult);
    return (0, $c86e3701e284ece4$export$a8f5efe603803b77)({
        stdout: stdout,
        stderr: stderr,
        error: error,
        exitCode: exitCode,
        signal: signal,
        timedOut: timedOut,
        command: "osascript",
        options: options,
        parentError: new Error()
    });
}





export {$cefc05764ce5eacd$export$dd6b79aaabe7bc37 as usePromise, $c40d7eded38ca69c$export$14afb9e4c16377d3 as useCachedState, $a7f3824c7be647eb$export$b15740c74e256244 as useCachedPromise, $07de1abb43355e7b$export$d852f5f778460fa4 as useFetch, $915f026f98ccae1a$export$3f4d948c82873887 as useExec, $ba5a1846053848df$export$48c74caed1925dc8 as useStreamJSON, $e88ea8aee5e4e2a5$export$d74ef0af94fb1db6 as useSQL, $79498421851e7e84$export$87c0cf8eb5a167e0 as useForm, $79498421851e7e84$export$cd58ffd7e3880e66 as FormValidation, $571cbdb5f3bef8a7$export$835fc3bd312a97c as useAI, $ae6377cf02b7ee4b$export$5f452da9662a701d as useFrecencySorting, $a9c4f9ee71c485e4$export$86e2cef2561044ac as useLocalStorage, $7d691a802b89543d$export$378f247c814d3cc9 as getAvatarIcon, $d64c8f690cf3b07a$export$30c2a0095db95d06 as getFavicon, $a5607a6abce196a8$export$e7138cfc4fbdc77c as getProgressIcon, $a613cd7627dc664f$export$905bf243392675e9 as OAuthService, $610e71f8af383779$export$d6bc6f4110e73ed6 as withAccessToken, $610e71f8af383779$export$84773cc9bf7dbe87 as getAccessToken, $179449bbef1d262b$export$8982d27dc997624f as runAppleScript, $c718fd03aba6111c$export$80e5033e369189f3 as showFailureToast};
//# sourceMappingURL=module.js.map
