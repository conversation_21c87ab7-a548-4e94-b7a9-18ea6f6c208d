{"version": 3, "file": "useForm.d.ts", "sourceRoot": "", "sources": ["../src/useForm.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACpC,OAAO,EAA0C,cAAc,EAAE,MAAM,OAAO,CAAC;AAG/E;;GAEG;AACH,oBAAY,cAAc;IACxB,wDAAwD;IACxD,QAAQ,aAAa;CACtB;AAED,KAAK,eAAe,GAAG,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;AACjD,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS,GAAG,SAAS,KAAK,eAAe,CAAC,GAAG,cAAc,CAAC;AAkCjG,KAAK,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,MAAM,IAAI;KAAG,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;CAAE,CAAC;AAEhF,UAAU,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,MAAM;IACvC,0KAA0K;IAC1K,YAAY,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,IAAI,GAAG,OAAO,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;IACtE,6FAA6F;IAC7F,SAAS,EAAE;SACR,EAAE,IAAI,MAAM,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;YAC1D,EAAE,EAAE,MAAM,CAAC;SACZ;KACF,CAAC;IACF,4FAA4F;IAC5F,kBAAkB,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IAClE,uFAAuF;IACvF,QAAQ,EAAE,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAC1E,sCAAsC;IACtC,MAAM,EAAE,CAAC,CAAC;IACV,4EAA4E;IAC5E,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC;IAC7B,iEAAiE;IACjE,KAAK,EAAE,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;CAC7C;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkDG;AACH,iBAAS,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE;IAC7C,wFAAwF;IACxF,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,IAAI,GAAG,OAAO,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;IAClE,iEAAiE;IACjE,aAAa,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B;;;SAGK;IACL,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;CAC5B,GAAG,SAAS,CAAC,CAAC,CAAC,CAiHf;AAED,OAAO,EAAE,OAAO,EAAE,CAAC"}