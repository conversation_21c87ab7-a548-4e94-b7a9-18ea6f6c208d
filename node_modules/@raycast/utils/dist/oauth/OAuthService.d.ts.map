{"version": 3, "file": "OAuthService.d.ts", "sourceRoot": "", "sources": ["../../src/oauth/OAuthService.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AAErC,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAWtD,MAAM,WAAW,mBAAmB;IAClC,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IACzB,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,YAAY,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC;IACtC,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAClD,mBAAmB,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,KAAK,CAAC,aAAa,CAAC;IACjE,0BAA0B,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,KAAK,CAAC,aAAa,CAAC;CACzE;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,qBAAa,YAAa,YAAW,mBAAmB;IAC/C,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC;IACzB,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,YAAY,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC;IACtC,mBAAmB,CAAC,EAAE,MAAM,CAAC;IACpC,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAClD,mBAAmB,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,KAAK,CAAC,aAAa,CAAC;IAChE,0BAA0B,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,KAAK,CAAC,aAAa,CAAC;gBAE3D,OAAO,EAAE,mBAAmB;IAgBxC;;;;;;;OAOG;IACH,OAAc,KAAK,oFAAgB;IAEnC;;;;;;;OAOG;IACH,OAAc,MAAM,oFAAiB;IAErC;;;;;;;;;;;;OAYG;IACH,OAAc,MAAM,mEAAiB;IAErC;;;;;;;;;;;;OAYG;IACH,OAAc,IAAI,mEAAe;IAEjC;;;;;;;OAOG;IACH,OAAc,MAAM,oFAAiB;IAErC;;;;;;;OAOG;IACH,OAAc,KAAK,oFAAgB;IAEnC;;;;;;;;;;;;;OAaG;IACH,OAAc,IAAI,mEAAe;IAEjC;;;;;;OAMG;IACG,SAAS;YAmCD,WAAW;YA0CX,aAAa;CAkC5B"}