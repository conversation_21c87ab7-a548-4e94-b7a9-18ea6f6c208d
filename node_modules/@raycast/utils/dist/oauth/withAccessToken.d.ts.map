{"version": 3, "file": "withAccessToken.d.ts", "sourceRoot": "", "sources": ["../../src/oauth/withAccessToken.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAe,KAAK,EAAE,MAAM,cAAc,CAAC;AAElD,MAAM,MAAM,iBAAiB,GAAG;IAC9B,IAAI,EAAE,SAAS,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,KAAK,SAAS,GAAG,OAAO,GAAG,UAAU,CAAC;AAQtC,KAAK,yBAAyB,GAAG;IAC/B;;;OAGG;IACH,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;IAC1B;;;OAGG;IACH,SAAS,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC;IACjC;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,KAAK,IAAI,CAAC;CACnD,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,4BAA4B,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAEtH;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,eAAe,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAC9C,OAAO,EAAE,yBAAyB,GACjC,CAAC,CAAC,SAAS,4BAA4B,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9C,aAAa,EAAE,CAAC,KACb,CAAC,SAAS,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;AA2D9F;;;;;;;GAOG;AACH,wBAAgB,cAAc;;;EAM7B"}