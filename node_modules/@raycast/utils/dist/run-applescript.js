"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runAppleScript = void 0;
const node_child_process_1 = __importDefault(require("node:child_process"));
const exec_utils_1 = require("./exec-utils");
async function runAppleScript(script, optionsOrArgs, options) {
    const { humanReadableOutput, language, timeout, ...execOptions } = Array.isArray(optionsOrArgs)
        ? options || {}
        : optionsOrArgs || {};
    const outputArguments = humanReadableOutput !== false ? [] : ["-ss"];
    if (language === "JavaScript") {
        outputArguments.push("-l", "JavaScript");
    }
    if (Array.isArray(optionsOrArgs)) {
        outputArguments.push("-", ...optionsOrArgs);
    }
    const spawned = node_child_process_1.default.spawn("osascript", outputArguments, {
        ...execOptions,
        env: { PATH: "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin" },
    });
    const spawnedPromise = (0, exec_utils_1.getSpawnedPromise)(spawned, { timeout: timeout ?? 10000 });
    spawned.stdin.end(script);
    const [{ error, exitCode, signal, timedOut }, stdoutResult, stderrResult] = await (0, exec_utils_1.getSpawnedResult)(spawned, { encoding: "utf8" }, spawnedPromise);
    const stdout = (0, exec_utils_1.handleOutput)({ stripFinalNewline: true }, stdoutResult);
    const stderr = (0, exec_utils_1.handleOutput)({ stripFinalNewline: true }, stderrResult);
    return (0, exec_utils_1.defaultParsing)({
        stdout,
        stderr,
        error,
        exitCode,
        signal,
        timedOut,
        command: "osascript",
        options,
        parentError: new Error(),
    });
}
exports.runAppleScript = runAppleScript;
