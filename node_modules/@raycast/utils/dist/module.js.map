{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AEOO,SAAS,0CAAe,KAAQ;IACrC,MAAM,MAAM,CAAA,GAAA,aAAK,EAAK;IACtB,MAAM,YAAY,CAAA,GAAA,aAAK,EAAU;IAEjC,IAAI,CAAC,CAAA,GAAA,aAAK,EAAE,OAAO,IAAI,OAAO,GAAG;QAC/B,IAAI,OAAO,GAAG;QACd,UAAU,OAAO,IAAI;IACvB;IAEA,uDAAuD;IACvD,OAAO,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,OAAO,EAAE;QAAC,UAAU,OAAO;KAAC;AACvD;;;;ACXO,SAAS,0CAAa,KAAQ;IACnC,MAAM,MAAM,CAAA,GAAA,aAAK,EAAE;IACnB,IAAI,OAAO,GAAG;IACd,OAAO;AACT;;;;;;ACkBO,SAAS,0CAAiB,KAAc,EAAE,OAAiE;IAChH,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAChE,OAAO,CAAA,GAAA,gBAAQ,EAAE;QACf,OAAO,CAAA,GAAA,YAAI,EAAE,KAAK,CAAC,OAAO;QAC1B,OAAO,SAAS,SAAS;QACzB,SAAS;QACT,eAAe,SAAS,iBAAiB,6CAAuB;QAChE,iBAAiB,SAAS,gBAAgB,6CAAuB,SAAS;IAC5E;AACF;AAEA,MAAM,+CAAyB,CAAC;IAC9B,IAAI,mBAAmB;IACvB,IAAI,QAAQ;IACZ,IAAI,eAAe;IACnB,IAAI;QACF,MAAM,cAAc,KAAK,KAAK,CAAC,oBAAgB,YAAU,CAAA,GAAA,kBAAU,EAAE,UAAU,EAAE,MAAM,iBAAiB;QACxG,QAAQ,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,IAAI,CAAC;QACnC,eAAe,CAAC,oBAAoB,EAAE,YAAY,KAAK,IAAI,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,CAAC;QACnG,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,MAAM,KAAK,UAC/C,mBAAmB;IAEvB,EAAE,OAAO,KAAK;IACZ,QAAQ;IACV;IAEA,8EAA8E;IAC9E,uDAAuD;IACvD,MAAM,WAAW,CAAA,GAAA,kBAAU,EAAE,aAAa,IAAI;IAE9C,MAAM,QAAQ,iBAAiB,QAAQ,OAAO,SAAS,OAAO,WAAW,KAAK,OAAO;IAErF,OAAO;QACL,OAAO,WAAW,cAAc;QAChC,UAAS,KAAK;YACZ,MAAM,IAAI;YACV,IAAI,UACF,CAAA,GAAA,gBAAQ,EAAE,IAAI,CAAC;iBAEf,CAAA,GAAA,WAAG,EACD,CAAC,iHAAiH,EAAE,mBAClH,OACA,eAAe,EAAE,UAAU,cAAc,aAAa,EAAE,mBACxD,CAAC;;AAEb,EAAE,MAAM;;AAER,CAAC,EACW,CAAC;QAGT;IACF;AACF;;;AHsDO,SAAS,0CACd,EAAK,EACL,IAAoB,EACpB,OAA2B;IAE3B,MAAM,aAAa,CAAA,GAAA,aAAK,EAAE;IAC1B,MAAM,CAAC,OAAO,IAAI,GAAG,CAAA,GAAA,eAAO,EAA+B;QAAE,WAAW;IAAK;IAE7E,MAAM,QAAQ,CAAA,GAAA,yCAAQ,EAAE;IACxB,MAAM,kBAAkB,CAAA,GAAA,yCAAQ,EAAE,SAAS;IAC3C,MAAM,aAAa,CAAA,GAAA,yCAAQ,EAAE,QAAQ,EAAE;IACvC,MAAM,gBAAgB,CAAA,GAAA,yCAAQ,EAAE,SAAS;IACzC,MAAM,eAAe,CAAA,GAAA,yCAAQ,EAAE,SAAS;IACxC,MAAM,sBAAsB,CAAA,GAAA,yCAAQ,EAAE,SAAS;IAC/C,MAAM,cAAc,CAAA,GAAA,yCAAQ,EAAE,MAAM,IAAI;IACxC,MAAM,iBAAiB,CAAA,GAAA,aAAK;IAE5B,MAAM,oBAAoB,CAAA,GAAA,aAAK,EAAqB;QAAE,MAAM;IAAE;IAC9D,MAAM,mBAAmB,CAAA,GAAA,aAAK,EAAE;IAChC,MAAM,aAAa,CAAA,GAAA,aAAK,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,aAAK,EAAE;IAE3B,MAAM,WAAW,CAAA,GAAA,kBAAU,EACzB,CAAC,GAAG;QACF,MAAM,SAAS,EAAE,WAAW,OAAO;QAEnC,IAAI,gBAAgB,OAAO,EAAE;YAC3B,gBAAgB,OAAO,CAAC,OAAO,EAAE;YACjC,gBAAgB,OAAO,CAAC,OAAO,GAAG,IAAI;QACxC;QAEA,oBAAoB,OAAO,GAAG;QAE9B,IAAI,CAAC,YAAe,CAAA;gBAAE,GAAG,SAAS;gBAAE,WAAW;YAAK,CAAA;QAEpD,MAAM,4BAA4B,0CAAoB,MAAM,OAAO,KAAK;QAExE,SAAS,YAAY,KAAU;YAC7B,IAAI,MAAM,IAAI,IAAI,cAChB,OAAO;YAGT,IAAI,WAAW,WAAW,OAAO,EAAE;gBACjC,gBAAgB;gBAChB,IAAI,cAAc,OAAO,EACvB,cAAc,OAAO,CAAC;qBAEtB,IAAI,CAAA,GAAA,kBAAU,EAAE,UAAU,KAAK,CAAA,GAAA,iBAAS,EAAE,UAAU,EAClD,CAAA,GAAA,yCAAe,EAAE,OAAO;oBACtB,OAAO;oBACP,eAAe;wBACb,OAAO;wBACP,UAAS,KAAK;4BACZ,MAAM,IAAI;4BACV,eAAe,OAAO,MAAQ,WAAW,OAAO,IAAI,EAAE;wBACxD;oBACF;gBACF;gBAGJ,IAAI;2BAAE;oBAAO,WAAW;gBAAM;YAChC;YAEA,OAAO;QACT;QAEA,IAAI,OAAO,8BAA8B,YAAY;YACnD,iBAAiB,OAAO,GAAG;YAC3B,OAAO,0BAA0B,kBAAkB,OAAO,EAAE,IAAI,CAC9D,0CAA0C;YAC1C,CAAC,QAAE,IAAI,WAAE,OAAO,UAAE,MAAM,EAA6D;gBACnF,IAAI,WAAW,WAAW,OAAO,EAAE;oBACjC,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,kBAAkB,OAAO,CAAC,MAAM,GAAG;wBACnC,kBAAkB,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,MAAM,GAAG,EAAE;oBAC9D;oBAEA,IAAI,aAAa,OAAO,EACtB,aAAa,OAAO,CAAC,MAAM,kBAAkB,OAAO;oBAGtD,IAAI,SACF,YAAY,OAAO,GAAG,KAAK,MAAM;oBAEnC,WAAW,OAAO,GAAG;oBAErB,IAAI,CAAC;wBACH,IAAI,kBAAkB,OAAO,CAAC,IAAI,KAAK,GACrC,OAAO;kCAAE;4BAAM,WAAW;wBAAM;wBAElC,8CAA8C;wBAC9C,OAAO;4BAAE,MAAO,CAAA,aAAa,IAAI,IAAI,EAAE,AAAD,GAAI,OAAO;4BAAO,WAAW;wBAAM;oBAC3E;gBACF;gBAEA,OAAO;YACT,GACA,CAAC;gBACC,WAAW,OAAO,GAAG;gBACrB,OAAO,YAAY;YACrB;QAEJ;QAEA,iBAAiB,OAAO,GAAG;QAC3B,OAAO,0BAA0B,IAAI,CAAC,CAAC;YACrC,IAAI,WAAW,WAAW,OAAO,EAAE;gBACjC,IAAI,aAAa,OAAO,EACtB,aAAa,OAAO,CAAC;gBAEvB,IAAI;0BAAE;oBAAM,WAAW;gBAAM;YAC/B;YAEA,OAAO;QACT,GAAG;IACL,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAIH,eAAe,OAAO,GAAG;IAEzB,MAAM,aAAa,CAAA,GAAA,kBAAU,EAAE;QAC7B,uBAAuB;QACvB,kBAAkB,OAAO,GAAG;YAAE,MAAM;QAAE;QAEtC,MAAM,OAAQ,WAAW,OAAO,IAAI,EAAE;QACtC,OAAO,YAAY;IACrB,GAAG;QAAC;QAAU;KAAW;IAEzB,MAAM,SAAS,CAAA,GAAA,kBAAU,EACvB,OAAO,aAAa;QAClB,IAAI;QACJ,IAAI;YACF,IAAI,SAAS,kBAAkB;gBAC7B,IAAI,OAAO,SAAS,oBAAoB,cAAc,SAAS,oBAAoB,OACjF,uDAAuD;gBACvD,iFAAiF;gBACjF,6BAA6B,gBAAgB,YAAY,OAAO,EAAE;gBAEpE,MAAM,SAAS,QAAQ,gBAAgB;gBACvC,IAAI,CAAC,YAAe,CAAA;wBAAE,GAAG,SAAS;wBAAE,MAAM,OAAO,UAAU,IAAI;oBAAE,CAAA;YACnE;YACA,OAAO,MAAM;QACf,EAAE,OAAO,KAAK;YACZ,IAAI,OAAO,SAAS,oBAAoB,YAAY;gBAClD,MAAM,SAAS,QAAQ,eAAe;gBACtC,IAAI,CAAC,YAAe,CAAA;wBAAE,GAAG,SAAS;wBAAE,MAAM,OAAO,UAAU,IAAI;oBAAE,CAAA;YACnE,OAAO,IAAI,SAAS,oBAAoB,SAAS,oBAAoB,OACnE,IAAI,CAAC,YAAe,CAAA;oBAAE,GAAG,SAAS;oBAAE,MAAM;gBAA2B,CAAA;YAEvE,MAAM;QACR,SAAU;YACR,IAAI,SAAS,0BAA0B;gBACrC,IAAI,CAAA,GAAA,kBAAU,EAAE,UAAU,KAAK,CAAA,GAAA,iBAAS,EAAE,UAAU,IAAI,CAAA,GAAA,kBAAU,EAAE,WAAW,KAAK,YAClF,kFAAkF;gBAClF,gEAAgE;gBAChE,MAAM;qBAEN;;QAGN;IACF,GACA;QAAC;QAAY;QAAa;KAAI;IAGhC,MAAM,aAAa,CAAA,GAAA,kBAAU,EAAE;QAC7B,kBAAkB,OAAO,CAAC,IAAI,IAAI;QAClC,MAAM,OAAQ,WAAW,OAAO,IAAI,EAAE;QACtC,YAAY;IACd,GAAG;QAAC;QAAmB;QAAa;QAAY;KAAS;IAEzD,kCAAkC;IAClC,CAAA,GAAA,gBAAQ,EAAE;QACR,uBAAuB;QACvB,kBAAkB,OAAO,GAAG;YAAE,MAAM;QAAE;QAEtC,IAAI,SAAS,YAAY,OACvB,YAAc,QAAQ,EAAE;aAExB,kEAAkE;QAClE,IAAI,gBAAgB,OAAO,EACzB,gBAAgB,OAAO,CAAC,OAAO,EAAE;IAGrC,uDAAuD;IACzD,GAAG;QAAC,CAAA,GAAA,yCAAU,EAAE;YAAC;YAAM,SAAS;YAAS;SAAS;QAAG;QAAiB;KAAkB;IAExF,gCAAgC;IAChC,CAAA,GAAA,gBAAQ,EAAE;QACR,OAAO;YACL,IAAI,gBAAgB,OAAO,EACzB,uDAAuD;YACvD,gBAAgB,OAAO,CAAC,OAAO,EAAE;QAErC;IACF,GAAG;QAAC;KAAgB;IAEpB,yEAAyE;IACzE,MAAM,YAAY,SAAS,YAAY,QAAQ,MAAM,SAAS,GAAG;IAEjE,qEAAqE;IACrE,MAAM,wBAA4D;QAAE,GAAG,KAAK;mBAAE;IAAU;IAExF,MAAM,aAAa,iBAAiB,OAAO,GACvC;QACE,UAAU,YAAY,OAAO;QAC7B,SAAS,WAAW,OAAO;oBAC3B;IACF,IACA;IAEJ,OAAO;QAAE,GAAG,qBAAqB;oBAAE;gBAAY;oBAAQ;IAAW;AACpE;AAEA,yCAAyC,GACzC,SAAS,0CAAuB,EAAK;IACnC,IAAI,OAAQ,QAAQ,GAAG,EACrB,gCAAgC;IAChC,OAAO,GAAG,IAAI,CAAC;IAEjB,IAAI,OAAQ,QAAQ,IAAI,EACtB,gCAAgC;IAChC,OAAO,GAAG,IAAI,CAAC;IAEjB,IAAI,OAAQ,QAAQ,OAAO,EACzB,gCAAgC;IAChC,OAAO,GAAG,IAAI,CAAC;IAEjB,IAAI,OAAQ,QAAQ,MAAM,EACxB,gCAAgC;IAChC,OAAO,GAAG,IAAI,CAAC;IAEjB,OAAO;AACT;;;;;;AK7XA,8DAA8D;;;AACvD,SAAS,0CAAoB,GAAW,EAAE,MAAe;IAC9D,MAAM,QAAQ,IAAI,CAAC,IAAI;IACvB,IAAI,iBAAiB,MACnB,OAAO,CAAC,uBAAuB,EAAE,MAAM,QAAQ,GAAG,CAAC;IAErD,IAAI,iCAAO,QAAQ,CAAC,QAClB,OAAO,CAAC,yBAAyB,EAAE,MAAM,QAAQ,CAAC,UAAU,CAAC;IAE/D,OAAO;AACT;AAEO,SAAS,0CAAQ,IAAY,EAAE,KAAc;IAClD,IAAI,OAAO,UAAU,YAAY,MAAM,UAAU,CAAC,4BAChD,OAAO,IAAI,KAAK,MAAM,OAAO,CAAC,2BAA2B;IAE3D,IAAI,OAAO,UAAU,YAAY,MAAM,UAAU,CAAC,8BAChD,OAAO,iCAAO,IAAI,CAAC,MAAM,OAAO,CAAC,6BAA6B,KAAK;IAErE,OAAO;AACT;;;ADfA,MAAM,kCAAY,aAAa,GAAG,OAAO;AACzC,MAAM,iCAAW,aAAa,GAAG,IAAI;AAgB9B,SAAS,0CACd,GAAW,EACX,YAAgB,EAChB,MAAoC;IAEpC,MAAM,WAAW,QAAQ,kBAAkB;IAC3C,MAAM,QACJ,+BAAS,GAAG,CAAC,aAAa,+BAAS,GAAG,CAAC,UAAU,IAAI,CAAA,GAAA,YAAI,EAAE;QAAE,WAAW,QAAQ;IAAe,IAAI,GAAG,CAAC;IAEzG,IAAI,CAAC,OACH,MAAM,IAAI,MAAM;IAGlB,MAAM,SAAS,CAAA,GAAA,yCAAQ,EAAE;IACzB,MAAM,kBAAkB,CAAA,GAAA,yCAAQ,EAAE;IAElC,MAAM,cAAc,CAAA,GAAA,2BAAmB,EAAE,MAAM,SAAS,EAAE;QACxD,IAAI;YACF,OAAO,MAAM,GAAG,CAAC,OAAO,OAAO;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF;IAEA,MAAM,QAAQ,CAAA,GAAA,cAAM,EAAE;QACpB,IAAI,OAAO,gBAAgB,aAAa;YACtC,IAAI,gBAAgB,aAClB,OAAO;YAET,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC,aAAa,CAAA,GAAA,yCAAM;YACvC,EAAE,OAAO,KAAK;gBACZ,iCAAiC;gBACjC,QAAQ,IAAI,CAAC,gCAAgC;gBAC7C,OAAO,gBAAgB,OAAO;YAChC;QACF,OACE,OAAO,gBAAgB,OAAO;IAElC,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,WAAW,CAAA,GAAA,yCAAQ,EAAE;IAE3B,MAAM,mBAAmB,CAAA,GAAA,kBAAU,EACjC,CAAC;QACC,wFAAwF;QACxF,MAAM,WAAW,OAAO,YAAY,aAAa,QAAQ,SAAS,OAAO,IAAI;QAC7E,IAAI,OAAO,aAAa,aACtB,MAAM,GAAG,CAAC,OAAO,OAAO,EAAE;aACrB;YACL,MAAM,mBAAmB,KAAK,SAAS,CAAC,UAAU,CAAA,GAAA,yCAAO;YACzD,MAAM,GAAG,CAAC,OAAO,OAAO,EAAE;QAC5B;QACA,OAAO;IACT,GACA;QAAC;QAAO;QAAQ;KAAS;IAG3B,OAAO;QAAC;QAAO;KAAiB;AAClC;;;;;;;;AEnEA,0DAA0D;AAC1D,MAAM,mCAAa,aAAa,GAAG;AAmH5B,SAAS,0CAId,EAAK,EAAE,IAAoB,EAAE,OAAoC;IACjE;;;;;;GAMC,GACD,MAAM,eACJ,WAAW,oBACX,gBAAgB,2BAChB,uBAAuB,EACvB,GAAG,mBACJ,GAAsE,WAAW,CAAC;IACnF,MAAM,iBAAiB,CAAA,GAAA,aAAK;IAE5B,MAAM,CAAC,YAAY,YAAY,GAAG,CAAA,GAAA,yCAAa,EAC7C,CAAA,GAAA,iBAAG,EAAE,QAAQ,EAAE,IAAK,CAAA,2BAA2B,EAAC,GAChD,kCACA;QACE,gBAAgB,CAAA,GAAA,iBAAG,EAAE;IACvB;IAGF,qGAAqG;IACrG,MAAM,eAAe,CAAA,GAAA,aAAK,EAA8B,eAAe,mCAAa,aAAc;IAClG,MAAM,oBAAoB,CAAA,GAAA,aAAK,EAAsD;IAErF,MAAM,EACJ,QAAQ,OAAO,cACf,UAAU,EACV,GAAG,OAGJ,GAAG,CAAA,GAAA,yCAAS,EAAE,IAAI,QAAS,EAAE,EAA2B;QACvD,GAAG,iBAAiB;QACpB,QAAO,IAAI,EAAE,UAAU;YACrB,kBAAkB,OAAO,GAAG;YAC5B,IAAI,kBAAkB,MAAM,EAC1B,kBAAkB,MAAM,CAAC,MAAM;YAEjC,IAAI,cAAc,WAAW,IAAI,GAAG,GAClC,oCAAoC;YACpC;YAEF,eAAe,OAAO,GAAG;YACzB,aAAa,OAAO,GAAG;YACvB,YAAY;QACd;IACF;IAEA,IAAI;IACJ,MAAM,aAAa,MAAM,UAAU;IACnC,4GAA4G;IAC5G,0BAA0B;IAC1B,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,CAAC,IAAI,GAAG,KAAK,MAAM,IAAI,EAC/E,eAAe,MAAM,IAAI;SAEpB,IAAI,eAAe,OAAO,KAAK,WACpC,eAAe,aAAa,OAAO;SAC9B,IAAI,oBAAoB,eAAe,kCAAY;QACxD,mFAAmF;QACnF,eAAe;QACf,IAAI,YAAY;YACd,WAAW,OAAO,GAAG;YACrB,WAAW,QAAQ,GAAG,WAAW,MAAM;QACzC;IACF,OAAO,IAAI,oBAAoB,eAAe,kCAC5C,0DAA0D;IAC1D,eAAe,aAAa,OAAO;SAE9B,IAAI,eAAe,kCAAY;QACpC,eAAe;QACf,IAAI,YAAY;YACd,WAAW,OAAO,GAAG;YACrB,WAAW,QAAQ,GAAG,WAAW,MAAM;QACzC;IACF,OACE,eAAe;IAGjB,MAAM,aAAa,CAAA,GAAA,yCAAQ,EAAE;IAE7B,6DAA6D;IAC7D,MAAM,SAAS,CAAA,GAAA,kBAAU,EACvB,OAAO,aAAa;QAClB,IAAI;QACJ,IAAI;YACF,IAAI,SAAS,kBAAkB;gBAC7B,IAAI,OAAO,SAAS,oBAAoB,cAAc,SAAS,oBAAoB,OACjF,uDAAuD;gBACvD,iFAAiF;gBACjF,6BAA6B,gBAAgB,WAAW,OAAO;gBAEjE,MAAM,OAAO,QAAQ,gBAAgB,CAAC,WAAW,OAAO;gBACxD,eAAe,OAAO,GAAG;gBACzB,aAAa,OAAO,GAAG;gBACvB,YAAY;YACd;YACA,OAAO,MAAM,QAAQ,aAAa;gBAAE,uBAAuB,SAAS;YAAsB;QAC5F,EAAE,OAAO,KAAK;YACZ,IAAI,OAAO,SAAS,oBAAoB,YAAY;gBAClD,MAAM,OAAO,QAAQ,eAAe,CAAC,WAAW,OAAO;gBACvD,eAAe,OAAO,GAAG;gBACzB,aAAa,OAAO,GAAG;gBACvB,YAAY;YACd,OAAO,IAAI,SAAS,oBAAoB,SAAS,oBAAoB,OAAO;gBAC1E,eAAe,OAAO,GAAG;gBACzB,iDAAiD;gBACjD,aAAa,OAAO,GAAG;gBACvB,iDAAiD;gBACjD,YAAY;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;QAAa;QAAS;QAAY;QAAc;KAAe;IAGlE,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,eAAe,kCAAY;YAC7B,eAAe,OAAO,GAAG;YACzB,aAAa,OAAO,GAAG;QACzB;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL,MAAM;QACN,WAAW,MAAM,SAAS;QAC1B,OAAO,MAAM,KAAK;QAClB,QAAQ,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,CAAC,IAAI,GAAG,IAAI,UAAU;oBACpF;oBACA;IACF;AACF;;;;;;;;AE7QO,SAAS,0CAAO,iBAA4C;IACjE,IAAI,mBAAmB;QACrB,MAAM,YAAY,uCAAiB;QAEnC,IAAI,UAAU,OAAO,KAAK,QACxB,OAAO;QAGT,IAAI,UAAU,MAAM,KAAK,QACvB,OAAO;QAGT,IAAI,UAAU,MAAM,IAAI,YAAY,IAAI,CAAC,UAAU,MAAM,GACvD,OAAO;QAGT,IAAI,UAAU,OAAO,IAAI,YAAY,IAAI,CAAC,UAAU,OAAO,GACzD,OAAO;IAEX;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,MAAM,uCAAiB;AAEvB;;;;;;;;;;;;;;;CAeC,GACD,MAAM,0CAAoB;AAE1B,SAAS,uCAAiB,MAAc;IACtC,MAAM,0BAA0B,OAAO,OAAO,CAAC;IAC/C,MAAM,cAAc,4BAA4B,KAAK,OAAO,KAAK,CAAC,GAAG,yBAAyB,IAAI,KAAK,OAAO,IAAI;IAElH,IAAI,CAAC,qCAAe,IAAI,CAAC,cACvB,MAAM,IAAI,UAAU;IAGtB,MAAM,QAAQ,wCAAkB,IAAI,CAAC,YAAY,WAAW,GAAG,WAAW;IAE1E,IAAI,CAAC,OACH,MAAM,IAAI,UAAU;IAGtB,MAAM,OAAO,KAAK,CAAC,EAAE;IACrB,IAAI,UAAU,KAAK,CAAC,EAAE;IACtB,IAAI;IAEJ,sBAAsB;IACtB,MAAM,QAAQ,QAAQ,WAAW,CAAC;IAClC,IAAI,UAAU,IAAI;QAChB,SAAS,QAAQ,SAAS,CAAC,QAAQ;QACnC,UAAU,QAAQ,SAAS,CAAC,GAAG;IACjC;IAEA,OAAO;cAAE;iBAAM;gBAAS;IAAO;AACjC;;;ADpEA,eAAe,qCAAe,QAAkB;IAC9C,IAAI,CAAC,SAAS,EAAE,EACd,MAAM,IAAI,MAAM,SAAS,UAAU;IAGrC,MAAM,oBAAoB,SAAS,OAAO,CAAC,GAAG,CAAC;IAE/C,IAAI,qBAAqB,CAAA,GAAA,yCAAK,EAAE,oBAC9B,OAAO,MAAM,SAAS,IAAI;IAE5B,OAAO,MAAM,SAAS,IAAI;AAC5B;AAEA,SAAS,qCAAuC,MAAS;IACvD,OAAO;QAAE,MAAM;QAAwB,SAAS;IAAM;AACxD;AAyFO,SAAS,0CACd,GAAuC,EACvC,OAGuG;IAEvG,MAAM,iBACJ,aAAa,aACb,SAAS,eACT,WAAW,WACX,OAAO,oBACP,gBAAgB,WAChB,OAAO,UACP,MAAM,iBACN,aAAa,EACb,GAAG,cACJ,GAAG,WAAW,CAAC;IAEhB,MAAM,0BAA4G;qBAChH;iBACA;0BACA;iBACA;gBACA;uBACA;IACF;IAEA,MAAM,mBAAmB,CAAA,GAAA,yCAAQ,EAAE,iBAAiB;IACpD,MAAM,eAAe,CAAA,GAAA,yCAAQ,EAAE,aAAa;IAC5C,MAAM,SAAS,CAAA,GAAA,aAAK;IACpB,MAAM,kBAAkB,CAAA,GAAA,aAAK;IAC7B,MAAM,eAAe,OAAO,QAAQ,aAAa,IAAI;QAAE,MAAM;IAAE,KAAK;IACpE;;;GAGC,GACD,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,gBAAgB,OAAO,KAAK,eAAe,gBAAgB,OAAO,KAAK,cACnG,OAAO,OAAO,GAAG;IAEnB,gBAAgB,OAAO,GAAG;IAC1B,MAAM,YAAY,CAAA,GAAA,aAAK;IAEvB,MAAM,cAAiG,CAAA,GAAA,kBAAU,EAC/G,CAAC,KAA2B,UAA0B,OAAO;YAC3D,MAAM,MAAM,MAAM,CAAA,GAAA,mBAAI,EAAE,IAAI,aAAa;gBAAE,QAAQ,UAAU,OAAO,EAAE;gBAAQ,GAAG,OAAO;YAAC;YACzF,MAAM,SAAU,MAAM,iBAAiB,OAAO,CAAC;YAC/C,OAAO,aAAa,OAAO,GAAG;QAChC,GACA;QAAC;QAAkB;KAAa;IAElC,MAAM,KAA+D,CAAA,GAAA,kBAAU,EAC7E,OAAO,KAAkB;QACvB,MAAM,MAAM,MAAM,CAAA,GAAA,mBAAI,EAAE,KAAK;YAAE,QAAQ,UAAU,OAAO,EAAE;YAAQ,GAAG,OAAO;QAAC;QAC7E,MAAM,SAAU,MAAM,iBAAiB,OAAO,CAAC;QAC/C,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,OAAO,QAAQ;IACjB,GACA;QAAC;QAAkB;KAAa;IAGlC,MAAM,UAAU,CAAA,GAAA,cAAM,EAAE;QACtB,IAAI,gBAAgB,OAAO,EACzB,OAAO;QAET,OAAO;IACT,GAAG;QAAC;QAAiB;QAAI;KAAY;IAErC,uDAAuD;IACvD,OAAO,CAAA,GAAA,yCAAe,EAAE,SAAS;QAAC,OAAO,OAAO;QAA0B;KAAa,EAAE;QACvF,GAAG,uBAAuB;QAC1B,yBAAyB,gBAAgB,OAAO,GAAG,CAAA,GAAA,iBAAG,EAAE,aAAa,OAAO,IAAI,CAAA,GAAA,iBAAG,EAAE,iBAAiB,OAAO;mBAC7G;IACF;AACF;;;AE1LA;;CAEC;;;;;;;;;;ACUM,SAAS,0CACd,OAAoD,EACpD,WAAE,OAAO,EAAwB,GAAG,CAAC,CAAC;IAEtC,MAAM,UAAE,MAAM,EAAE,GAAG;IACnB,MAAM,iBAAiC,IAAI,QAAQ,CAAC,SAAS;QAC3D,QAAQ,EAAE,CAAC,QAAQ,CAAC,UAAU;YAC5B,QAAQ;0BAAE;wBAAU;gBAAQ,UAAU;YAAM;QAC9C;QAEA,QAAQ,EAAE,CAAC,SAAS,CAAC;YACnB,OAAO;QACT;QAEA,IAAI,QAAQ,KAAK,EACf,QAAQ,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC;YACzB,OAAO;QACT;IAEJ;IAEA,MAAM,oBAAoB,OAAO;QAC/B,QAAQ,IAAI;IACd;IAEA,IAAI,YAAY,KAAK,YAAY,WAC/B,OAAO,eAAe,OAAO,CAAC,IAAM;IAGtC,IAAI;IACJ,MAAM,iBAAiC,IAAI,QAAQ,CAAC,UAAU;QAC5D,YAAY,WAAW;YACrB,QAAQ,IAAI,CAAC;YACb,OAAO,OAAO,MAAM,CAAC,IAAI,MAAM,cAAc;gBAAE,UAAU;gBAAM,QAAQ;YAAU;QACnF,GAAG;IACL;IAEA,MAAM,qBAAqB,eAAe,OAAO,CAAC;QAChD,aAAa;IACf;IAEA,OAAO,QAAQ,IAAI,CAAC;QAAC;QAAgB;KAAmB,EAAE,OAAO,CAAC,IAAM;AAC1E;AAEA,MAAM,6CAAuB;IAC3B,aAAc;QACZ,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,SAAS,mCAAwC,OAAgD;IAC/F,MAAM,YAAE,QAAQ,EAAE,GAAG;IACrB,MAAM,WAAW,aAAa;IAE9B,2DAA2D;IAC3D,MAAM,SACJ,IAAI,CAAA,GAAA,iBAAK,EAAE,WAAW,CAAC;QAAE,YAAY;IAAM;IAE7C,IAAI,YAAY,aAAa,UAC3B,OAAO,WAAW,CAAC;IAGrB,IAAI,SAAS;IACb,MAAM,SAAgB,EAAE;IAExB,OAAO,EAAE,CAAC,QAAQ,CAAC;QACjB,OAAO,IAAI,CAAC;QAEZ,UAAU,MAAM,MAAM;IACxB;IAEA,OAAO,gBAAgB,GAAG;QACxB,OAAQ,WAAW,iCAAO,MAAM,CAAC,QAAQ,UAAU,OAAO,IAAI,CAAC;IACjE;IAEA,OAAO,iBAAiB,GAAG,IAAM;IAEjC,OAAO;AACT;AAEA,eAAe,gCACb,WAA4B,EAC5B,OAAgD;IAEhD,MAAM,SAAS,mCAAgB;IAE/B,MAAM,IAAI,QAAc,CAAC,SAAS;QAChC,MAAM,gBAAgB,CAAC;YACrB,sCAAsC;YACtC,IAAI,SAAS,OAAO,iBAAiB,MAAM,CAAA,GAAA,gBAAc,EAAE,UAAU,EACnE,MAAM,YAAY,GAAG,OAAO,gBAAgB;YAG9C,OAAO;QACT;QAEC,CAAA;YACC,IAAI;gBACF,MAAM,CAAA,GAAA,gBAAQ,EAAE,CAAA,GAAA,iBAAK,EAAE,QAAQ,EAAE,aAAa;gBAC9C;YACF,EAAE,OAAO,OAAO;gBACd,cAAc;YAChB;QACF,CAAA;QAEA,OAAO,EAAE,CAAC,QAAQ;YAChB,OAAO;YACP,IAAI,OAAO,iBAAiB,KAAK,UAC/B,cAAc,IAAI;QAEtB;IACF;IAEA,OAAO,OAAO,gBAAgB;AAChC;AAEA,kFAAkF;AAClF,eAAe,sCAA2C,MAAuB,EAAE,aAAyB;IAC1G,OAAO,OAAO;IAEd,IAAI;QACF,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,OAAO,AAAC,MAAqC,YAAY;IAC3D;AACF;AAEO,eAAe,0CACpB,UAAE,MAAM,UAAE,MAAM,EAA+C,EAC/D,YAAE,QAAQ,EAA2C,EACrD,WAA2B;IAE3B,MAAM,gBAAgB,gCAAa,QAAQ;kBAAE;IAAS;IACtD,MAAM,gBAAgB,gCAAa,QAAQ;kBAAE;IAAS;IAEtD,IAAI;QACF,OAAO,MAAM,QAAQ,GAAG,CAAC;YAAC;YAAa;YAAe;SAAc;IACtE,EAAE,OAAO,OAAY;QACnB,OAAO,QAAQ,GAAG,CAAC;YACjB;gBACE,OAAO;gBACP,UAAU;gBACV,QAAQ,MAAM,MAAM;gBACpB,UAAU,AAAC,MAAM,QAAQ,IAAgB;YAC3C;YACA,sCAAgB,QAAQ;YACxB,sCAAgB,QAAQ;SACzB;IACH;AACF;AAEA,SAAS,wCAA6C,KAAQ;IAC5D,MAAM,KAAK,OAAO,UAAU,WAAW,OAAO,KAAK,UAAU,CAAC;IAC9D,MAAM,KAAK,OAAO,UAAU,WAAW,OAAO,KAAK,UAAU,CAAC;IAE9D,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,IAC9B,sDAAsD;IACtD,QAAQ,MAAM,KAAK,CAAC,GAAG;IAGzB,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,IAC9B,sDAAsD;IACtD,QAAQ,MAAM,KAAK,CAAC,GAAG;IAGzB,OAAO;AACT;AAEO,SAAS,yCAAwC,OAAwC,EAAE,KAAQ;IACxG,IAAI,QAAQ,iBAAiB,EAC3B,OAAO,wCAAkB;IAG3B,OAAO;AACT;AAEA,SAAS,qCAAe,YACtB,QAAQ,WACR,OAAO,UACP,MAAM,YACN,QAAQ,EAMT;IACC,IAAI,UACF,OAAO,CAAC,gBAAgB,EAAE,QAAQ,aAAa,CAAC;IAGlD,IAAI,WAAW,aAAa,WAAW,MACrC,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC;IAGpC,IAAI,aAAa,aAAa,aAAa,MACzC,OAAO,CAAC,sBAAsB,EAAE,SAAS,CAAC;IAG5C,OAAO;AACT;AAEA,SAAS,gCAAU,UACjB,MAAM,UACN,MAAM,SACN,KAAK,UACL,MAAM,YACN,QAAQ,WACR,OAAO,YACP,QAAQ,WACR,OAAO,eACP,WAAW,EAWZ;IACC,MAAM,SAAS,qCAAe;kBAAE;QAAU,SAAS,SAAS;gBAAS;kBAAQ;IAAS;IACtF,MAAM,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC;IACpD,MAAM,eAAe,QAAQ,CAAC,EAAE,aAAa,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,GAAG;IACnE,MAAM,UAAU;QAAC;QAAc;QAAQ;KAAO,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEpE,IAAI,OACF,gCAAgC;IAChC,MAAM,eAAe,GAAG,MAAM,OAAO;SAErC,QAAQ;IAGV,MAAM,OAAO,GAAG;IAEhB,gCAAgC;IAChC,MAAM,YAAY,GAAG;IACrB,gCAAgC;IAChC,MAAM,OAAO,GAAG;IAChB,gCAAgC;IAChC,MAAM,QAAQ,GAAG;IACjB,gCAAgC;IAChC,MAAM,MAAM,GAAG;IACf,gCAAgC;IAChC,MAAM,MAAM,GAAG;IACf,gCAAgC;IAChC,MAAM,MAAM,GAAG;IAEf,IAAI,kBAAkB,OACpB,OAAO,KAAK,CAAC,eAAe;IAG9B,OAAO;AACT;AA2BO,SAAS,0CAA0C,UACxD,MAAM,UACN,MAAM,SACN,KAAK,YACL,QAAQ,UACR,MAAM,YACN,QAAQ,WACR,OAAO,WACP,OAAO,eACP,WAAW,EAWZ;IACC,IAAI,SAAS,aAAa,KAAK,WAAW,MAAM;QAC9C,MAAM,gBAAgB,gCAAU;mBAC9B;sBACA;oBACA;oBACA;oBACA;qBACA;sBACA;qBACA;yBACA;QACF;QAEA,MAAM;IACR;IAEA,OAAO;AACT;;;;AD/QA,MAAM,sCAAgB;AACtB,SAAS,mCAAa,OAAe,EAAE,IAAe;IACpD,IAAI,MACF,OAAO;QAAC;WAAY;KAAK;IAE3B,MAAM,SAAmB,EAAE;IAC3B,KAAK,MAAM,SAAS,QAAQ,IAAI,GAAG,KAAK,CAAC,qCAAgB;QACvD,wEAAwE;QACxE,MAAM,gBAAgB,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAC/C,IAAI,iBAAiB,cAAc,QAAQ,CAAC,OAC1C,wCAAwC;QACxC,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,GAAG,CAAC,EAAE,cAAc,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC;aAEpE,OAAO,IAAI,CAAC;IAEhB;IAEA,OAAO;AACT;AA+EO,SAAS,0CACd,OAAe,EACf,aAKqC,EACrC,OAGgC;IAEhC,MAAM,eAAE,WAAW,SAAE,KAAK,UAAE,MAAM,iBAAE,aAAa,eAAE,WAAW,WAAE,OAAO,oBAAE,gBAAgB,WAAE,OAAO,EAAE,GAAG,aAAa,GAClH,MAAM,OAAO,CAAC,iBAAiB,WAAW,CAAC,IAAI,iBAAiB,CAAC;IAEnE,MAAM,0BAA0D;qBAC9D;iBACA;0BACA;iBACA;gBACA;uBACA;IACF;IAEA,MAAM,YAAY,CAAA,GAAA,aAAK;IACvB,MAAM,iBAAiB,CAAA,GAAA,yCAAQ,EAAE,eAAe,CAAA,GAAA,yCAAa;IAE7D,MAAM,KAAK,CAAA,GAAA,kBAAU,EACnB,OAAO,UAAkB,OAAiB,UAAwB;QAChE,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,mCAAa,UAAU;QAC/C,MAAM,UAAU;YAAC;eAAS;SAAK,CAAC,IAAI,CAAC;QAErC,MAAM,UAAU;YACd,mBAAmB;YACnB,GAAG,QAAQ;YACX,SAAS,UAAU,WAAW;YAC9B,QAAQ,UAAU,OAAO,EAAE;YAC3B,UAAU,UAAU,aAAa,OAAO,WAAW,UAAU,YAAY;YACzE,KAAK;gBAAE,MAAM;gBAAgD,GAAG,UAAW;gBAAE,GAAG,UAAU,GAAG;YAAC;QAChG;QAEA,MAAM,UAAU,CAAA,GAAA,wBAAW,EAAE,KAAK,CAAC,MAAM,MAAM;QAC/C,MAAM,iBAAiB,CAAA,GAAA,yCAAgB,EAAE,SAAS;QAElD,IAAI,OACF,QAAQ,KAAK,CAAC,GAAG,CAAC;QAGpB,MAAM,CAAC,SAAE,KAAK,YAAE,QAAQ,UAAE,MAAM,YAAE,QAAQ,EAAE,EAAE,cAAc,aAAa,GAAG,MAAM,CAAA,GAAA,yCAAe,EAC/F,SACA,SACA;QAEF,MAAM,SAAS,CAAA,GAAA,wCAAW,EAAE,SAAS;QACrC,MAAM,SAAS,CAAA,GAAA,wCAAW,EAAE,SAAS;QAErC,OAAO,eAAe,OAAO,CAAC;oBAE5B,AADA,gDAAgD;YAChD;oBAEA,AADA,gDAAgD;YAChD;mBACA;sBACA;oBACA;sBACA;qBACA;qBACA;YACA,aAAa,IAAI;QACnB;IACF,GACA;QAAC;KAAe;IAGlB,kEAAkE;IAClE,OAAO,CAAA,GAAA,yCAAe,EAAE,IAAI;QAAC;QAAS,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;QAAE;QAAa;KAAM,EAAE;QAC5G,GAAG,uBAAuB;mBAC1B;IACF;AACF;;;;;;;;;;;;;;;;;AE9NA,eAAe,4BAAM,GAAgB,EAAE,WAAmB,EAAE,YAA0B;IACpF,IAAI,OAAO,QAAQ,YAAY,IAAI,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,aACzE,OAAO,MAAM,+BAAS,KAAK,aAAa;SACnC,IAAI,IAAI,UAAU,CAAC,YACxB,OAAO,MAAM,gCACX,CAAA,GAAA,gBAAQ,EAAE,mBAAmB,IAAI,IAAI,KAAK,QAAQ,IAClD,aACA,cAAc,SAAU,aAAa,MAAM,GAAmB;SAGhE,MAAM,IAAI,MAAM;AAEpB;AAEA,eAAe,+BAAS,GAAgB,EAAE,WAAmB,EAAE,YAA0B;IACvF,MAAM,WAAW,MAAM,CAAA,GAAA,mBAAI,EAAE,KAAK;IAElC,IAAI,CAAC,SAAS,EAAE,EACd,MAAM,IAAI,MAAM;IAGlB,IAAI,CAAC,CAAA,GAAA,yCAAK,EAAE,SAAS,OAAO,CAAC,GAAG,CAAC,kBAC/B,MAAM,IAAI,MAAM;IAElB,IAAI,CAAC,SAAS,IAAI,EAChB,MAAM,IAAI,MAAM;IAElB,MAAM,CAAA,GAAA,eAAO,EACX,SAAS,IAAI,EACb,CAAA,GAAA,wBAAgB,EAAE,cAClB,cAAc,SAAS;QAAE,QAAQ,aAAa,MAAM;IAAgB,IAAI;AAE5E;AAEA,eAAe,gCAAU,MAAc,EAAE,WAAmB,EAAE,WAAyB;IACrF,MAAM,CAAA,GAAA,eAAO,EACX,CAAA,GAAA,uBAAe,EAAE,SACjB,CAAA,GAAA,wBAAgB,EAAE,cAClB,cAAc;QAAE,QAAQ;IAAY,IAAI;AAE5C;AAEA,eAAe,0CACb,GAAgB,EAChB,MAAc,EACd,QAAgB,EAChB,WAAoB,EACpB,YAA0B;IAE1B,MAAM,cAAc,CAAA,GAAA,WAAG,EAAE,QAAQ;IAEjC,IAAI;QACF,MAAM,CAAA,GAAA,WAAG,EAAE;IACb,EAAE,OAAO,GAAG;QACV,CAAA,GAAA,gBAAQ,EAAE,QAAQ;YAAE,WAAW;QAAK;QACpC,MAAM,4BAAM,KAAK,aAAa;QAC9B;IACF;IACA,IAAI,aAAa;QACf,MAAM,4BAAM,KAAK,aAAa;QAC9B;IACF;IAEA,IAAI,QAA2B;IAC/B,IAAI;QACF,QAAQ,MAAM,CAAA,GAAA,WAAG,EAAE;IACrB,EAAE,OAAO,GAAG;QACV,MAAM,4BAAM,KAAK,aAAa;QAC9B;IACF;IAEA,IAAI,OAAO,QAAQ,YAAY,IAAI,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,aAAa;QACtF,MAAM,eAAe,MAAM,CAAA,GAAA,mBAAI,EAAE,KAAK;YAAE,GAAG,YAAY;YAAE,QAAQ;QAAO;QACxE,IAAI,CAAC,aAAa,EAAE,EAClB,MAAM,IAAI,MAAM;QAGlB,IAAI,CAAC,CAAA,GAAA,yCAAK,EAAE,aAAa,OAAO,CAAC,GAAG,CAAC,kBACnC,MAAM,IAAI,MAAM;QAGlB,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,GAAG,CAAC,oBAAoB;QAC7E,IAAI,MAAM,IAAI,KAAK,KAAK,OAAO,KAAK,CAAC,iBAAiB,eAAe,MAAM,OAAO,EAAE;YAClF,MAAM,4BAAM,KAAK,aAAa;YAC9B;QACF;IACF,OAAO,IAAI,IAAI,UAAU,CAAC,YACxB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,WAAG,EAAE,CAAA,GAAA,gBAAQ,EAAE,mBAAmB,IAAI,IAAI,KAAK,QAAQ;QACjF,IAAI,YAAY,OAAO,GAAG,MAAM,OAAO,EACrC,MAAM,4BAAM,KAAK,aAAa;IAElC,EAAE,OAAO,GAAG;QACV,MAAM,IAAI,MAAM;IAClB;SAEA,MAAM,IAAI,MAAM;AAEpB;AAEA,gBAAgB,qCACd,QAAgB,EAChB,QAAgB,EAChB,WAAyB,EACzB,QAA0B,EAC1B,QAAwC,EACxC,WAA8B;IAE9B,IAAI,OAAsC,EAAE;IAE5C,MAAM,WAAW,IAAI,CAAA,GAAA,kBAAI,EAAE;QACzB,CAAA,GAAA,uBAAe,EAAE;QACjB,WAAW,CAAA,GAAA,4BAAG,EAAE,UAAU,CAAC;YAAE,QAAQ;QAAS,KAAK,CAAA,GAAA,aAAK;QACxD,IAAI,CAAA,GAAA,qCAAU;QACd,CAAC,OAAS,cAAc,KAAK,KAAK,KAAK,KAAK,KAAK;KAClD;IAED,aAAa,iBAAiB,SAAS;QACrC,SAAS,OAAO;IAClB;IAEA,IAAI;QACF,WAAW,MAAM,QAAQ,SAAU;YACjC,IAAI,aAAa,SACf,OAAO,EAAE;YAEX,IAAI,CAAC,YAAY,SAAS,OACxB,KAAK,IAAI,CAAC;YAEZ,IAAI,KAAK,MAAM,IAAI,UAAU;gBAC3B,MAAM;gBACN,OAAO,EAAE;YACX;QACF;IACF,EAAE,OAAO,GAAG;QACV,SAAS,OAAO;QAChB,MAAM;IACR;IAEA,IAAI,KAAK,MAAM,GAAG,GAChB,MAAM;IAGR,OAAO,EAAE;AACX;AA8NO,SAAS,0CACd,GAAgB,EAChB,OAAkH;IAElH,MAAM,eACJ,WAAW,WACX,OAAO,oBACP,gBAAgB,WAChB,OAAO,UACP,MAAM,iBACN,aAAa,YACb,QAAQ,UACR,MAAM,aACN,SAAS,YACT,WAAW,IACX,GAAG,cACJ,GAAG,WAAW,CAAC;IAChB,MAAM,cAAc,CAAA,GAAA,aAAK;IACzB,MAAM,sBAAsB,CAAA,GAAA,aAAK;IAEjC,MAAM,0BAAsF;qBAC1F;iBACA;0BACA;iBACA;gBACA;uBACA;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,aAAK,EAAwD;IAClF,MAAM,gBAAgB,CAAA,GAAA,aAAK,EAA0B;IACrD,MAAM,aAAa,CAAA,GAAA,aAAK,EAAE;IAE1B,OAAO,CAAA,GAAA,yCAAe,EACpB,CACE,KACA,UACA,cACA,UACA,QACA,YAEA,OAAO,QAAE,IAAI,EAAE;YACb,MAAM,WAAW,CAAA,GAAA,iBAAS,EAAE,OAAO;YACnC,MAAM,SAAS,CAAA,GAAA,kBAAU,EAAE,WAAW;YACtC,IAAI,SAAS,GAAG;gBACd,cAAc,OAAO,EAAE;gBACvB,cAAc,OAAO,GAAG,IAAI;gBAC5B,MAAM,cAAc,CAAA,GAAA,WAAG,EAAE,QAAQ;gBACjC;;WAEC,GACD,MAAM,mBAAmB,QACvB,YAAY,OAAO,IACjB,YAAY,OAAO,KAAK,OACxB,oBAAoB,OAAO,IAC3B,oBAAoB,OAAO,KAAK;gBAEpC,YAAY,OAAO,GAAG;gBACtB,oBAAoB,OAAO,GAAG;gBAC9B,MAAM,0CAAoB,KAAK,QAAQ,UAAU,kBAAkB;oBACjE,GAAG,YAAY;oBACf,QAAQ,cAAc,OAAO,EAAE;gBACjC;gBACA,aAAa,OAAO,GAAG,qCACrB,aACA,UACA,cAAc,OAAO,EAAE,QACvB,UACA,QACA;YAEJ;YACA,IAAI,CAAC,aAAa,OAAO,EACvB,OAAO;gBAAE,SAAS,WAAW,OAAO;gBAAE,MAAM,EAAE;YAAkC;YAElF,MAAM,EAAE,OAAO,OAAO,QAAE,IAAI,EAAE,GAAG,MAAM,aAAa,OAAO,CAAC,IAAI;YAChE,WAAW,OAAO,GAAG,CAAC;YACtB,OAAO;gBAAE,SAAS,WAAW,OAAO;gBAAE,MAAO,WAAW,EAAE;YAAmC;QAC/F,GACF;QAAC;QAAK;QAAU;QAAc;QAAU;QAAQ;KAAU,EAC1D;AAEJ;;;;;;;;;;;;;;;;ACnaO,SAAS,0CACd,YAAoB,EACpB,KAAa,EACb,OAGwF;IAExF,6DAA6D;IAC7D,MAAM,qBAAE,iBAAiB,EAAE,GAAG,mBAAmB,GAAG,WAAW,CAAC;IAEhE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,eAAO;IACnD,MAAM,gBAAgB,CAAA,GAAA,yCAAQ,EAAE,WAAW,CAAC;IAC5C,MAAM,YAAY,CAAA,GAAA,aAAK;IAEvB,MAAM,cAAc,CAAA,GAAA,kBAAU,EAC5B,CAAC;QACC,QAAQ,KAAK,CAAC;QACd,MAAM,QACJ,kBAAkB,SAAS,OAAO,OAAO,CAAC,QAAQ,CAAC,0BAC/C,IAAI,sCAAgB,wDACnB;QAEP,IAAI,wCAAkB,QACpB,gCAAkB,gBAAC;YAAsB,SAAS,cAAc,OAAO,CAAC,iBAAiB;;aACpF;YACL,IAAI,cAAc,OAAO,CAAC,OAAO,EAC/B,cAAc,OAAO,CAAC,OAAO,CAAC;iBAE9B,IAAI,CAAA,GAAA,kBAAU,EAAE,UAAU,KAAK,CAAA,GAAA,iBAAS,EAAE,UAAU,EAClD,CAAA,GAAA,yCAAe,EAAE,OAAO;gBACtB,OAAO;YACT;QAGN;IACF,GACA;QAAC;KAAc;IAGjB,MAAM,KAAK,CAAA,GAAA,cAAM,EAAE;QACjB,IAAI,CAAC,CAAA,GAAA,iBAAS,EAAE,eACd,MAAM,IAAI,MAAM;QAElB,IAAI,qBAAyC;QAE7C,OAAO,OAAO,cAAsB;YAClC,MAAM,cAAc,UAAU,OAAO,EAAE;YACvC,MAAM,UAAU,CAAA,GAAA,wBAAW,EAAE,KAAK,CAAC,WAAW;gBAAC;gBAAU;gBAAc;gBAAc;aAAM,EAAE;gBAC3F,QAAQ;YACV;YACA,MAAM,iBAAiB,CAAA,GAAA,yCAAgB,EAAE;YACzC,IAAI,CAAC,SAAE,KAAK,YAAE,QAAQ,UAAE,MAAM,EAAE,EAAE,cAAc,aAAa,GAAG,MAAM,CAAA,GAAA,yCAAe,EACnF,SACA;gBAAE,UAAU;YAAQ,GACpB;YAGF,mCAAa;YAEb,IAAI,aAAa,KAAK,CAAC,UAAU,aAAa,KAAK,CAAC,SAAS;gBAC3D,sEAAsE;gBACtE,uEAAuE;gBACvE,qEAAqE;gBACrE,sDAAsD;gBACtD,IAAI,CAAC,oBAAoB;oBACvB,MAAM,aAAa,CAAA,GAAA,eAAG,EAAE,IAAI,CAAC,CAAA,GAAA,aAAC,EAAE,MAAM,IAAI,UAAU,CAAA,GAAA,iBAAG,EAAE;oBACzD,MAAM,CAAA,GAAA,YAAI,EAAE,YAAY;wBAAE,WAAW;oBAAK;oBAC1C,mCAAa;oBAEb,qBAAqB,CAAA,GAAA,eAAG,EAAE,IAAI,CAAC,YAAY;oBAC3C,MAAM,CAAA,GAAA,eAAO,EAAE,cAAc;oBAE7B,wBAAwB;oBACxB,MAAM,CAAA,GAAA,gBAAQ,EAAE,qBAAqB,QAAQ;oBAC7C,MAAM,CAAA,GAAA,gBAAQ,EAAE,qBAAqB,QAAQ;oBAE7C,mCAAa;gBACf;gBACA,MAAM,UAAU,CAAA,GAAA,wBAAW,EAAE,KAAK,CAChC,WACA;oBAAC;oBAAU;oBAAc;oBAAS;oBAAa;oBAAoB;iBAAM,EACzE;oBACE,QAAQ;gBACV;gBAEF,MAAM,iBAAiB,CAAA,GAAA,yCAAgB,EAAE;gBACzC,CAAC,SAAE,KAAK,YAAE,QAAQ,UAAE,MAAM,EAAE,EAAE,cAAc,aAAa,GAAG,MAAM,CAAA,GAAA,yCAAe,EAC/E,SACA;oBAAE,UAAU;gBAAQ,GACpB;gBAEF,mCAAa;YACf;YAEA,IAAI,SAAS,aAAa,KAAK,WAAW,MACxC,MAAM,IAAI,MAAM;YAGlB,OAAO,KAAK,KAAK,CAAC,aAAa,IAAI,MAAM;QAC3C;IACF,GAAG;QAAC;KAAa;IAEjB,OAAO;QACL,GAAG,CAAA,GAAA,yCAAS,EAAE,IAAI;YAAC;YAAc;SAAM,EAAE;YAAE,GAAG,iBAAiB;YAAE,SAAS;QAAY,EAAE;wBACxF;IACF;AACF;AAEA,MAAM,8CAAwB;IAC5B,YAAY,OAAe,CAAE;QAC3B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,SAAS,wCAAkB,KAAc;IACvC,OAAO,iBAAiB,SAAS,MAAM,IAAI,KAAK;AAClD;AAEA,SAAS,4CAAsB,KAA2B;IACxD,MAAM,uBAAuB,SAAS,CAAA,GAAA,aAAC,EAAE,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK;IACrE,MAAM,oBAAoB,uBAAuB,aAAa;IAC9D,MAAM,SAAS,uBACX;QACE,OAAO;QACP,QAAQ;IACV,IACA;QACE,OAAO;QACP,QAAQ;IACV;IAEJ,IAAI,CAAA,GAAA,kBAAU,EAAE,WAAW,KAAK,YAC9B,qBACE,iBAAC,CAAA,GAAA,mBAAW;QAAE,MAAM,CAAA,GAAA,WAAG,EAAE,OAAO;QAAE,OAAO,CAAA,GAAA,kBAAU,EAAE,WAAW;;0BAC9D,gBAAC,CAAA,GAAA,mBAAW,EAAE,IAAI;gBAChB,OAAM;gBACN,SAAS,CAAC,8BAA8B,EAAE,kBAAkB,kBAAkB,CAAC;;YAEhF,MAAM,OAAO,iBACZ,gBAAC,CAAA,GAAA,mBAAW,EAAE,IAAI;gBAChB,OAAO,MAAM,OAAO;gBACpB,SAAS,CAAC,8BAA8B,EAAE,kBAAkB,kBAAkB,CAAC;iBAE/E;0BACJ,gBAAC,CAAA,GAAA,mBAAW,EAAE,SAAS;0BACvB,gBAAC,CAAA,GAAA,mBAAW,EAAE,IAAI;gBAAC,OAAO,OAAO,KAAK;gBAAE,UAAU,IAAM,CAAA,GAAA,WAAG,EAAE,OAAO,MAAM;;;;IAKhF,qBACE,gBAAC,CAAA,GAAA,WAAG;kBACF,cAAA,gBAAC,CAAA,GAAA,WAAG,EAAE,SAAS;YACb,MAAM;gBACJ,QAAQ;oBACN,OAAO;oBACP,MAAM;gBACR;YACF;YACA,OAAM;YACN,aAAa,CAAC,EACZ,MAAM,OAAO,GAAG,MAAM,OAAO,GAAG,OAAO,GACxC,8BAA8B,EAAE,kBAAkB,mBAAmB,CAAC;YACvE,uBACE,gBAAC,CAAA,GAAA,kBAAU;0BACT,cAAA,gBAAC,CAAA,GAAA,aAAK,EAAE,IAAI;oBAAE,GAAG,MAAM;;;;;AAMnC;AAEA,SAAS,mCAAa,MAAoB;IACxC,IAAI,QAAQ,SAAS;QACnB,MAAM,QAAQ,IAAI,MAAM;QACxB,MAAM,IAAI,GAAG;QACb,MAAM;IACR;AACF;;;;;;UC5NY;IACV,sDAAsD;GAD5C,8CAAA;AAQZ,SAAS,sCACP,UAA4C,EAC5C,KAA4B;IAE5B,IAAI,YAAY;QACd,IAAI,OAAO,eAAe,YACxB,OAAO,WAAW;aACb,IAAI,2BAAwC;YACjD,IAAI,eAAe,OAAO,UAAU,eAAe,UAAU;YAC7D,IAAI,cACF,OAAQ,OAAO;gBACb,KAAK;oBACH,eAAe,MAAM,MAAM,GAAG;oBAC9B;gBACF,KAAK;oBACH,IAAI,MAAM,OAAO,CAAC,QAChB,eAAe,MAAM,MAAM,GAAG;yBACzB,IAAI,iBAAiB,MAC1B,eAAe,MAAM,OAAO,KAAK;oBAEnC;gBACF;oBACE;YACJ;YAEF,IAAI,CAAC,cACH,OAAO;QAEX;IACF;AACF;AA4EO,SAAS,0CAA+B,KAU9C;IACC,MAAM,EAAE,UAAU,SAAS,cAAE,UAAU,iBAAE,gBAAgB,CAAC,GAAG,GAAG;IAEhE,gEAAgE;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,eAAO,EAAK;IACxC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,eAAO,EAAyC,CAAC;IAC7E,MAAM,OAAO,CAAA,GAAA,aAAK,EAA4C,CAAC;IAE/D,MAAM,mBAAmB,CAAA,GAAA,yCAAQ,EAAiB,cAAc,CAAC;IACjE,MAAM,iBAAiB,CAAA,GAAA,yCAAQ,EAAE;IAEjC,MAAM,QAAQ,CAAA,GAAA,kBAAU,EACtB,CAAC;QACC,KAAK,OAAO,CAAC,GAAG,EAAE;IACpB,GACA;QAAC;KAAK;IAGR,MAAM,eAAe,CAAA,GAAA,kBAAU,EAC7B,OAAO;QACL,IAAI,mBAAmE;QACvE,KAAK,MAAM,CAAC,IAAI,WAAW,IAAI,OAAO,OAAO,CAAC,iBAAiB,OAAO,EAAG;YACvE,MAAM,QAAQ,sCAAgB,YAAY,MAAM,CAAC,GAAG;YACpD,IAAI,OAAO;gBACT,IAAI,CAAC,kBAAkB;oBACrB,mBAAmB,CAAC;oBACpB,4CAA4C;oBAC5C,MAAM;gBACR;gBACA,gBAAgB,CAAC,GAAc,GAAG;YACpC;QACF;QACA,IAAI,kBAAkB;YACpB,UAAU;YACV,OAAO;QACT;QACA,MAAM,SAAS,MAAM,eAAe,OAAO,CAAC;QAC5C,OAAO,OAAO,WAAW,YAAY,SAAS;IAChD,GACA;QAAC;QAAkB;QAAgB;KAAM;IAG3C,MAAM,qBAAqB,CAAA,GAAA,kBAAU,EACnC,CAAC,IAAa;QACZ,UAAU,CAAC,SAAY,CAAA;gBAAE,GAAG,MAAM;gBAAE,CAAC,GAAG,EAAE;YAAM,CAAA;IAClD,GACA;QAAC;KAAU;IAGb,MAAM,WAAW,CAAA,GAAA,kBAAU,EACzB,SAA6B,EAAK,EAAE,KAA2B;QAC7D,kFAAkF;QAClF,UAAU,CAAC,SAAY,CAAA;gBAAE,GAAG,MAAM;gBAAE,CAAC,GAAG,EAAE,OAAO,UAAU,aAAa,MAAM,MAAM,CAAC,GAAG,IAAI;YAAM,CAAA;IACpG,GACA;QAAC;KAAU;IAGb,MAAM,YAAY,CAAA,GAAA,cAAM,EAAkF;QACxG,sFAAsF;QACtF,2DAA2D;QAC3D,OAAO,IAAI,MACT,iDAAiD;QACjD,CAAC,GACD;YACE,KAAI,MAAM,EAAE,EAAW;gBACrB,MAAM,aAAa,iBAAiB,OAAO,CAAC,GAAG;gBAC/C,MAAM,QAAQ,MAAM,CAAC,GAAG;gBACxB,OAAO;oBACL,UAAS,KAAK;wBACZ,IAAI,MAAM,CAAC,GAAG,EAAE;4BACd,MAAM,QAAQ,sCAAgB,YAAY;4BAC1C,IAAI,CAAC,OACH,mBAAmB,IAAI;wBAE3B;wBACA,SAAS,IAAI;oBACf;oBACA,QAAO,KAAK;wBACV,MAAM,QAAQ,sCAAgB,YAAY,MAAM,MAAM,CAAC,KAAK;wBAC5D,IAAI,OACF,mBAAmB,IAAI;oBAE3B;oBACA,OAAO,MAAM,CAAC,GAAG;wBACjB;oBACA,iFAAiF;oBACjF,OAAO,OAAO,UAAU,cAAc,OAAO;oBAC7C,KAAK,CAAC;wBACJ,KAAK,OAAO,CAAC,GAAG,GAAG;oBACrB;gBACF;YACF;QACF;IAEJ,GAAG;QAAC;QAAQ;QAAkB;QAAoB;QAAQ;QAAM;KAAS;IAEzE,MAAM,QAAQ,CAAA,GAAA,kBAAU,EACtB,CAAC;QACC,UAAU,CAAC;QACX,OAAO,OAAO,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;YAC7C,IAAI,CAAC,QAAQ,CAAC,GAAG,EACf,KAAK;QAET;QACA,IAAI,QACF,gEAAgE;QAChE,UAAU;IAEd,GACA;QAAC;QAAW;QAAW;KAAK;IAG9B,OAAO;sBAAE;4BAAc;kBAAoB;gBAAU;mBAAQ;eAAW;eAAO;IAAM;AACvF;;;;;;AChOO,SAAS,yCACd,MAAc,EACd,UAckE,CAAC,CAAC;IAEpE,MAAM,cAAE,UAAU,UAAE,MAAM,SAAE,KAAK,EAAE,GAAG,mBAAmB,GAAG;IAC5D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,eAAO,EAAE;IACjC,MAAM,YAAY,CAAA,GAAA,aAAK;IACvB,MAAM,aAAE,SAAS,SAAE,KAAK,cAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yCAAS,EAChD,OAAO,QAAgB,YAA4B;QACjD,QAAQ;QACR,MAAM,SAAS,CAAA,GAAA,SAAC,EAAE,GAAG,CAAC,QAAQ;wBAAE;mBAAY;YAAO,QAAQ,UAAU,OAAO,EAAE;QAAO;QACrF,IAAI,iBAAiB,OACnB,QAAQ,MAAM;aACT;YACL,OAAO,EAAE,CAAC,QAAQ,CAAC;gBACjB,QAAQ,CAAC,IAAM,IAAI;YACrB;YACA,MAAM;QACR;IACF,GACA;QAAC;QAAQ;QAAY;KAAO,EAC5B;QAAE,GAAG,iBAAiB;mBAAE;IAAU;IAGpC,OAAO;mBAAE;cAAW;eAAM;oBAAO;IAAW;AAC9C;;;;;;AC/CA,MAAM,uCAAiB;AAEvB,MAAM,mCAAa;AAEnB,MAAM,0CAAoB;IACxB,SAAS;IACT,OAAO;IACP,UAAU;AACZ;AAEA,SAAS,qCAAe,IAAe;IACrC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,cAAc,OAAO,KAAK,WAAW,GAAG;IAC9C,MAAM,WAAW,OAAO,KAAK,QAAQ,GAAG;IAExC,MAAM,iBAAiB,AAAC,CAAA,MAAM,WAAU,IAAK;IAC7C,MAAM,sBAAsB,KAAK,GAAG,CAAC,KAAM,CAAA,uCAAiB,gCAAS;IACrE,MAAM,oBAAoB,wCAAkB,OAAO,GAAG,KAAK,GAAG,CAAC,CAAC,sBAAsB;IACtF,MAAM,kBAAkB,WAAW;IAEnC,OAAO;QACL,aAAa;QACb,UAAU;IACZ;AACF;AAEA,8DAA8D;AAC9D,MAAM,mCAAa,CAAC;IAOlB,OAAO,KAAK,EAAE;AAChB;AAmDO,SAAS,0CACd,IAAU,EACV,OAAmG;IAMnG,MAAM,SAAS,CAAA,GAAA,yCAAQ,EAAE,SAAS,OAAO;IACzC,MAAM,mBAAmB,CAAA,GAAA,yCAAQ,EAAE,SAAS;IAE5C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,yCAAa,EAC3D,CAAC,iBAAiB,EAAE,SAAS,UAAU,CAAC,EACxC,CAAC;IAGH,MAAM,YAAY,CAAA,GAAA,kBAAU,EAC1B,eAAe,eAAe,IAAO;QACnC,MAAM,UAAU,OAAO,OAAO,CAAC;QAE/B,oBAAoB,CAAC;YACnB,MAAM,WAAW,gBAAgB,CAAC,QAAQ;YAC1C,MAAM,cAAc,qCAAe;YAEnC,OAAO;gBACL,GAAG,gBAAgB;gBACnB,CAAC,QAAQ,EAAE;YACb;QACF;IACF,GACA;QAAC;QAAQ;KAAoB;IAG/B,MAAM,eAAe,CAAA,GAAA,kBAAU,EAC7B,eAAe,eAAe,IAAO;QACnC,MAAM,UAAU,OAAO,OAAO,CAAC;QAE/B,oBAAoB,CAAC;YACnB,MAAM,iBAAiB;gBAAE,GAAG,gBAAgB;YAAC;YAC7C,OAAO,cAAc,CAAC,QAAQ;YAE9B,OAAO;QACT;IACF,GACA;QAAC;QAAQ;KAAoB;IAG/B,MAAM,aAAa,CAAA,GAAA,cAAM,EAAE;QACzB,IAAI,CAAC,MACH,OAAO,EAAE;QAGX,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG;YACnB,MAAM,YAAY,gBAAgB,CAAC,OAAO,OAAO,CAAC,GAAG;YACrD,MAAM,YAAY,gBAAgB,CAAC,OAAO,OAAO,CAAC,GAAG;YAErD,0DAA0D;YAC1D,IAAI,aAAa,CAAC,WAChB,OAAO;YAGT,0DAA0D;YAC1D,IAAI,CAAC,aAAa,WAChB,OAAO;YAGT,4EAA4E;YAC5E,IAAI,aAAa,WACf,OAAO,UAAU,QAAQ,GAAG,UAAU,QAAQ;YAGhD,4DAA4D;YAC5D,OAAO,iBAAiB,OAAO,GAAG,iBAAiB,OAAO,CAAC,GAAG,KAAK;QACrE;IACF,GAAG;QAAC;QAAkB;QAAM;QAAQ;KAAiB;IAErD,OAAO;QAAE,MAAM;mBAAY;sBAAW;IAAa;AACrD;;;;;;;ACvJO,SAAS,0CAAmB,GAAW,EAAE,YAAgB;IAC9D,MAAM,EACJ,MAAM,KAAK,aACX,SAAS,UACT,MAAM,EACP,GAAG,CAAA,GAAA,yCAAS,EACX,OAAO;QACL,MAAM,OAAO,MAAM,CAAA,GAAA,mBAAW,EAAE,OAAO,CAAS;QAEhD,OAAO,OAAO,SAAS,cAAe,KAAK,KAAK,CAAC,MAAM,CAAA,GAAA,yCAAM,KAAW;IAC1E,GACA;QAAC;KAAI;IAGP,eAAe,SAAS,KAAQ;QAC9B,IAAI;YACF,MAAM,OAAO,CAAA,GAAA,mBAAW,EAAE,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,OAAO,CAAA,GAAA,yCAAO,KAAK;gBACvE,kBAAiB,KAAK;oBACpB,OAAO;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,CAAA,GAAA,yCAAe,EAAE,OAAO;gBAAE,OAAO;YAAuC;QAChF;IACF;IAEA,eAAe;QACb,IAAI;YACF,MAAM,OAAO,CAAA,GAAA,mBAAW,EAAE,UAAU,CAAC,MAAM;gBACzC;oBACE,OAAO;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,CAAA,GAAA,yCAAe,EAAE,OAAO;gBAAE,OAAO;YAA4C;QACrF;IACF;IAEA,OAAO;eAAE;kBAAO;qBAAU;mBAAa;IAAU;AACnD;;;AG/DA,SAAS,+BAAS,GAAW;IAC3B,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI;IAER,WAAW;IACX,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;QACnC,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;QACnC,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;IAEnC,WAAW;IACb,OAAO,IAAI,IAAI,MAAM,KAAK,GAAG;QAC3B,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;QACnC,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;QACnC,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;IACrC,OACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC;IAG/C,OAAO;WAAE;WAAG;WAAG;IAAE;AACnB;AAEA,SAAS,+BAAS,KAAE,CAAC,KAAE,CAAC,KAAE,CAAC,EAAuC;IAChE,IAAI,UAAU,EAAE,QAAQ,CAAC;IACzB,IAAI,UAAU,EAAE,QAAQ,CAAC;IACzB,IAAI,UAAU,EAAE,QAAQ,CAAC;IAEzB,IAAI,QAAQ,MAAM,KAAK,GACrB,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC;IAEzB,IAAI,QAAQ,MAAM,KAAK,GACrB,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC;IAEzB,IAAI,QAAQ,MAAM,KAAK,GACrB,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC;IAGzB,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAC1C;AAEA,SAAS,+BAAS,KAAE,CAAC,KAAE,CAAC,KAAE,CAAC,EAAuC;IAChE,kCAAkC;IAClC,KAAK;IACL,KAAK;IACL,KAAK;IAEL,4CAA4C;IAC5C,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG;IAC5B,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG;IAC5B,MAAM,QAAQ,OAAO;IACrB,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI;IAER,gBAAgB;IAChB,gBAAgB;IAChB,IAAI,UAAU,GACZ,IAAI;SAGD,IAAI,SAAS,GAChB,IAAI,AAAE,CAAA,IAAI,CAAA,IAAK,QAAS;SAGrB,IAAI,SAAS,GAChB,IAAI,AAAC,CAAA,IAAI,CAAA,IAAK,QAAQ;SAItB,IAAI,AAAC,CAAA,IAAI,CAAA,IAAK,QAAQ;IAGxB,IAAI,KAAK,KAAK,CAAC,IAAI;IAEnB,0CAA0C;IAC1C,IAAI,IAAI,GACN,KAAK;IAGP,sBAAsB;IACtB,IAAI,AAAC,CAAA,OAAO,IAAG,IAAK;IAEpB,uBAAuB;IACvB,IAAI,UAAU,IAAI,IAAI,QAAS,CAAA,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,EAAC;IAErD,0BAA0B;IAC1B,IAAI,CAAC,AAAC,CAAA,IAAI,GAAE,EAAG,OAAO,CAAC;IACvB,IAAI,CAAC,AAAC,CAAA,IAAI,GAAE,EAAG,OAAO,CAAC;IAEvB,OAAO;WAAE;WAAG;WAAG;IAAE;AACnB;AAEA,SAAS,+BAAS,KAAE,CAAC,KAAE,CAAC,KAAE,CAAC,EAAuC;IAChE,yBAAyB;IACzB,KAAK;IACL,KAAK;IAEL,MAAM,IAAI,AAAC,CAAA,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,EAAC,IAAK;IACtC,MAAM,IAAI,IAAK,CAAA,IAAI,KAAK,GAAG,CAAC,AAAE,IAAI,KAAM,IAAK,EAAC;IAC9C,MAAM,IAAI,IAAI,IAAI;IAClB,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI;IAER,IAAI,KAAK,KAAK,IAAI,IAAI;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK;QAC7B,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;QAC9B,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;QAC9B,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;QAC9B,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;QAC9B,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,IAAI,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,CAAA,IAAK;IACzB,IAAI,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,CAAA,IAAK;IACzB,IAAI,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,CAAA,IAAK;IAEzB,OAAO;WAAE;WAAG;WAAG;IAAE;AACnB;AAEA,SAAS,+BAAS,GAAW;IAC3B,OAAO,+BAAS,+BAAS;AAC3B;AAEA,SAAS,+BAAS,GAAwC;IACxD,OAAO,+BAAS,+BAAS;AAC3B;AAEA,SAAS,4BAAM,KAAa,EAAE,GAAW,EAAE,GAAW;IACpD,OAAO,MAAM,MAAO,QAAQ,MAAM,MAAM,QAAQ,MAAM,MAAM,QAAS,QAAQ,MAAM,MAAM,QAAQ,MAAM,MAAM;AAC/G;AAEA,MAAM,+BAAS;AAER,SAAS,0CAAoB,GAAW;IAC7C,MAAM,MAAM,+BAAS;IAErB,OAAO,+BAAS;QACd,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC;QACR,GAAG,4BAAM,IAAI,CAAC,GAAG,8BAAQ,GAAG;IAC9B;AACF;AAEO,SAAS,0CAAqB,GAAW;IAC9C,MAAM,MAAM,+BAAS;IAErB,OAAO,+BAAS;QACd,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC;QACR,GAAG,4BAAM,IAAI,CAAC,GAAG,8BAAQ,GAAG;IAC9B;AACF;;;ADtKA,SAAS,uCAAiB,GAAW,EAAE,CAAS;IAC9C,MAAM,OAAO,IAAI,UAAU,CAAC;IAE5B,IAAI,OAAO,KAAK,CAAC,OACf,OAAO;QAAC;QAAI;KAAE;IAEhB,IAAI,OAAO,UAAU,OAAO,QAC1B,OAAO;QAAC,IAAI,MAAM,CAAC;QAAI;KAAE,EAAE,yCAAyC;IAGtE,wEAAwE;IACxE,mCAAmC;IACnC,IAAI,UAAU,QAAQ,QAAQ,QAAQ;QACpC,IAAI,IAAI,MAAM,IAAI,IAAI,GACpB,MAAM,IAAI,MAAM;QAElB,MAAM,OAAO,IAAI,UAAU,CAAC,IAAI;QAChC,IAAI,SAAS,QAAQ,OAAO,QAC1B,MAAM,IAAI,MAAM;QAElB,OAAO;YAAC,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;YAAI,IAAI;SAAE;IACnD;IAEA,mDAAmD;IACnD,IAAI,MAAM,GACR,MAAM,IAAI,MAAM;IAGlB,MAAM,OAAO,IAAI,UAAU,CAAC,IAAI;IAEhC,oEAAoE;IACpE,wBAAwB;IACxB,IAAI,SAAS,QAAQ,OAAO,QAC1B,MAAM,IAAI,MAAM;IAGlB,oDAAoD;IACpD,OAAO;QAAC,IAAI,MAAM,CAAC,IAAI;QAAI,IAAI;KAAE;AACnC;AAEA,MAAM,uCAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAaM,SAAS,0CACd,IAAY,EACZ,OAUC;IAED,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI;IACJ,IAAI,MAAM,MAAM,IAAI,KAAK,uCAAiB,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EACvD,WAAW,uCAAiB,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;SACtC,IAAI,MAAM,MAAM,GAAG,GAAG;QAC3B,MAAM,uBAAuB,uCAAiB,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI;QACjE,MAAM,sBAAsB,uCAAiB,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI;QAC/E,WAAW,uBAAuB;IACpC,OACE,WAAW;IAGb,IAAI;IAEJ,IAAI,SAAS,YACX,kBAAkB,SAAS;SACtB;QACL,IAAI,oBAAoB;QACxB,IAAI,CAAC,MAAM,EAAE,GAAG,uCAAiB,UAAU;QAC3C,MAAO,KAAM;YACX,qBAAqB,KAAK,UAAU,CAAC;YACrC,CAAC,MAAM,EAAE,GAAG,uCAAiB,UAAU,IAAI;QAC7C;QAEA,MAAM,aAAa,oBAAoB,qCAAe,MAAM;QAC5D,kBAAkB,oCAAc,CAAC,WAAW;IAC9C;IAEA,MAAM,UAAU;IAChB,MAAM,SAAS,KAAK;IAEpB,MAAM,MAAM,CAAC;EACb,EACE,SAAS,aAAa,QAClB,CAAC;;sCAE6B,EAAE,CAAA,GAAA,yCAAmB,EAAE,iBAAiB;uCACvC,EAAE,gBAAgB;wCACjB,EAAE,CAAA,GAAA,yCAAkB,EAAE,iBAAiB;;SAEtE,CAAC,GACF,GACL;iCAC8B,EAAE,OAAO,QAAQ,EAC1C,SAAS,aAAa,QAAQ,mBAAmB,gBAClD;MACD,EACE,WACI,CAAC,+BAA+B,EAC9B,SAAS,EACV,oEAAoE,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,GACtG,GACL;;EAEL,CAAC,CAAC,UAAU,CAAC,MAAM;IACnB,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC;AACpC;;;;;AE5HO,SAAS,0CACd,GAAiB,EACjB,OAeC;IAED,IAAI;QACF,MAAM,SAAS,OAAO,QAAQ,WAAW,IAAI,CAAA,GAAA,UAAE,EAAE,OAAO;QACxD,MAAM,WAAW,OAAO,QAAQ;QAChC,OAAO;YACL,QAAQ,CAAC,sCAAsC,EAAE,SAAS,QAAQ,GAAG,QAAQ,EAAE,SAAS,CAAC;YACzF,UAAU,SAAS,YAAY,CAAA,GAAA,WAAG,EAAE,IAAI;YACxC,MAAM,SAAS;QACjB;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC;QACd,OAAO,CAAA,GAAA,WAAG,EAAE,IAAI;IAClB;AACF;;;;AC7CA,SAAS,uCAAiB,OAAe,EAAE,OAAe,EAAE,MAAc,EAAE,cAAsB;IAChG,MAAM,iBAAiB,AAAE,CAAA,iBAAiB,EAAC,IAAK,KAAK,EAAE,GAAI;IAE3D,OAAO;QACL,GAAG,UAAU,SAAS,KAAK,GAAG,CAAC;QAC/B,GAAG,UAAU,SAAS,KAAK,GAAG,CAAC;IACjC;AACF;AAEA,SAAS,kCAAY,CAAS,EAAE,CAAS,EAAE,MAAc,EAAE,UAAkB,EAAE,QAAgB;IAC7F,MAAM,QAAQ,uCAAiB,GAAG,GAAG,QAAQ;IAC7C,MAAM,MAAM,uCAAiB,GAAG,GAAG,QAAQ;IAE3C,MAAM,eAAe,WAAW,cAAc,MAAM,MAAM;IAE1D,MAAM,IAAI;QAAC;QAAK,MAAM,CAAC;QAAE,MAAM,CAAC;QAAE;QAAK;QAAQ;QAAQ;QAAG;QAAc;QAAG,IAAI,CAAC;QAAE,IAAI,CAAC;KAAC,CAAC,IAAI,CAAC;IAE9F,OAAO;AACT;AAeO,SAAS,0CACd,QAAgB,EAChB,QAAwB,CAAA,GAAA,YAAI,EAAE,GAAG,EACjC,OAAqE;IAErE,MAAM,aAAa,SAAS,cAAe,CAAA,CAAA,GAAA,kBAAU,EAAE,UAAU,KAAK,UAAU,UAAU,OAAM;IAChG,MAAM,oBAAoB,SAAS,qBAAqB;IAExD,MAAM,SAAS;IACf,MAAM,UAAU;IAChB,MAAM,SAAS,KAAK,UAAU,SAAS;IAEvC,MAAM,MAAM,CAAC;iCACkB,EAAE,OAAO,gBAAgB,EAAE,OAAO,UAAU,EACrE,WAAW,IAAI,aAAa,MAC7B,WAAW,EAAE,WAAW,IAAI,oBAAoB,IAAI;MACrD,EACE,WAAW,KAAK,WAAW,IACvB,CAAC,SAAS,EAAE,kCACV,IACA,IACA,QACA,GACA,WAAW,KACX,UAAU,EAAE,MAAM,gBAAgB,EAAE,OAAO,gBAAgB,CAAC,GAC9D,GACL;;EAEL,CAAC,CAAC,UAAU,CAAC,MAAM;IACnB,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC;AACpC;;;;;;;;AG9DA,MAAM,4CAAsB;IAC1B,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,gCAAU,CAAC,SAAmB,CAAC,mBAAmB,EAAE,OAAO,CAAC;AAElE,MAAM,wCAAkB,aAAa,GAAG;IACtC,OAAO,aAAa,GAAG,8BACrB,CAAC,glBAAglB,CAAC;IAEplB,QAAQ;QACN,QAAQ,aAAa,GAAG,8BACtB,CAAC,mrBAAmrB,CAAC;QAEvrB,WAAW,CAAA,GAAA,YAAI,EAAE,WAAW;IAC9B;IACA,QAAQ,aAAa,GAAG,8BACtB,CAAC,ovBAAovB,CAAC;IAExvB,MAAM,aAAa,GAAG,8BACpB,CAAC,43BAA43B,CAAC;IAEh4B,QAAQ;QACN,QAAQ;YACN,OAAO,aAAa,GAAG,8BACrB,CAAC,whCAAwhC,CAAC;YAE5hC,MAAM,aAAa,GAAG,8BACpB,CAAC,shCAAshC,CAAC;QAE5hC;IACF;IACA,OAAO,aAAa,GAAG,8BACrB,CAAC,ulCAAulC,CAAC;IAE3lC,MAAM,aAAa,GAAG,8BACpB,CAAC,mrCAAmrC,CAAC;AAEzrC;AAEO,MAAM,4CAAe,CAAC,UAC3B,IAAI,CAAA,GAAA,yCAAW,EAAE;QACf,QAAQ,IAAI,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC;YAC3B,gBAAgB,CAAA,GAAA,YAAI,EAAE,cAAc,CAAC,GAAG;YACxC,cAAc;YACd,cAAc,sCAAgB,KAAK;YACnC,YAAY;YACZ,aAAa;QACf;QACA,UAAU,QAAQ,QAAQ,IAAI,0CAAoB,KAAK;QACvD,cAAc,QAAQ,YAAY,IAAI;QACtC,UAAU,QAAQ,QAAQ,IAAI;QAC9B,iBAAiB,QAAQ,eAAe,IAAI;QAC5C,OAAO,QAAQ,KAAK;QACpB,qBAAqB,QAAQ,mBAAmB;QAChD,aAAa,QAAQ,WAAW;QAChC,cAAc,QAAQ,YAAY;QAClC,4BAA4B,QAAQ,0BAA0B;QAC9D,qBAAqB,QAAQ,mBAAmB;IAClD;AAEK,MAAM,4CAAgB,CAAC,UAC5B,IAAI,CAAA,GAAA,yCAAW,EAAE;QACf,QAAQ,IAAI,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC;YAC3B,gBAAgB,CAAA,GAAA,YAAI,EAAE,cAAc,CAAC,GAAG;YACxC,cAAc;YACd,cAAc,sCAAgB,MAAM;YACpC,YAAY;YACZ,aAAa;QACf;QACA,UAAU,QAAQ,QAAQ,IAAI,0CAAoB,MAAM;QACxD,cAAc,QAAQ,YAAY,IAAI;QACtC,UAAU,QAAQ,QAAQ,IAAI;QAC9B,iBAAiB,QAAQ,eAAe,IAAI;QAC5C,OAAO,QAAQ,KAAK;QACpB,qBAAqB,QAAQ,mBAAmB;QAChD,aAAa,QAAQ,WAAW;QAChC,cAAc,QAAQ,YAAY;QAClC,4BAA4B,QAAQ,0BAA0B;QAC9D,qBAAqB,QAAQ,mBAAmB;IAClD;AAEK,MAAM,4CAAgB,CAAC,UAC5B,IAAI,CAAA,GAAA,yCAAW,EAAE;QACf,QAAQ,IAAI,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC;YAC3B,gBAAgB,CAAA,GAAA,YAAI,EAAE,cAAc,CAAC,MAAM;YAC3C,cAAc;YACd,cAAc,sCAAgB,MAAM;YACpC,YAAY;YACZ,aAAa;QACf;QACA,UAAU,QAAQ,QAAQ;QAC1B,cAAc,QAAQ,YAAY,IAAI;QACtC,UAAU,QAAQ,QAAQ,IAAI;QAC9B,iBAAiB,QAAQ,QAAQ;QACjC,OAAO,QAAQ,KAAK;QACpB,qBAAqB,QAAQ,mBAAmB;QAChD,cAAc,QAAQ,YAAY,IAAI;QACtC,aAAa,QAAQ,WAAW;QAChC,4BAA4B,QAAQ,0BAA0B;QAC9D,qBAAqB,QAAQ,mBAAmB;IAClD;AAEK,MAAM,4CAAc,CAAC,UAC1B,IAAI,CAAA,GAAA,yCAAW,EAAE;QACf,QAAQ,IAAI,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC;YAC3B,gBAAgB,CAAA,GAAA,YAAI,EAAE,cAAc,CAAC,GAAG;YACxC,cAAc;YACd,cAAc,sCAAgB,IAAI;YAClC,YAAY;YACZ,aAAa;QACf;QACA,UAAU,QAAQ,QAAQ;QAC1B,cAAc,QAAQ,YAAY,IAAI;QACtC,UAAU,QAAQ,QAAQ,IAAI;QAC9B,iBAAiB,QAAQ,eAAe;QACxC,OAAO,QAAQ,KAAK;QACpB,qBAAqB,QAAQ,mBAAmB;QAChD,aAAa,QAAQ,WAAW;QAChC,cAAc,QAAQ,YAAY;QAClC,4BAA4B,QAAQ,0BAA0B;QAC9D,qBAAqB,QAAQ,mBAAmB;IAClD;AAEK,MAAM,4CAAgB,CAAC,UAC5B,IAAI,CAAA,GAAA,yCAAW,EAAE;QACf,QAAQ,IAAI,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC;YAC3B,gBAAgB,CAAA,GAAA,YAAI,EAAE,cAAc,CAAC,GAAG;YACxC,cAAc;YACd,cAAc,sCAAgB,MAAM;YACpC,YAAY;YACZ,aAAa;QACf;QACA,UAAU,QAAQ,QAAQ,IAAI,0CAAoB,MAAM;QACxD,cAAc,QAAQ,YAAY,IAAI;QACtC,UAAU,QAAQ,QAAQ,IAAI;QAC9B,iBAAiB,QAAQ,eAAe,IAAI;QAC5C,OAAO,QAAQ,KAAK;QACpB,iBAAiB;YACf,OAAO;QACT;QACA,aAAa,QAAQ,WAAW;QAChC,cAAc,QAAQ,YAAY;QAClC,4BAA4B,QAAQ,0BAA0B;QAC9D,qBAAqB,QAAQ,mBAAmB;IAClD;AAEK,MAAM,4CAAe,CAAC,UAC3B,IAAI,CAAA,GAAA,yCAAW,EAAE;QACf,QAAQ,IAAI,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC;YAC3B,gBAAgB,CAAA,GAAA,YAAI,EAAE,cAAc,CAAC,GAAG;YACxC,cAAc;YACd,cAAc,sCAAgB,KAAK;YACnC,YAAY;YACZ,aAAa;QACf;QACA,UAAU,QAAQ,QAAQ,IAAI,0CAAoB,KAAK;QACvD,cAAc,QAAQ,YAAY,IAAI;QACtC,UAAU,QAAQ,QAAQ,IAAI;QAC9B,iBAAiB,QAAQ,QAAQ,IAAI;QACrC,OAAO;QACP,iBAAiB;YACf,YAAY,QAAQ,KAAK;QAC3B;QACA,qBAAqB,QAAQ,mBAAmB;QAChD,cAAc,QAAQ,QAAQ,GAAG,QAAQ,YAAY,IAAI,gBAAgB;QACzE,aAAa,QAAQ,WAAW;QAChC,4BAA4B,QAAQ,0BAA0B;QAC9D,8DAA8D;QAC9D,qBACE,QAAQ,mBAAmB,IAC1B,CAAA,CAAC;YACA,OAAO;gBACL,cAAc,SAAS,WAAW,CAAC,YAAY;gBAC/C,OAAO,SAAS,WAAW,CAAC,KAAK;YACnC;QACF,CAAA;IACJ;AAEK,MAAM,4CAAc,CAAC,UAC1B,IAAI,CAAA,GAAA,yCAAW,EAAE;QACf,QAAQ,IAAI,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC;YAC3B,gBAAgB,CAAA,GAAA,YAAI,EAAE,cAAc,CAAC,GAAG;YACxC,cAAc;YACd,cAAc,sCAAgB,IAAI;YAClC,YAAY;YACZ,aAAa;QACf;QACA,UAAU,QAAQ,QAAQ;QAC1B,cAAc,QAAQ,YAAY,IAAI;QACtC,UAAU,QAAQ,QAAQ,IAAI;QAC9B,iBAAiB,QAAQ,eAAe;QACxC,OAAO,QAAQ,KAAK;QACpB,qBAAqB,QAAQ,mBAAmB;QAChD,cAAc,QAAQ,YAAY,IAAI;QACtC,aAAa,QAAQ,WAAW;QAChC,4BAA4B,QAAQ,0BAA0B;QAC9D,qBAAqB,QAAQ,mBAAmB;IAClD;;;ADrKK,MAAM;IAcX,YAAY,OAA4B,CAAE;QACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;QAChC,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO,CAAC,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,OAAO,QAAQ,KAAK;QACnF,IAAI,CAAC,mBAAmB,GAAG,QAAQ,mBAAmB;QACtD,IAAI,CAAC,YAAY,GAAG,QAAQ,YAAY;QACxC,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;QAC5B,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe;QAC9C,IAAI,CAAC,YAAY,GAAG,QAAQ,YAAY;QACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;QAChC,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe;QAC9C,IAAI,CAAC,WAAW,GAAG,QAAQ,WAAW;QACtC,IAAI,CAAC,mBAAmB,GAAG,QAAQ,mBAAmB,IAAK,CAAA,CAAC,IAAM,CAAuB;QACzF,IAAI,CAAC,0BAA0B,GAAG,QAAQ,0BAA0B,IAAK,CAAA,CAAC,IAAM,CAAuB;QACvG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IAC3C;;QAEA;;;;;;;GAOC,QACa,QAA6B,CAAA,GAAA,yCAAW;;;QAEtD;;;;;;;GAOC,QACa,SAA+B,CAAA,GAAA,yCAAY;;;QAEzD;;;;;;;;;;;;GAYC,QACa,SAA+B,CAAA,GAAA,yCAAY;;;QAEzD;;;;;;;;;;;;GAYC,QACa,OAA2B,CAAA,GAAA,yCAAU;;;QAEnD;;;;;;;GAOC,QACa,SAA+B,CAAA,GAAA,yCAAY;;;QAEzD;;;;;;;GAOC,QACa,QAA6B,CAAA,GAAA,yCAAW;;;QAEtD;;;;;;;;;;;;;GAaC,QACa,OAA2B,CAAA,GAAA,yCAAU;;IAEnD;;;;;;GAMC,GACD,MAAM,YAAY;QAChB,MAAM,kBAAkB,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS;QACnD,IAAI,iBAAiB,aAAa;YAChC,IAAI,gBAAgB,YAAY,IAAI,gBAAgB,SAAS,IAAI;gBAC/D,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC;oBACtC,OAAO,gBAAgB,YAAY;oBACnC,UAAU,IAAI,CAAC,QAAQ;oBACvB,UAAU,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ;gBACjD;gBACA,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC5B,OAAO,OAAO,YAAY;YAC5B;YACA,OAAO,gBAAgB,WAAW;QACpC;QAEA,MAAM,cAAc,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;YACzD,UAAU,IAAI,CAAC,YAAY;YAC3B,UAAU,IAAI,CAAC,QAAQ;YACvB,OAAO,IAAI,CAAC,KAAK;YACjB,iBAAiB,IAAI,CAAC,eAAe;QACvC;QAEA,MAAM,qBAAE,iBAAiB,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAC1D,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;yBACpC;+BACA;YACA,UAAU,IAAI,CAAC,QAAQ;YACvB,UAAU,IAAI,CAAC,QAAQ;QACzB;QAEA,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAE5B,OAAO,OAAO,YAAY;IAC5B;IAEA,MAAc,YAAY,eAAE,WAAW,qBAAE,iBAAiB,YAAE,QAAQ,YAAE,QAAQ,gBAAE,YAAY,EAAmB,EAAE;QAC/G,IAAI;QACJ,IAAI,iBAAiB,eAAe;YAClC,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,aAAa;YAC3B,OAAO,MAAM,CAAC,QAAQ;YACtB,OAAO,MAAM,CAAC,iBAAiB,YAAY,YAAY;YACvD,OAAO,MAAM,CAAC,cAAc;YAC5B,OAAO,MAAM,CAAC,gBAAgB,YAAY,WAAW;YAErD,UAAU;gBAAE,MAAM;YAAO;QAC3B,OACE,UAAU;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;gBACX,MAAM;gBACN,eAAe,YAAY,YAAY;gBACvC,YAAY;gBACZ,cAAc,YAAY,WAAW;YACvC;YACA,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;QAGF,MAAM,WAAW,MAAM,CAAA,GAAA,mBAAI,EAAE,UAAU;YAAE,QAAQ;YAAQ,GAAG,OAAO;QAAC;QACpE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,eAAe,MAAM,SAAS,IAAI;YACxC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,aAAa,CAAC;QAC7G;QACA,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,MAAM,SAAS,IAAI;QAE3D,2EAA2E;QAC3E,OAAO,MAAM,OAAO,CAAC,OAAO,KAAK,IAAI;YAAE,GAAG,MAAM;YAAE,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC;QAAK,IAAI;IACtF;IAEA,MAAc,cAAc,SAAE,KAAK,YAAE,QAAQ,YAAE,QAAQ,gBAAE,YAAY,EAAqB,EAAE;QAC1F,IAAI;QACJ,IAAI,iBAAiB,eAAe;YAClC,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,aAAa;YAC3B,OAAO,MAAM,CAAC,iBAAiB;YAC/B,OAAO,MAAM,CAAC,cAAc;YAE5B,UAAU;gBAAE,MAAM;YAAO;QAC3B,OACE,UAAU;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;gBACX,eAAe;gBACf,YAAY;YACd;YACA,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;QAGF,MAAM,WAAW,MAAM,CAAA,GAAA,mBAAI,EAAE,UAAU;YAAE,QAAQ;YAAQ,GAAG,OAAO;QAAC;QACpE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,eAAe,MAAM,SAAS,IAAI;YACxC,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,aAAa,CAAC;QAC/G;QACA,MAAM,gBAAgB,IAAI,CAAC,0BAA0B,CAAC,MAAM,SAAS,IAAI;QACzE,cAAc,aAAa,GAAG,cAAc,aAAa,IAAI;QAC7D,OAAO;IACT;AACF;;;;;;AEpQA,IAAI,8BAAuB;AAC3B,IAAI,6BAAyB;AAC7B,IAAI,kCAAuC;AAC3C,IAAI,mCAA4D;AAChE,IAAI,oCAAuC;AAgEpC,SAAS,0CAAmB,OAAkC;IACnE,IAAI,CAAA,GAAA,kBAAU,EAAE,WAAW,KAAK,WAC9B,OAAO,CAAC;QACN,MAAM,WAAW,OAAO;YACtB,IAAI,CAAC,6BAAO;gBACV,8BAAQ,QAAQ,mBAAmB,IAAK,MAAM,QAAQ,SAAS;gBAC/D,6BAAO,QAAQ,mBAAmB,GAAG,aAAa;gBAClD,MAAM,UAAW,CAAA,MAAM,QAAQ,MAAM,EAAE,WAAU,GAAI;gBAErD,IAAI,QAAQ,WAAW,EACrB,MAAM,QAAQ,OAAO,CAAC,QAAQ,WAAW,CAAC;2BAAE;0BAAO;6BAAM;gBAAQ;YAErE;YAEA,OAAO,GAAG;QACZ;QAEA,OAAO;IACT;IAGF,OAAO,CAAC;QACN,MAAM,mBAA2C,CAAC;YAChD,IAAI,QAAQ,mBAAmB,EAAE;gBAC/B,8BAAQ,QAAQ,mBAAmB;gBACnC,6BAAO;YACT,OAAO;gBACL,IAAI,CAAC,iCACH,kCAAY,kCAAY,QAAQ,SAAS;gBAE3C,8BAAQ,gCAAU,IAAI;gBACtB,6BAAO;YACT;YAEA,IAAI;YACJ,IAAI,QAAQ,MAAM,EAAE;gBAClB,IAAI,CAAC,kCACH,mCAAa,kCAAY,QAAQ,MAAM,CAAC,SAAS;gBAEnD,UAAU,iCAAW,IAAI,IAAI;YAC/B;YAEA,IAAI,CAAC,qCAAe,QAAQ,WAAW,EACrC,oCAAc,kCAAY,QAAQ,OAAO,CAAC,QAAQ,WAAW,CAAC;uBAAE;sBAAO;yBAAM;YAAQ;YAEvF,mCAAa;YAEb,6DAA6D;YAC7D,oCAAoC;YACpC,qBAAO,gBAAC;gBAAW,GAAG,KAAK;;QAC7B;QAEA,iBAAiB,WAAW,GAAG,CAAC,gBAAgB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;QAE5F,OAAO;IACT;AACF;AAUO,SAAS;IACd,IAAI,CAAC,+BAAS,CAAC,4BACb,MAAM,IAAI,MAAM;IAGlB,OAAO;eAAE;cAAO;IAAK;AACvB;AAEA,SAAS,kCAAe,OAAmB;IACzC,IAAI,SAAS;IACb,IAAI;IAEJ,MAAM,YAAY,QAAQ,IAAI,CAC5B,CAAC;QACC,SAAS;QACT,WAAW;IACb,GACA,CAAC;QACC,SAAS;QACT,WAAW;IACb;IAGF,MAAM,OAAO;QACX,OAAQ;YACN,KAAK;gBACH,MAAM;YACR,KAAK;gBACH,MAAM;YACR;gBACE,OAAO;QACX;IACF;IAEA,OAAO;cAAE;IAAK;AAChB;;;;;;;ACnGO,eAAe,0CACpB,MAAc,EACd,aAIM,EACN,OAEC;IAED,MAAM,uBAAE,mBAAmB,YAAE,QAAQ,WAAE,OAAO,EAAE,GAAG,aAAa,GAAG,MAAM,OAAO,CAAC,iBAC7E,WAAW,CAAC,IACZ,iBAAiB,CAAC;IAEtB,MAAM,kBAAkB,wBAAwB,QAAQ,EAAE,GAAG;QAAC;KAAM;IACpE,IAAI,aAAa,cACf,gBAAgB,IAAI,CAAC,MAAM;IAE7B,IAAI,MAAM,OAAO,CAAC,gBAChB,gBAAgB,IAAI,CAAC,QAAQ;IAG/B,MAAM,UAAU,CAAA,GAAA,wBAAW,EAAE,KAAK,CAAC,aAAa,iBAAiB;QAC/D,GAAG,WAAW;QACd,KAAK;YAAE,MAAM;QAA+C;IAC9D;IACA,MAAM,iBAAiB,CAAA,GAAA,yCAAgB,EAAE,SAAS;QAAE,SAAS,WAAW;IAAM;IAE9E,QAAQ,KAAK,CAAC,GAAG,CAAC;IAElB,MAAM,CAAC,SAAE,KAAK,YAAE,QAAQ,UAAE,MAAM,YAAE,QAAQ,EAAE,EAAE,cAAc,aAAa,GAAG,MAAM,CAAA,GAAA,yCAAe,EAC/F,SACA;QAAE,UAAU;IAAO,GACnB;IAEF,MAAM,SAAS,CAAA,GAAA,wCAAW,EAAE;QAAE,mBAAmB;IAAK,GAAG;IACzD,MAAM,SAAS,CAAA,GAAA,wCAAW,EAAE;QAAE,mBAAmB;IAAK,GAAG;IAEzD,OAAO,CAAA,GAAA,yCAAa,EAAE;gBACpB;gBACA;eACA;kBACA;gBACA;kBACA;QACA,SAAS;iBACT;QACA,aAAa,IAAI;IACnB;AACF;;", "sources": ["src/index.ts", "src/usePromise.ts", "src/useDeepMemo.ts", "src/useLatest.ts", "src/showFailureToast.ts", "src/useCachedState.ts", "src/helpers.ts", "src/useCachedPromise.ts", "src/useFetch.ts", "src/fetch-utils.ts", "src/useExec.ts", "src/exec-utils.ts", "src/useStreamJSON.ts", "src/useSQL.tsx", "src/useForm.tsx", "src/useAI.ts", "src/useFrecencySorting.ts", "src/useLocalStorage.ts", "src/icon/index.ts", "src/icon/avatar.ts", "src/icon/color.ts", "src/icon/favicon.ts", "src/icon/progress.ts", "src/oauth/index.ts", "src/oauth/OAuthService.ts", "src/oauth/providers.ts", "src/oauth/withAccessToken.tsx", "src/run-applescript.ts"], "sourcesContent": ["export { usePromise } from \"./usePromise\";\nexport { useCachedState } from \"./useCachedState\";\nexport { useCachedPromise } from \"./useCachedPromise\";\nexport { useFetch } from \"./useFetch\";\nexport { useExec } from \"./useExec\";\nexport { useStreamJSON } from \"./useStreamJSON\";\nexport { useSQL } from \"./useSQL\";\nexport { useForm, FormValidation } from \"./useForm\";\nexport { useAI } from \"./useAI\";\nexport { useFrecencySorting } from \"./useFrecencySorting\";\nexport { useLocalStorage } from \"./useLocalStorage\";\n\nexport { getAvatarIcon, getFavicon, getProgressIcon } from \"./icon\";\n\nexport { OAuthService, withAccessToken, getAccessToken } from \"./oauth\";\n\nexport { runAppleScript } from \"./run-applescript\";\nexport { showFailureToast } from \"./showFailureToast\";\n\nexport type { PromiseOptions } from \"./usePromise\";\nexport type { CachedPromiseOptions } from \"./useCachedPromise\";\nexport type {\n  OAuthServiceOptions,\n  OnAuthorizeParams,\n  WithAccessTokenComponentOrFn,\n  ProviderWithDefaultClientOptions,\n  ProviderOptions,\n} from \"./oauth\";\nexport type { AsyncState, MutatePromise } from \"./types\";\n\nexport type { Response } from \"node-fetch-cjs\";\n", "import { useEffect, useCallback, MutableRefObject, useRef, useState } from \"react\";\nimport { environment, LaunchType } from \"@raycast/api\";\nimport { useDeepMemo } from \"./useDeepMemo\";\nimport {\n  FunctionReturningPromise,\n  MutatePromise,\n  UsePromiseReturnType,\n  AsyncState,\n  FunctionReturningPaginatedPromise,\n  UnwrapReturn,\n  PaginationOptions,\n} from \"./types\";\nimport { useLatest } from \"./useLatest\";\nimport { showFailureToast } from \"./showFailureToast\";\n\nexport type PromiseOptions<T extends FunctionReturningPromise | FunctionReturningPaginatedPromise> = {\n  /**\n   * A reference to an `AbortController` to cancel a previous call when triggering a new one\n   */\n  abortable?: MutableRefObject<AbortController | null | undefined>;\n  /**\n   * Whether to actually execute the function or not.\n   * This is useful for cases where one of the function's arguments depends on something that\n   * might not be available right away (for example, depends on some user inputs). Because React requires\n   * every hooks to be defined on the render, this flag enables you to define the hook right away but\n   * wait util you have all the arguments ready to execute the function.\n   */\n  execute?: boolean;\n  /**\n   * Called when an execution fails. By default it will log the error and show\n   * a generic failure toast.\n   */\n  onError?: (error: Error) => void | Promise<void>;\n  /**\n   * Called when an execution succeeds.\n   */\n  onData?: (data: UnwrapReturn<T>, pagination?: PaginationOptions<UnwrapReturn<T>>) => void | Promise<void>;\n  /**\n   * Called when an execution will start\n   */\n  onWillExecute?: (parameters: Parameters<T>) => void;\n};\n\n/**\n * Wraps an asynchronous function or a function that returns a Promise in another function, and returns the {@link AsyncState} corresponding to the execution of the function.\n *\n * @remark This overload should be used when working with paginated data sources.\n *\n * @example\n * ```\n * import { setTimeout } from \"node:timers/promises\";\n * import { useState } from \"react\";\n * import { List } from \"@raycast/api\";\n * import { usePromise } from \"@raycast/utils\";\n *\n * export default function Command() {\n *   const [searchText, setSearchText] = useState(\"\");\n *\n *   const { isLoading, data, pagination } = usePromise(\n *     (searchText: string) => async (options: { page: number }) => {\n *       await setTimeout(200);\n *       const newData = Array.from({ length: 25 }, (_v, index) => ({\n *         index,\n *         page: options.page,\n *         text: searchText,\n *       }));\n *       return { data: newData, hasMore: options.page < 10 };\n *     },\n *     [searchText]\n *   );\n *\n *   return (\n *     <List isLoading={isLoading} onSearchTextChange={setSearchText} pagination={pagination}>\n *       {data?.map((item) => (\n *         <List.Item\n *           key={`${item.page} ${item.index} ${item.text}`}\n *           title={`Page ${item.page} Item ${item.index}`}\n *           subtitle={item.text}\n *         />\n *       ))}\n *     </List>\n *   );\n * };\n * ```\n */\nexport function usePromise<T extends FunctionReturningPaginatedPromise<[]>>(\n  fn: T,\n): UsePromiseReturnType<UnwrapReturn<T>>;\nexport function usePromise<T extends FunctionReturningPaginatedPromise>(\n  fn: T,\n  args: Parameters<T>,\n  options?: PromiseOptions<T>,\n): UsePromiseReturnType<UnwrapReturn<T>>;\n\n/**\n * Wraps an asynchronous function or a function that returns a Promise and returns the {@link AsyncState} corresponding to the execution of the function.\n *\n * @remark The function is assumed to be constant (eg. changing it won't trigger a revalidation).\n *\n * @example\n * ```\n * import { usePromise } from '@raycast/utils';\n *\n * export default function Command() {\n *   const abortable = useRef<AbortController>();\n *   const { isLoading, data, revalidate } = usePromise(async (url: string) => {\n *     const response = await fetch(url, { signal: abortable.current?.signal });\n *     const result = await response.text();\n *     return result\n *   },\n *   ['https://api.example'],\n *   {\n *     abortable\n *   });\n *\n *   return (\n *     <Detail\n *       isLoading={isLoading}\n *       markdown={data}\n *       actions={\n *         <ActionPanel>\n *           <Action title=\"Reload\" onAction={() => revalidate()} />\n *         </ActionPanel>\n *       }\n *     />\n *   );\n * };\n * ```\n */\nexport function usePromise<T extends FunctionReturningPromise<[]>>(fn: T): UsePromiseReturnType<UnwrapReturn<T>>;\nexport function usePromise<T extends FunctionReturningPromise>(\n  fn: T,\n  args: Parameters<T>,\n  options?: PromiseOptions<T>,\n): UsePromiseReturnType<UnwrapReturn<T>>;\n\nexport function usePromise<T extends FunctionReturningPromise | FunctionReturningPaginatedPromise>(\n  fn: T,\n  args?: Parameters<T>,\n  options?: PromiseOptions<T>,\n): UsePromiseReturnType<any> {\n  const lastCallId = useRef(0);\n  const [state, set] = useState<AsyncState<UnwrapReturn<T>>>({ isLoading: true });\n\n  const fnRef = useLatest(fn);\n  const latestAbortable = useLatest(options?.abortable);\n  const latestArgs = useLatest(args || []);\n  const latestOnError = useLatest(options?.onError);\n  const latestOnData = useLatest(options?.onData);\n  const latestOnWillExecute = useLatest(options?.onWillExecute);\n  const latestValue = useLatest(state.data);\n  const latestCallback = useRef<(...args: Parameters<T>) => Promise<UnwrapReturn<T>>>();\n\n  const paginationArgsRef = useRef<PaginationOptions>({ page: 0 });\n  const usePaginationRef = useRef(false);\n  const hasMoreRef = useRef(true);\n  const pageSizeRef = useRef(50);\n\n  const callback = useCallback(\n    (...args: Parameters<T>): Promise<UnwrapReturn<T>> => {\n      const callId = ++lastCallId.current;\n\n      if (latestAbortable.current) {\n        latestAbortable.current.current?.abort();\n        latestAbortable.current.current = new AbortController();\n      }\n\n      latestOnWillExecute.current?.(args);\n\n      set((prevState) => ({ ...prevState, isLoading: true }));\n\n      const promiseOrPaginatedPromise = bindPromiseIfNeeded(fnRef.current)(...args);\n\n      function handleError(error: any) {\n        if (error.name == \"AbortError\") {\n          return error;\n        }\n\n        if (callId === lastCallId.current) {\n          // handle errors\n          if (latestOnError.current) {\n            latestOnError.current(error);\n          } else {\n            if (environment.launchType !== LaunchType.Background) {\n              showFailureToast(error, {\n                title: \"Failed to fetch latest data\",\n                primaryAction: {\n                  title: \"Retry\",\n                  onAction(toast) {\n                    toast.hide();\n                    latestCallback.current?.(...((latestArgs.current || []) as Parameters<T>));\n                  },\n                },\n              });\n            }\n          }\n          set({ error, isLoading: false });\n        }\n\n        return error;\n      }\n\n      if (typeof promiseOrPaginatedPromise === \"function\") {\n        usePaginationRef.current = true;\n        return promiseOrPaginatedPromise(paginationArgsRef.current).then(\n          // @ts-expect-error too complicated for TS\n          ({ data, hasMore, cursor }: { data: UnwrapReturn<T>; hasMore: boolean; cursor?: any }) => {\n            if (callId === lastCallId.current) {\n              if (paginationArgsRef.current) {\n                paginationArgsRef.current.cursor = cursor;\n                paginationArgsRef.current.lastItem = data?.[data.length - 1];\n              }\n\n              if (latestOnData.current) {\n                latestOnData.current(data, paginationArgsRef.current);\n              }\n\n              if (hasMore) {\n                pageSizeRef.current = data.length;\n              }\n              hasMoreRef.current = hasMore;\n\n              set((previousData) => {\n                if (paginationArgsRef.current.page === 0) {\n                  return { data, isLoading: false };\n                }\n                // @ts-expect-error we know it's an array here\n                return { data: (previousData.data || [])?.concat(data), isLoading: false };\n              });\n            }\n\n            return data;\n          },\n          (error: unknown) => {\n            hasMoreRef.current = false;\n            return handleError(error);\n          },\n        ) as Promise<UnwrapReturn<T>>;\n      }\n\n      usePaginationRef.current = false;\n      return promiseOrPaginatedPromise.then((data: UnwrapReturn<T>) => {\n        if (callId === lastCallId.current) {\n          if (latestOnData.current) {\n            latestOnData.current(data);\n          }\n          set({ data, isLoading: false });\n        }\n\n        return data;\n      }, handleError) as Promise<UnwrapReturn<T>>;\n    },\n    [\n      latestAbortable,\n      latestOnData,\n      latestOnError,\n      latestArgs,\n      fnRef,\n      set,\n      latestCallback,\n      latestOnWillExecute,\n      paginationArgsRef,\n    ],\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  );\n\n  latestCallback.current = callback;\n\n  const revalidate = useCallback(() => {\n    // reset the pagination\n    paginationArgsRef.current = { page: 0 };\n\n    const args = (latestArgs.current || []) as Parameters<T>;\n    return callback(...args);\n  }, [callback, latestArgs]);\n\n  const mutate = useCallback<MutatePromise<Awaited<ReturnType<T>>, undefined>>(\n    async (asyncUpdate, options) => {\n      let dataBeforeOptimisticUpdate: Awaited<ReturnType<T>> | undefined;\n      try {\n        if (options?.optimisticUpdate) {\n          if (typeof options?.rollbackOnError !== \"function\" && options?.rollbackOnError !== false) {\n            // keep track of the data before the optimistic update,\n            // but only if we need it (eg. only when we want to automatically rollback after)\n            dataBeforeOptimisticUpdate = structuredClone(latestValue.current?.value);\n          }\n          const update = options.optimisticUpdate;\n          set((prevState) => ({ ...prevState, data: update(prevState.data) }));\n        }\n        return await asyncUpdate;\n      } catch (err) {\n        if (typeof options?.rollbackOnError === \"function\") {\n          const update = options.rollbackOnError;\n          set((prevState) => ({ ...prevState, data: update(prevState.data) }));\n        } else if (options?.optimisticUpdate && options?.rollbackOnError !== false) {\n          set((prevState) => ({ ...prevState, data: dataBeforeOptimisticUpdate }));\n        }\n        throw err;\n      } finally {\n        if (options?.shouldRevalidateAfter !== false) {\n          if (environment.launchType === LaunchType.Background || environment.commandMode === \"menu-bar\") {\n            // when in the background or in a menu bar, we are going to await the revalidation\n            // to make sure we get the right data at the end of the mutation\n            await revalidate();\n          } else {\n            revalidate();\n          }\n        }\n      }\n    },\n    [revalidate, latestValue, set],\n  );\n\n  const onLoadMore = useCallback(() => {\n    paginationArgsRef.current.page += 1;\n    const args = (latestArgs.current || []) as Parameters<T>;\n    callback(...args);\n  }, [paginationArgsRef, latestValue, latestArgs, callback]);\n\n  // revalidate when the args change\n  useEffect(() => {\n    // reset the pagination\n    paginationArgsRef.current = { page: 0 };\n\n    if (options?.execute !== false) {\n      callback(...((args || []) as Parameters<T>));\n    } else {\n      // cancel the previous request if we don't want to execute anymore\n      if (latestAbortable.current) {\n        latestAbortable.current.current?.abort();\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [useDeepMemo([args, options?.execute, callback]), latestAbortable, paginationArgsRef]);\n\n  // abort request when unmounting\n  useEffect(() => {\n    return () => {\n      if (latestAbortable.current) {\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        latestAbortable.current.current?.abort();\n      }\n    };\n  }, [latestAbortable]);\n\n  // we only want to show the loading indicator if the promise is executing\n  const isLoading = options?.execute !== false ? state.isLoading : false;\n\n  // @ts-expect-error loading is has some fixed value in the enum which\n  const stateWithLoadingFixed: AsyncState<Awaited<ReturnType<T>>> = { ...state, isLoading };\n\n  const pagination = usePaginationRef.current\n    ? {\n        pageSize: pageSizeRef.current,\n        hasMore: hasMoreRef.current,\n        onLoadMore,\n      }\n    : undefined;\n\n  return { ...stateWithLoadingFixed, revalidate, mutate, pagination };\n}\n\n/** Bind the fn if it's a Promise method */\nfunction bindPromiseIfNeeded<T>(fn: T): T {\n  if (fn === (Promise.all as any)) {\n    // @ts-expect-error this is fine\n    return fn.bind(Promise);\n  }\n  if (fn === (Promise.race as any)) {\n    // @ts-expect-error this is fine\n    return fn.bind(Promise);\n  }\n  if (fn === (Promise.resolve as any)) {\n    // @ts-expect-error this is fine\n    return fn.bind(Promise as any);\n  }\n  if (fn === (Promise.reject as any)) {\n    // @ts-expect-error this is fine\n    return fn.bind(Promise);\n  }\n  return fn;\n}\n", "import { useRef, useMemo } from \"react\";\nimport { dequal } from \"dequal/lite\";\n\n/**\n * @param value the value to be memoized (usually a dependency list)\n * @returns a memoized version of the value as long as it remains deeply equal\n */\nexport function useDeepMemo<T>(value: T) {\n  const ref = useRef<T>(value);\n  const signalRef = useRef<number>(0);\n\n  if (!dequal(value, ref.current)) {\n    ref.current = value;\n    signalRef.current += 1;\n  }\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return useMemo(() => ref.current, [signalRef.current]);\n}\n", "import { useRef } from \"react\";\n\n/**\n * Returns the latest state.\n *\n * This is mostly useful to get access to the latest value of some props or state inside an asynchronous callback, instead of that value at the time the callback was created from.\n */\nexport function useLatest<T>(value: T): { readonly current: T } {\n  const ref = useRef(value);\n  ref.current = value;\n  return ref;\n}\n", "import * as fs from \"node:fs\";\nimport * as path from \"node:path\";\nimport { Clipboard, environment, open, Toast, showToast } from \"@raycast/api\";\n\n/**\n * Shows a failure Toast for a given Error.\n *\n * @example\n * ```typescript\n * import { showHUD } from \"@raycast/api\";\n * import { runAppleScript, showFailureToast } from \"@raycast/utils\";\n *\n * export default async function () {\n *   try {\n *     const res = await runAppleScript(\n *       `\n *       on run argv\n *         return \"hello, \" & item 1 of argv & \".\"\n *       end run\n *       `,\n *       [\"world\"]\n *     );\n *     await showHUD(res);\n *   } catch (error) {\n *     showFailureToast(error, { title: \"Could not run AppleScript\" });\n *   }\n * }\n * ```\n */\nexport function showFailureToast(error: unknown, options?: { title?: string; primaryAction?: Toast.ActionOptions }) {\n  const message = error instanceof Error ? error.message : String(error);\n  return showToast({\n    style: Toast.Style.Failure,\n    title: options?.title ?? \"Something went wrong\",\n    message: message,\n    primaryAction: options?.primaryAction ?? handleErrorToastAction(error),\n    secondaryAction: options?.primaryAction ? handleErrorToastAction(error) : undefined,\n  });\n}\n\nconst handleErrorToastAction = (error: unknown): Toast.ActionOptions => {\n  let privateExtension = true;\n  let title = \"[Extension Name]...\";\n  let extensionURL = \"\";\n  try {\n    const packageJSON = JSON.parse(fs.readFileSync(path.join(environment.assetsPath, \"..\", \"package.json\"), \"utf8\"));\n    title = `[${packageJSON.title}]...`;\n    extensionURL = `https://raycast.com/${packageJSON.owner || packageJSON.author}/${packageJSON.name}`;\n    if (!packageJSON.owner || packageJSON.access === \"public\") {\n      privateExtension = false;\n    }\n  } catch (err) {\n    // no-op\n  }\n\n  // if it's a private extension, we can't construct the URL to report the error\n  // so we fallback to copying the error to the clipboard\n  const fallback = environment.isDevelopment || privateExtension;\n\n  const stack = error instanceof Error ? error?.stack || error?.message || \"\" : String(error);\n\n  return {\n    title: fallback ? \"Copy Logs\" : \"Report Error\",\n    onAction(toast) {\n      toast.hide();\n      if (fallback) {\n        Clipboard.copy(stack);\n      } else {\n        open(\n          `https://github.com/raycast/extensions/issues/new?&labels=extension%2Cbug&template=extension_bug_report.yml&title=${encodeURIComponent(\n            title,\n          )}&extension-url=${encodeURI(extensionURL)}&description=${encodeURIComponent(\n            `#### Error:\n\\`\\`\\`\n${stack}\n\\`\\`\\`\n`,\n          )}`,\n        );\n      }\n    },\n  };\n};\n", "import { use<PERSON><PERSON>back, Dispatch, SetStateAction, useSyncExternalStore, useMemo } from \"react\";\nimport { Cache } from \"@raycast/api\";\nimport { useLatest } from \"./useLatest\";\nimport { replacer, reviver } from \"./helpers\";\n\nconst rootCache = /* #__PURE__ */ Symbol(\"cache without namespace\");\nconst cacheMap = /* #__PURE__ */ new Map<string | symbol, Cache>();\n\n/**\n * Returns a stateful value, and a function to update it. The value will be kept between command runs.\n *\n * @remark The value needs to be JSON serializable.\n *\n * @param key - The unique identifier of the state. This can be used to share the state across components and/or commands.\n * @param initialState - The initial value of the state if there aren't any in the Cache yet.\n */\nexport function useCachedState<T>(\n  key: string,\n  initialState: T,\n  config?: { cacheNamespace?: string },\n): [T, Dispatch<SetStateAction<T>>];\nexport function useCachedState<T = undefined>(key: string): [T | undefined, Dispatch<SetStateAction<T | undefined>>];\nexport function useCachedState<T>(\n  key: string,\n  initialState?: T,\n  config?: { cacheNamespace?: string },\n): [T, Dispatch<SetStateAction<T>>] {\n  const cacheKey = config?.cacheNamespace || rootCache;\n  const cache =\n    cacheMap.get(cacheKey) || cacheMap.set(cacheKey, new Cache({ namespace: config?.cacheNamespace })).get(cacheKey);\n\n  if (!cache) {\n    throw new Error(\"Missing cache\");\n  }\n\n  const keyRef = useLatest(key);\n  const initialValueRef = useLatest(initialState);\n\n  const cachedState = useSyncExternalStore(cache.subscribe, () => {\n    try {\n      return cache.get(keyRef.current);\n    } catch (error) {\n      console.error(\"Could not get Cache data:\", error);\n      return undefined;\n    }\n  });\n\n  const state = useMemo(() => {\n    if (typeof cachedState !== \"undefined\") {\n      if (cachedState === \"undefined\") {\n        return undefined;\n      }\n      try {\n        return JSON.parse(cachedState, reviver);\n      } catch (err) {\n        // the data got corrupted somehow\n        console.warn(\"The cached data is corrupted\", err);\n        return initialValueRef.current;\n      }\n    } else {\n      return initialValueRef.current;\n    }\n  }, [cachedState, initialValueRef]);\n\n  const stateRef = useLatest(state);\n\n  const setStateAndCache = useCallback(\n    (updater: SetStateAction<T>) => {\n      // @ts-expect-error TS struggles to infer the types as T could potentially be a function\n      const newValue = typeof updater === \"function\" ? updater(stateRef.current) : updater;\n      if (typeof newValue === \"undefined\") {\n        cache.set(keyRef.current, \"undefined\");\n      } else {\n        const stringifiedValue = JSON.stringify(newValue, replacer);\n        cache.set(keyRef.current, stringifiedValue);\n      }\n      return newValue;\n    },\n    [cache, keyRef, stateRef],\n  );\n\n  return [state, setStateAndCache];\n}\n", "// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function replacer(this: any, key: string, _value: unknown) {\n  const value = this[key];\n  if (value instanceof Date) {\n    return `__raycast_cached_date__${value.toString()}`;\n  }\n  if (Buffer.isBuffer(value)) {\n    return `__raycast_cached_buffer__${value.toString(\"base64\")}`;\n  }\n  return _value;\n}\n\nexport function reviver(_key: string, value: unknown) {\n  if (typeof value === \"string\" && value.startsWith(\"__raycast_cached_date__\")) {\n    return new Date(value.replace(\"__raycast_cached_date__\", \"\"));\n  }\n  if (typeof value === \"string\" && value.startsWith(\"__raycast_cached_buffer__\")) {\n    return Buffer.from(value.replace(\"__raycast_cached_buffer__\", \"\"), \"base64\");\n  }\n  return value;\n}\n", "import { useEffect, useRef, useCallback } from \"react\";\nimport hash from \"object-hash\";\nimport {\n  FunctionReturningPromise,\n  UseCachedPromiseReturnType,\n  MutatePromise,\n  FunctionReturningPaginatedPromise,\n  UnwrapReturn,\n  PaginationOptions,\n} from \"./types\";\nimport { useCachedState } from \"./useCachedState\";\nimport { usePromise, PromiseOptions } from \"./usePromise\";\n\nimport { useLatest } from \"./useLatest\";\n\n// Symbol to differentiate an empty cache from `undefined`\nconst emptyCache = /* #__PURE__ */ Symbol();\n\nexport type CachedPromiseOptions<\n  T extends FunctionReturningPromise | FunctionReturningPaginatedPromise,\n  U,\n> = PromiseOptions<T> & {\n  /**\n   * The initial data if there aren't any in the Cache yet.\n   */\n  initialData?: U;\n  /**\n   * Tells the hook to keep the previous results instead of returning the initial value\n   * if there aren't any in the cache for the new arguments.\n   * This is particularly useful when used for data for a List to avoid flickering.\n   */\n  keepPreviousData?: boolean;\n};\n\n/**\n * Wraps an asynchronous function or a function that returns a Promise in another function, and returns the {@link AsyncState} corresponding to the execution of the function. The last value will be kept between command runs.\n *\n * @remark This overload should be used when working with paginated data sources.\n * @remark When paginating, only the first page will be cached.\n *\n * @example\n * ```\n * import { setTimeout } from \"node:timers/promises\";\n * import { useState } from \"react\";\n * import { List } from \"@raycast/api\";\n * import { useCachedPromise } from \"@raycast/utils\";\n *\n * export default function Command() {\n *   const [searchText, setSearchText] = useState(\"\");\n *\n *   const { isLoading, data, pagination } = useCachedPromise(\n *     (searchText: string) => async (options: { page: number }) => {\n *       await setTimeout(200);\n *       const newData = Array.from({ length: 25 }, (_v, index) => ({\n *         index,\n *         page: options.page,\n *         text: searchText,\n *       }));\n *       return { data: newData, hasMore: options.page < 10 };\n *     },\n *     [searchText],\n *   );\n *\n *   return (\n *     <List isLoading={isLoading} onSearchTextChange={setSearchText} pagination={pagination}>\n *       {data?.map((item) => (\n *         <List.Item\n *           key={`${item.page} ${item.index} ${item.text}`}\n *           title={`Page ${item.page} Item ${item.index}`}\n *           subtitle={item.text}\n *         />\n *       ))}\n *     </List>\n *   );\n * }\n * ```\n */\nexport function useCachedPromise<T extends FunctionReturningPaginatedPromise<[]>>(\n  fn: T,\n): UseCachedPromiseReturnType<UnwrapReturn<T>, undefined>;\nexport function useCachedPromise<T extends FunctionReturningPaginatedPromise, U extends any[] = any[]>(\n  fn: T,\n  args: Parameters<T>,\n  options?: CachedPromiseOptions<T, U>,\n): UseCachedPromiseReturnType<UnwrapReturn<T>, U>;\n\n/**\n * Wraps an asynchronous function or a function that returns a Promise and returns the {@link AsyncState} corresponding to the execution of the function. The last value will be kept between command runs.\n *\n * @remark The value needs to be JSON serializable.\n * @remark The function is assumed to be constant (eg. changing it won't trigger a revalidation).\n *\n * @example\n * ```\n * import { useCachedPromise } from '@raycast/utils';\n *\n * export default function Command() {\n *   const abortable = useRef<AbortController>();\n *   const { isLoading, data, revalidate } = useCachedPromise(async (url: string) => {\n *     const response = await fetch(url, { signal: abortable.current?.signal });\n *     const result = await response.text();\n *     return result\n *   },\n *   ['https://api.example'],\n *   {\n *     abortable\n *   });\n *\n *   return (\n *     <Detail\n *       isLoading={isLoading}\n *       markdown={data}\n *       actions={\n *         <ActionPanel>\n *           <Action title=\"Reload\" onAction={() => revalidate()} />\n *         </ActionPanel>\n *       }\n *     />\n *   );\n * };\n * ```\n */\nexport function useCachedPromise<T extends FunctionReturningPromise<[]>>(\n  fn: T,\n): UseCachedPromiseReturnType<UnwrapReturn<T>, undefined>;\nexport function useCachedPromise<T extends FunctionReturningPromise, U = undefined>(\n  fn: T,\n  args: Parameters<T>,\n  options?: CachedPromiseOptions<T, U>,\n): UseCachedPromiseReturnType<UnwrapReturn<T>, U>;\n\nexport function useCachedPromise<\n  T extends FunctionReturningPromise | FunctionReturningPaginatedPromise,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  U extends any[] | undefined = undefined,\n>(fn: T, args?: Parameters<T>, options?: CachedPromiseOptions<T, U>) {\n  /**\n   * The hook generates a cache key from the promise it receives & its arguments.\n   * Sometimes that's not enough to guarantee uniqueness, so hooks that build on top of `useCachedPromise` can\n   * use an `internal_cacheKeySuffix` to help it.\n   *\n   * @remark For internal use only.\n   */\n  const {\n    initialData,\n    keepPreviousData,\n    internal_cacheKeySuffix,\n    ...usePromiseOptions\n  }: CachedPromiseOptions<T, U> & { internal_cacheKeySuffix?: string } = options || {};\n  const lastUpdateFrom = useRef<\"cache\" | \"promise\">();\n\n  const [cachedData, mutateCache] = useCachedState<typeof emptyCache | (UnwrapReturn<T> | U)>(\n    hash(args || []) + (internal_cacheKeySuffix ?? \"\"),\n    emptyCache,\n    {\n      cacheNamespace: hash(fn),\n    },\n  );\n\n  // Use a ref to store previous returned data. Use the inital data as its inital value from the cache.\n  const laggyDataRef = useRef<Awaited<ReturnType<T>> | U>(cachedData !== emptyCache ? cachedData : (initialData as U));\n  const paginationArgsRef = useRef<PaginationOptions<UnwrapReturn<T> | U> | undefined>(undefined);\n\n  const {\n    mutate: _mutate,\n    revalidate,\n    ...state\n    // @ts-expect-error fn has the same signature in both usePromise and useCachedPromise\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } = usePromise(fn, args || ([] as any as Parameters<T>), {\n    ...usePromiseOptions,\n    onData(data, pagination) {\n      paginationArgsRef.current = pagination;\n      if (usePromiseOptions.onData) {\n        usePromiseOptions.onData(data, pagination);\n      }\n      if (pagination && pagination.page > 0) {\n        // don't cache beyond the first page\n        return;\n      }\n      lastUpdateFrom.current = \"promise\";\n      laggyDataRef.current = data;\n      mutateCache(data);\n    },\n  });\n\n  let returnedData: U | Awaited<ReturnType<T>> | UnwrapReturn<T>;\n  const pagination = state.pagination;\n  // when paginating, only the first page gets cached, so we return the data we get from `usePromise`, because\n  // it will be accumulated.\n  if (paginationArgsRef.current && paginationArgsRef.current.page > 0 && state.data) {\n    returnedData = state.data as UnwrapReturn<T>;\n    // if the latest update if from the Promise, we keep it\n  } else if (lastUpdateFrom.current === \"promise\") {\n    returnedData = laggyDataRef.current;\n  } else if (keepPreviousData && cachedData !== emptyCache) {\n    // if we want to keep the latest data, we pick the cache but only if it's not empty\n    returnedData = cachedData;\n    if (pagination) {\n      pagination.hasMore = true;\n      pagination.pageSize = cachedData.length;\n    }\n  } else if (keepPreviousData && cachedData === emptyCache) {\n    // if the cache is empty, we will return the previous data\n    returnedData = laggyDataRef.current;\n    // there are no special cases, so either return the cache or initial data\n  } else if (cachedData !== emptyCache) {\n    returnedData = cachedData;\n    if (pagination) {\n      pagination.hasMore = true;\n      pagination.pageSize = cachedData.length;\n    }\n  } else {\n    returnedData = initialData as U;\n  }\n\n  const latestData = useLatest(returnedData);\n\n  // we rewrite the mutate function to update the cache instead\n  const mutate = useCallback<MutatePromise<Awaited<ReturnType<T>> | U>>(\n    async (asyncUpdate, options) => {\n      let dataBeforeOptimisticUpdate;\n      try {\n        if (options?.optimisticUpdate) {\n          if (typeof options?.rollbackOnError !== \"function\" && options?.rollbackOnError !== false) {\n            // keep track of the data before the optimistic update,\n            // but only if we need it (eg. only when we want to automatically rollback after)\n            dataBeforeOptimisticUpdate = structuredClone(latestData.current);\n          }\n          const data = options.optimisticUpdate(latestData.current);\n          lastUpdateFrom.current = \"cache\";\n          laggyDataRef.current = data;\n          mutateCache(data);\n        }\n        return await _mutate(asyncUpdate, { shouldRevalidateAfter: options?.shouldRevalidateAfter });\n      } catch (err) {\n        if (typeof options?.rollbackOnError === \"function\") {\n          const data = options.rollbackOnError(latestData.current);\n          lastUpdateFrom.current = \"cache\";\n          laggyDataRef.current = data;\n          mutateCache(data);\n        } else if (options?.optimisticUpdate && options?.rollbackOnError !== false) {\n          lastUpdateFrom.current = \"cache\";\n          // @ts-expect-error when undefined, it's expected\n          laggyDataRef.current = dataBeforeOptimisticUpdate;\n          // @ts-expect-error when undefined, it's expected\n          mutateCache(dataBeforeOptimisticUpdate);\n        }\n        throw err;\n      }\n    },\n    [mutateCache, _mutate, latestData, laggyDataRef, lastUpdateFrom],\n  );\n\n  useEffect(() => {\n    if (cachedData !== emptyCache) {\n      lastUpdateFrom.current = \"cache\";\n      laggyDataRef.current = cachedData;\n    }\n  }, [cachedData]);\n\n  return {\n    data: returnedData,\n    isLoading: state.isLoading,\n    error: state.error,\n    mutate: paginationArgsRef.current && paginationArgsRef.current.page > 0 ? _mutate : mutate,\n    pagination,\n    revalidate,\n  };\n}\n", "import { use<PERSON><PERSON>back, useMemo, useRef } from \"react\";\nimport hash from \"object-hash\";\nimport { useCachedPromise, CachedPromiseOptions } from \"./useCachedPromise\";\nimport { useLatest } from \"./useLatest\";\nimport { FunctionReturningPaginatedPromise, FunctionReturningPromise, UseCachedPromiseReturnType } from \"./types\";\nimport fetch, { Response, RequestInfo, RequestInit } from \"node-fetch-cjs\";\nimport { isJSON } from \"./fetch-utils\";\n\nasync function defaultParsing(response: Response) {\n  if (!response.ok) {\n    throw new Error(response.statusText);\n  }\n\n  const contentTypeHeader = response.headers.get(\"content-type\");\n\n  if (contentTypeHeader && isJSON(contentTypeHeader)) {\n    return await response.json();\n  }\n  return await response.text();\n}\n\nfunction defaultMapping<V, T extends unknown[]>(result: V): { data: T; hasMore?: boolean; cursor?: any } {\n  return { data: result as unknown as T, hasMore: false };\n}\n\ntype PaginatedRequestInfo = (pagination: { page: number; lastItem?: any; cursor?: any }) => RequestInfo;\n\n/**\n * Fetches the paginatedURL and returns the {@link AsyncState} corresponding to the execution of the fetch. The last value will be kept between command runs.\n *\n * @remark This overload should be used when working with paginated data sources.\n * @remark When paginating, only the first page will be cached.\n *\n * @example\n * ```\n * import { Icon, Image, List } from \"@raycast/api\";\n * import { useFetch } from \"@raycast/utils\";\n * import { useState } from \"react\";\n *\n * type SearchResult = { companies: Company[]; page: number; totalPages: number };\n * type Company = { id: number; name: string; smallLogoUrl?: string };\n * export default function Command() {\n *   const [searchText, setSearchText] = useState(\"\");\n *   const { isLoading, data, pagination } = useFetch(\n *     (options) =>\n *       \"https://api.ycombinator.com/v0.1/companies?\" +\n *       new URLSearchParams({ page: String(options.page + 1), q: searchText }).toString(),\n *     {\n *       mapResult(result: SearchResult) {\n *         return {\n *           data: result.companies,\n *           hasMore: result.page < result.totalPages,\n *         };\n *       },\n *       keepPreviousData: true,\n *       initialData: [],\n *     },\n *   );\n *\n *   return (\n *     <List isLoading={isLoading} pagination={pagination} onSearchTextChange={setSearchText}>\n *       {data.map((company) => (\n *         <List.Item\n *           key={company.id}\n *           icon={{ source: company.smallLogoUrl ?? Icon.MinusCircle, mask: Image.Mask.RoundedRectangle }}\n *           title={company.name}\n *         />\n *       ))}\n *     </List>\n *   );\n * }\n * ```\n */\nexport function useFetch<V = unknown, U = undefined, T extends unknown[] = unknown[]>(\n  url: PaginatedRequestInfo,\n  options: RequestInit & {\n    mapResult: (result: V) => { data: T; hasMore?: boolean; cursor?: any };\n    parseResponse?: (response: Response) => Promise<V>;\n  } & Omit<CachedPromiseOptions<(url: RequestInfo, options?: RequestInit) => Promise<T>, U>, \"abortable\">,\n): UseCachedPromiseReturnType<T, U>;\n/**\n * Fetch the URL and returns the {@link AsyncState} corresponding to the execution of the fetch. The last value will be kept between command runs.\n *\n * @example\n * ```\n * import { useFetch } from '@raycast/utils';\n *\n * export default function Command() {\n *   const { isLoading, data, revalidate } = useFetch('https://api.example');\n *\n *   return (\n *     <Detail\n *       isLoading={isLoading}\n *       markdown={data}\n *       actions={\n *         <ActionPanel>\n *           <Action title=\"Reload\" onAction={() => revalidate()} />\n *         </ActionPanel>\n *       }\n *     />\n *   );\n * };\n * ```\n */\nexport function useFetch<V = unknown, U = undefined, T = V>(\n  url: RequestInfo,\n  options?: RequestInit & {\n    mapResult?: (result: V) => { data: T; hasMore?: boolean; cursor?: any };\n    parseResponse?: (response: Response) => Promise<V>;\n  } & Omit<CachedPromiseOptions<(url: RequestInfo, options?: RequestInit) => Promise<T>, U>, \"abortable\">,\n): UseCachedPromiseReturnType<T, U> & { pagination: undefined };\n\nexport function useFetch<V = unknown, U = undefined, T extends unknown[] = unknown[]>(\n  url: RequestInfo | PaginatedRequestInfo,\n  options?: RequestInit & {\n    mapResult?: (result: V) => { data: T; hasMore?: boolean; cursor?: any };\n    parseResponse?: (response: Response) => Promise<V>;\n  } & Omit<CachedPromiseOptions<(url: RequestInfo, options?: RequestInit) => Promise<T>, U>, \"abortable\">,\n): UseCachedPromiseReturnType<T, U> {\n  const {\n    parseResponse,\n    mapResult,\n    initialData,\n    execute,\n    keepPreviousData,\n    onError,\n    onData,\n    onWillExecute,\n    ...fetchOptions\n  } = options || {};\n\n  const useCachedPromiseOptions: CachedPromiseOptions<(url: RequestInfo, options?: RequestInit) => Promise<T>, U> = {\n    initialData,\n    execute,\n    keepPreviousData,\n    onError,\n    onData,\n    onWillExecute,\n  };\n\n  const parseResponseRef = useLatest(parseResponse || defaultParsing);\n  const mapResultRef = useLatest(mapResult || defaultMapping);\n  const urlRef = useRef<RequestInfo | PaginatedRequestInfo>();\n  const firstPageUrlRef = useRef<RequestInfo | undefined>();\n  const firstPageUrl = typeof url === \"function\" ? url({ page: 0 }) : undefined;\n  /**\n   * When paginating, `url` is a `PaginatedRequestInfo`, so we only want to update the ref when the `firstPageUrl` changes.\n   * When not paginating, `url` is a `RequestInfo`, so we want to update the ref whenever `url` changes.\n   */\n  if (!urlRef.current || typeof firstPageUrlRef.current === \"undefined\" || firstPageUrlRef.current !== firstPageUrl) {\n    urlRef.current = url;\n  }\n  firstPageUrlRef.current = firstPageUrl;\n  const abortable = useRef<AbortController>();\n\n  const paginatedFn: FunctionReturningPaginatedPromise<[PaginatedRequestInfo, typeof fetchOptions], T> = useCallback(\n    (url: PaginatedRequestInfo, options?: RequestInit) => async (pagination: { page: number }) => {\n      const res = await fetch(url(pagination), { signal: abortable.current?.signal, ...options });\n      const parsed = (await parseResponseRef.current(res)) as V;\n      return mapResultRef.current?.(parsed);\n    },\n    [parseResponseRef, mapResultRef],\n  );\n  const fn: FunctionReturningPromise<[RequestInfo, RequestInit?], T> = useCallback(\n    async (url: RequestInfo, options?: RequestInit) => {\n      const res = await fetch(url, { signal: abortable.current?.signal, ...options });\n      const parsed = (await parseResponseRef.current(res)) as V;\n      const mapped = mapResultRef.current(parsed);\n      return mapped?.data as unknown as T;\n    },\n    [parseResponseRef, mapResultRef],\n  );\n\n  const promise = useMemo(() => {\n    if (firstPageUrlRef.current) {\n      return paginatedFn;\n    }\n    return fn;\n  }, [firstPageUrlRef, fn, paginatedFn]);\n\n  // @ts-expect-error lastItem can't be inferred properly\n  return useCachedPromise(promise, [urlRef.current as PaginatedRequestInfo, fetchOptions], {\n    ...useCachedPromiseOptions,\n    internal_cacheKeySuffix: firstPageUrlRef.current + hash(mapResultRef.current) + hash(parseResponseRef.current),\n    abortable,\n  });\n}\n", "export function isJSON(contentTypeHeader: string | null | undefined): boolean {\n  if (contentTypeHeader) {\n    const mediaType = parseContentType(contentTypeHeader);\n\n    if (mediaType.subtype === \"json\") {\n      return true;\n    }\n\n    if (mediaType.suffix === \"json\") {\n      return true;\n    }\n\n    if (mediaType.suffix && /\\bjson\\b/i.test(mediaType.suffix)) {\n      return true;\n    }\n\n    if (mediaType.subtype && /\\bjson\\b/i.test(mediaType.subtype)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * RegExp to match type in RFC 7231 sec 3.1.1.1\n *\n * media-type = type \"/\" subtype\n * type       = token\n * subtype    = token\n */\nconst CT_TYPE_REGEXP = /^[!#$%&'*+.^_`|~0-9A-Za-z-]+\\/[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;\n\n/**\n * RegExp to match type in RFC 6838\n *\n * type-name = restricted-name\n * subtype-name = restricted-name\n * restricted-name = restricted-name-first *126restricted-name-chars\n * restricted-name-first  = ALPHA / DIGIT\n * restricted-name-chars  = ALPHA / DIGIT / \"!\" / \"#\" /\n *                          \"$\" / \"&\" / \"-\" / \"^\" / \"_\"\n * restricted-name-chars =/ \".\" ; Characters before first dot always\n *                              ; specify a facet name\n * restricted-name-chars =/ \"+\" ; Characters after last plus always\n *                              ; specify a structured syntax suffix\n * ALPHA =  %x41-5A / %x61-7A   ; A-Z / a-z\n * DIGIT =  %x30-39             ; 0-9\n */\nconst MEDIA_TYPE_REGEXP = /^ *([A-Za-z0-9][A-Za-z0-9!#$&^_-]{0,126})\\/([A-Za-z0-9][A-Za-z0-9!#$&^_.+-]{0,126}) *$/;\n\nfunction parseContentType(header: string) {\n  const headerDelimitationindex = header.indexOf(\";\");\n  const contentType = headerDelimitationindex !== -1 ? header.slice(0, headerDelimitationindex).trim() : header.trim();\n\n  if (!CT_TYPE_REGEXP.test(contentType)) {\n    throw new TypeError(\"invalid media type\");\n  }\n\n  const match = MEDIA_TYPE_REGEXP.exec(contentType.toLowerCase().toLowerCase());\n\n  if (!match) {\n    throw new TypeError(\"invalid media type\");\n  }\n\n  const type = match[1];\n  let subtype = match[2];\n  let suffix;\n\n  // suffix after last +\n  const index = subtype.lastIndexOf(\"+\");\n  if (index !== -1) {\n    suffix = subtype.substring(index + 1);\n    subtype = subtype.substring(0, index);\n  }\n\n  return { type, subtype, suffix };\n}\n", "/*\n * Inspired by Execa\n */\n\nimport childProcess from \"node:child_process\";\nimport { useCallback, useRef } from \"react\";\n\nimport { useCachedPromise, CachedPromiseOptions } from \"./useCachedPromise\";\nimport { useLatest } from \"./useLatest\";\nimport { UseCachedPromiseReturnType } from \"./types\";\nimport {\n  getSpawnedPromise,\n  getSpawnedResult,\n  handleOutput,\n  defaultParsing,\n  ParseExecOutputHandler,\n} from \"./exec-utils\";\n\ntype ExecOptions = {\n  /**\n   * If `true`, runs the command inside of a shell. Uses `/bin/sh`. A different shell can be specified as a string. The shell should understand the `-c` switch.\n   *\n   * We recommend against using this option since it is:\n   * - not cross-platform, encouraging shell-specific syntax.\n   * - slower, because of the additional shell interpretation.\n   * - unsafe, potentially allowing command injection.\n   *\n   * @default false\n   */\n  shell?: boolean | string;\n  /**\n   * Strip the final newline character from the output.\n   * @default true\n   */\n  stripFinalNewline?: boolean;\n  /**\n   * Current working directory of the child process.\n   * @default process.cwd()\n   */\n  cwd?: string;\n  /**\n   * Environment key-value pairs. Extends automatically from `process.env`.\n   * @default process.env\n   */\n  env?: NodeJS.ProcessEnv;\n  /**\n   * Specify the character encoding used to decode the stdout and stderr output. If set to `\"buffer\"`, then stdout and stderr will be a Buffer instead of a string.\n   *\n   * @default \"utf8\"\n   */\n  encoding?: BufferEncoding | \"buffer\";\n  /**\n   * Write some input to the `stdin` of your binary.\n   */\n  input?: string | Buffer;\n  /** If timeout is greater than `0`, the parent will send the signal `SIGTERM` if the child runs longer than timeout milliseconds.\n   *\n   * @default 10000\n   */\n  timeout?: number;\n};\n\nconst SPACES_REGEXP = / +/g;\nfunction parseCommand(command: string, args?: string[]) {\n  if (args) {\n    return [command, ...args];\n  }\n  const tokens: string[] = [];\n  for (const token of command.trim().split(SPACES_REGEXP)) {\n    // Allow spaces to be escaped by a backslash if not meant as a delimiter\n    const previousToken = tokens[tokens.length - 1];\n    if (previousToken && previousToken.endsWith(\"\\\\\")) {\n      // Merge previous token with current one\n      tokens[tokens.length - 1] = `${previousToken.slice(0, -1)} ${token}`;\n    } else {\n      tokens.push(token);\n    }\n  }\n\n  return tokens;\n}\n\ntype ExecCachedPromiseOptions<T, U> = Omit<\n  CachedPromiseOptions<\n    (_command: string, _args: string[], _options?: ExecOptions, input?: string | Buffer) => Promise<T>,\n    U\n  >,\n  \"abortable\"\n>;\n\n/**\n * Executes a command and returns the {@link AsyncState} corresponding to the execution of the command. The last value will be kept between command runs.\n *\n * @remark When specifying the arguments via the `command` string, if the file or an argument of the command contains spaces, they must be escaped with backslashes. This matters especially if `command` is not a constant but a variable, for example with `__dirname` or `process.cwd()`. Except for spaces, no escaping/quoting is needed.\n *\n * The `shell` option must be used if the command uses shell-specific features (for example, `&&` or `||`), as opposed to being a simple file followed by its arguments.\n *\n * @example\n * ```\n * import { useExec } from '@raycast/utils';\n *\n * export default function Command() {\n *   const { isLoading, data, revalidate } = useExec(\"brew\", [\"info\", \"--json=v2\", \"--installed\"]);\n *   const results = useMemo<{}[]>(() => JSON.parse(data || \"[]\"), [data]);\n *\n *   return (\n *     <List isLoading={isLoading}>\n *      {(data || []).map((item) => (\n *        <List.Item key={item.id} title={item.name} />\n *      ))}\n *    </List>\n *   );\n * };\n * ```\n */\nexport function useExec<T = Buffer, U = undefined>(\n  command: string,\n  options: {\n    parseOutput?: ParseExecOutputHandler<T, Buffer, ExecOptions>;\n  } & ExecOptions & {\n      encoding: \"buffer\";\n    } & ExecCachedPromiseOptions<T, U>,\n): UseCachedPromiseReturnType<T, U>;\nexport function useExec<T = string, U = undefined>(\n  command: string,\n  options?: {\n    parseOutput?: ParseExecOutputHandler<T, string, ExecOptions>;\n  } & ExecOptions & {\n      encoding?: BufferEncoding;\n    } & ExecCachedPromiseOptions<T, U>,\n): UseCachedPromiseReturnType<T, U>;\nexport function useExec<T = Buffer, U = undefined>(\n  file: string,\n  /**\n   * The arguments to pass to the file. No escaping/quoting is needed.\n   *\n   * If defined, the commands needs to be a file to execute. If undefined, the arguments will be parsed from the command.\n   */\n  args: string[],\n  options: {\n    parseOutput?: ParseExecOutputHandler<T, Buffer, ExecOptions>;\n  } & ExecOptions & {\n      encoding: \"buffer\";\n    } & ExecCachedPromiseOptions<T, U>,\n): UseCachedPromiseReturnType<T, U>;\nexport function useExec<T = string, U = undefined>(\n  file: string,\n  /**\n   * The arguments to pass to the file. No escaping/quoting is needed.\n   *\n   * If defined, the commands needs to be a file to execute. If undefined, the arguments will be parsed from the command.\n   */\n  args: string[],\n  options?: {\n    parseOutput?: ParseExecOutputHandler<T, string, ExecOptions>;\n  } & ExecOptions & {\n      encoding?: BufferEncoding;\n    } & ExecCachedPromiseOptions<T, U>,\n): UseCachedPromiseReturnType<T, U>;\nexport function useExec<T, U = undefined>(\n  command: string,\n  optionsOrArgs?:\n    | string[]\n    | ({\n        parseOutput?: ParseExecOutputHandler<T, Buffer, ExecOptions> | ParseExecOutputHandler<T, string, ExecOptions>;\n      } & ExecOptions &\n        ExecCachedPromiseOptions<T, U>),\n  options?: {\n    parseOutput?: ParseExecOutputHandler<T, Buffer, ExecOptions> | ParseExecOutputHandler<T, string, ExecOptions>;\n  } & ExecOptions &\n    ExecCachedPromiseOptions<T, U>,\n): UseCachedPromiseReturnType<T, U> {\n  const { parseOutput, input, onData, onWillExecute, initialData, execute, keepPreviousData, onError, ...execOptions } =\n    Array.isArray(optionsOrArgs) ? options || {} : optionsOrArgs || {};\n\n  const useCachedPromiseOptions: ExecCachedPromiseOptions<T, U> = {\n    initialData,\n    execute,\n    keepPreviousData,\n    onError,\n    onData,\n    onWillExecute,\n  };\n\n  const abortable = useRef<AbortController>();\n  const parseOutputRef = useLatest(parseOutput || defaultParsing);\n\n  const fn = useCallback(\n    async (_command: string, _args: string[], _options?: ExecOptions, input?: string | Buffer) => {\n      const [file, ...args] = parseCommand(_command, _args);\n      const command = [file, ...args].join(\" \");\n\n      const options = {\n        stripFinalNewline: true,\n        ..._options,\n        timeout: _options?.timeout || 10000,\n        signal: abortable.current?.signal,\n        encoding: _options?.encoding === null ? \"buffer\" : _options?.encoding || \"utf8\",\n        env: { PATH: \"/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin\", ...process.env, ..._options?.env },\n      };\n\n      const spawned = childProcess.spawn(file, args, options);\n      const spawnedPromise = getSpawnedPromise(spawned, options);\n\n      if (input) {\n        spawned.stdin.end(input);\n      }\n\n      const [{ error, exitCode, signal, timedOut }, stdoutResult, stderrResult] = await getSpawnedResult(\n        spawned,\n        options,\n        spawnedPromise,\n      );\n      const stdout = handleOutput(options, stdoutResult);\n      const stderr = handleOutput(options, stderrResult);\n\n      return parseOutputRef.current({\n        // @ts-expect-error too many generics, I give up\n        stdout,\n        // @ts-expect-error too many generics, I give up\n        stderr,\n        error,\n        exitCode,\n        signal,\n        timedOut,\n        command,\n        options,\n        parentError: new Error(),\n      }) as T;\n    },\n    [parseOutputRef],\n  );\n\n  // @ts-expect-error T can't be a Promise so it's actually the same\n  return useCachedPromise(fn, [command, Array.isArray(optionsOrArgs) ? optionsOrArgs : [], execOptions, input], {\n    ...useCachedPromiseOptions,\n    abortable,\n  });\n}\n", "import childProcess from \"node:child_process\";\nimport { constants as Buffer<PERSON>onstants } from \"node:buffer\";\nimport Stream from \"node:stream\";\nimport { promisify } from \"node:util\";\n\nexport type SpawnedPromise = Promise<{\n  exitCode: number | null;\n  error?: Error;\n  signal: NodeJS.Signals | null;\n  timedOut: boolean;\n}>;\n\nexport function getSpawnedPromise(\n  spawned: childProcess.ChildProcessWithoutNullStreams,\n  { timeout }: { timeout?: number } = {},\n): SpawnedPromise {\n  const { onExit } = require(\"signal-exit\");\n  const spawnedPromise: SpawnedPromise = new Promise((resolve, reject) => {\n    spawned.on(\"exit\", (exitCode, signal) => {\n      resolve({ exitCode, signal, timedOut: false });\n    });\n\n    spawned.on(\"error\", (error) => {\n      reject(error);\n    });\n\n    if (spawned.stdin) {\n      spawned.stdin.on(\"error\", (error) => {\n        reject(error);\n      });\n    }\n  });\n\n  const removeExitHandler = onExit(() => {\n    spawned.kill();\n  });\n\n  if (timeout === 0 || timeout === undefined) {\n    return spawnedPromise.finally(() => removeExitHandler());\n  }\n\n  let timeoutId: NodeJS.Timeout;\n  const timeoutPromise: SpawnedPromise = new Promise((_resolve, reject) => {\n    timeoutId = setTimeout(() => {\n      spawned.kill(\"SIGTERM\");\n      reject(Object.assign(new Error(\"Timed out\"), { timedOut: true, signal: \"SIGTERM\" }));\n    }, timeout);\n  });\n\n  const safeSpawnedPromise = spawnedPromise.finally(() => {\n    clearTimeout(timeoutId);\n  });\n\n  return Promise.race([timeoutPromise, safeSpawnedPromise]).finally(() => removeExitHandler());\n}\n\nclass MaxBufferError extends Error {\n  constructor() {\n    super(\"The output is too big\");\n    this.name = \"MaxBufferError\";\n  }\n}\n\nfunction bufferStream<T extends string | Buffer>(options: { encoding: BufferEncoding | \"buffer\" }) {\n  const { encoding } = options;\n  const isBuffer = encoding === \"buffer\";\n\n  // @ts-expect-error missing the methods we are adding below\n  const stream: Stream.PassThrough & { getBufferedValue: () => T; getBufferedLength: () => number } =\n    new Stream.PassThrough({ objectMode: false });\n\n  if (encoding && encoding !== \"buffer\") {\n    stream.setEncoding(encoding);\n  }\n\n  let length = 0;\n  const chunks: any[] = [];\n\n  stream.on(\"data\", (chunk) => {\n    chunks.push(chunk);\n\n    length += chunk.length;\n  });\n\n  stream.getBufferedValue = () => {\n    return (isBuffer ? Buffer.concat(chunks, length) : chunks.join(\"\")) as T;\n  };\n\n  stream.getBufferedLength = () => length;\n\n  return stream;\n}\n\nasync function getStream<T extends string | Buffer>(\n  inputStream: Stream.Readable,\n  options: { encoding: BufferEncoding | \"buffer\" },\n) {\n  const stream = bufferStream<T>(options);\n\n  await new Promise<void>((resolve, reject) => {\n    const rejectPromise = (error: Error & { bufferedData?: T }) => {\n      // Don't retrieve an oversized buffer.\n      if (error && stream.getBufferedLength() <= BufferConstants.MAX_LENGTH) {\n        error.bufferedData = stream.getBufferedValue();\n      }\n\n      reject(error);\n    };\n\n    (async () => {\n      try {\n        await promisify(Stream.pipeline)(inputStream, stream);\n        resolve();\n      } catch (error) {\n        rejectPromise(error as any);\n      }\n    })();\n\n    stream.on(\"data\", () => {\n      // 80mb\n      if (stream.getBufferedLength() > 1000 * 1000 * 80) {\n        rejectPromise(new MaxBufferError());\n      }\n    });\n  });\n\n  return stream.getBufferedValue();\n}\n\n// On failure, `result.stdout|stderr` should contain the currently buffered stream\nasync function getBufferedData<T extends string | Buffer>(stream: Stream.Readable, streamPromise: Promise<T>) {\n  stream.destroy();\n\n  try {\n    return await streamPromise;\n  } catch (error) {\n    return (error as any as { bufferedData: T }).bufferedData;\n  }\n}\n\nexport async function getSpawnedResult<T extends string | Buffer>(\n  { stdout, stderr }: childProcess.ChildProcessWithoutNullStreams,\n  { encoding }: { encoding: BufferEncoding | \"buffer\" },\n  processDone: SpawnedPromise,\n) {\n  const stdoutPromise = getStream<T>(stdout, { encoding });\n  const stderrPromise = getStream<T>(stderr, { encoding });\n\n  try {\n    return await Promise.all([processDone, stdoutPromise, stderrPromise]);\n  } catch (error: any) {\n    return Promise.all([\n      {\n        error: error as Error,\n        exitCode: null,\n        signal: error.signal as NodeJS.Signals | null,\n        timedOut: (error.timedOut as boolean) || false,\n      },\n      getBufferedData(stdout, stdoutPromise),\n      getBufferedData(stderr, stderrPromise),\n    ]);\n  }\n}\n\nfunction stripFinalNewline<T extends string | Buffer>(input: T) {\n  const LF = typeof input === \"string\" ? \"\\n\" : \"\\n\".charCodeAt(0);\n  const CR = typeof input === \"string\" ? \"\\r\" : \"\\r\".charCodeAt(0);\n\n  if (input[input.length - 1] === LF) {\n    // @ts-expect-error we are doing some nasty stuff here\n    input = input.slice(0, -1);\n  }\n\n  if (input[input.length - 1] === CR) {\n    // @ts-expect-error we are doing some nasty stuff here\n    input = input.slice(0, -1);\n  }\n\n  return input;\n}\n\nexport function handleOutput<T extends string | Buffer>(options: { stripFinalNewline?: boolean }, value: T) {\n  if (options.stripFinalNewline) {\n    return stripFinalNewline(value);\n  }\n\n  return value;\n}\n\nfunction getErrorPrefix({\n  timedOut,\n  timeout,\n  signal,\n  exitCode,\n}: {\n  exitCode: number | null;\n  signal: NodeJS.Signals | null;\n  timedOut: boolean;\n  timeout?: number;\n}) {\n  if (timedOut) {\n    return `timed out after ${timeout} milliseconds`;\n  }\n\n  if (signal !== undefined && signal !== null) {\n    return `was killed with ${signal}`;\n  }\n\n  if (exitCode !== undefined && exitCode !== null) {\n    return `failed with exit code ${exitCode}`;\n  }\n\n  return \"failed\";\n}\n\nfunction makeError({\n  stdout,\n  stderr,\n  error,\n  signal,\n  exitCode,\n  command,\n  timedOut,\n  options,\n  parentError,\n}: {\n  stdout: string | Buffer;\n  stderr: string | Buffer;\n  error?: Error;\n  exitCode: number | null;\n  signal: NodeJS.Signals | null;\n  timedOut: boolean;\n  command: string;\n  options?: { timeout?: number };\n  parentError: Error;\n}) {\n  const prefix = getErrorPrefix({ timedOut, timeout: options?.timeout, signal, exitCode });\n  const execaMessage = `Command ${prefix}: ${command}`;\n  const shortMessage = error ? `${execaMessage}\\n${error.message}` : execaMessage;\n  const message = [shortMessage, stderr, stdout].filter(Boolean).join(\"\\n\");\n\n  if (error) {\n    // @ts-expect-error not on Error\n    error.originalMessage = error.message;\n  } else {\n    error = parentError;\n  }\n\n  error.message = message;\n\n  // @ts-expect-error not on Error\n  error.shortMessage = shortMessage;\n  // @ts-expect-error not on Error\n  error.command = command;\n  // @ts-expect-error not on Error\n  error.exitCode = exitCode;\n  // @ts-expect-error not on Error\n  error.signal = signal;\n  // @ts-expect-error not on Error\n  error.stdout = stdout;\n  // @ts-expect-error not on Error\n  error.stderr = stderr;\n\n  if (\"bufferedData\" in error) {\n    delete error[\"bufferedData\"];\n  }\n\n  return error;\n}\n\nexport type ParseExecOutputHandler<\n  T,\n  DecodedOutput extends string | Buffer = string | Buffer,\n  Options = unknown,\n> = (args: {\n  /** The output of the process on stdout. */\n  stdout: DecodedOutput;\n  /** The output of the process on stderr. */\n  stderr: DecodedOutput;\n  error?: Error;\n  /** The numeric exit code of the process that was run. */\n  exitCode: number | null;\n  /**\n   * The name of the signal that was used to terminate the process. For example, SIGFPE.\n   *\n   * If a signal terminated the process, this property is defined. Otherwise it is null.\n   */\n  signal: NodeJS.Signals | null;\n  /** Whether the process timed out. */\n  timedOut: boolean;\n  /** The command that was run, for logging purposes. */\n  command: string;\n  options?: Options;\n}) => T;\n\nexport function defaultParsing<T extends string | Buffer>({\n  stdout,\n  stderr,\n  error,\n  exitCode,\n  signal,\n  timedOut,\n  command,\n  options,\n  parentError,\n}: {\n  stdout: T;\n  stderr: T;\n  error?: Error;\n  exitCode: number | null;\n  signal: NodeJS.Signals | null;\n  timedOut: boolean;\n  command: string;\n  options?: { timeout?: number };\n  parentError: Error;\n}) {\n  if (error || exitCode !== 0 || signal !== null) {\n    const returnedError = makeError({\n      error,\n      exitCode,\n      signal,\n      stdout,\n      stderr,\n      command,\n      timedOut,\n      options,\n      parentError,\n    });\n\n    throw returnedError;\n  }\n\n  return stdout;\n}\n", "import { environment } from \"@raycast/api\";\nimport fetch, { RequestInfo, RequestInit } from \"node-fetch-cjs\";\nimport { createReadStream, createWriteStream, mkdirSync, Stats } from \"node:fs\";\nimport { stat } from \"node:fs/promises\";\nimport { join, normalize } from \"node:path\";\nimport { pipeline } from \"node:stream/promises\";\nimport { useRef } from \"react\";\nimport Chain from \"stream-chain\";\nimport { parser } from \"stream-json\";\nimport Pick from \"stream-json/filters/Pick\";\nimport StreamArray from \"stream-json/streamers/StreamArray\";\nimport { isJSON } from \"./fetch-utils\";\nimport { Flatten, FunctionReturningPaginatedPromise, UseCachedPromiseReturnType } from \"./types\";\nimport { CachedPromiseOptions, useCachedPromise } from \"./useCachedPromise\";\nimport objectHash from \"object-hash\";\n\nasync function cache(url: RequestInfo, destination: string, fetchOptions?: RequestInit) {\n  if (typeof url === \"object\" || url.startsWith(\"http://\") || url.startsWith(\"https://\")) {\n    return await cacheURL(url, destination, fetchOptions);\n  } else if (url.startsWith(\"file://\")) {\n    return await cacheFile(\n      normalize(decodeURIComponent(new URL(url).pathname)),\n      destination,\n      fetchOptions?.signal ? (fetchOptions.signal as AbortSignal) : undefined,\n    );\n  } else {\n    throw new Error(\"Only HTTP(S) or file URLs are supported\");\n  }\n}\n\nasync function cacheURL(url: RequestInfo, destination: string, fetchOptions?: RequestInit) {\n  const response = await fetch(url, fetchOptions);\n\n  if (!response.ok) {\n    throw new Error(\"Failed to fetch URL\");\n  }\n\n  if (!isJSON(response.headers.get(\"content-type\"))) {\n    throw new Error(\"URL does not return JSON\");\n  }\n  if (!response.body) {\n    throw new Error(\"Failed to retrieve expected JSON content: Response body is missing or inaccessible.\");\n  }\n  await pipeline(\n    response.body as unknown as NodeJS.ReadableStream,\n    createWriteStream(destination),\n    fetchOptions?.signal ? { signal: fetchOptions.signal as AbortSignal } : undefined,\n  );\n}\n\nasync function cacheFile(source: string, destination: string, abortSignal?: AbortSignal) {\n  await pipeline(\n    createReadStream(source),\n    createWriteStream(destination),\n    abortSignal ? { signal: abortSignal } : undefined,\n  );\n}\n\nasync function cacheURLIfNecessary(\n  url: RequestInfo,\n  folder: string,\n  fileName: string,\n  forceUpdate: boolean,\n  fetchOptions?: RequestInit,\n) {\n  const destination = join(folder, fileName);\n\n  try {\n    await stat(folder);\n  } catch (e) {\n    mkdirSync(folder, { recursive: true });\n    await cache(url, destination, fetchOptions);\n    return;\n  }\n  if (forceUpdate) {\n    await cache(url, destination, fetchOptions);\n    return;\n  }\n\n  let stats: Stats | undefined = undefined;\n  try {\n    stats = await stat(destination);\n  } catch (e) {\n    await cache(url, destination, fetchOptions);\n    return;\n  }\n\n  if (typeof url === \"object\" || url.startsWith(\"http://\") || url.startsWith(\"https://\")) {\n    const headResponse = await fetch(url, { ...fetchOptions, method: \"HEAD\" });\n    if (!headResponse.ok) {\n      throw new Error(\"Could not fetch URL\");\n    }\n\n    if (!isJSON(headResponse.headers.get(\"content-type\"))) {\n      throw new Error(\"URL does not return JSON\");\n    }\n\n    const lastModified = Date.parse(headResponse.headers.get(\"last-modified\") ?? \"\");\n    if (stats.size === 0 || Number.isNaN(lastModified) || lastModified > stats.mtimeMs) {\n      await cache(url, destination, fetchOptions);\n      return;\n    }\n  } else if (url.startsWith(\"file://\")) {\n    try {\n      const sourceStats = await stat(normalize(decodeURIComponent(new URL(url).pathname)));\n      if (sourceStats.mtimeMs > stats.mtimeMs) {\n        await cache(url, destination, fetchOptions);\n      }\n    } catch (e) {\n      throw new Error(\"Source file could not be read\");\n    }\n  } else {\n    throw new Error(\"Only HTTP(S) or file URLs are supported\");\n  }\n}\n\nasync function* streamJsonFile<T>(\n  filePath: string,\n  pageSize: number,\n  abortSignal?: AbortSignal,\n  dataPath?: string | RegExp,\n  filterFn?: (item: Flatten<T>) => boolean,\n  transformFn?: (item: any) => T,\n): AsyncGenerator<T extends unknown[] ? T : T[]> {\n  let page: T extends unknown[] ? T : T[] = [] as T extends unknown[] ? T : T[];\n\n  const pipeline = new Chain([\n    createReadStream(filePath),\n    dataPath ? Pick.withParser({ filter: dataPath }) : parser(),\n    new StreamArray(),\n    (data) => transformFn?.(data.value) ?? data.value,\n  ]);\n\n  abortSignal?.addEventListener(\"abort\", () => {\n    pipeline.destroy();\n  });\n\n  try {\n    for await (const data of pipeline) {\n      if (abortSignal?.aborted) {\n        return [];\n      }\n      if (!filterFn || filterFn(data)) {\n        page.push(data);\n      }\n      if (page.length >= pageSize) {\n        yield page;\n        page = [] as T extends unknown[] ? T : T[];\n      }\n    }\n  } catch (e) {\n    pipeline.destroy();\n    throw e;\n  }\n\n  if (page.length > 0) {\n    yield page;\n  }\n\n  return [];\n}\n\ntype Options<T> = {\n  /**\n   * The hook expects to iterate through an array of data, so by default, it assumes the JSON it receives itself represents an array. However, sometimes the array of data is wrapped in an object,\n   * i.e. `{ \"success\": true, \"data\": […] }`, or even `{ \"success\": true, \"results\": { \"data\": […] } }`. In those cases, you can use `dataPath` to specify where the data array can be found.\n   *\n   * @remark If your JSON object has multiple arrays that you want to stream data from, you can pass a regular expression to stream through all of them.\n   *\n   * @example For `{ \"success\": true, \"data\": […] }`, dataPath would be `data`\n   * @example For `{ \"success\": true, \"results\": { \"data\": […] } }`, dataPath would be `results.data`\n   * @example For `{ \"success\": true, \"results\": { \"first_list\": […], \"second_list\": […], \"third_list\": […] } }`, dataPath would be `/^results\\.(first_list|second_list|third_list)$\n/`.\n   */\n  dataPath?: string | RegExp;\n  /**\n   * A function to decide whether a particular item should be kept or not.\n   * Defaults to `undefined`, keeping any encountered item.\n   *\n   * @remark The hook will revalidate every time the filter function changes, so you need to use [useCallback](https://react.dev/reference/react/useCallback) to make sure it only changes when it needs to.\n   */\n  filter?: (item: Flatten<T>) => boolean;\n  /**\n   * A function to apply to each item as it is encountered. Useful for a couple of things:\n   * 1. ensuring that all items have the expected properties, and, as on optimization, for getting rid of the properties that you don't care about.\n   * 2. when top-level objects actually represent nested data, which should be flattened. In this case, `transform` can return an array of items, and the hook will stream through each one of those items,\n   * passing them to `filter` etc.\n   *\n   * Defaults to a passthrough function if not provided.\n   *\n   * @remark The hook will revalidate every time the transform function changes, so it is important to use [useCallback](https://react.dev/reference/react/useCallback) to ensure it only changes when necessary to prevent unnecessary re-renders or computations.\n   *\n   * @example\n   * ```\n   * // For data: `{ \"data\": [ { \"type\": \"folder\", \"name\": \"item 1\", \"children\": [ { \"type\": \"item\", \"name\": \"item 2\" }, { \"type\": \"item\", \"name\": \"item 3\" } ] }, { \"type\": \"folder\", \"name\": \"item 4\", children: [] } ] }`\n   *\n   * type Item = {\n   *  type: \"item\";\n   *  name: string;\n   * };\n   *\n   * type Folder = {\n   *   type: \"folder\";\n   *   name: string;\n   *   children: (Item | Folder)[];\n   * };\n   *\n   * function flatten(item: Item | Folder): { name: string }[] {\n   *   const flattened: { name: string }[] = [];\n   *   if (item.type === \"folder\") {\n   *     flattened.push(...item.children.map(flatten).flat());\n   *   }\n   *   if (item.type === \"item\") {\n   *     flattened.push({ name: item.name });\n   *   }\n   *   return flattened;\n   * }\n   *\n   * const transform = useCallback(flatten, []);\n   * const filter = useCallback((item: { name: string }) => {\n   *   …\n   * })\n   * ```\n   */\n  transform?: (item: any) => T;\n  /**\n   * The amount of items to return for each page.\n   * Defaults to `20`.\n   */\n  pageSize?: number;\n};\n\n/**\n * Takes a `http://`, `https://` or `file:///` URL pointing to a JSON resource, caches it to the command's support\n * folder, and streams through its content. Useful when dealing with large JSON arrays which would be too big to fit\n * in the command's memory.\n *\n * @remark The JSON resource needs to consist of an array of objects\n *\n * @example\n * ```\n * import { List } from \"@raycast/api\";\n * import { useStreamJSON } from \"@raycast/utils\";\n *\n * type Formula = { name: string; desc?: string };\n *\n * export default function Main(): JSX.Element {\n *   const { data, isLoading, pagination } = useStreamJSON<Formula>(\"https://formulae.brew.sh/api/formula.json\");\n *\n *   return (\n *     <List isLoading={isLoading} pagination={pagination}>\n *       <List.Section title=\"Formulae\">\n *         {data?.map((d) => <List.Item key={d.name} title={d.name} subtitle={d.desc} />)}\n *       </List.Section>\n *     </List>\n *   );\n * }\n * ```\n *\n * @example\n * ```\n * import { List } from \"@raycast/api\";\n * import { useStreamJSON } from \"@raycast/utils\";\n * import { homedir } from \"os\";\n * import { join } from \"path\";\n *\n * type Formula = { name: string; desc?: string };\n *\n * export default function Main(): JSX.Element {\n *   const { data, isLoading, pagination } = useStreamJSON<Formula>(`file:///${join(homedir(), \"Downloads\", \"formulae.json\")}`);\n *\n *   return (\n *     <List isLoading={isLoading} pagination={pagination}>\n *       <List.Section title=\"Formulae\">\n *         {data?.map((d) => <List.Item key={d.name} title={d.name} subtitle={d.desc} />)}\n *       </List.Section>\n *     </List>\n *   );\n * }\n * ```\n */\nexport function useStreamJSON<T, U = unknown>(url: RequestInfo): UseCachedPromiseReturnType<T, U>;\n\n/**\n * Takes a `http://`, `https://` or `file:///` URL pointing to a JSON resource, caches it to the command's support\n * folder, and streams through its content. Useful when dealing with large JSON arrays which would be too big to fit\n * in the command's memory.\n *\n * @remark The JSON resource needs to consist of an array of objects\n *\n * @example\n * ```\n * import { List, environment } from \"@raycast/api\";\n * import { useStreamJSON } from \"@raycast/utils\";\n * import { join } from 'path';\n * import { useCallback, useState } from \"react\";\n *\n * type Formula = { name: string; desc?: string };\n *\n * export default function Main(): JSX.Element {\n *   const [searchText, setSearchText] = useState(\"\");\n *\n *   const formulaFilter = useCallback(\n *     (item: Formula) => {\n *       if (!searchText) return true;\n *       return item.name.toLocaleLowerCase().includes(searchText);\n *     },\n *     [searchText],\n *   );\n *\n *   const formulaTransform = useCallback((item: any): Formula => {\n *     return { name: item.name, desc: item.desc };\n *   }, []);\n *\n *   const { data, isLoading, pagination } = useStreamJSON(\"https://formulae.brew.sh/api/formula.json\", {\n *     initialData: [] as Formula[],\n *     pageSize: 20,\n *     filter: formulaFilter,\n *     transform: formulaTransform,\n *   });\n *\n *   return (\n *     <List isLoading={isLoading} pagination={pagination} onSearchTextChange={setSearchText}>\n *       <List.Section title=\"Formulae\">\n *         {data.map((d) => (\n *           <List.Item key={d.name} title={d.name} subtitle={d.desc} />\n *         ))}\n *       </List.Section>\n *     </List>\n *   );\n * }\n * ``` support folder, and streams through its content.\n *\n * @example\n * ```\n * import { List, environment } from \"@raycast/api\";\n * import { useStreamJSON } from \"@raycast/utils\";\n * import { join } from \"path\";\n * import { homedir } from \"os\";\n * import { useCallback, useState } from \"react\";\n *\n * type Formula = { name: string; desc?: string };\n *\n * export default function Main(): JSX.Element {\n *   const [searchText, setSearchText] = useState(\"\");\n *\n *   const formulaFilter = useCallback(\n *     (item: Formula) => {\n *       if (!searchText) return true;\n *       return item.name.toLocaleLowerCase().includes(searchText);\n *     },\n *     [searchText],\n *   );\n *\n *   const formulaTransform = useCallback((item: any): Formula => {\n *     return { name: item.name, desc: item.desc };\n *   }, []);\n *\n *   const { data, isLoading, pagination } = useStreamJSON(`file:///${join(homedir(), \"Downloads\", \"formulae.json\")}`, {\n *     initialData: [] as Formula[],\n *     pageSize: 20,\n *     filter: formulaFilter,\n *     transform: formulaTransform,\n *   });\n *\n *   return (\n *     <List isLoading={isLoading} pagination={pagination} onSearchTextChange={setSearchText}>\n *       <List.Section title=\"Formulae\">\n *         {data.map((d) => (\n *           <List.Item key={d.name} title={d.name} subtitle={d.desc} />\n *         ))}\n *       </List.Section>\n *     </List>\n *   );\n * }\n * ```\n */\nexport function useStreamJSON<T, U extends any[] = any[]>(\n  url: RequestInfo,\n  options: Options<T> & RequestInit & Omit<CachedPromiseOptions<FunctionReturningPaginatedPromise, U>, \"abortable\">,\n): UseCachedPromiseReturnType<T extends unknown[] ? T : T[], U>;\n\nexport function useStreamJSON<T, U extends any[] = any[]>(\n  url: RequestInfo,\n  options?: Options<T> & RequestInit & Omit<CachedPromiseOptions<FunctionReturningPaginatedPromise, U>, \"abortable\">,\n): UseCachedPromiseReturnType<T extends unknown[] ? T : T[], U> {\n  const {\n    initialData,\n    execute,\n    keepPreviousData,\n    onError,\n    onData,\n    onWillExecute,\n    dataPath,\n    filter,\n    transform,\n    pageSize = 20,\n    ...fetchOptions\n  } = options ?? {};\n  const previousUrl = useRef<RequestInfo>();\n  const previousDestination = useRef<string>();\n\n  const useCachedPromiseOptions: CachedPromiseOptions<FunctionReturningPaginatedPromise, U> = {\n    initialData,\n    execute,\n    keepPreviousData,\n    onError,\n    onData,\n    onWillExecute,\n  };\n\n  const generatorRef = useRef<AsyncGenerator<T extends unknown[] ? T : T[]> | null>(null);\n  const controllerRef = useRef<AbortController | null>(null);\n  const hasMoreRef = useRef(false);\n\n  return useCachedPromise(\n    (\n      url: RequestInfo,\n      pageSize: number,\n      fetchOptions: RequestInit | undefined,\n      dataPath: string | RegExp | undefined,\n      filter: ((item: Flatten<T>) => boolean) | undefined,\n      transform: ((item: unknown) => T) | undefined,\n    ) =>\n      async ({ page }) => {\n        const fileName = objectHash(url) + \".json\";\n        const folder = environment.supportPath;\n        if (page === 0) {\n          controllerRef.current?.abort();\n          controllerRef.current = new AbortController();\n          const destination = join(folder, fileName);\n          /**\n           * Force update the cache when the URL changes but the cache destination does not.\n           */\n          const forceCacheUpdate = Boolean(\n            previousUrl.current &&\n              previousUrl.current !== url &&\n              previousDestination.current &&\n              previousDestination.current === destination,\n          );\n          previousUrl.current = url;\n          previousDestination.current = destination;\n          await cacheURLIfNecessary(url, folder, fileName, forceCacheUpdate, {\n            ...fetchOptions,\n            signal: controllerRef.current?.signal,\n          });\n          generatorRef.current = streamJsonFile(\n            destination,\n            pageSize,\n            controllerRef.current?.signal,\n            dataPath,\n            filter,\n            transform,\n          );\n        }\n        if (!generatorRef.current) {\n          return { hasMore: hasMoreRef.current, data: [] as T extends unknown[] ? T : T[] };\n        }\n        const { value: newData, done } = await generatorRef.current.next();\n        hasMoreRef.current = !done;\n        return { hasMore: hasMoreRef.current, data: (newData ?? []) as T extends unknown[] ? T : T[] };\n      },\n    [url, pageSize, fetchOptions, dataPath, filter, transform],\n    useCachedPromiseOptions,\n  );\n}\n", "import { List, ActionPanel, Action, environment, MenuBarExtra, Icon, open, LaunchType } from \"@raycast/api\";\nimport { existsSync } from \"node:fs\";\nimport { copyFile, mkdir, writeFile } from \"node:fs/promises\";\nimport os from \"node:os\";\nimport childProcess from \"node:child_process\";\nimport path from \"node:path\";\nimport hash from \"object-hash\";\nimport { useRef, useState, useCallback, useMemo } from \"react\";\nimport { usePromise, PromiseOptions } from \"./usePromise\";\nimport { useLatest } from \"./useLatest\";\nimport { getSpawnedPromise, getSpawnedResult } from \"./exec-utils\";\nimport { showFailureToast } from \"./showFailureToast\";\n\n/**\n * Executes a query on a local SQL database and returns the {@link AsyncState} corresponding to the query of the command. The last value will be kept between command runs.\n *\n * @example\n * ```\n * import { useSQL } from \"@raycast/utils\";\n * import { resolve } from \"path\";\n * import { homedir } from \"os\";\n *\n * const NOTES_DB = resolve(homedir(), \"Library/Group Containers/group.com.apple.notes/NoteStore.sqlite\");\n * const notesQuery = `SELECT id, title FROM ...`;\n * type NoteItem = {\n *   id: string;\n *   title: string;\n * };\n *\n * export default function Command() {\n *   const { isLoading, data, permissionView } = useSQL<NoteItem>(NOTES_DB, notesQuery);\n *\n *   if (permissionView) {\n *     return permissionView;\n *   }\n *\n *   return (\n *     <List isLoading={isLoading}>\n *       {(data || []).map((item) => (\n *         <List.Item key={item.id} title={item.title} />\n *       ))}\n *     </List>\n *  );\n * };\n * ```\n */\nexport function useSQL<T = unknown>(\n  databasePath: string,\n  query: string,\n  options?: {\n    /** A string explaining why the extension needs full disk access. For example, the Apple Notes extension uses `\"This is required to search your Apple Notes.\"`. While it is optional, we recommend setting it to help users understand. */\n    permissionPriming?: string;\n  } & Omit<PromiseOptions<(database: string, query: string) => Promise<T[]>>, \"abortable\">,\n) {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const { permissionPriming, ...usePromiseOptions } = options || {};\n\n  const [permissionView, setPermissionView] = useState<JSX.Element>();\n  const latestOptions = useLatest(options || {});\n  const abortable = useRef<AbortController>();\n\n  const handleError = useCallback(\n    (_error: Error) => {\n      console.error(_error);\n      const error =\n        _error instanceof Error && _error.message.includes(\"authorization denied\")\n          ? new PermissionError(\"You do not have permission to access the database.\")\n          : (_error as Error);\n\n      if (isPermissionError(error)) {\n        setPermissionView(<PermissionErrorScreen priming={latestOptions.current.permissionPriming} />);\n      } else {\n        if (latestOptions.current.onError) {\n          latestOptions.current.onError(error);\n        } else {\n          if (environment.launchType !== LaunchType.Background) {\n            showFailureToast(error, {\n              title: \"Cannot query the data\",\n            });\n          }\n        }\n      }\n    },\n    [latestOptions],\n  );\n\n  const fn = useMemo(() => {\n    if (!existsSync(databasePath)) {\n      throw new Error(\"The database does not exist\");\n    }\n    let workaroundCopiedDb: string | undefined = undefined;\n\n    return async (databasePath: string, query: string) => {\n      const abortSignal = abortable.current?.signal;\n      const spawned = childProcess.spawn(\"sqlite3\", [\"--json\", \"--readonly\", databasePath, query], {\n        signal: abortSignal,\n      });\n      const spawnedPromise = getSpawnedPromise(spawned);\n      let [{ error, exitCode, signal }, stdoutResult, stderrResult] = await getSpawnedResult<string>(\n        spawned,\n        { encoding: \"utf-8\" },\n        spawnedPromise,\n      );\n\n      checkAborted(abortSignal);\n\n      if (stderrResult.match(\"(5)\") || stderrResult.match(\"(14)\")) {\n        // That means that the DB is busy because of another app is locking it\n        // This happens when Chrome or Arc is opened: they lock the History db.\n        // As an ugly workaround, we duplicate the file and read that instead\n        // (with vfs unix - none to just not care about locks)\n        if (!workaroundCopiedDb) {\n          const tempFolder = path.join(os.tmpdir(), \"useSQL\", hash(databasePath));\n          await mkdir(tempFolder, { recursive: true });\n          checkAborted(abortSignal);\n\n          workaroundCopiedDb = path.join(tempFolder, \"db.db\");\n          await copyFile(databasePath, workaroundCopiedDb);\n\n          // needed for certain db\n          await writeFile(workaroundCopiedDb + \"-shm\", \"\");\n          await writeFile(workaroundCopiedDb + \"-wal\", \"\");\n\n          checkAborted(abortSignal);\n        }\n        const spawned = childProcess.spawn(\n          \"sqlite3\",\n          [\"--json\", \"--readonly\", \"--vfs\", \"unix-none\", workaroundCopiedDb, query],\n          {\n            signal: abortSignal,\n          },\n        );\n        const spawnedPromise = getSpawnedPromise(spawned);\n        [{ error, exitCode, signal }, stdoutResult, stderrResult] = await getSpawnedResult<string>(\n          spawned,\n          { encoding: \"utf-8\" },\n          spawnedPromise,\n        );\n        checkAborted(abortSignal);\n      }\n\n      if (error || exitCode !== 0 || signal !== null) {\n        throw new Error(stderrResult);\n      }\n\n      return JSON.parse(stdoutResult.trim() || \"[]\") as T[];\n    };\n  }, [databasePath]);\n\n  return {\n    ...usePromise(fn, [databasePath, query], { ...usePromiseOptions, onError: handleError }),\n    permissionView,\n  };\n}\n\nclass PermissionError extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = \"PermissionError\";\n  }\n}\n\nfunction isPermissionError(error: unknown) {\n  return error instanceof Error && error.name === \"PermissionError\";\n}\n\nfunction PermissionErrorScreen(props: { priming?: string }) {\n  const macosVenturaAndLater = parseInt(os.release().split(\".\")[0]) >= 22;\n  const preferencesString = macosVenturaAndLater ? \"Settings\" : \"Preferences\";\n  const action = macosVenturaAndLater\n    ? {\n        title: \"Open System Settings -> Privacy\",\n        target: \"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles\",\n      }\n    : {\n        title: \"Open System Preferences -> Security\",\n        target: \"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles\",\n      };\n\n  if (environment.commandMode === \"menu-bar\") {\n    return (\n      <MenuBarExtra icon={Icon.Warning} title={environment.commandName}>\n        <MenuBarExtra.Item\n          title=\"Raycast needs full disk access\"\n          tooltip={`You can revert this access in ${preferencesString} whenever you want`}\n        />\n        {props.priming ? (\n          <MenuBarExtra.Item\n            title={props.priming}\n            tooltip={`You can revert this access in ${preferencesString} whenever you want`}\n          />\n        ) : null}\n        <MenuBarExtra.Separator />\n        <MenuBarExtra.Item title={action.title} onAction={() => open(action.target)} />\n      </MenuBarExtra>\n    );\n  }\n\n  return (\n    <List>\n      <List.EmptyView\n        icon={{\n          source: {\n            light: \"https://raycast.com/uploads/extensions-utils-security-permissions-light.png\",\n            dark: \"https://raycast.com/uploads/extensions-utils-security-permissions-dark.png\",\n          },\n        }}\n        title=\"Raycast needs full disk access.\"\n        description={`${\n          props.priming ? props.priming + \"\\n\" : \"\"\n        }You can revert this access in ${preferencesString} whenever you want.`}\n        actions={\n          <ActionPanel>\n            <Action.Open {...action} />\n          </ActionPanel>\n        }\n      />\n    </List>\n  );\n}\n\nfunction checkAborted(signal?: AbortSignal) {\n  if (signal?.aborted) {\n    const error = new Error(\"aborted\");\n    error.name = \"AbortError\";\n    throw error;\n  }\n}\n", "import { Form } from \"@raycast/api\";\nimport { useState, useCallback, useMemo, useRef, SetStateAction } from \"react\";\nimport { useLatest } from \"./useLatest\";\n\n/**\n * Shorthands for common validation cases\n */\nexport enum FormValidation {\n  /** Show an error when the value of the item is empty */\n  Required = \"required\",\n}\n\ntype ValidationError = string | undefined | null;\ntype Validator<ValueType> = ((value: ValueType | undefined) => ValidationError) | FormValidation;\n\nfunction validationError<ValueType>(\n  validation: Validator<ValueType> | undefined,\n  value: ValueType | undefined,\n): ValidationError {\n  if (validation) {\n    if (typeof validation === \"function\") {\n      return validation(value);\n    } else if (validation === FormValidation.Required) {\n      let valueIsValid = typeof value !== \"undefined\" && value !== null;\n      if (valueIsValid) {\n        switch (typeof value) {\n          case \"string\":\n            valueIsValid = value.length > 0;\n            break;\n          case \"object\":\n            if (Array.isArray(value)) {\n              valueIsValid = value.length > 0;\n            } else if (value instanceof Date) {\n              valueIsValid = value.getTime() > 0;\n            }\n            break;\n          default:\n            break;\n        }\n      }\n      if (!valueIsValid) {\n        return \"The item is required\";\n      }\n    }\n  }\n}\n\ntype Validation<T extends Form.Values> = { [id in keyof T]?: Validator<T[id]> };\n\ninterface FormProps<T extends Form.Values> {\n  /** Function to pass to the `onSubmit` prop of the `<Action.SubmitForm>` element. It wraps the initial `onSubmit` argument with some goodies related to the validation. */\n  handleSubmit: (values: T) => void | boolean | Promise<void | boolean>;\n  /** The props that must be passed to the `<Form.Item>` elements to handle the validations. */\n  itemProps: {\n    [id in keyof Required<T>]: Partial<Form.ItemProps<T[id]>> & {\n      id: string;\n    };\n  };\n  /** Function that can be used to programmatically set the validation of a specific field. */\n  setValidationError: (id: keyof T, error: ValidationError) => void;\n  /** Function that can be used to programmatically set the value of a specific field. */\n  setValue: <K extends keyof T>(id: K, value: SetStateAction<T[K]>) => void;\n  /** The current values of the form. */\n  values: T;\n  /** Function that can be used to programmatically focus a specific field. */\n  focus: (id: keyof T) => void;\n  /** Function that can be used to reset the values of the Form. */\n  reset: (initialValues?: Partial<T>) => void;\n}\n\n/**\n * Hook that provides a high-level interface to work with Forms, and more particularly, with Form validations. It incorporates all the good practices to provide a great User Experience for your Forms.\n *\n * @returns an object which contains the necessary methods and props to provide a good User Experience in your Form.\n *\n * @example\n * ```\n * import { Action, ActionPanel, Form, showToast, Toast } from \"@raycast/api\";\n * import { useForm, FormValidation } from \"@raycast/utils\";\n *\n * interface SignUpFormValues {\n *   nickname: string;\n *   password: string;\n * }\n *\n * export default function Command() {\n *   const { handleSubmit, itemProps } = useForm<SignUpFormValues>({\n *     onSubmit(values) {\n *       showToast(Toast.Style.Success, \"Yay!\", `${values.nickname} account created`);\n *     },\n *     validation: {\n *       nickname: FormValidation.Required,\n *       password: (value) => {\n *         if (value && value.length < 8) {\n *           return \"Password must be at least 8 symbols\";\n *         } else if (!value) {\n *           return \"The item is required\";\n *         }\n *       },\n *     },\n *   });\n *\n *   return (\n *     <Form\n *       actions={\n *         <ActionPanel>\n *           <Action.SubmitForm title=\"Submit\" onSubmit={handleSubmit} />\n *         </ActionPanel>\n *       }\n *     >\n *       <Form.TextField title=\"Nickname\" placeholder=\"Enter your nickname\" {...itemProps.nickname} />\n *       <Form.PasswordField\n *         title=\"Password\"\n *         placeholder=\"Enter password at least 8 characters long\"\n *         {...itemProps.password}\n *       />\n *     </Form>\n *   );\n * }\n * ```\n */\nexport function useForm<T extends Form.Values>(props: {\n  /** Callback that will be called when the form is submitted and all validations pass. */\n  onSubmit: (values: T) => void | boolean | Promise<void | boolean>;\n  /** The initial values to set when the Form is first rendered. */\n  initialValues?: Partial<T>;\n  /** The validation rules for the Form. A validation for a Form item is a function that takes the current value of the item as an argument and must return a string when the validation is failing.\n   *\n   * There are also some shorthands for common cases, see {@link FormValidation}.\n   * */\n  validation?: Validation<T>;\n}): FormProps<T> {\n  const { onSubmit: _onSubmit, validation, initialValues = {} } = props;\n\n  // @ts-expect-error it's fine if we don't specify all the values\n  const [values, setValues] = useState<T>(initialValues);\n  const [errors, setErrors] = useState<{ [id in keyof T]?: ValidationError }>({});\n  const refs = useRef<{ [id in keyof T]?: Form.ItemReference }>({});\n\n  const latestValidation = useLatest<Validation<T>>(validation || {});\n  const latestOnSubmit = useLatest(_onSubmit);\n\n  const focus = useCallback(\n    (id: keyof T) => {\n      refs.current[id]?.focus();\n    },\n    [refs],\n  );\n\n  const handleSubmit = useCallback(\n    async (values: T): Promise<boolean> => {\n      let validationErrors: false | { [key in keyof T]?: ValidationError } = false;\n      for (const [id, validation] of Object.entries(latestValidation.current)) {\n        const error = validationError(validation, values[id]);\n        if (error) {\n          if (!validationErrors) {\n            validationErrors = {};\n            // we focus the first item that has an error\n            focus(id);\n          }\n          validationErrors[id as keyof T] = error;\n        }\n      }\n      if (validationErrors) {\n        setErrors(validationErrors);\n        return false;\n      }\n      const result = await latestOnSubmit.current(values);\n      return typeof result === \"boolean\" ? result : true;\n    },\n    [latestValidation, latestOnSubmit, focus],\n  );\n\n  const setValidationError = useCallback(\n    (id: keyof T, error: ValidationError) => {\n      setErrors((errors) => ({ ...errors, [id]: error }));\n    },\n    [setErrors],\n  );\n\n  const setValue = useCallback(\n    function <K extends keyof T>(id: K, value: SetStateAction<T[K]>) {\n      // @ts-expect-error TS is always confused about SetStateAction, but it's fine here\n      setValues((values) => ({ ...values, [id]: typeof value === \"function\" ? value(values[id]) : value }));\n    },\n    [setValues],\n  );\n\n  const itemProps = useMemo<{ [id in keyof Required<T>]: Partial<Form.ItemProps<T[id]>> & { id: string } }>(() => {\n    // we have to use a proxy because we don't actually have any object to iterate through\n    // so instead we dynamically create the props when required\n    return new Proxy<{ [id in keyof Required<T>]: Partial<Form.ItemProps<T[id]>> & { id: string } }>(\n      // @ts-expect-error the whole point of a proxy...\n      {},\n      {\n        get(target, id: keyof T) {\n          const validation = latestValidation.current[id];\n          const value = values[id];\n          return {\n            onChange(value) {\n              if (errors[id]) {\n                const error = validationError(validation, value);\n                if (!error) {\n                  setValidationError(id, undefined);\n                }\n              }\n              setValue(id, value);\n            },\n            onBlur(event) {\n              const error = validationError(validation, event.target.value);\n              if (error) {\n                setValidationError(id, error);\n              }\n            },\n            error: errors[id],\n            id,\n            // we shouldn't return `undefined` otherwise it will be an uncontrolled component\n            value: typeof value === \"undefined\" ? null : value,\n            ref: (instance: Form.ItemReference) => {\n              refs.current[id] = instance;\n            },\n          } as Partial<Form.ItemProps<T[keyof T]>> & { id: string };\n        },\n      },\n    );\n  }, [errors, latestValidation, setValidationError, values, refs, setValue]);\n\n  const reset = useCallback(\n    (values?: Partial<T>) => {\n      setErrors({});\n      Object.entries(refs.current).forEach(([id, ref]) => {\n        if (!values?.[id]) {\n          ref?.reset();\n        }\n      });\n      if (values) {\n        // @ts-expect-error it's fine if we don't specify all the values\n        setValues(values);\n      }\n    },\n    [setValues, setErrors, refs],\n  );\n\n  return { handleSubmit, setValidationError, setValue, values, itemProps, focus, reset };\n}\n", "import { useRef, useState } from \"react\";\nimport { AI } from \"@raycast/api\";\nimport { PromiseOptions, usePromise } from \"./usePromise\";\nimport { FunctionReturningPromise } from \"./types\";\n\n/**\n * Stream a prompt completion.\n *\n * @example\n * ```typescript\n * import { Detail, LaunchProps } from \"@raycast/api\";\n * import { use AI } from \"@raycast/utils\";\n *\n * export default function Command(props: LaunchProps<{ arguments: { prompt: string } }>) {\n *   const { isLoading, data } = useAI(props.arguments.prompt);\n *\n *   return <Detail isLoading={isLoading} markdown={data} />;\n * }\n * ```\n */\nexport function useAI(\n  prompt: string,\n  options: {\n    /**\n     * Concrete tasks, such as fixing grammar, require less creativity while open-ended questions, such as generating ideas, require more.\n     * If a number is passed, it needs to be in the range 0-2. For larger values, 2 will be used. For lower values, 0 will be used.\n     */\n    creativity?: AI.Creativity;\n    /**\n     * The AI model to use to answer to the prompt.\n     */\n    model?: AI.Model;\n    /**\n     * Whether to stream the answer or only update the data when the entire answer has been received.\n     */\n    stream?: boolean;\n  } & Omit<PromiseOptions<FunctionReturningPromise>, \"abortable\"> = {},\n) {\n  const { creativity, stream, model, ...usePromiseOptions } = options;\n  const [data, setData] = useState(\"\");\n  const abortable = useRef<AbortController>();\n  const { isLoading, error, revalidate } = usePromise(\n    async (prompt: string, creativity?: AI.Creativity, shouldStream?: boolean) => {\n      setData(\"\");\n      const stream = AI.ask(prompt, { creativity, model, signal: abortable.current?.signal });\n      if (shouldStream === false) {\n        setData(await stream);\n      } else {\n        stream.on(\"data\", (data) => {\n          setData((x) => x + data);\n        });\n        await stream;\n      }\n    },\n    [prompt, creativity, stream],\n    { ...usePromiseOptions, abortable },\n  );\n\n  return { isLoading, data, error, revalidate };\n}\n", "import { useMemo, useCallback } from \"react\";\nimport { useLatest } from \"./useLatest\";\nimport { useCachedState } from \"./useCachedState\";\n\n// The algorithm below is inspired by the one used by Firefox:\n// https://wiki.mozilla.org/User:Jesse/NewFrecency\n\ntype Frecency = {\n  lastVisited: number;\n  frecency: number;\n};\n\nconst HALF_LIFE_DAYS = 10;\n\nconst MS_PER_DAY = 24 * 60 * 60 * 1000;\n\nconst VISIT_TYPE_POINTS = {\n  Default: 100,\n  Embed: 0,\n  Bookmark: 140,\n};\n\nfunction getNewFrecency(item?: Frecency): Frecency {\n  const now = Date.now();\n  const lastVisited = item ? item.lastVisited : 0;\n  const frecency = item ? item.frecency : 0;\n\n  const visitAgeInDays = (now - lastVisited) / MS_PER_DAY;\n  const DECAY_RATE_CONSTANT = Math.log(2) / (HALF_LIFE_DAYS * MS_PER_DAY);\n  const currentVisitValue = VISIT_TYPE_POINTS.Default * Math.exp(-DECAY_RATE_CONSTANT * visitAgeInDays);\n  const totalVisitValue = frecency + currentVisitValue;\n\n  return {\n    lastVisited: now,\n    frecency: totalVisitValue,\n  };\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst defaultKey = (item: any): string => {\n  if (\n    process.env.NODE_ENV !== \"production\" &&\n    (typeof item !== \"object\" || !item || !(\"id\" in item) || typeof item.id != \"string\")\n  ) {\n    throw new Error(\"Specify a key function or make sure your items have an 'id' property\");\n  }\n  return item.id;\n};\n\n/**\n * Sort an array by its frecency and provide methods to update the frecency of its items.\n * Frecency is a measure that combines frequency and recency. The more often an item is visited/used, and the more recently an item is visited/used, the higher it will rank.\n *\n * @example\n * ```\n * import { List, ActionPanel, Action, Icon } from \"@raycast/api\";\n * import { useFetch, useFrecencySorting } from \"@raycast/utils\";\n *\n * export default function Command() {\n *   const { isLoading, data } = useFetch(\"https://api.example\");\n *   const { data: sortedData, visitItem, resetRanking } = useFrecencySorting(data);\n *\n *   return (\n *     <List isLoading={isLoading}>\n *       {sortedData.map((item) => (\n *         <List.Item\n *           key={item.id}\n *           title={item.title}\n *           actions={\n *             <ActionPanel>\n *               <Action.OpenInBrowser url={item.url} onOpen={() => visitItem(item)} />\n *               <Action.CopyToClipboard title=\"Copy Link\" content={item.url} onCopy={() => visitItem(item)} />\n *               <Action title=\"Reset Ranking\" icon={Icon.ArrowCounterClockwise} onAction={() => resetRanking(item)} />\n *             </ActionPanel>\n *           }\n *         />\n *       ))}\n *     </List>\n *   );\n * };\n * ```\n */\nexport function useFrecencySorting<T extends { id: string }>(\n  data?: T[],\n  options?: { namespace?: string; key?: (item: T) => string; sortUnvisited?: (a: T, b: T) => number },\n): {\n  data: T[];\n  visitItem: (item: T) => Promise<void>;\n  resetRanking: (item: T) => Promise<void>;\n};\nexport function useFrecencySorting<T>(\n  data: T[] | undefined,\n  options: { namespace?: string; key: (item: T) => string; sortUnvisited?: (a: T, b: T) => number },\n): {\n  data: T[];\n  visitItem: (item: T) => Promise<void>;\n  resetRanking: (item: T) => Promise<void>;\n};\nexport function useFrecencySorting<T>(\n  data?: T[],\n  options?: { namespace?: string; key?: (item: T) => string; sortUnvisited?: (a: T, b: T) => number },\n): {\n  data: T[];\n  visitItem: (item: T) => Promise<void>;\n  resetRanking: (item: T) => Promise<void>;\n} {\n  const keyRef = useLatest(options?.key || defaultKey);\n  const sortUnvisitedRef = useLatest(options?.sortUnvisited);\n\n  const [storedFrecencies, setStoredFrecencies] = useCachedState<Record<string, Frecency | undefined>>(\n    `raycast_frecency_${options?.namespace}`,\n    {},\n  );\n\n  const visitItem = useCallback(\n    async function updateFrecency(item: T) {\n      const itemKey = keyRef.current(item);\n\n      setStoredFrecencies((storedFrecencies) => {\n        const frecency = storedFrecencies[itemKey];\n        const newFrecency = getNewFrecency(frecency);\n\n        return {\n          ...storedFrecencies,\n          [itemKey]: newFrecency,\n        };\n      });\n    },\n    [keyRef, setStoredFrecencies],\n  );\n\n  const resetRanking = useCallback(\n    async function removeFrecency(item: T) {\n      const itemKey = keyRef.current(item);\n\n      setStoredFrecencies((storedFrecencies) => {\n        const newFrencencies = { ...storedFrecencies };\n        delete newFrencencies[itemKey];\n\n        return newFrencencies;\n      });\n    },\n    [keyRef, setStoredFrecencies],\n  );\n\n  const sortedData = useMemo(() => {\n    if (!data) {\n      return [];\n    }\n\n    return data.sort((a, b) => {\n      const frecencyA = storedFrecencies[keyRef.current(a)];\n      const frecencyB = storedFrecencies[keyRef.current(b)];\n\n      // If a has a frecency, but b doesn't, a should come first\n      if (frecencyA && !frecencyB) {\n        return -1;\n      }\n\n      // If b has a frecency, but a doesn't, b should come first\n      if (!frecencyA && frecencyB) {\n        return 1;\n      }\n\n      // If both frecencies are defined,put the one with the higher frecency first\n      if (frecencyA && frecencyB) {\n        return frecencyB.frecency - frecencyA.frecency;\n      }\n\n      // If both frecencies are undefined, keep the original order\n      return sortUnvisitedRef.current ? sortUnvisitedRef.current(a, b) : 0;\n    });\n  }, [storedFrecencies, data, keyRef, sortUnvisitedRef]);\n\n  return { data: sortedData, visitItem, resetRanking };\n}\n", "import { LocalStorage } from \"@raycast/api\";\nimport { showFailureToast } from \"./showFailureToast\";\nimport { replacer, reviver } from \"./helpers\";\nimport { usePromise } from \"./usePromise\";\n\n/**\n * A hook to manage a value in the local storage.\n *\n * @remark The value is stored as a JSON string in the local storage.\n *\n * @param key - The key to use for the value in the local storage.\n * @param initialValue - The initial value to use if the key doesn't exist in the local storage.\n * @returns An object with the following properties:\n * - `value`: The value from the local storage or the initial value if the key doesn't exist.\n * - `setValue`: A function to update the value in the local storage.\n * - `removeValue`: A function to remove the value from the local storage.\n * - `isLoading`: A boolean indicating if the value is loading.\n *\n * @example\n * ```\n * const { value, setValue } = useLocalStorage<string>(\"my-key\");\n * const { value, setValue } = useLocalStorage<string>(\"my-key\", \"default value\");\n * ```\n */\nexport function useLocalStorage<T>(key: string, initialValue?: T) {\n  const {\n    data: value,\n    isLoading,\n    mutate,\n  } = usePromise(\n    async (storageKey: string) => {\n      const item = await LocalStorage.getItem<string>(storageKey);\n\n      return typeof item !== \"undefined\" ? (JSON.parse(item, reviver) as T) : initialValue;\n    },\n    [key],\n  );\n\n  async function setValue(value: T) {\n    try {\n      await mutate(LocalStorage.setItem(key, JSON.stringify(value, replacer)), {\n        optimisticUpdate(value) {\n          return value;\n        },\n      });\n    } catch (error) {\n      await showFailureToast(error, { title: \"Failed to set value in local storage\" });\n    }\n  }\n\n  async function removeValue() {\n    try {\n      await mutate(LocalStorage.removeItem(key), {\n        optimisticUpdate() {\n          return undefined;\n        },\n      });\n    } catch (error) {\n      await showFailureToast(error, { title: \"Failed to remove value from local storage\" });\n    }\n  }\n\n  return { value, setValue, removeValue, isLoading };\n}\n", "export { getAvatarIcon } from \"./avatar\";\nexport { getFavicon } from \"./favicon\";\nexport { getProgressIcon } from \"./progress\";\n", "import type { Image } from \"@raycast/api\";\nimport { slightlyLighterColor, slightlyDarkerColor } from \"./color\";\n\nfunction getWholeCharAndI(str: string, i: number): [string, number] {\n  const code = str.charCodeAt(i);\n\n  if (Number.isNaN(code)) {\n    return [\"\", i];\n  }\n  if (code < 0xd800 || code > 0xdfff) {\n    return [str.charAt(i), i]; // Normal character, keeping 'i' the same\n  }\n\n  // High surrogate (could change last hex to 0xDB7F to treat high private\n  // surrogates as single characters)\n  if (0xd800 <= code && code <= 0xdbff) {\n    if (str.length <= i + 1) {\n      throw new Error(\"High surrogate without following low surrogate\");\n    }\n    const next = str.charCodeAt(i + 1);\n    if (0xdc00 > next || next > 0xdfff) {\n      throw new Error(\"High surrogate without following low surrogate\");\n    }\n    return [str.charAt(i) + str.charAt(i + 1), i + 1];\n  }\n\n  // Low surrogate (0xDC00 <= code && code <= 0xDFFF)\n  if (i === 0) {\n    throw new Error(\"Low surrogate without preceding high surrogate\");\n  }\n\n  const prev = str.charCodeAt(i - 1);\n\n  // (could change last hex to 0xDB7F to treat high private surrogates\n  // as single characters)\n  if (0xd800 > prev || prev > 0xdbff) {\n    throw new Error(\"Low surrogate without preceding high surrogate\");\n  }\n\n  // Return the next character instead (and increment)\n  return [str.charAt(i + 1), i + 1];\n}\n\nconst avatarColorSet = [\n  \"#DC829A\", // Pink\n  \"#D64854\", // Red\n  \"#D47600\", // YellowOrange\n  \"#D36CDD\", // Magenta\n  \"#52A9E4\", // Aqua\n  \"#7871E8\", // Indigo\n  \"#70920F\", // YellowGreen\n  \"#43B93A\", // Green\n  \"#EB6B3E\", // Orange\n  \"#26B795\", // BlueGreen\n  \"#D85A9B\", // HotPink\n  \"#A067DC\", // Purple\n  \"#BD9500\", // Yellow\n  \"#5385D9\", // Blue\n];\n\n/**\n * Icon to represent an avatar when you don't have one. The generated avatar\n * will be generated from the initials of the name and have a colorful but consistent background.\n *\n * @returns an Image that can be used where Raycast expects them.\n *\n * @example\n * ```\n * <List.Item icon={getAvatarIcon('Mathieu Dutour')} title=\"Project\" />\n * ```\n */\nexport function getAvatarIcon(\n  name: string,\n  options?: {\n    /**\n     * Custom background color\n     */\n    background?: string;\n    /**\n     * Whether to use a gradient for the background or not.\n     * @default true\n     */\n    gradient?: boolean;\n  },\n): Image.Asset {\n  const words = name.trim().split(\" \");\n  let initials: string;\n  if (words.length == 1 && getWholeCharAndI(words[0], 0)[0]) {\n    initials = getWholeCharAndI(words[0], 0)[0];\n  } else if (words.length > 1) {\n    const firstWordFirstLetter = getWholeCharAndI(words[0], 0)[0] || \"\";\n    const lastWordFirstLetter = getWholeCharAndI(words[words.length - 1], 0)[0] ?? \"\";\n    initials = firstWordFirstLetter + lastWordFirstLetter;\n  } else {\n    initials = \"\";\n  }\n\n  let backgroundColor: string;\n\n  if (options?.background) {\n    backgroundColor = options?.background;\n  } else {\n    let initialsCharIndex = 0;\n    let [char, i] = getWholeCharAndI(initials, 0);\n    while (char) {\n      initialsCharIndex += char.charCodeAt(0);\n      [char, i] = getWholeCharAndI(initials, i + 1);\n    }\n\n    const colorIndex = initialsCharIndex % avatarColorSet.length;\n    backgroundColor = avatarColorSet[colorIndex];\n  }\n\n  const padding = 0;\n  const radius = 50 - padding;\n\n  const svg = `<svg width=\"100px\" height=\"100px\">\n  ${\n    options?.gradient !== false\n      ? `<defs>\n      <linearGradient id=\"Gradient\" x1=\"0.25\" x2=\"0.75\" y1=\"0\" y2=\"1\">\n        <stop offset=\"0%\" stop-color=\"${slightlyLighterColor(backgroundColor)}\"/>\n        <stop offset=\"50%\" stop-color=\"${backgroundColor}\"/>\n        <stop offset=\"100%\" stop-color=\"${slightlyDarkerColor(backgroundColor)}\"/>\n      </linearGradient>\n  </defs>`\n      : \"\"\n  }\n      <circle cx=\"50\" cy=\"50\" r=\"${radius}\" fill=\"${\n        options?.gradient !== false ? \"url(#Gradient)\" : backgroundColor\n      }\" />\n      ${\n        initials\n          ? `<text x=\"50\" y=\"80\" font-size=\"${\n              radius - 1\n            }\" font-family=\"Inter, sans-serif\" text-anchor=\"middle\" fill=\"white\">${initials.toUpperCase()}</text>`\n          : \"\"\n      }\n    </svg>\n  `.replaceAll(\"\\n\", \"\");\n  return `data:image/svg+xml,${svg}`;\n}\n", "function hexToRGB(hex: string) {\n  let r = 0;\n  let g = 0;\n  let b = 0;\n\n  // 3 digits\n  if (hex.length === 4) {\n    r = parseInt(`${hex[1]}${hex[1]}`, 16);\n    g = parseInt(`${hex[2]}${hex[2]}`, 16);\n    b = parseInt(`${hex[3]}${hex[3]}`, 16);\n\n    // 6 digits\n  } else if (hex.length === 7) {\n    r = parseInt(`${hex[1]}${hex[2]}`, 16);\n    g = parseInt(`${hex[3]}${hex[4]}`, 16);\n    b = parseInt(`${hex[5]}${hex[6]}`, 16);\n  } else {\n    throw new Error(`Malformed hex color: ${hex}`);\n  }\n\n  return { r, g, b };\n}\n\nfunction rgbToHex({ r, g, b }: { r: number; g: number; b: number }) {\n  let rString = r.toString(16);\n  let gString = g.toString(16);\n  let bString = b.toString(16);\n\n  if (rString.length === 1) {\n    rString = `0${rString}`;\n  }\n  if (gString.length === 1) {\n    gString = `0${gString}`;\n  }\n  if (bString.length === 1) {\n    bString = `0${bString}`;\n  }\n\n  return `#${rString}${gString}${bString}`;\n}\n\nfunction rgbToHSL({ r, g, b }: { r: number; g: number; b: number }) {\n  // Make r, g, and b fractions of 1\n  r /= 255;\n  g /= 255;\n  b /= 255;\n\n  // Find greatest and smallest channel values\n  const cmin = Math.min(r, g, b);\n  const cmax = Math.max(r, g, b);\n  const delta = cmax - cmin;\n  let h = 0;\n  let s = 0;\n  let l = 0;\n\n  // Calculate hue\n  // No difference\n  if (delta === 0) {\n    h = 0;\n  }\n  // Red is max\n  else if (cmax === r) {\n    h = ((g - b) / delta) % 6;\n  }\n  // Green is max\n  else if (cmax === g) {\n    h = (b - r) / delta + 2;\n  }\n  // Blue is max\n  else {\n    h = (r - g) / delta + 4;\n  }\n\n  h = Math.round(h * 60);\n\n  // Make negative hues positive behind 360°\n  if (h < 0) {\n    h += 360;\n  }\n\n  // Calculate lightness\n  l = (cmax + cmin) / 2;\n\n  // Calculate saturation\n  s = delta === 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));\n\n  // Multiply l and s by 100\n  s = +(s * 100).toFixed(1);\n  l = +(l * 100).toFixed(1);\n\n  return { h, s, l };\n}\n\nfunction hslToRGB({ h, s, l }: { h: number; s: number; l: number }) {\n  // Must be fractions of 1\n  s /= 100;\n  l /= 100;\n\n  const c = (1 - Math.abs(2 * l - 1)) * s;\n  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));\n  const m = l - c / 2;\n  let r = 0;\n  let g = 0;\n  let b = 0;\n\n  if (h >= 0 && h < 60) {\n    r = c;\n    g = x;\n    b = 0;\n  } else if (h >= 60 && h < 120) {\n    r = x;\n    g = c;\n    b = 0;\n  } else if (h >= 120 && h < 180) {\n    r = 0;\n    g = c;\n    b = x;\n  } else if (h >= 180 && h < 240) {\n    r = 0;\n    g = x;\n    b = c;\n  } else if (h >= 240 && h < 300) {\n    r = x;\n    g = 0;\n    b = c;\n  } else if (h >= 300 && h < 360) {\n    r = c;\n    g = 0;\n    b = x;\n  }\n  r = Math.round((r + m) * 255);\n  g = Math.round((g + m) * 255);\n  b = Math.round((b + m) * 255);\n\n  return { r, g, b };\n}\n\nfunction hexToHSL(hex: string) {\n  return rgbToHSL(hexToRGB(hex));\n}\n\nfunction hslToHex(hsl: { h: number; s: number; l: number }) {\n  return rgbToHex(hslToRGB(hsl));\n}\n\nfunction clamp(value: number, min: number, max: number) {\n  return min < max ? (value < min ? min : value > max ? max : value) : value < max ? max : value > min ? min : value;\n}\n\nconst offset = 12;\n\nexport function slightlyDarkerColor(hex: string) {\n  const hsl = hexToHSL(hex);\n\n  return hslToHex({\n    h: hsl.h,\n    s: hsl.s,\n    l: clamp(hsl.l - offset, 0, 100),\n  });\n}\n\nexport function slightlyLighterColor(hex: string) {\n  const hsl = hexToHSL(hex);\n\n  return hslToHex({\n    h: hsl.h,\n    s: hsl.s,\n    l: clamp(hsl.l + offset, 0, 100),\n  });\n}\n", "import { Icon, Image } from \"@raycast/api\";\nimport { URL } from \"node:url\";\n\n/**\n * Icon showing the favicon of a website.\n *\n * A favicon (favorite icon) is a tiny icon included along with a website, which is displayed in places like the browser's address bar, page tabs, and bookmarks menu.\n *\n * @param url The URL of the website to represent.\n *\n * @returns an Image that can be used where Raycast expects them.\n *\n * @example\n * ```\n * <List.Item icon={getFavicon(\"https://raycast.com\")} title=\"Raycast Website\" />\n * ```\n */\nexport function getFavicon(\n  url: string | URL,\n  options?: {\n    /**\n     * Size of the Favicon\n     * @default 64\n     */\n    size?: number;\n    /**\n     * Fallback icon in case the Favicon is not found.\n     * @default Icon.Link\n     */\n    fallback?: Image.Fallback;\n    /**\n     * A {@link Image.Mask} to apply to the Favicon.\n     */\n    mask?: Image.Mask;\n  },\n): Image.ImageLike {\n  try {\n    const urlObj = typeof url === \"string\" ? new URL(url) : url;\n    const hostname = urlObj.hostname;\n    return {\n      source: `https://www.google.com/s2/favicons?sz=${options?.size ?? 64}&domain=${hostname}`,\n      fallback: options?.fallback ?? Icon.Link,\n      mask: options?.mask,\n    };\n  } catch (e) {\n    console.error(e);\n    return Icon.Link;\n  }\n}\n", "import { environment, Color } from \"@raycast/api\";\nimport type { Image } from \"@raycast/api\";\n\nfunction polarToCartesian(centerX: number, centerY: number, radius: number, angleInDegrees: number) {\n  const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;\n\n  return {\n    x: centerX + radius * Math.cos(angleInRadians),\n    y: centerY + radius * Math.sin(angleInRadians),\n  };\n}\n\nfunction describeArc(x: number, y: number, radius: number, startAngle: number, endAngle: number) {\n  const start = polarToCartesian(x, y, radius, endAngle);\n  const end = polarToCartesian(x, y, radius, startAngle);\n\n  const largeArcFlag = endAngle - startAngle <= 180 ? \"0\" : \"1\";\n\n  const d = [\"M\", start.x, start.y, \"A\", radius, radius, 0, largeArcFlag, 0, end.x, end.y].join(\" \");\n\n  return d;\n}\n\n/**\n * Icon to represent the progress of _something_.\n *\n * @param progress Number between 0 and 1.\n * @param color Hex color (default `\"#FF6363\"`) or Color.\n *\n * @returns an Image that can be used where Raycast expects them.\n *\n * @example\n * ```\n * <List.Item icon={getProgressIcon(0.1)} title=\"Project\" />\n * ```\n */\nexport function getProgressIcon(\n  progress: number,\n  color: Color | string = Color.Red,\n  options?: { background?: Color | string; backgroundOpacity?: number },\n): Image.Asset {\n  const background = options?.background || (environment.appearance === \"light\" ? \"black\" : \"white\");\n  const backgroundOpacity = options?.backgroundOpacity || 0.1;\n\n  const stroke = 10;\n  const padding = 5;\n  const radius = 50 - padding - stroke / 2;\n\n  const svg = `<svg width=\"100px\" height=\"100px\">\n      <circle cx=\"50\" cy=\"50\" r=\"${radius}\" stroke-width=\"${stroke}\" stroke=\"${\n        progress < 1 ? background : color\n      }\" opacity=\"${progress < 1 ? backgroundOpacity : \"1\"}\" fill=\"none\" />\n      ${\n        progress > 0 && progress < 1\n          ? `<path d=\"${describeArc(\n              50,\n              50,\n              radius,\n              0,\n              progress * 360,\n            )}\" stroke=\"${color}\" stroke-width=\"${stroke}\" fill=\"none\" />`\n          : \"\"\n      }\n    </svg>\n  `.replaceAll(\"\\n\", \"\");\n  return `data:image/svg+xml,${svg}`;\n}\n", "export { OAuthService } from \"./OAuthService\";\nexport { withAccessToken, getAccessToken } from \"./withAccessToken\";\n\nexport type { WithAccessTokenComponentOrFn } from \"./withAccessToken\";\nexport type {\n  OnAuthorizeParams,\n  OAuthServiceOptions,\n  ProviderWithDefaultClientOptions,\n  ProviderOptions,\n} from \"./types\";\n", "import { OAuth } from \"@raycast/api\";\nimport fetch from \"node-fetch-cjs\";\nimport {\n  asanaService,\n  githubService,\n  googleService,\n  jiraService,\n  linearService,\n  slackService,\n  zoomService,\n} from \"./providers\";\nimport type { OAuthServiceOptions, OnAuthorizeParams } from \"./types\";\n\ntype FetchTokensArgs = { authRequest: OAuth.AuthorizationRequest; authorizationCode: string } & Pick<\n  OAuthServiceOptions,\n  \"clientId\" | \"tokenUrl\" | \"bodyEncoding\"\n>;\n\ntype RefreshTokensArgs = { token: string } & Pick<OAuthServiceOptions, \"clientId\" | \"tokenUrl\" | \"bodyEncoding\">;\n\n/**\n * Class allowing to create an OAuth service using the the PKCE (Proof Key for Code Exchange) flow.\n *\n * This service is capable of starting the authorization process, fetching and refreshing tokens,\n * as well as managing the authentication state.\n *\n * @example\n * ```typescript\n * const oauthClient = new OAuth.PKCEClient({ ... });\n * const oauthService = new OAuthService({\n *   client: oauthClient,\n *   clientId: 'your-client-id',\n *   scope: 'required scopes',\n *   authorizeUrl: 'https://provider.com/oauth/authorize',\n *   tokenUrl: 'https://provider.com/oauth/token',\n *   refreshTokenUrl: 'https://provider.com/oauth/token',\n *   extraParameters: { 'additional_param': 'value' }\n * });\n * ```\n */\nexport class OAuthService implements OAuthServiceOptions {\n  public clientId: string;\n  public scope: string;\n  public client: OAuth.PKCEClient;\n  public extraParameters?: Record<string, string>;\n  public authorizeUrl: string;\n  public tokenUrl: string;\n  public refreshTokenUrl?: string;\n  public bodyEncoding?: \"json\" | \"url-encoded\";\n  public personalAccessToken?: string;\n  onAuthorize?: (params: OnAuthorizeParams) => void;\n  tokenResponseParser: (response: unknown) => OAuth.TokenResponse;\n  tokenRefreshResponseParser: (response: unknown) => OAuth.TokenResponse;\n\n  constructor(options: OAuthServiceOptions) {\n    this.clientId = options.clientId;\n    this.scope = Array.isArray(options.scope) ? options.scope.join(\" \") : options.scope;\n    this.personalAccessToken = options.personalAccessToken;\n    this.bodyEncoding = options.bodyEncoding;\n    this.client = options.client;\n    this.extraParameters = options.extraParameters;\n    this.authorizeUrl = options.authorizeUrl;\n    this.tokenUrl = options.tokenUrl;\n    this.refreshTokenUrl = options.refreshTokenUrl;\n    this.onAuthorize = options.onAuthorize;\n    this.tokenResponseParser = options.tokenResponseParser ?? ((x) => x as OAuth.TokenResponse);\n    this.tokenRefreshResponseParser = options.tokenRefreshResponseParser ?? ((x) => x as OAuth.TokenResponse);\n    this.authorize = this.authorize.bind(this);\n  }\n\n  /**\n   * Asana OAuth service provided out of the box.\n   *\n   * @example\n   * ```typescript\n   * const asana = OAuthService.asana({ scope: 'default' })\n   * ```\n   */\n  public static asana: typeof asanaService = asanaService;\n\n  /**\n   * GitHub OAuth service provided out of the box.\n   *\n   * @example\n   * ```typescript\n   * const github = OAuthService.github({ scope: 'repo user' })\n   * ```\n   */\n  public static github: typeof githubService = githubService;\n\n  /**\n   * Google OAuth service provided out of the box.\n   *\n   * @example\n   * ```typescript\n   * const google = OAuthService.google({\n   *   clientId: 'custom-client-id',\n   *   authorizeUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n   *   tokenUrl: 'https://oauth2.googleapis.com/token',\n   *   scope: 'https://www.googleapis.com/auth/drive.readonly',\n   * });\n   * ```\n   */\n  public static google: typeof googleService = googleService;\n\n  /**\n   * Jira OAuth service provided out of the box.\n   *\n   * @example\n   * ```typescript\n   * const jira = OAuthService.jira({\n   *   clientId: 'custom-client-id',\n   *   authorizeUrl: 'https://auth.atlassian.com/authorize',\n   *   tokenUrl: 'https://api.atlassian.com/oauth/token',\n   *   scope: 'read:jira-user read:jira-work offline_access'\n   * });\n   * ```\n   */\n  public static jira: typeof jiraService = jiraService;\n\n  /**\n   * Linear OAuth service provided out of the box.\n   *\n   * @example\n   * ```typescript\n   * const linear = OAuthService.linear({ scope: 'read write' })\n   * ```\n   */\n  public static linear: typeof linearService = linearService;\n\n  /**\n   * Slack OAuth service provided out of the box.\n   *\n   * @example\n   * ```typescript\n   * const slack = OAuthService.slack({ scope: 'emoji:read' })\n   * ```\n   */\n  public static slack: typeof slackService = slackService;\n\n  /**\n   * Zoom OAuth service provided out of the box.\n   *\n   * @example\n   * ```typescript\n   * const zoom = OAuthService.zoom({\n   *   clientId: 'custom-client-id',\n   *   authorizeUrl: 'https://zoom.us/oauth/authorize',\n   *   tokenUrl: 'https://zoom.us/oauth/token',\n   *   scope: 'meeting:write',\n   *   personalAccessToken: 'personal-access-token',\n   * });\n   * ```\n   */\n  public static zoom: typeof zoomService = zoomService;\n\n  /**\n   * Initiates the OAuth authorization process or refreshes existing tokens if necessary.\n   * If the current token set has a refresh token and it is expired, then the function will refresh the tokens.\n   * If no tokens exist, it will initiate the OAuth authorization process and fetch the tokens.\n   *\n   * @returns {Promise<string>} A promise that resolves with the access token obtained from the authorization flow, or null if the token could not be obtained.\n   */\n  async authorize() {\n    const currentTokenSet = await this.client.getTokens();\n    if (currentTokenSet?.accessToken) {\n      if (currentTokenSet.refreshToken && currentTokenSet.isExpired()) {\n        const tokens = await this.refreshTokens({\n          token: currentTokenSet.refreshToken,\n          clientId: this.clientId,\n          tokenUrl: this.refreshTokenUrl ?? this.tokenUrl,\n        });\n        await this.client.setTokens(tokens);\n        return tokens.access_token;\n      }\n      return currentTokenSet.accessToken;\n    }\n\n    const authRequest = await this.client.authorizationRequest({\n      endpoint: this.authorizeUrl,\n      clientId: this.clientId,\n      scope: this.scope,\n      extraParameters: this.extraParameters,\n    });\n\n    const { authorizationCode } = await this.client.authorize(authRequest);\n    const tokens = await this.fetchTokens({\n      authRequest,\n      authorizationCode,\n      clientId: this.clientId,\n      tokenUrl: this.tokenUrl,\n    });\n\n    await this.client.setTokens(tokens);\n\n    return tokens.access_token;\n  }\n\n  private async fetchTokens({ authRequest, authorizationCode, clientId, tokenUrl, bodyEncoding }: FetchTokensArgs) {\n    let options;\n    if (bodyEncoding === \"url-encoded\") {\n      const params = new URLSearchParams();\n      params.append(\"client_id\", clientId);\n      params.append(\"code\", authorizationCode);\n      params.append(\"code_verifier\", authRequest.codeVerifier);\n      params.append(\"grant_type\", \"authorization_code\");\n      params.append(\"redirect_uri\", authRequest.redirectURI);\n\n      options = { body: params };\n    } else {\n      options = {\n        body: JSON.stringify({\n          client_id: clientId,\n          code: authorizationCode,\n          code_verifier: authRequest.codeVerifier,\n          grant_type: \"authorization_code\",\n          redirect_uri: authRequest.redirectURI,\n        }),\n        headers: { \"Content-Type\": \"application/json\" },\n      };\n    }\n\n    const response = await fetch(tokenUrl, { method: \"POST\", ...options });\n    if (!response.ok) {\n      const responseText = await response.text();\n      console.error(\"fetch tokens error:\", responseText);\n      throw new Error(`Error while fetching tokens: ${response.status} (${response.statusText})\\n${responseText}`);\n    }\n    const tokens = this.tokenResponseParser(await response.json());\n\n    // Some clients such as Linear can return a scope array instead of a string\n    return Array.isArray(tokens.scope) ? { ...tokens, scope: tokens.scope.join(\" \") } : tokens;\n  }\n\n  private async refreshTokens({ token, clientId, tokenUrl, bodyEncoding }: RefreshTokensArgs) {\n    let options;\n    if (bodyEncoding === \"url-encoded\") {\n      const params = new URLSearchParams();\n      params.append(\"client_id\", clientId);\n      params.append(\"refresh_token\", token);\n      params.append(\"grant_type\", \"refresh_token\");\n\n      options = { body: params };\n    } else {\n      options = {\n        body: JSON.stringify({\n          client_id: clientId,\n          refresh_token: token,\n          grant_type: \"refresh_token\",\n        }),\n        headers: { \"Content-Type\": \"application/json\" },\n      };\n    }\n\n    const response = await fetch(tokenUrl, { method: \"POST\", ...options });\n    if (!response.ok) {\n      const responseText = await response.text();\n      console.error(\"refresh tokens error:\", responseText);\n      throw new Error(`Error while refreshing tokens: ${response.status} (${response.statusText})\\n${responseText}`);\n    }\n    const tokenResponse = this.tokenRefreshResponseParser(await response.json());\n    tokenResponse.refresh_token = tokenResponse.refresh_token ?? token;\n    return tokenResponse;\n  }\n}\n", "import { Color, OAuth } from \"@raycast/api\";\nimport { OAuthService } from \"./OAuthService\";\nimport type { ProviderOptions, ProviderWithDefaultClientOptions } from \"./types\";\n\nconst PROVIDER_CLIENT_IDS = {\n  asana: \"1191201745684312\",\n  github: \"7235fe8d42157f1f38c0\",\n  linear: \"c8ff37b9225c3c9aefd7d66ea0e5b6f1\",\n  slack: \"851756884692.5546927290212\",\n};\n\nconst getIcon = (markup: string) => `data:image/svg+xml,${markup}`;\n\nconst PROVIDERS_ICONS = /* #__PURE__ */ {\n  asana: /* #__PURE__ */ getIcon(\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"251\" height=\"232\" fill=\"none\"><path fill=\"#F06A6A\" d=\"M179.383 54.373c0 30.017-24.337 54.374-54.354 54.374-30.035 0-54.373-24.338-54.373-54.374C70.656 24.338 94.993 0 125.029 0c30.017 0 54.354 24.338 54.354 54.373ZM54.393 122.33C24.376 122.33.02 146.668.02 176.685c0 30.017 24.337 54.373 54.373 54.373 30.035 0 54.373-24.338 54.373-54.373 0-30.017-24.338-54.355-54.373-54.355Zm141.253 0c-30.035 0-54.373 24.338-54.373 54.374 0 30.035 24.338 54.373 54.373 54.373 30.017 0 54.374-24.338 54.374-54.373 0-30.036-24.338-54.374-54.374-54.374Z\"/></svg>`,\n  ),\n  github: {\n    source: /* #__PURE__ */ getIcon(\n      `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" viewBox=\"0 0 16 16\"><path fill-rule=\"evenodd\" d=\"M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z\"/></svg>`,\n    ),\n    tintColor: Color.PrimaryText,\n  },\n  google: /* #__PURE__ */ getIcon(\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" style=\"display:block\" viewBox=\"0 0 48 48\"><path fill=\"#EA4335\" d=\"M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z\"/><path fill=\"#4285F4\" d=\"M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z\"/><path fill=\"#FBBC05\" d=\"M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z\"/><path fill=\"#34A853\" d=\"M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z\"/><path fill=\"none\" d=\"M0 0h48v48H0z\"/></svg>`,\n  ),\n  jira: /* #__PURE__ */ getIcon(\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"2361\" height=\"2500\" viewBox=\"2.59 0 214.091 224\"><linearGradient id=\"a\" x1=\"102.4\" x2=\"56.15\" y1=\"218.63\" y2=\"172.39\" gradientTransform=\"matrix(1 0 0 -1 0 264)\" gradientUnits=\"userSpaceOnUse\"><stop offset=\".18\" stop-color=\"#0052cc\"/><stop offset=\"1\" stop-color=\"#2684ff\"/></linearGradient><linearGradient xlink:href=\"#a\" id=\"b\" x1=\"114.65\" x2=\"160.81\" y1=\"85.77\" y2=\"131.92\"/><path fill=\"#2684ff\" d=\"M214.06 105.73 117.67 9.34 108.33 0 35.77 72.56 2.59 105.73a8.89 8.89 0 0 0 0 12.54l66.29 66.29L108.33 224l72.55-72.56 1.13-1.12 32.05-32a8.87 8.87 0 0 0 0-12.59zm-105.73 39.39L75.21 112l33.12-33.12L141.44 112z\"/><path fill=\"url(#a)\" d=\"M108.33 78.88a55.75 55.75 0 0 1-.24-78.61L35.62 72.71l39.44 39.44z\"/><path fill=\"url(#b)\" d=\"m141.53 111.91-33.2 33.21a55.77 55.77 0 0 1 0 78.86L181 151.35z\"/></svg>`,\n  ),\n  linear: {\n    source: {\n      light: /* #__PURE__ */ getIcon(\n        `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"#222326\" width=\"200\" height=\"200\" viewBox=\"0 0 100 100\"><path d=\"M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z\"/></svg>`,\n      ),\n      dark: /* #__PURE__ */ getIcon(\n        `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"#fff\" width=\"200\" height=\"200\" viewBox=\"0 0 100 100\"><path d=\"M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z\" /></svg>`,\n      ),\n    },\n  },\n  slack: /* #__PURE__ */ getIcon(\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"73 73 124 124\"><style>.st0{fill:#e01e5a}.st1{fill:#36c5f0}.st2{fill:#2eb67d}.st3{fill:#ecb22e}</style><path d=\"M99.4 151.2c0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9h12.9v12.9zM105.9 151.2c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v32.3c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9v-32.3z\" class=\"st0\"/><path d=\"M118.8 99.4c-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v12.9h-12.9zM118.8 105.9c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H86.5c-7.1 0-12.9-5.8-12.9-12.9s5.8-12.9 12.9-12.9h32.3z\" class=\"st1\"/><path d=\"M170.6 118.8c0-7.1 5.8-12.9 12.9-12.9 7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9h-12.9v-12.9zM164.1 118.8c0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9V86.5c0-7.1 5.8-12.9 12.9-12.9 7.1 0 12.9 5.8 12.9 12.9v32.3z\" class=\"st2\"/><path d=\"M151.2 170.6c7.1 0 12.9 5.8 12.9 12.9 0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9v-12.9h12.9zM151.2 164.1c-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9h32.3c7.1 0 12.9 5.8 12.9 12.9 0 7.1-5.8 12.9-12.9 12.9h-32.3z\" class=\"st3\"/></svg>`,\n  ),\n  zoom: /* #__PURE__ */ getIcon(\n    `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 351.845 80\"><path d=\"M73.786 78.835H10.88A10.842 10.842 0 0 1 .833 72.122a10.841 10.841 0 0 1 2.357-11.85L46.764 16.7h-31.23C6.954 16.699 0 9.744 0 1.165h58.014c4.414 0 8.357 2.634 10.046 6.712a10.843 10.843 0 0 1-2.356 11.85L22.13 63.302h36.122c8.58 0 15.534 6.955 15.534 15.534Zm278.059-48.544C351.845 13.588 338.256 0 321.553 0c-8.934 0-16.975 3.89-22.524 10.063C293.48 3.89 285.44 0 276.505 0c-16.703 0-30.291 13.588-30.291 30.291v48.544c8.579 0 15.534-6.955 15.534-15.534v-33.01c0-8.137 6.62-14.757 14.757-14.757s14.757 6.62 14.757 14.757v33.01c0 8.58 6.955 15.534 15.534 15.534V30.291c0-8.137 6.62-14.757 14.757-14.757s14.758 6.62 14.758 14.757v33.01c0 8.58 6.954 15.534 15.534 15.534V30.291ZM238.447 40c0 22.091-17.909 40-40 40s-40-17.909-40-40 17.908-40 40-40 40 17.909 40 40Zm-15.534 0c0-13.512-10.954-24.466-24.466-24.466S173.98 26.488 173.98 40s10.953 24.466 24.466 24.466S222.913 53.512 222.913 40Zm-70.68 0c0 22.091-17.909 40-40 40s-40-17.909-40-40 17.909-40 40-40 40 17.909 40 40Zm-15.534 0c0-13.512-10.954-24.466-24.466-24.466S87.767 26.488 87.767 40s10.954 24.466 24.466 24.466S136.699 53.512 136.699 40Z\" style=\"fill:#0b5cff\"/></svg>`,\n  ),\n};\n\nexport const asanaService = (options: ProviderWithDefaultClientOptions) =>\n  new OAuthService({\n    client: new OAuth.PKCEClient({\n      redirectMethod: OAuth.RedirectMethod.Web,\n      providerName: \"Asana\",\n      providerIcon: PROVIDERS_ICONS.asana,\n      providerId: \"asana\",\n      description: \"Connect your Asana account\",\n    }),\n    clientId: options.clientId ?? PROVIDER_CLIENT_IDS.asana,\n    authorizeUrl: options.authorizeUrl ?? \"https://asana.oauth.raycast.com/authorize\",\n    tokenUrl: options.tokenUrl ?? \"https://asana.oauth.raycast.com/token\",\n    refreshTokenUrl: options.refreshTokenUrl ?? \"https://asana.oauth.raycast.com/refresh-token\",\n    scope: options.scope,\n    personalAccessToken: options.personalAccessToken,\n    onAuthorize: options.onAuthorize,\n    bodyEncoding: options.bodyEncoding,\n    tokenRefreshResponseParser: options.tokenRefreshResponseParser,\n    tokenResponseParser: options.tokenResponseParser,\n  });\n\nexport const githubService = (options: ProviderWithDefaultClientOptions) =>\n  new OAuthService({\n    client: new OAuth.PKCEClient({\n      redirectMethod: OAuth.RedirectMethod.Web,\n      providerName: \"GitHub\",\n      providerIcon: PROVIDERS_ICONS.github,\n      providerId: \"github\",\n      description: \"Connect your GitHub account\",\n    }),\n    clientId: options.clientId ?? PROVIDER_CLIENT_IDS.github,\n    authorizeUrl: options.authorizeUrl ?? \"https://github.oauth.raycast.com/authorize\",\n    tokenUrl: options.tokenUrl ?? \"https://github.oauth.raycast.com/token\",\n    refreshTokenUrl: options.refreshTokenUrl ?? \"https://github.oauth.raycast.com/refresh-token\",\n    scope: options.scope,\n    personalAccessToken: options.personalAccessToken,\n    onAuthorize: options.onAuthorize,\n    bodyEncoding: options.bodyEncoding,\n    tokenRefreshResponseParser: options.tokenRefreshResponseParser,\n    tokenResponseParser: options.tokenResponseParser,\n  });\n\nexport const googleService = (options: ProviderOptions) =>\n  new OAuthService({\n    client: new OAuth.PKCEClient({\n      redirectMethod: OAuth.RedirectMethod.AppURI,\n      providerName: \"Google\",\n      providerIcon: PROVIDERS_ICONS.google,\n      providerId: \"google\",\n      description: \"Connect your Google account\",\n    }),\n    clientId: options.clientId,\n    authorizeUrl: options.authorizeUrl ?? \"https://accounts.google.com/o/oauth2/v2/auth\",\n    tokenUrl: options.tokenUrl ?? \"https://oauth2.googleapis.com/token\",\n    refreshTokenUrl: options.tokenUrl,\n    scope: options.scope,\n    personalAccessToken: options.personalAccessToken,\n    bodyEncoding: options.bodyEncoding ?? \"url-encoded\",\n    onAuthorize: options.onAuthorize,\n    tokenRefreshResponseParser: options.tokenRefreshResponseParser,\n    tokenResponseParser: options.tokenResponseParser,\n  });\n\nexport const jiraService = (options: ProviderOptions) =>\n  new OAuthService({\n    client: new OAuth.PKCEClient({\n      redirectMethod: OAuth.RedirectMethod.Web,\n      providerName: \"Jira\",\n      providerIcon: PROVIDERS_ICONS.jira,\n      providerId: \"jira\",\n      description: \"Connect your Jira account\",\n    }),\n    clientId: options.clientId,\n    authorizeUrl: options.authorizeUrl ?? \"https://auth.atlassian.com/authorize\",\n    tokenUrl: options.tokenUrl ?? \"https://auth.atlassian.com/oauth/token\",\n    refreshTokenUrl: options.refreshTokenUrl,\n    scope: options.scope,\n    personalAccessToken: options.personalAccessToken,\n    onAuthorize: options.onAuthorize,\n    bodyEncoding: options.bodyEncoding,\n    tokenRefreshResponseParser: options.tokenRefreshResponseParser,\n    tokenResponseParser: options.tokenResponseParser,\n  });\n\nexport const linearService = (options: ProviderWithDefaultClientOptions) =>\n  new OAuthService({\n    client: new OAuth.PKCEClient({\n      redirectMethod: OAuth.RedirectMethod.Web,\n      providerName: \"Linear\",\n      providerIcon: PROVIDERS_ICONS.linear,\n      providerId: \"linear\",\n      description: \"Connect your Linear account\",\n    }),\n    clientId: options.clientId ?? PROVIDER_CLIENT_IDS.linear,\n    authorizeUrl: options.authorizeUrl ?? \"https://linear.oauth.raycast.com/authorize\",\n    tokenUrl: options.tokenUrl ?? \"https://linear.oauth.raycast.com/token\",\n    refreshTokenUrl: options.refreshTokenUrl ?? \"https://linear.oauth.raycast.com/refresh-token\",\n    scope: options.scope,\n    extraParameters: {\n      actor: \"user\",\n    },\n    onAuthorize: options.onAuthorize,\n    bodyEncoding: options.bodyEncoding,\n    tokenRefreshResponseParser: options.tokenRefreshResponseParser,\n    tokenResponseParser: options.tokenResponseParser,\n  });\n\nexport const slackService = (options: ProviderWithDefaultClientOptions) =>\n  new OAuthService({\n    client: new OAuth.PKCEClient({\n      redirectMethod: OAuth.RedirectMethod.Web,\n      providerName: \"Slack\",\n      providerIcon: PROVIDERS_ICONS.slack,\n      providerId: \"slack\",\n      description: \"Connect your Slack account\",\n    }),\n    clientId: options.clientId ?? PROVIDER_CLIENT_IDS.slack,\n    authorizeUrl: options.authorizeUrl ?? \"https://slack.oauth.raycast.com/authorize\",\n    tokenUrl: options.tokenUrl ?? \"https://slack.oauth.raycast.com/token\",\n    refreshTokenUrl: options.tokenUrl ?? \"https://slack.oauth.raycast.com/refresh-token\",\n    scope: \"\",\n    extraParameters: {\n      user_scope: options.scope,\n    },\n    personalAccessToken: options.personalAccessToken,\n    bodyEncoding: options.tokenUrl ? options.bodyEncoding ?? \"url-encoded\" : \"json\",\n    onAuthorize: options.onAuthorize,\n    tokenRefreshResponseParser: options.tokenRefreshResponseParser,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    tokenResponseParser:\n      options.tokenResponseParser ??\n      ((response: any) => {\n        return {\n          access_token: response.authed_user.access_token,\n          scope: response.authed_user.scope,\n        };\n      }),\n  });\n\nexport const zoomService = (options: ProviderOptions) =>\n  new OAuthService({\n    client: new OAuth.PKCEClient({\n      redirectMethod: OAuth.RedirectMethod.Web,\n      providerName: \"Zoom\",\n      providerIcon: PROVIDERS_ICONS.zoom,\n      providerId: \"zoom\",\n      description: \"Connect your Zoom account\",\n    }),\n    clientId: options.clientId,\n    authorizeUrl: options.authorizeUrl ?? \"https://zoom.us/oauth/authorize\",\n    tokenUrl: options.tokenUrl ?? \"https://zoom.us/oauth/token\",\n    refreshTokenUrl: options.refreshTokenUrl,\n    scope: options.scope,\n    personalAccessToken: options.personalAccessToken,\n    bodyEncoding: options.bodyEncoding ?? \"url-encoded\",\n    onAuthorize: options.onAuthorize,\n    tokenRefreshResponseParser: options.tokenRefreshResponseParser,\n    tokenResponseParser: options.tokenResponseParser,\n  });\n", "import React from \"react\";\nimport { environment, OAuth } from \"@raycast/api\";\nimport type { OAuthType, OnAuthorizeParams } from \"./types\";\n\nlet token: string | null = null;\nlet type: OAuthType | null = null;\nlet authorize: { read(): string } | null = null;\nlet getIdToken: { read(): OAuth.TokenSet | undefined } | null = null;\nlet onAuthorize: { read(): void } | null = null;\n\ntype WithAccessTokenParameters = {\n  /**\n   * An optional instance of a PKCE Client that you can create using Raycast API.\n   * This client is used to return the `idToken` as part of the `onAuthorize` callback.\n   */\n  client?: OAuth.PKCEClient;\n  /**\n   * A function that initiates the OAuth token retrieval process\n   * @returns a promise that resolves to an access token.\n   */\n  authorize: () => Promise<string>;\n  /**\n   * An optional string that represents an already obtained personal access token\n   */\n  personalAccessToken?: string;\n  /**\n   * An optional callback function that is called once the user has been properly logged in through OAuth.\n   * @param {object} params - Parameters of the callback\n   * @param {string} options.token - The retrieved access token\n   * @param {string} options.type - The access token's type (either `oauth` or `personal`)\n   * @param {string} options.idToken - The optional id token. The `idToken` is returned if `options.client` is provided and if it's returned in the initial token set.\n   */\n  onAuthorize?: (params: OnAuthorizeParams) => void;\n};\n\n/**\n * The component (for a view/menu-bar commands) or function (for a no-view command) that is passed to withAccessToken.\n */\nexport type WithAccessTokenComponentOrFn<T = any> = ((params: T) => Promise<void> | void) | React.ComponentType<T>;\n\n/**\n * Higher-order component to wrap a given component or function and set an access token in a shared global variable.\n *\n * The function intercepts the component rendering process to either fetch an OAuth token asynchronously\n * or use a provided personal access token. A global variable will be then set with the received token\n * that you can get with the `getAccessToken` function.\n *\n * @example\n * ```typescript\n * import { Detail } from \"@raycast/api\";\n * import { OAuthService, getAccessToken, withAccessToken } from \"@raycast/utils\";\n *\n * const github = OAuthService.github({ scope: \"notifications repo read:org read:user read:project\" });\n *\n * function AuthorizedComponent() {\n *  const { token } = getAccessToken();\n *  ...\n * }\n *\n * export default withAccessToken(github)(AuthorizedComponent);\n * ```\n *\n * @param {object} options - Configuration options for the token retrieval.\n * @param {() => Promise<string>} options.authorize - A function to retrieve an OAuth token.\n * @param {string} options.personalAccessToken - An optional personal access token.\n * @returns {React.ComponentType<T>} The wrapped component.\n */\nexport function withAccessToken<T = any>(\n  options: WithAccessTokenParameters,\n): <U extends WithAccessTokenComponentOrFn<T>>(\n  fnOrComponent: U,\n) => U extends (props: T) => Promise<void> | void ? Promise<void> : React.FunctionComponent<T>;\nexport function withAccessToken<T>(options: WithAccessTokenParameters) {\n  if (environment.commandMode === \"no-view\") {\n    return (fn: (props: T) => Promise<void> | (() => void)) => {\n      const noViewFn = async (props: T) => {\n        if (!token) {\n          token = options.personalAccessToken ?? (await options.authorize());\n          type = options.personalAccessToken ? \"personal\" : \"oauth\";\n          const idToken = (await options.client?.getTokens())?.idToken;\n\n          if (options.onAuthorize) {\n            await Promise.resolve(options.onAuthorize({ token, type, idToken }));\n          }\n        }\n\n        return fn(props);\n      };\n\n      return noViewFn;\n    };\n  }\n\n  return (Component: React.ComponentType<T>) => {\n    const WrappedComponent: React.ComponentType<T> = (props) => {\n      if (options.personalAccessToken) {\n        token = options.personalAccessToken;\n        type = \"personal\";\n      } else {\n        if (!authorize) {\n          authorize = wrapPromise(options.authorize());\n        }\n        token = authorize.read();\n        type = \"oauth\";\n      }\n\n      let idToken: string | undefined;\n      if (options.client) {\n        if (!getIdToken) {\n          getIdToken = wrapPromise(options.client.getTokens());\n        }\n        idToken = getIdToken.read()?.idToken;\n      }\n\n      if (!onAuthorize && options.onAuthorize) {\n        onAuthorize = wrapPromise(Promise.resolve(options.onAuthorize({ token, type, idToken })));\n      }\n      onAuthorize?.read();\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore too complicated for TS\n      return <Component {...props} />;\n    };\n\n    WrappedComponent.displayName = `withAccessToken(${Component.displayName || Component.name})`;\n\n    return WrappedComponent;\n  };\n}\n\n/**\n * Returns the access token and its type. Note that this function must be called in a component wrapped with `withAccessToken`.\n *\n * @throws {Error} If called outside of a component wrapped with `withAccessToken`\n * @returns {{ token: string, type: \"oauth\" | \"personal\" }} An object containing the `token`\n * and its `type`, where type can be either 'oauth' for OAuth tokens or 'personal' for a\n * personal access token.\n */\nexport function getAccessToken() {\n  if (!token || !type) {\n    throw new Error(\"getAccessToken must be used when authenticated (eg. used inside `withAccessToken`)\");\n  }\n\n  return { token, type };\n}\n\nfunction wrapPromise<T>(promise: Promise<T>): { read(): T } {\n  let status = \"pending\";\n  let response: T;\n\n  const suspender = promise.then(\n    (res) => {\n      status = \"success\";\n      response = res;\n    },\n    (err) => {\n      status = \"error\";\n      response = err;\n    },\n  );\n\n  const read = () => {\n    switch (status) {\n      case \"pending\":\n        throw suspender;\n      case \"error\":\n        throw response;\n      default:\n        return response;\n    }\n  };\n\n  return { read };\n}\n", "import childProcess from \"node:child_process\";\nimport {\n  defaultParsing,\n  getSpawnedPromise,\n  getSpawnedResult,\n  handleOutput,\n  ParseExecOutputHandler,\n} from \"./exec-utils\";\n\ntype AppleScriptOptions = {\n  /**\n   * By default, `runAppleScript` returns its results in human-readable form: strings do not have quotes around them, characters are not escaped, braces for lists and records are omitted, etc. This is generally more useful, but can introduce ambiguities. For example, the lists `{\"foo\", \"bar\"}` and `{{\"foo\", {\"bar\"}}}` would both be displayed as ‘foo, bar’. To see the results in an unambiguous form that could be recompiled into the same value, set `humanReadableOutput` to `false`.\n   *\n   * @default true\n   */\n  humanReadableOutput?: boolean;\n  /**\n   * Whether the script is using [`AppleScript`](https://developer.apple.com/library/archive/documentation/AppleScript/Conceptual/AppleScriptLangGuide/introduction/ASLR_intro.html#//apple_ref/doc/uid/**********) or [`JavaScript`](https://developer.apple.com/library/archive/releasenotes/InterapplicationCommunication/RN-JavaScriptForAutomation/Articles/Introduction.html#//apple_ref/doc/uid/TP40014508-CH111-SW1).\n   *\n   * @default \"AppleScript\"\n   */\n  language?: \"AppleScript\" | \"JavaScript\";\n  /**\n   * A Signal object that allows you to abort the request if required via an AbortController object.\n   */\n  signal?: AbortSignal;\n  /** If timeout is greater than `0`, the parent will send the signal `SIGTERM` if the child runs longer than timeout milliseconds.\n   *\n   * @default 10000\n   */\n  timeout?: number;\n};\n\n/**\n * Executes an AppleScript script.\n *\n * @example\n * ```typescript\n * import { showHUD } from \"@raycast/api\";\n * import { runAppleScript, showFailureToast } from \"@raycast/utils\";\n *\n * export default async function () {\n *   try {\n *     const res = await runAppleScript(\n *       `\n *       on run argv\n *         return \"hello, \" & item 1 of argv & \".\"\n *       end run\n *       `,\n *       [\"world\"]\n *     );\n *     await showHUD(res);\n *   } catch (error) {\n *     showFailureToast(error, { title: \"Could not run AppleScript\" });\n *   }\n * }\n * ```\n */\nexport async function runAppleScript<T = string>(\n  script: string,\n  options?: AppleScriptOptions & {\n    parseOutput?: ParseExecOutputHandler<T, string, AppleScriptOptions>;\n  },\n): Promise<string>;\nexport async function runAppleScript<T = string>(\n  script: string,\n  /**\n   * The arguments to pass to the script.\n   */\n  args: string[],\n  options?: AppleScriptOptions & {\n    parseOutput?: ParseExecOutputHandler<T, string, AppleScriptOptions>;\n  },\n): Promise<string>;\nexport async function runAppleScript<T = string>(\n  script: string,\n  optionsOrArgs?:\n    | string[]\n    | (AppleScriptOptions & {\n        parseOutput?: ParseExecOutputHandler<T, string, AppleScriptOptions>;\n      }),\n  options?: AppleScriptOptions & {\n    parseOutput?: ParseExecOutputHandler<T, string, AppleScriptOptions>;\n  },\n): Promise<string> {\n  const { humanReadableOutput, language, timeout, ...execOptions } = Array.isArray(optionsOrArgs)\n    ? options || {}\n    : optionsOrArgs || {};\n\n  const outputArguments = humanReadableOutput !== false ? [] : [\"-ss\"];\n  if (language === \"JavaScript\") {\n    outputArguments.push(\"-l\", \"JavaScript\");\n  }\n  if (Array.isArray(optionsOrArgs)) {\n    outputArguments.push(\"-\", ...optionsOrArgs);\n  }\n\n  const spawned = childProcess.spawn(\"osascript\", outputArguments, {\n    ...execOptions,\n    env: { PATH: \"/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin\" },\n  });\n  const spawnedPromise = getSpawnedPromise(spawned, { timeout: timeout || 10000 });\n\n  spawned.stdin.end(script);\n\n  const [{ error, exitCode, signal, timedOut }, stdoutResult, stderrResult] = await getSpawnedResult<string>(\n    spawned,\n    { encoding: \"utf8\" },\n    spawnedPromise,\n  );\n  const stdout = handleOutput({ stripFinalNewline: true }, stdoutResult);\n  const stderr = handleOutput({ stripFinalNewline: true }, stderrResult);\n\n  return defaultParsing({\n    stdout,\n    stderr,\n    error,\n    exitCode,\n    signal,\n    timedOut,\n    command: \"osascript\",\n    options,\n    parentError: new Error(),\n  });\n}\n"], "names": [], "version": 3, "file": "module.js.map"}