{"version": 3, "file": "useCachedPromise.d.ts", "sourceRoot": "", "sources": ["../src/useCachedPromise.ts"], "names": [], "mappings": "AACA,OAAO,EACL,wBAAwB,EACxB,0BAA0B,EAE1B,iCAAiC,EACjC,YAAY,EAEb,MAAM,SAAS,CAAC;AAEjB,OAAO,EAAc,cAAc,EAAE,MAAM,cAAc,CAAC;AAO1D,MAAM,MAAM,oBAAoB,CAC9B,CAAC,SAAS,wBAAwB,GAAG,iCAAiC,EACtE,CAAC,IACC,cAAc,CAAC,CAAC,CAAC,GAAG;IACtB;;OAEG;IACH,WAAW,CAAC,EAAE,CAAC,CAAC;IAChB;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,iCAAiC,CAAC,EAAE,CAAC,EAC9E,EAAE,EAAE,CAAC,GACJ,0BAA0B,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC1D,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,iCAAiC,EAAE,CAAC,SAAS,GAAG,EAAE,GAAG,GAAG,EAAE,EACnG,EAAE,EAAE,CAAC,EACL,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EACnB,OAAO,CAAC,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,GACnC,0BAA0B,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAElD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,wBAAwB,CAAC,EAAE,CAAC,EACrE,EAAE,EAAE,CAAC,GACJ,0BAA0B,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC1D,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,wBAAwB,EAAE,CAAC,GAAG,SAAS,EAChF,EAAE,EAAE,CAAC,EACL,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EACnB,OAAO,CAAC,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,GACnC,0BAA0B,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC"}