"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withCache = void 0;
const api_1 = require("@raycast/api");
const helpers_1 = require("./helpers");
/**
 * Wraps a function with caching functionality using Raycast's Cache API.
 * Allows for caching of expensive functions like paginated API calls that rarely change.
 *
 * @param fn - The async function to cache results from
 * @param options - Optional configuration for the cache behavior
 * @param options.validate - Optional validation function for cached data
 * @param options.maxAge - Maximum age of cached data in milliseconds
 * @returns An async function that returns the result of the function, either from cache or fresh execution
 *
 * @example
 * ```ts
 * const cachedFunction = withCache(fetchExpensiveData, {
 *   maxAge: 5 * 60 * 1000 // Cache for 5 minutes
 * });
 *
 * const result = await cachedFunction(query);
 * ```
 */
function withCache(fn, options) {
    const cache = new api_1.Cache({ namespace: (0, helpers_1.hash)(fn) });
    const wrappedFn = async (...args) => {
        const key = (0, helpers_1.hash)(args || []) + options?.internal_cacheKeySuffix;
        const cached = cache.get(key);
        if (cached) {
            const { data, timestamp } = JSON.parse(cached, helpers_1.reviver);
            const isExpired = options?.maxAge && Date.now() - timestamp > options.maxAge;
            if (!isExpired && (!options?.validate || options.validate(data))) {
                return data;
            }
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const result = await fn(...args);
        cache.set(key, JSON.stringify({
            data: result,
            timestamp: Date.now(),
        }, helpers_1.replacer));
        return result;
    };
    wrappedFn.clearCache = () => {
        cache.clear();
    };
    // @ts-expect-error too complex for TS
    return wrappedFn;
}
exports.withCache = withCache;
